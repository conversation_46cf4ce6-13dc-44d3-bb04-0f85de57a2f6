#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阿里云短信服务配置文件
"""

# 阿里云访问凭证
ALIYUN_ACCESS_KEY_ID = 'LTAI5t9h7n35k8RUwcBBVuYn'  # 替换为实际的Access Key ID
ALIYUN_ACCESS_KEY_SECRET = '******************************'  # 替换为实际的Access Key Secret

# 短信服务配置
SMS_SIGN_NAME = '北京联东物业管理股份'  # 短信签名名称
SMS_REGION_ID = 'cn-beijing'  # 区域ID，使用北京区域

# 短信模板配置
SMS_TEMPLATE_CODE = 'SMS_484695223'  # 通知消息模板ID

# 发送短信相关配置
SMS_SEND_CONFIG = {
    'timeout': 10,  # 连接超时时间(秒)
    'max_retries': 3,  # 最大重试次数
    'retry_delay': 2,  # 重试延迟时间(秒)
}

# 短信发送频率限制
SMS_RATE_LIMIT = {
    'per_day': 10,      # 每个手机号每天最大发送次数
    'per_hour': 5,      # 每个手机号每小时最大发送次数
    'interval': 60,     # 同一手机号两次发送最小间隔(秒)
} 