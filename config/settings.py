#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优推科技和中介ERP系统对接配置文件
"""

# API环境配置
ENVIRONMENT = 'production'  # 'test' 或 'production'

# 城市配置
DEFAULT_CITY = 'cc'  # 默认城市代码: 'bj'(北京), 'cc'(长春), 等

# 城市域名映射
CITY_DOMAINS = {
"bj": "bj.youtui01.com",
        "cc": "cc.youtui01.com",
        "sz": "sz.youtui01.com",
        "gz": "gz.youtui01.com",
        "tj": "tj.youtui01.com",
        "nmgbt": "nmgbt.youtui01.com",
        "sxbaoji": "sxbaoji.youtui01.com",
        "hbbd": "hbbd.youtui01.com",
        "jscz": "jscz.youtui01.com",
        "cd": "cd.youtui01.com",
        "hbchengde": "hbchengde.youtui01.com",
        "ahchuzhou": "ahchuzhou.youtui01.com",
        "dl": "dl.youtui01.com",
        "scdeyang": "scdeyang.youtui01.com",
        "sddz": "sddz.youtui01.com",
        "dg": "dg.youtui01.com",
        "fs": "fs.youtui01.com",
        "fz": "fz.youtui01.com",
        "gy": "gy.youtui01.com",
        "hrb": "hrb.youtui01.com",
        "hz": "hz.youtui01.com",
        "hf": "hf.youtui01.com",
        "xjht": "xjht.youtui01.com",
        "zjzhuzhou": "zjzhuzhou.youtui01.com",
        "jsha": "jsha.youtui01.com",
        "gdhz": "gdhz.youtui01.com",
        "jn": "jn.youtui01.com",
        "zjzx": "zjzx.youtui01.com",
        "gdjm": "gdjm.youtui01.com",
        "zjjh": "zjjh.youtui01.com",
        "jxjj": "jxjj.youtui01.com",
        "km": "km.youtui01.com",
        "hblf": "hblf.youtui01.com",
        "jslyg": "jslyg.youtui01.com",
        "gxliuzhou": "gxliuzhou.youtui01.com",
        "hnluoyang": "hnluoyang.youtui01.com",
        "scms": "scms.youtui01.com",
        "scmianyang": "scmianyang.youtui01.com",
        "jxnc": "jxnc.youtui01.com",
        "nj": "nj.youtui01.com",
        "gxnn": "gxnn.youtui01.com",
        "jsnt": "jsnt.youtui01.com",
        "hnny": "hnny.youtui01.com",
        "zjnb": "zjnb.youtui01.com",
        "fjpt": "fjpt.youtui01.com",
        "sdqd": "sdqd.youtui01.com",
        "gdqingyuan": "gdqingyuan.youtui01.com",
        "qz": "qz.youtui01.com",
        "xm": "xm.youtui01.com",
        "gdst": "gdst.youtui01.com",
        "gdsw": "gdsw.youtui01.com",
        "sh": "sh.youtui01.com",
        "zjsx": "zjsx.youtui01.com",
        "sy": "sy.youtui01.com",
        "sjz": "sjz.youtui01.com",
        "jssz": "jssz.youtui01.com",
        "jssuqian": "jssuqian.youtui01.com",
        "scsuining": "scsuining.youtui01.com",
        "zjtz": "zjtz.youtui01.com",
        "ty": "ty.youtui01.com",
        "jstz": "jstz.youtui01.com",
        "hbts": "hbts.youtui01.com",
        "sdwh": "sdwh.youtui01.com",
        "sdwf": "sdwf.youtui01.com",
        "sxwn": "sxwn.youtui01.com",
        "wx": "wx.youtui01.com",
        "ahwuhu": "ahwuhu.youtui01.com",
        "wh": "wh.youtui01.com",
        "xa": "xa.youtui01.com",
        "hnxiangtan": "hnxiangtan.youtui01.com",
        "hnxx": "hnxx.youtui01.com",
        "jxxinyu": "jxxinyu.youtui01.com",
        "jsxz": "jsxz.youtui01.com",
        "hnxc": "hnxc.youtui01.com",
        "sdyt": "sdyt.youtui01.com",
        "jsyancheng": "jsyancheng.youtui01.com",
        "jsyz": "jsyz.youtui01.com",
        "hbyc": "hbyc.youtui01.com",
        "hnyyueyang": "hnyy.youtui01.com",
        "fjzhangzhou": "fjzhangzhou.youtui01.com",
        "cs": "cs.youtui01.com",
        "jszj": "jszj.youtui01.com",
        "zz": "zz.youtui01.com",
        "gdzs": "gdzs.youtui01.com",
        "cq": "cq.youtui01.com",
        "hnzk": "hnzk.youtui01.com",
        "zh": "zh.youtui01.com",
        "sdzb": "sdzb.youtui01.com"
        
        }


# 城市名称到城市代码的映射
CITY_NAME_TO_CODE = {
    # 标准格式
    '上海': 'sh',
    '北京': 'bj',
    '天津': 'tj',
    '深圳': 'sz',
    '长春': 'cc',
    
    # 带"市"的格式
    '上海市': 'sh',
    '北京市': 'bj',
    '天津市': 'tj',
    '深圳市': 'sz',
    '长春市': 'cc',
    # 可根据需要添加更多城市
}

# API基础URL
API_BASE_URLS = {
    'test': f'http://{CITY_DOMAINS[DEFAULT_CITY]}/youtui/',
    'production': f'http://{CITY_DOMAINS[DEFAULT_CITY]}/youtui/'
}

# API端点 - 使用正确的格式（带问号）
API_ENDPOINTS = {
    # 正确的格式（带问号）
    'user_bind': '?api/user/open.html',           # 账号绑定接口
    'user_unbind': '?api/user/unbind.html',       # 账号解除绑定接口
    'house_sync': '?api/house/open.html',         # 房源同步/更新接口
    'house_delete': '?api/house/del.html',        # 房源删除接口
    'house_remove': '?api/house/remove.html',     # 外网房源下架接口
    'user_login': '?api/user/login.html',         # 账号自动登录接口
    'community_info': '?api/house/communityinfo.html',  # 楼盘详情接口
    'house_gethouse': '?api/house/gethouse.html', # 获取经纪人名下所有帖子点击量接口
    
    # 网站账号管理相关接口
    'website_manage': '?api/website/open.html',   # 网站账号导入/更新/验证接口
    'website_delete': '?api/website/del.html',    # 网站账号删除接口
    'website_info': '?api/website/info.html',     # 获取网站账号信息接口
    'website_push_info': '?api/website/pushinfo.html'  # 获取房源推送详情接口
}

# 平台信息
PLATFORM_ID = '10007'  # 替换为实际的平台ID
COMPANY_ID = '1013'   # 替换为实际的公司ID

# 城市对应的用户账号配置
CITY_USER_ACCOUNTS = {
    'cc': {  # 长春
        'user_id': '1505526',
        'user_key': '********'
    },
    'bj': {  # 北京
        'user_id': '***********',
        'user_key': 'meiyoumima333'
    },
    'tj': {  # 天津
        'user_id': '***********',
        'user_key': 'meiyoumima333'
    },
    'sh': {  # 上海
        'user_id': '123456',
        'user_key': '123456'
    },
    'sz': {  # 深圳
        'user_id': '123',
        'user_key': '123'
    },
    # 可以根据需要添加更多城市的账号配置
}

# 调试模式
DEBUG = True

# 数据库配置
DB_CONFIG = {
    'host': '**********',
    'port': 3306,
    'user': 'root',
    'password': 'LianDodmx_03',
    'database': 'erp_integration'
}
