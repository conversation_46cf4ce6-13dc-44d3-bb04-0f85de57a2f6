#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频生成服务配置
"""

import os

# 远程视频生成服务配置
REMOTE_VIDEO_SERVICE = {
    # 服务基础地址
    "base_url": os.getenv("VIDEO_SERVICE_URL", "http://8.141.20.160:8003/api/v1"),
    
    # API超时设置（秒）
    "timeout": {
        "connect": 30,      # 连接超时
        "read": 300,        # 读取超时
        "upload": 600       # 文件上传超时
    },
    
    # 重试配置
    "retry": {
        "max_attempts": 3,  # 最大重试次数
        "delay": 2,         # 重试间隔（秒）
        "backoff": 2        # 退避倍数
    },
    
    # 任务轮询配置
    "polling": {
        "interval": 5,      # 轮询间隔（秒）
        "max_wait": 1800    # 最大等待时间（秒，30分钟）
    },
    
    # 默认视频生成参数
    "defaults": {
        "fps": 30,
        "image_duration": 3.0,
        "transition_type": "push_right",
        "voice_id": "male-qn-qingse",
        "emotion": "happy"
    }
}


