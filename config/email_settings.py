#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
邮件服务配置文件
支持163邮箱SMTP发送
"""

# 163邮箱SMTP配置
EMAIL_CONFIG = {
    'smtp_server': 'smtp.163.com',
    'smtp_port': 465,  # SSL端口
    'use_ssl': True,
    'username': '<EMAIL>',  # 请填入您的163邮箱地址
    'password': 'MTbCgQ8QXyypBY34',  # 请填入您的163邮箱授权码（不是登录密码）
    'sender_name': '联东ERP系统',  # 发件人显示名称
}

# 收件人配置
EMAIL_RECIPIENTS = {
    'offline_report': [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ],
    'admin': [
        # '<EMAIL>',
        # 管理员邮箱地址
    ]
}

# 邮件模板配置
EMAIL_TEMPLATES = {
    'offline_report': {
        'subject': '帖子下架结果报告 - {city} - {timestamp}',
        'template_file': 'offline_report_template.html'
    }
}

# 邮件发送配置
EMAIL_SEND_CONFIG = {
    'timeout': 30,  # 连接超时时间(秒)
    'max_retries': 3,  # 最大重试次数
    'retry_delay': 5,  # 重试延迟时间(秒)
    'batch_size': 10,  # 批量发送时每批次的邮件数量
    'batch_delay': 2,  # 批次间延迟时间(秒)
}

# 邮件内容配置
EMAIL_CONTENT_CONFIG = {
    'charset': 'utf-8',
    'html_enabled': True,
    'attachment_enabled': True,
    'max_attachment_size': 10 * 1024 * 1024,  # 10MB
}
