#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阿里云OSS对象存储配置文件
"""

import os
import json
from pathlib import Path

# 获取配置文件路径
oss_config_path = Path(__file__).parent / "oss_config.json"

# 从JSON配置文件加载配置
def load_oss_config():
    try:
        if not oss_config_path.exists():
            raise FileNotFoundError(f"OSS配置文件不存在: {oss_config_path}")

        with open(oss_config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            return config.get("oss_settings", {})
    except Exception as e:
        print(f"加载OSS配置失败: {str(e)}")
        return {}

# 加载配置
oss_config = load_oss_config()

# OSS访问凭证
ACCESS_KEY_ID = oss_config.get("access_key_id", "")
ACCESS_KEY_SECRET = oss_config.get("access_key_secret", "")

# OSS区域节点
ENDPOINT = oss_config.get("endpoint", "oss-cn-beijing.aliyuncs.com")

# OSS存储空间名称
BUCKET_NAME = oss_config.get("bucket_name", "")

# OSS域名（可选，如果有自定义域名）
OSS_DOMAIN = oss_config.get("oss_domain", "")

# 文件存储的各种前缀路径
STORAGE_PATHS = {
    # 房源相关图片
    "house": {
        "thumbnail": "houses/thumbnails",  # 缩略图路径
        "photooutdoor": "houses/outdoor",  # 外景图路径
        "photointerior": "houses/interior",  # 室内图路径
        "floorplans": "houses/floorplans",  # 户型图路径
    },

    # 用户相关文件
    "user": {
        "avatar": "users/avatars",  # 用户头像路径
        "idcard": "users/idcards",  # 身份证照片路径
        "certificate": "users/certificates",  # 证书照片路径
    },

    # 合同相关文件
    "contract": {
        "document": "contracts/documents",  # 合同文档路径
        "attachment": "contracts/attachments",  # 附件路径
    },

    # 临时文件
    "temp": "temp",
}

# 文件访问URL的有效期（秒）
URL_EXPIRES = 3600  # 1小时

# 允许的文件类型
ALLOWED_FILE_TYPES = {
    "image": ["jpg", "jpeg", "png", "gif", "webp"],
    "document": ["pdf", "doc", "docx", "xls", "xlsx", "txt"],
    "video": ["mp4", "mov", "avi"],
}

# 文件大小限制（单位：字节）
FILE_SIZE_LIMITS = {
    "image": 20 * 1024 * 1024,  # 20MB
    "document": 20 * 1024 * 1024,  # 20MB
    "video": 100 * 1024 * 1024,  # 100MB
}