#!/bin/bash

# 定义变量
PORT=8000
WORKERS=10  # 10个进程
LOG_FILE="liando_erp_integration.log"
APP_COMMAND="uvicorn main:app --host 0.0.0.0 --port $PORT --reload"

# 停止 FastAPI 程序
stop_fastapi() {
    echo "Stopping FastAPI running on port $PORT..."
    # 尝试使用 lsof 查找进程
    if command -v lsof > /dev/null 2>&1; then
        PID=$(lsof -t -i :$PORT)
    else
        # 如果没有 lsof，使用 netstat 查找进程
        PID=$(netstat -tlnp | grep ":$PORT" | awk '{print $7}' | cut -d'/' -f1)
    fi
    if [ -z "$PID" ]; then
        echo "No FastAPI process found running on port $PORT."
    else
        kill $PID
        echo "FastAPI process (PID: $PID) stopped."
    fi
}

# 启动 FastAPI 程序
start_fastapi() {
    echo "Starting FastAPI on port $PORT..."
    nohup $APP_COMMAND > $LOG_FILE 2>&1 &
    echo "FastAPI started. Logs are being written to $LOG_FILE."
}

# 重启 FastAPI 程序
restart_fastapi() {
    stop_fastapi
    sleep 2  # 等待进程完全停止
    start_fastapi
}

# 根据参数执行操作
case "$1" in
    start)
        start_fastapi
        ;;
    stop)
        stop_fastapi
        ;;
    restart)
        restart_fastapi
        ;;
    *)
        echo "Usage: $0 {start|stop|restart}"
        exit 1
        ;;
esac