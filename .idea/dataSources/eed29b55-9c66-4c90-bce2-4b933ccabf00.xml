<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="智能托管">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultCasing>exact</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||root||ALTER|G
|root||root|localhost|ALTER|G
|root||root||ALTER ROUTINE|G
|root||root|localhost|ALTER ROUTINE|G
|root||root||APPLICATION_PASSWORD_ADMIN|G
|root||root|localhost|APPLICATION_PASSWORD_ADMIN|G
|root||mysql.infoschema|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.session|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.sys|localhost|AUDIT_ABORT_EXEMPT|G
|root||root||AUDIT_ABORT_EXEMPT|G
|root||root|localhost|AUDIT_ABORT_EXEMPT|G
|root||root||AUDIT_ADMIN|G
|root||root|localhost|AUDIT_ADMIN|G
|root||mysql.session|localhost|AUTHENTICATION_POLICY_ADMIN|G
|root||root||AUTHENTICATION_POLICY_ADMIN|G
|root||root|localhost|AUTHENTICATION_POLICY_ADMIN|G
|root||mysql.session|localhost|BACKUP_ADMIN|G
|root||root||BACKUP_ADMIN|G
|root||root|localhost|BACKUP_ADMIN|G
|root||root||BINLOG_ADMIN|G
|root||root|localhost|BINLOG_ADMIN|G
|root||root||BINLOG_ENCRYPTION_ADMIN|G
|root||root|localhost|BINLOG_ENCRYPTION_ADMIN|G
|root||mysql.session|localhost|CLONE_ADMIN|G
|root||root||CLONE_ADMIN|G
|root||root|localhost|CLONE_ADMIN|G
|root||mysql.session|localhost|CONNECTION_ADMIN|G
|root||root||CONNECTION_ADMIN|G
|root||root|localhost|CONNECTION_ADMIN|G
|root||root||CREATE|G
|root||root|localhost|CREATE|G
|root||root||CREATE ROLE|G
|root||root|localhost|CREATE ROLE|G
|root||root||CREATE ROUTINE|G
|root||root|localhost|CREATE ROUTINE|G
|root||root||CREATE TABLESPACE|G
|root||root|localhost|CREATE TABLESPACE|G
|root||root||CREATE TEMPORARY TABLES|G
|root||root|localhost|CREATE TEMPORARY TABLES|G
|root||root||CREATE USER|G
|root||root|localhost|CREATE USER|G
|root||root||CREATE VIEW|G
|root||root|localhost|CREATE VIEW|G
|root||root||DELETE|G
|root||root|localhost|DELETE|G
|root||root||DROP|G
|root||root|localhost|DROP|G
|root||root||DROP ROLE|G
|root||root|localhost|DROP ROLE|G
|root||root||ENCRYPTION_KEY_ADMIN|G
|root||root|localhost|ENCRYPTION_KEY_ADMIN|G
|root||root||EVENT|G
|root||root|localhost|EVENT|G
|root||root||EXECUTE|G
|root||root|localhost|EXECUTE|G
|root||root||FILE|G
|root||root|localhost|FILE|G
|root||mysql.infoschema|localhost|FIREWALL_EXEMPT|G
|root||mysql.session|localhost|FIREWALL_EXEMPT|G
|root||mysql.sys|localhost|FIREWALL_EXEMPT|G
|root||root||FIREWALL_EXEMPT|G
|root||root|localhost|FIREWALL_EXEMPT|G
|root||root||FLUSH_OPTIMIZER_COSTS|G
|root||root|localhost|FLUSH_OPTIMIZER_COSTS|G
|root||root||FLUSH_STATUS|G
|root||root|localhost|FLUSH_STATUS|G
|root||root||FLUSH_TABLES|G
|root||root|localhost|FLUSH_TABLES|G
|root||root||FLUSH_USER_RESOURCES|G
|root||root|localhost|FLUSH_USER_RESOURCES|G
|root||root||GROUP_REPLICATION_ADMIN|G
|root||root|localhost|GROUP_REPLICATION_ADMIN|G
|root||root||GROUP_REPLICATION_STREAM|G
|root||root|localhost|GROUP_REPLICATION_STREAM|G
|root||root||INDEX|G
|root||root|localhost|INDEX|G
|root||root||INNODB_REDO_LOG_ARCHIVE|G
|root||root|localhost|INNODB_REDO_LOG_ARCHIVE|G
|root||root||INNODB_REDO_LOG_ENABLE|G
|root||root|localhost|INNODB_REDO_LOG_ENABLE|G
|root||root||INSERT|G
|root||root|localhost|INSERT|G
|root||root||LOCK TABLES|G
|root||root|localhost|LOCK TABLES|G
|root||root||PASSWORDLESS_USER_ADMIN|G
|root||root|localhost|PASSWORDLESS_USER_ADMIN|G
|root||mysql.session|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||root||PERSIST_RO_VARIABLES_ADMIN|G
|root||root|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||root||PROCESS|G
|root||root|localhost|PROCESS|G
|root||root||REFERENCES|G
|root||root|localhost|REFERENCES|G
|root||root||RELOAD|G
|root||root|localhost|RELOAD|G
|root||root||REPLICATION CLIENT|G
|root||root|localhost|REPLICATION CLIENT|G
|root||root||REPLICATION SLAVE|G
|root||root|localhost|REPLICATION SLAVE|G
|root||root||REPLICATION_APPLIER|G
|root||root|localhost|REPLICATION_APPLIER|G
|root||root||REPLICATION_SLAVE_ADMIN|G
|root||root|localhost|REPLICATION_SLAVE_ADMIN|G
|root||root||RESOURCE_GROUP_ADMIN|G
|root||root|localhost|RESOURCE_GROUP_ADMIN|G
|root||root||RESOURCE_GROUP_USER|G
|root||root|localhost|RESOURCE_GROUP_USER|G
|root||root||ROLE_ADMIN|G
|root||root|localhost|ROLE_ADMIN|G
|root||mysql.infoschema|localhost|SELECT|G
|root||root||SELECT|G
|root||root|localhost|SELECT|G
|root||root||SENSITIVE_VARIABLES_OBSERVER|G
|root||root|localhost|SENSITIVE_VARIABLES_OBSERVER|G
|root||root||SERVICE_CONNECTION_ADMIN|G
|root||root|localhost|SERVICE_CONNECTION_ADMIN|G
|root||mysql.session|localhost|SESSION_VARIABLES_ADMIN|G
|root||root||SESSION_VARIABLES_ADMIN|G
|root||root|localhost|SESSION_VARIABLES_ADMIN|G
|root||root||SET_USER_ID|G
|root||root|localhost|SET_USER_ID|G
|root||root||SHOW DATABASES|G
|root||root|localhost|SHOW DATABASES|G
|root||root||SHOW VIEW|G
|root||root|localhost|SHOW VIEW|G
|root||root||SHOW_ROUTINE|G
|root||root|localhost|SHOW_ROUTINE|G
|root||mysql.session|localhost|SHUTDOWN|G
|root||root||SHUTDOWN|G
|root||root|localhost|SHUTDOWN|G
|root||mysql.session|localhost|SUPER|G
|root||root||SUPER|G
|root||root|localhost|SUPER|G
|root||mysql.infoschema|localhost|SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_USER|G
|root||mysql.sys|localhost|SYSTEM_USER|G
|root||root||SYSTEM_USER|G
|root||root|localhost|SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||root||SYSTEM_VARIABLES_ADMIN|G
|root||root|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||root||TABLE_ENCRYPTION_ADMIN|G
|root||root|localhost|TABLE_ENCRYPTION_ADMIN|G
|root||root||TELEMETRY_LOG_ADMIN|G
|root||root|localhost|TELEMETRY_LOG_ADMIN|G
|root||root||TRIGGER|G
|root||root|localhost|TRIGGER|G
|root||root||UPDATE|G
|root||root|localhost|UPDATE|G
|root||root||XA_RECOVER_ADMIN|G
|root||root|localhost|XA_RECOVER_ADMIN|G
|root||root||grant option|G
|root||root|localhost|grant option|G
performance_schema|schema||mysql.session|localhost|SELECT|G
sys|schema||mysql.sys|localhost|TRIGGER|G</Grants>
      <ServerVersion>8.0.41</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="3" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="4" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="5" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="6" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="7" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="10" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="18" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="20" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="21" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="23" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="24" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="25" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="27" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="29" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="31" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="33" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="39" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="42" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="43" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="44" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="45" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="46" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="47" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="48" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="49" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="50" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="52" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="56" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="58" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="59" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="61" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="67" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="69" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="71" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="73" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="74" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="77" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="79" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="80" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="81" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="82" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="83" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="85" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="86" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="87" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="88" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="95" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="116" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="118" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="124" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="144" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="145" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="146" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="152" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="erp_integration">
      <AutoIntrospectionLevel>3</AutoIntrospectionLevel>
      <LastIntrospectionLevel>3</LastIntrospectionLevel>
      <LastIntrospectionLocalTimestamp>2025-04-29.09:42:35</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="industrial_chain">
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="291" parent="1" name="mysql">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="292" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="293" parent="1" name="sys">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <user id="294" parent="1" name="mysql.infoschema">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="295" parent="1" name="mysql.session">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="296" parent="1" name="mysql.sys">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="297" parent="1" name="root"/>
    <user id="298" parent="1" name="root">
      <Host>localhost</Host>
    </user>
    <table id="299" parent="288" name="account_info">
      <Comment>账号信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="300" parent="288" name="apscheduler_jobs">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="301" parent="288" name="gallery">
      <Comment>园区图库表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="302" parent="288" name="parsed_factory_data">
      <Comment>解析的厂房数据表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="303" parent="288" name="push_history">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="304" parent="288" name="scheduled_tasks">
      <Comment>定时任务配置表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <column id="305" parent="299" name="id">
      <AutoIncrement>31</AutoIncrement>
      <Comment>自增主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="306" parent="299" name="account">
      <Comment>账号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="307" parent="299" name="account_id">
      <Comment>账号ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="308" parent="299" name="name">
      <Comment>姓名</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="309" parent="299" name="city">
      <Comment>城市</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="310" parent="299" name="port_type">
      <Comment>端口类型，如free、paid等</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="311" parent="299" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="312" parent="299" name="is_deleted">
      <Comment>是否删除：0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="313" parent="299" name="dingtalk_userid">
      <Comment>钉钉用户ID</Comment>
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <index id="314" parent="299" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="315" parent="299" name="uk_account_id">
      <ColNames>account_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="316" parent="299" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="317" parent="299" name="uk_account_id">
      <UnderlyingIndexName>uk_account_id</UnderlyingIndexName>
    </key>
    <column id="318" parent="300" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(191)|0s</StoredType>
    </column>
    <column id="319" parent="300" name="next_run_time">
      <Position>2</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="320" parent="300" name="job_state">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <index id="321" parent="300" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="322" parent="300" name="ix_apscheduler_jobs_next_run_time">
      <ColNames>next_run_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="323" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="324" parent="301" name="id">
      <AutoIncrement>716</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="325" parent="301" name="community">
      <Comment>园区名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="326" parent="301" name="image_url">
      <Comment>图片URL</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="327" parent="301" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="328" parent="301" name="status">
      <Comment>是否视屏 0是 1否</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="329" parent="301" name="handle_url">
      <Comment>处理后的图片url</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="330" parent="301" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="331" parent="301" name="idx_gallery_campus_name">
      <ColNames>community</ColNames>
      <Type>btree</Type>
    </index>
    <key id="332" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="333" parent="302" name="id">
      <AutoIncrement>218</AutoIncrement>
      <Comment>自增主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="334" parent="302" name="erp_id">
      <Comment>解析ID，与前端交互使用的唯一标识</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="335" parent="302" name="youtui_house_id">
      <Comment>房源ID，发布到优推平台后的ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="336" parent="302" name="city">
      <Comment>城市代码，如bj、cc等</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="337" parent="302" name="city_name">
      <Comment>城市名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="338" parent="302" name="port_type">
      <Comment>端口类型，如free_rent、paid_sale等</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="339" parent="302" name="topic">
      <Comment>标题</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="340" parent="302" name="zone">
      <Comment>区域</Comment>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="341" parent="302" name="street">
      <Comment>街道</Comment>
      <Position>9</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="342" parent="302" name="type">
      <Comment>租售类型：出租、出售</Comment>
      <Position>10</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="343" parent="302" name="towards">
      <Comment>朝向</Comment>
      <Position>11</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="344" parent="302" name="community">
      <Comment>园区名称</Comment>
      <Position>12</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="345" parent="302" name="content">
      <Comment>描述</Comment>
      <Position>13</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="346" parent="302" name="total">
      <Comment>价格</Comment>
      <Position>14</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="347" parent="302" name="square">
      <Comment>面积（平方米）</Comment>
      <Position>15</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="348" parent="302" name="address">
      <Comment>地址</Comment>
      <Position>16</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="349" parent="302" name="type4property">
      <Comment>建筑类型：13表示厂房</Comment>
      <DefaultExpression>&apos;13&apos;</DefaultExpression>
      <Position>17</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="350" parent="302" name="floor">
      <Comment>楼层</Comment>
      <Position>18</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="351" parent="302" name="floors">
      <Comment>总楼层</Comment>
      <Position>19</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="352" parent="302" name="floortype">
      <Comment>楼层类型 1单层3多层</Comment>
      <Position>20</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="353" parent="302" name="Storey">
      <Comment>首层层高</Comment>
      <Position>21</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="354" parent="302" name="rentunitdaily">
      <Comment>租金单位，如元/㎡/天</Comment>
      <Position>22</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="355" parent="302" name="slease">
      <Comment>起租期</Comment>
      <Position>23</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="356" parent="302" name="Planteia">
      <Comment>可办环评</Comment>
      <Position>24</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="357" parent="302" name="Plantstructure">
      <Comment>厂房结构</Comment>
      <Position>25</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="358" parent="302" name="Typewarehouse">
      <Comment>厂房类型</Comment>
      <Position>26</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="359" parent="302" name="Plantnew">
      <Comment>厂房新旧</Comment>
      <Position>27</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="360" parent="302" name="isNewHouse">
      <Comment>性质</Comment>
      <Position>28</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="361" parent="302" name="AgentFree">
      <Comment>中介费</Comment>
      <Position>29</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="362" parent="302" name="thumb">
      <Comment>封面图/缩略图URL</Comment>
      <Position>30</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="363" parent="302" name="floorplans">
      <Comment>户型图URL</Comment>
      <Position>31</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="364" parent="302" name="photointerior">
      <Comment>内部图URL</Comment>
      <Position>32</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="365" parent="302" name="photooutdoor">
      <Comment>外部图URL</Comment>
      <Position>33</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="366" parent="302" name="infrastructure">
      <Comment>厂房配套设施</Comment>
      <Position>34</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="367" parent="302" name="raw_factory_data">
      <Comment>原始factory_data的JSON格式</Comment>
      <Position>35</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="368" parent="302" name="filtered_factory_data">
      <Comment>过滤后的factory_data的JSON格式</Comment>
      <Position>36</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="369" parent="302" name="is_published">
      <Comment>是否已发布：0=未发布，1=已发布</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>37</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="370" parent="302" name="publish_status">
      <Comment>发布状态：success=成功，failed=失败</Comment>
      <Position>38</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="371" parent="302" name="publish_message">
      <Comment>发布结果消息</Comment>
      <Position>39</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="372" parent="302" name="publish_time">
      <Comment>发布时间</Comment>
      <Position>40</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="373" parent="302" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>41</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="374" parent="302" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>42</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="375" parent="302" name="is_deleted">
      <Comment>是否删除：0=未删除，1=已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>43</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="376" parent="302" name="Name">
      <Comment>联系人</Comment>
      <Position>44</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="377" parent="302" name="Tel">
      <Comment>联系电话</Comment>
      <Position>45</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="378" parent="302" name="ExternalTag">
      <Comment>房源标签</Comment>
      <Position>46</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="379" parent="302" name="ts_status">
      <Comment>推送状态  0未推送 1已推送 2推送失败</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>47</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="380" parent="302" name="all_matched_posts">
      <Comment>所有匹配的标题和内容的JSON数组</Comment>
      <Position>48</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="381" parent="302" name="video_url">
      <Comment>视频地址</Comment>
      <Position>49</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="382" parent="302" name="years">
      <Comment>建筑年代</Comment>
      <Position>50</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="383" parent="302" name="Type4Years">
      <Comment>产权年限 （1：70年，2：40年，3：50年，4：永久）
</Comment>
      <Position>51</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="384" parent="302" name="Landnature">
      <Comment>土地性质1工业用地2集体建设用地3物流用地4其他
</Comment>
      <Position>52</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="385" parent="302" name="Powersupply">
      <Comment>供电容量 （数字）</Comment>
      <Position>53</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="386" parent="302" name="Loadbearing">
      <Comment>楼板承重(数字，单位吨)</Comment>
      <Position>54</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <index id="387" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="388" parent="302" name="uk_parse_id">
      <ColNames>erp_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="389" parent="302" name="idx_house_id">
      <ColNames>youtui_house_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="390" parent="302" name="idx_city">
      <ColNames>city</ColNames>
      <Type>btree</Type>
    </index>
    <index id="391" parent="302" name="idx_is_published">
      <ColNames>is_published</ColNames>
      <Type>btree</Type>
    </index>
    <index id="392" parent="302" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="393" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="394" parent="302" name="uk_parse_id">
      <UnderlyingIndexName>uk_parse_id</UnderlyingIndexName>
    </key>
    <column id="395" parent="303" name="id">
      <AutoIncrement>590</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="396" parent="303" name="push_time">
      <Comment>推送时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="397" parent="303" name="erp_house_id">
      <Comment>ERP房源ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="398" parent="303" name="tt_id">
      <Comment>推推ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="399" parent="303" name="website_id">
      <Comment>网站ID</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="400" parent="303" name="website_name">
      <Comment>网站名称</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="401" parent="303" name="city">
      <Comment>城市代码</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="402" parent="303" name="process_id">
      <Comment>推送进程ID</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="403" parent="303" name="final_status">
      <Comment>推送最终状态
0待更新 1推送成功 2推送失败</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="404" parent="303" name="account_id">
      <Comment>推送使用的账号信息</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="405" parent="303" name="image_url">
      <Comment>房源认证二维码地址</Comment>
      <Position>11</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="406" parent="303" name="push_topic">
      <Comment>推送使用的标题</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="407" parent="303" name="push_content">
      <Comment>推送使用的房源描述</Comment>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="408" parent="303" name="community">
      <Comment>园区名称</Comment>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="409" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="410" parent="303" name="id">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="411" parent="303" name="idx_push_history_push_time">
      <ColNames>push_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="412" parent="303" name="idx_push_history_erp_house_id">
      <ColNames>erp_house_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="413" parent="303" name="idx_push_history_city">
      <ColNames>city</ColNames>
      <Type>btree</Type>
    </index>
    <key id="414" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="415" parent="303" name="id">
      <UnderlyingIndexName>id</UnderlyingIndexName>
    </key>
    <column id="416" parent="304" name="id">
      <AutoIncrement>7</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="417" parent="304" name="task_name">
      <Comment>任务名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="418" parent="304" name="task_code">
      <Comment>任务代码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="419" parent="304" name="task_type">
      <Comment>任务类型(python_script/api/function)</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="420" parent="304" name="command">
      <Comment>执行命令/路径</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="421" parent="304" name="cron_expression">
      <Comment>cron表达式</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="422" parent="304" name="is_active">
      <Comment>是否启用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>7</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="423" parent="304" name="description">
      <Comment>任务描述</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="424" parent="304" name="last_run_time">
      <Comment>上次运行时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="425" parent="304" name="next_run_time">
      <Comment>下次运行时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="426" parent="304" name="create_time">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="427" parent="304" name="update_time">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="428" parent="304" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="429" parent="304" name="idx_task_code">
      <ColNames>task_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="430" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="431" parent="304" name="idx_task_code">
      <UnderlyingIndexName>idx_task_code</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>