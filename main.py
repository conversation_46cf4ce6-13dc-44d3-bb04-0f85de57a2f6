#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优推科技和中介ERP集成系统
主应用入口文件
"""

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

from api.youtui_routes import router as youtui_router
from api.push_routes import router as push_router
from api.config_routes import router as config_router
from api.system_routes import router as system_router
from api.excel_routes import router as excel_router
from api.file_routes import router as file_router
from api.factory_data_routes import router as factory_data_router
from api.statistics_routes import router as statistics_router
from api.dingtalk_routes import router as dingtalk_router
from api.listing_analysis_routes import router as listing_analysis_router  # 导入竞帖分析路由
from api.report_routes import router as report_router  # 导入报告生成路由
from api.task_sse_routes import router as task_sse_router  # 导入任务状态和SSE路由

# 导入定时任务系统
from tasks import init_scheduler
from tasks.api_routes import router as tasks_router
# 配置日志
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="优推科技ERP集成系统",
    description="优推科技和中介ERP集成系统API",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 集成路由
app.include_router(youtui_router)
app.include_router(push_router)
app.include_router(config_router)
app.include_router(system_router)
app.include_router(excel_router)
app.include_router(file_router)
app.include_router(factory_data_router)
app.include_router(statistics_router)
app.include_router(tasks_router)  # 添加定时任务API路由
app.include_router(dingtalk_router)  # 添加钉钉API路由
app.include_router(listing_analysis_router)  # 添加竞帖分析API路由
app.include_router(report_router)  # 添加报告生成API路由
app.include_router(task_sse_router)  # 添加任务状态和SSE API路由
# 挂载静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")

# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时执行的操作"""
    logger.info("正在初始化应用...")

    # 初始化定时任务系统
    init_scheduler()

    # 启动新的SSE广播处理器
    try:
        from services.sse_service import start_sse_background_processor
        broadcast_task = await start_sse_background_processor()
        logger.info("新的SSE广播处理器已启动")
    except Exception as e:
        logger.error(f"启动SSE广播处理器失败: {str(e)}")

    logger.info("应用初始化完成，使用基于文件系统的任务状态管理")

# 运行应用（在以模块方式导入时不会执行）
if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
