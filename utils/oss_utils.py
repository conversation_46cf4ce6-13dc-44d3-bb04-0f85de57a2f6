#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阿里云对象存储(OSS)工具类
用于处理文件上传、下载和管理
"""

import os
import logging
import uuid
import oss2
from datetime import datetime
from typing import Dict, List, Optional, Tuple, BinaryIO

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OssClient:
    """阿里云对象存储客户端类"""
    
    def __init__(
        self, 
        access_key_id: str, 
        access_key_secret: str, 
        endpoint: str, 
        bucket_name: str,
        oss_domain: str = None
    ):
        """
        初始化OSS客户端
        
        Args:
            access_key_id: 阿里云AccessKeyId
            access_key_secret: 阿里云AccessKeySecret
            endpoint: OSS区域节点
            bucket_name: OSS存储空间名称
            oss_domain: 自定义域名(可选)
        """
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.endpoint = endpoint
        self.bucket_name = bucket_name
        self.oss_domain = oss_domain
        
        # 创建Auth和Bucket实例
        self.auth = oss2.Auth(access_key_id, access_key_secret)
        self.bucket = oss2.Bucket(self.auth, endpoint, bucket_name)
        
        logger.info(f"初始化OSS客户端，Endpoint: {endpoint}, Bucket: {bucket_name}")
        if oss_domain:
            logger.info(f"使用自定义域名: {oss_domain}")
    
    def _get_file_url(self, oss_file_path: str) -> str:
        """
        获取文件的URL
        
        Args:
            oss_file_path: OSS中的文件路径
            
        Returns:
            文件URL
        """
        # 对于房本认证二维码图片，直接使用不带目录的URL
        if oss_file_path.startswith('qr_'):
            return f"https://ld-erp.oss-cn-beijing.aliyuncs.com/{oss_file_path}"
        
        # 使用固定的域名 'https://ld-erp.oss-cn-beijing.aliyuncs.com/'
        return f"https://ld-erp.oss-cn-beijing.aliyuncs.com/{oss_file_path}"
    
    def upload_file(
        self, 
        local_file_path: str, 
        oss_file_path: Optional[str] = None, 
        headers: Optional[Dict] = None
    ) -> Tuple[bool, str]:
        """
        上传本地文件到OSS
        
        Args:
            local_file_path: 本地文件路径
            oss_file_path: OSS中的文件路径，如果不指定则使用本地文件名
            headers: 额外的HTTP头信息
            
        Returns:
            成功状态和文件访问URL
        """
        try:
            # 如果未指定OSS路径，使用本地文件名
            if not oss_file_path:
                oss_file_path = os.path.basename(local_file_path)
            
            # 执行文件上传
            result = self.bucket.put_object_from_file(
                oss_file_path, 
                local_file_path,
                headers=headers
            )
            
            # 检查上传结果
            if result.status == 200:
                file_url = self._get_file_url(oss_file_path)
                logger.info(f"文件上传成功: {file_url}")
                return True, file_url
            else:
                logger.error(f"文件上传失败，状态码: {result.status}")
                return False, f"上传失败，状态码: {result.status}"
        
        except Exception as e:
            logger.error(f"文件上传发生异常: {str(e)}")
            return False, f"上传异常: {str(e)}"
    
    def upload_binary(
        self, 
        binary_data: bytes, 
        oss_file_path: str, 
        content_type: Optional[str] = None,
        headers: Optional[Dict] = None
    ) -> Tuple[bool, str]:
        """
        上传二进制数据到OSS
        
        Args:
            binary_data: 二进制数据
            oss_file_path: OSS中的文件路径
            content_type: 内容类型，如 'image/jpeg'
            headers: 额外的HTTP头信息
            
        Returns:
            成功状态和文件访问URL
        """
        try:
            # 设置HTTP头信息
            if headers is None:
                headers = {}
            
            if content_type:
                headers['Content-Type'] = content_type
            
            # 执行二进制上传
            result = self.bucket.put_object(
                oss_file_path, 
                binary_data,
                headers=headers
            )
            
            # 检查上传结果
            if result.status == 200:
                file_url = self._get_file_url(oss_file_path)
                logger.info(f"二进制数据上传成功: {file_url}")
                return True, file_url
            else:
                logger.error(f"二进制数据上传失败，状态码: {result.status}")
                return False, f"上传失败，状态码: {result.status}"
        
        except Exception as e:
            logger.error(f"二进制数据上传发生异常: {str(e)}")
            return False, f"上传异常: {str(e)}"
    
    def upload_stream(
        self, 
        stream: BinaryIO, 
        oss_file_path: str, 
        content_type: Optional[str] = None,
        content_length: Optional[int] = None,
        headers: Optional[Dict] = None
    ) -> Tuple[bool, str]:
        """
        上传数据流到OSS
        
        Args:
            stream: 数据流对象，如open(file_path, 'rb')打开的文件对象
            oss_file_path: OSS中的文件路径
            content_type: 内容类型
            content_length: 内容长度
            headers: 额外的HTTP头信息
            
        Returns:
            成功状态和文件访问URL
        """
        try:
            # 设置HTTP头信息
            if headers is None:
                headers = {}
            
            if content_type:
                headers['Content-Type'] = content_type
            
            # 执行流式上传
            result = self.bucket.put_object(
                oss_file_path, 
                stream,
                headers=headers
            )
            
            # 检查上传结果
            if result.status == 200:
                file_url = self._get_file_url(oss_file_path)
                logger.info(f"数据流上传成功: {file_url}")
                return True, file_url
            else:
                logger.error(f"数据流上传失败，状态码: {result.status}")
                return False, f"上传失败，状态码: {result.status}"
        
        except Exception as e:
            logger.error(f"数据流上传发生异常: {str(e)}")
            return False, f"上传异常: {str(e)}"
    
    def delete_file(self, oss_file_path: str) -> bool:
        """
        删除OSS中的文件
        
        Args:
            oss_file_path: OSS中的文件路径
            
        Returns:
            删除是否成功
        """
        try:
            # 执行文件删除
            result = self.bucket.delete_object(oss_file_path)
            
            # 检查删除结果
            if result.status == 204:
                logger.info(f"文件删除成功: {oss_file_path}")
                return True
            else:
                logger.error(f"文件删除失败，状态码: {result.status}")
                return False
        
        except Exception as e:
            logger.error(f"文件删除发生异常: {str(e)}")
            return False
    
    def download_file(
        self, 
        oss_file_path: str, 
        local_file_path: str
    ) -> bool:
        """
        下载OSS文件到本地
        
        Args:
            oss_file_path: OSS中的文件路径
            local_file_path: 本地文件保存路径
            
        Returns:
            下载是否成功
        """
        try:
            # 执行文件下载
            result = self.bucket.get_object_to_file(oss_file_path, local_file_path)
            
            # 检查下载结果
            if result.status == 200:
                logger.info(f"文件下载成功: {local_file_path}")
                return True
            else:
                logger.error(f"文件下载失败，状态码: {result.status}")
                return False
        
        except Exception as e:
            logger.error(f"文件下载发生异常: {str(e)}")
            return False
    
    def get_file_url(self, oss_file_path: str, expires: int = 3600) -> str:
        """
        获取文件的临时访问URL
        
        Args:
            oss_file_path: OSS中的文件路径
            expires: URL有效期，单位为秒，默认1小时
            
        Returns:
            临时访问URL
        """
        try:
            # 如果使用了自定义域名且文件允许公开访问，则直接返回URL
            if self.oss_domain:
                url = self._get_file_url(oss_file_path)
                logger.info(f"使用自定义域名生成URL: {url}")
                return url
            
            # 否则生成签名URL
            url = self.bucket.sign_url('GET', oss_file_path, expires)
            logger.info(f"生成临时访问URL: {url}, 有效期: {expires}秒")
            return url
        
        except Exception as e:
            logger.error(f"生成临时访问URL发生异常: {str(e)}")
            return ""
    
    def check_file_exists(self, oss_file_path: str) -> bool:
        """
        检查文件是否存在于OSS中
        
        Args:
            oss_file_path: OSS中的文件路径
            
        Returns:
            文件是否存在
        """
        try:
            # 检查文件是否存在
            return self.bucket.object_exists(oss_file_path)
        
        except Exception as e:
            logger.error(f"检查文件存在状态发生异常: {str(e)}")
            return False
    
    def list_files(self, prefix: str = '', delimiter: str = '', max_keys: int = 100) -> List[Dict]:
        """
        列出OSS中符合条件的文件
        
        Args:
            prefix: 前缀过滤条件
            delimiter: 分隔符，通常用于模拟文件夹结构
            max_keys: 最大返回条目数
            
        Returns:
            文件列表
        """
        try:
            # 执行列表查询
            result = self.bucket.list_objects(
                prefix=prefix,
                delimiter=delimiter,
                max_keys=max_keys
            )
            
            # 构建文件列表
            file_list = []
            for obj in result.object_list:
                file_list.append({
                    'name': obj.key,
                    'size': obj.size,
                    'last_modified': obj.last_modified,
                    'type': obj.type,
                    'url': self._get_file_url(obj.key)
                })
            
            return file_list
        
        except Exception as e:
            logger.error(f"列出文件发生异常: {str(e)}")
            return []
    
    def generate_unique_filename(self, original_filename: str, prefix: str = '') -> str:
        """
        生成唯一的文件名
        
        Args:
            original_filename: 原始文件名
            prefix: 文件名前缀
            
        Returns:
            唯一文件名
        """
        # 获取文件扩展名
        _, ext = os.path.splitext(original_filename)
        
        # 生成基于时间和UUID的唯一文件名
        date_str = datetime.now().strftime('%Y%m%d%H%M%S')
        unique_id = uuid.uuid4().hex[:8]
        
        # 组合生成唯一文件名
        if prefix:
            return f"{prefix}/{date_str}_{unique_id}{ext}"
        else:
            return f"{date_str}_{unique_id}{ext}" 