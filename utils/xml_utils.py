#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
XML处理工具类
用于生成和解析XML数据
"""

import xml.etree.ElementTree as ET
from xml.dom import minidom


def dict_to_xml(data_dict, root_name='profile', item_name='houselist'):
    """
    将字典转换为XML字符串
    
    Args:
        data_dict: 字典数据，可以是单个字典或字典列表
        root_name: XML根元素名称
        item_name: 列表项元素名称
    
    Returns:
        XML字符串
    """
    root = ET.Element(root_name)
    
    # 如果是字典列表
    if isinstance(data_dict, list):
        for item in data_dict:
            item_elem = ET.SubElement(root, item_name)
            for key, value in item.items():
                child = ET.SubElement(item_elem, key)
                child.text = str(value)
    # 如果是单个字典
    else:
        item_elem = ET.SubElement(root, item_name)
        for key, value in data_dict.items():
            child = ET.SubElement(item_elem, key)
            child.text = str(value)
    
    # 将ElementTree转换为字符串并格式化
    rough_string = ET.tostring(root, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    return reparsed.toprettyxml(indent="  ", encoding='utf-8').decode('utf-8')


def xml_to_dict(xml_string):
    """
    将XML字符串转换为字典

    Args:
        xml_string: XML字符串

    Returns:
        字典或字典列表
    """
    try:
        print(f"\n===== 开始解析XML =====")

        # 预处理 XML 字符串，处理特殊字符
        processed_xml = xml_string

        # 检查并替换未转义的 URL 参数分隔符
        if '&' in processed_xml and not '&amp;' in processed_xml:
            processed_xml = processed_xml.replace('&', '&amp;')
            # 避免重复转义
            processed_xml = processed_xml.replace('&amp;amp;', '&amp;')

        print(f"原始XML: {processed_xml}")

        root = ET.fromstring(processed_xml)
        result = []

        # 首先解析根元素的直接子元素（如status、taoCan、msg等）
        root_metadata = {}
        for child in root:
            # 如果不是houselist相关标签，则作为元数据保存
            if child.tag.lower() not in ['houselist']:
                root_metadata[child.tag.lower()] = child.text

        # 尝试多种可能的标签名称（不区分大小写）
        possible_tags = ['houselist', 'houseList', 'HouseList', 'Houselist', 'HOUSELIST']

        found_items = False
        for tag in possible_tags:
            items = root.findall(f'./{tag}')
            if items:
                found_items = True
                print(f"找到标签: {tag}, 数量: {len(items)}")
                for item in items:
                    item_dict = {}
                    for child in item:
                        item_dict[child.tag.lower()] = child.text  # 将标签名转为小写
                    result.append(item_dict)
                break

        if not found_items:
            print("未找到任何匹配的标签，尝试直接解析子元素")
            # 如果没有找到任何匹配的标签，尝试直接解析根元素的子元素
            for child in root:
                print(f"子元素标签: {child.tag}")
                if child.tag.lower() in [t.lower() for t in possible_tags]:
                    item_dict = {}
                    for subchild in child:
                        item_dict[subchild.tag.lower()] = subchild.text  # 将标签名转为小写
                    result.append(item_dict)

        # 如果有根元素的元数据，将其添加到结果中
        if root_metadata:
            print(f"根元素元数据: {root_metadata}")
            # 如果有houselist数据，将元数据合并到第一个结果中
            if result:
                if isinstance(result, list) and len(result) > 0:
                    # 将元数据添加到第一个结果项中
                    result[0].update(root_metadata)
                else:
                    # 如果result是单个字典，直接合并
                    result.update(root_metadata)
            else:
                # 如果没有houselist数据，直接返回元数据
                result = [root_metadata]

        print(f"解析结果: {result}")
        print(f"========================\n")

        # 如果只有一个结果，返回字典而不是列表
        if len(result) == 1:
            return result[0]
        return result
    except Exception as e:
        print(f"\n===== XML解析错误 =====")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        print(f"XML内容: {xml_string}")
        print(f"========================\n")
        return None