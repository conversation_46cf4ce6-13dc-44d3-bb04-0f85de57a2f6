#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
HTTP请求工具类
用于发送HTTP请求到优推科技API
"""

import requests
import httpx
import logging
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HttpClient:
    """HTTP客户端类，用于发送请求到优推科技API"""
    
    def __init__(self, base_url):
        """
        初始化HTTP客户端
        
        Args:
            base_url: API基础URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/xml; charset=utf-8',
            'Accept': 'application/xml',
            'Accept-Charset': 'utf-8'
        })
        logger.info(f"初始化HTTP客户端，基础URL: {base_url}")
    
    def post(self, endpoint, data, timeout=30):
        """
        发送POST请求
        
        Args:
            endpoint: API端点
            data: 请求数据（XML字符串）
            timeout: 超时时间（秒）
            
        Returns:
            响应内容（XML字符串）
        """
        url = urljoin(self.base_url, endpoint)
        
        # 预处理请求数据
        if isinstance(data, str):
            # 处理 XML 中的特殊字符
            if '&' in data and not '&amp;' in data:
                data = data.replace('&', '&amp;')
                data = data.replace('&amp;amp;', '&amp;')  # 避免重复转义
        
        # 打印完整的请求信息
        print(f"\n===== 发送请求 =====")
        print(f"请求方法: POST")
        print(f"完整URL: {url}")
        print(f"请求头: {dict(self.session.headers)}")
        print(f"请求数据: {data}")
        print(f"=====================\n")
        
        logger.info(f"发送POST请求到: {url}")
        logger.info(f"请求数据: {data}")
        
        try:
            # 确保数据使用UTF-8编码
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            response = self.session.post(url, data=data, timeout=timeout)
            logger.info(f"响应状态码: {response.status_code}")
            
            # 打印响应头信息
            logger.info(f"响应头: {dict(response.headers)}")
            
            # 打印完整的响应信息到控制台
            print(f"\n===== 收到响应 =====")
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text[:500]}..." if len(response.text) > 500 else f"响应内容: {response.text}")
            print(f"=====================\n")
            
            # 即使状态码不是200，也尝试获取响应内容
            response_text = response.text
            logger.info(f"响应内容: {response_text}")
            
            # 然后再检查状态码
            response.raise_for_status()
            
            return response_text
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {str(e)}")
            # 如果是HTTP错误，尝试获取响应内容
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"错误响应状态码: {e.response.status_code}")
                logger.error(f"错误响应内容: {e.response.text}")
                
                # 打印错误信息到控制台
                print(f"\n===== 请求错误 =====")
                print(f"错误类型: {type(e).__name__}")
                print(f"错误信息: {str(e)}")
                print(f"错误响应状态码: {e.response.status_code}")
                print(f"错误响应内容: {e.response.text}")
                print(f"=====================\n")
            else:
                # 打印错误信息到控制台
                print(f"\n===== 请求错误 =====")
                print(f"错误类型: {type(e).__name__}")
                print(f"错误信息: {str(e)}")
                print(f"=====================\n")
            return None
    
    def get(self, endpoint, params=None, timeout=30):
        """
        发送GET请求
        
        Args:
            endpoint: API端点
            params: 请求参数
            timeout: 超时时间（秒）
            
        Returns:
            响应内容（XML字符串）
        """
        url = urljoin(self.base_url, endpoint)
        # 打印完整的请求信息
        print(f"\n===== 发送请求 =====")
        print(f"请求方法: GET")
        print(f"完整URL: {url}")
        print(f"请求头: {dict(self.session.headers)}")
        print(f"请求参数: {params}")
        print(f"=====================\n")
        
        logger.info(f"发送GET请求到: {url}")
        logger.info(f"请求参数: {params}")
        
        try:
            # 确保请求参数使用UTF-8编码
            if params:
                for key, value in params.items():
                    if isinstance(value, str):
                        params[key] = value.encode('utf-8').decode('utf-8')
            
            response = self.session.get(url, params=params, timeout=timeout)
            logger.info(f"响应状态码: {response.status_code}")
            
            # 打印响应头信息
            logger.info(f"响应头: {dict(response.headers)}")
            
            # 打印完整的响应信息到控制台
            print(f"\n===== 收到响应 =====")
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text[:500]}..." if len(response.text) > 500 else f"响应内容: {response.text}")
            print(f"=====================\n")
            
            # 即使状态码不是200，也尝试获取响应内容
            response_text = response.text
            logger.info(f"响应内容: {response_text}")
            
            # 然后再检查状态码
            response.raise_for_status()
            
            return response_text
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {str(e)}")
            # 如果是HTTP错误，尝试获取响应内容
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"错误响应状态码: {e.response.status_code}")
                logger.error(f"错误响应内容: {e.response.text}")
                
                # 打印错误信息到控制台
                print(f"\n===== 请求错误 =====")
                print(f"错误类型: {type(e).__name__}")
                print(f"错误信息: {str(e)}")
                print(f"错误响应状态码: {e.response.status_code}")
                print(f"错误响应内容: {e.response.text}")
                print(f"=====================\n")
            else:
                # 打印错误信息到控制台
                print(f"\n===== 请求错误 =====")
                print(f"错误类型: {type(e).__name__}")
                print(f"错误信息: {str(e)}")
                print(f"=====================\n")
            return None


class AsyncHttpClient:
    """异步HTTP客户端类，用于发送异步请求到优推科技API"""
    
    def __init__(self, base_url):
        """
        初始化异步HTTP客户端
        
        Args:
            base_url: API基础URL
        """
        self.base_url = base_url
        self.headers = {
            'Content-Type': 'application/xml; charset=utf-8',
            'Accept': 'application/xml',
            'Accept-Charset': 'utf-8'
        }
        logger.info(f"初始化异步HTTP客户端，基础URL: {base_url}")
    
    async def post(self, endpoint, data, timeout=30):
        """
        异步发送POST请求

        Args:
            endpoint: API端点
            data: 请求数据（XML字符串）
            timeout: 超时时间（秒），设置为None表示永不超时

        Returns:
            响应内容（XML字符串）
        """
        url = urljoin(self.base_url, endpoint)

        # 预处理请求数据
        if isinstance(data, str):
            # 处理 XML 中的特殊字符
            if '&' in data and not '&amp;' in data:
                data = data.replace('&', '&amp;')
                data = data.replace('&amp;amp;', '&amp;')  # 避免重复转义

        # 打印完整的请求信息
        print(f"\n===== 发送异步请求 =====")
        print(f"请求方法: POST")
        print(f"完整URL: {url}")
        print(f"请求头: {self.headers}")
        print(f"请求数据: {data}")
        print(f"超时设置: {'永不超时' if timeout is None else f'{timeout}秒'}")
        print(f"=====================\n")

        logger.info(f"异步发送POST请求到: {url}")
        logger.info(f"请求数据: {data}")
        logger.info(f"超时设置: {'永不超时' if timeout is None else f'{timeout}秒'}")

        try:
            # 确保数据使用UTF-8编码
            if isinstance(data, str):
                data = data.encode('utf-8')

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    data=data,
                    headers=self.headers,
                    timeout=timeout
                )
            
            logger.info(f"响应状态码: {response.status_code}")
            
            # 打印响应头信息
            logger.info(f"响应头: {dict(response.headers)}")
            
            # 打印完整的响应信息到控制台
            print(f"\n===== 收到异步响应 =====")
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text[:500]}..." if len(response.text) > 500 else f"响应内容: {response.text}")
            print(f"=====================\n")
            
            # 即使状态码不是200，也尝试获取响应内容
            response_text = response.text
            logger.info(f"响应内容: {response_text}")
            
            # 然后再检查状态码
            response.raise_for_status()
            
            return response_text
        except httpx.HTTPError as e:
            logger.error(f"异步请求失败: {str(e)}")
            # 如果是HTTP错误，尝试获取响应内容
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"错误响应状态码: {e.response.status_code}")
                logger.error(f"错误响应内容: {e.response.text}")
                
                # 打印错误信息到控制台
                print(f"\n===== 异步请求错误 =====")
                print(f"错误类型: {type(e).__name__}")
                print(f"错误信息: {str(e)}")
                print(f"错误响应状态码: {e.response.status_code}")
                print(f"错误响应内容: {e.response.text}")
                print(f"=====================\n")
            else:
                # 打印错误信息到控制台
                print(f"\n===== 异步请求错误 =====")
                print(f"错误类型: {type(e).__name__}")
                print(f"错误信息: {str(e)}")
                print(f"=====================\n")
            return None
    
    async def get(self, endpoint, params=None, timeout=30):
        """
        异步发送GET请求

        Args:
            endpoint: API端点
            params: 请求参数
            timeout: 超时时间（秒），设置为None表示永不超时

        Returns:
            响应内容（XML字符串）
        """
        url = urljoin(self.base_url, endpoint)
        # 打印完整的请求信息
        print(f"\n===== 发送异步请求 =====")
        print(f"请求方法: GET")
        print(f"完整URL: {url}")
        print(f"请求头: {self.headers}")
        print(f"请求参数: {params}")
        print(f"超时设置: {'永不超时' if timeout is None else f'{timeout}秒'}")
        print(f"=====================\n")

        logger.info(f"异步发送GET请求到: {url}")
        logger.info(f"请求参数: {params}")
        logger.info(f"超时设置: {'永不超时' if timeout is None else f'{timeout}秒'}")

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url,
                    params=params,
                    headers=self.headers,
                    timeout=timeout
                )
            
            logger.info(f"响应状态码: {response.status_code}")
            
            # 打印响应头信息
            logger.info(f"响应头: {dict(response.headers)}")
            
            # 打印完整的响应信息到控制台
            print(f"\n===== 收到异步响应 =====")
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text[:500]}..." if len(response.text) > 500 else f"响应内容: {response.text}")
            print(f"=====================\n")
            
            # 即使状态码不是200，也尝试获取响应内容
            response_text = response.text
            logger.info(f"响应内容: {response_text}")
            
            # 然后再检查状态码
            response.raise_for_status()
            
            return response_text
        except httpx.HTTPError as e:
            logger.error(f"异步请求失败: {str(e)}")
            # 如果是HTTP错误，尝试获取响应内容
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"错误响应状态码: {e.response.status_code}")
                logger.error(f"错误响应内容: {e.response.text}")
                
                # 打印错误信息到控制台
                print(f"\n===== 异步请求错误 =====")
                print(f"错误类型: {type(e).__name__}")
                print(f"错误信息: {str(e)}")
                print(f"错误响应状态码: {e.response.status_code}")
                print(f"错误响应内容: {e.response.text}")
                print(f"=====================\n")
            else:
                # 打印错误信息到控制台
                print(f"\n===== 异步请求错误 =====")
                print(f"错误类型: {type(e).__name__}")
                print(f"错误信息: {str(e)}")
                print(f"=====================\n")
            return None 