#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强园区数据服务模块
整合parsed_factory_data和park_info两个表的数据，为AI内容生成提供完整的园区信息
"""

import logging
import sys
import os
from typing import Optional, Dict, Any, List

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database.db_connection import get_db_connection
from ai_generate_content.data_models import (
    BasicParkData, DetailedParkInfo, EnhancedParkData,
    AIOptimizedParkData, DataRetrievalOptions, NearbyFacility, NearbyFacilities
)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据映射字典（从原get_community_data.py复制）
PLANTEIA_MAPPING = {
    '0': '否',
    '1': '是'
}

TYPE4YEARS_MAPPING = {
    '1': '70年',
    '2': '40年',
    '3': '50年',
    '4': '永久'
}

PLANTNEW_MAPPING = {
    '1': '全新',
    '2': '九成新',
    '3': '八成新',
    '4': '旧厂'
}

FLOORTYPE_MAPPING = {
    '1': '单层',
    '3': '多层'
}

AGENTFREE_MAPPING = {
    '0': '有',
    '1': '无'
}

# 厂房类型映射
TYPEWAREHOUSE_MAPPING = {
    '0': '标准厂房'
}

# 性质映射
ISNEWHOUSE_MAPPING = {
    '0': '新房'
}

# 土地性质映射
LANDNATURE_MAPPING = {
    '1': '工业用地',
    '2': '集体建设用地',
    '3': '物流用地',
    '4': '其他'
}


class ParkPOIService:
    """园区周边配套POI服务类"""

    # POI分类映射 - 基于categories字段进行分类
    CATEGORY_MAPPING = {
        # 交通配套
        'transportation': [
            '公交站', '地铁站', '火车站', '轻轨站',
            '长途汽车站', '机场', '高速入口','港口'
        ],
        # 生活配套
        'living_services': [
            '超市', '医院', '诊所', '教育学校'
        ],
        # 商业配套
        'commercial': [
            '商场', '酒店', '饭店', '热点区域'
        ],
        # 休闲配套
        'leisure': [
            '公园', '健身房', '羽毛球馆', '乒乓球馆',
            '篮球场', '电影院'
        ],
        # 产业配套
        'industrial': [
            '产业园区', '工厂', '物流仓储场地'
        ]
    }

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    def get_nearby_facilities(self, park_name: str, options: DataRetrievalOptions) -> Optional[NearbyFacilities]:
        """
        获取园区周边配套设施

        Args:
            park_name (str): 园区名称
            options (DataRetrievalOptions): 数据获取选项

        Returns:
            NearbyFacilities: 周边配套设施数据，未找到返回None
        """
        if not options.include_nearby_facilities:
            return None
        try:
            # 获取按categories分组的POI数据
            categorized_poi_data = self._get_poi_by_park_name(park_name, options)
            if not categorized_poi_data:
                return None

            # 转换为业务分类
            facilities = self._categorize_facilities(categorized_poi_data, options)
            return facilities

        except Exception as e:
            return None

    def _get_poi_by_park_name(self, park_name: str, options: DataRetrievalOptions) -> Dict[str, List[Dict]]:
        """
        根据园区名称查询POI数据，按categories分组并选择每组最近的数据

        Args:
            park_name (str): 园区名称
            options (DataRetrievalOptions): 数据获取选项

        Returns:
            Dict[str, List[Dict]]: 按categories分组的POI数据
        """
        try:
            connection = get_db_connection()
            categorized_poi_data = {}

            with connection.cursor() as cursor:
                # 获取所有相关的categories
                categories_sql = """
                SELECT DISTINCT categories
                FROM search_poi_nearby
                WHERE center_id LIKE %s
                """
                search_pattern = f"{park_name}_%"
                cursor.execute(categories_sql, (search_pattern,))
                available_categories = [row['categories'] for row in cursor.fetchall()]

                # 为每个categories获取最近的POI数据
                for category in available_categories:
                    poi_sql = """
                    SELECT poi_id, title, address, categories, distance, tel
                    FROM search_poi_nearby
                    WHERE center_id LIKE %s
                    AND categories = %s
                    AND distance <= %s
                    ORDER BY distance
                    LIMIT %s
                    """

                    cursor.execute(poi_sql, (
                        search_pattern,
                        category,
                        options.poi_max_distance,
                        options.poi_max_per_category
                    ))

                    category_results = cursor.fetchall()
                    if category_results:
                        categorized_poi_data[category] = []
                        for row in category_results:
                            categorized_poi_data[category].append({
                                'poi_id': row['poi_id'],
                                'title': row['title'],
                                'address': row['address'],
                                'categories': row['categories'],
                                'distance': row['distance'],
                                'tel': row['tel']
                            })

                return categorized_poi_data

        except Exception as e:
            return {}
        finally:
            if 'connection' in locals() and connection:
                connection.close()

    def _categorize_facilities(self, categorized_poi_data: Dict[str, List[Dict]], options: DataRetrievalOptions) -> NearbyFacilities:
        """
        将按categories分组的POI数据转换为业务分类

        Args:
            categorized_poi_data (Dict[str, List[Dict]]): 按categories分组的POI数据
            options (DataRetrievalOptions): 数据获取选项

        Returns:
            NearbyFacilities: 分类后的配套设施数据
        """
        facilities = NearbyFacilities()

        # 按业务分类分组POI数据
        business_categorized_data = {
            'transportation': [],
            'living_services': [],
            'commercial': [],
            'leisure': [],
            'industrial': []
        }

        # 将categories数据映射到业务分类
        for categories, poi_list in categorized_poi_data.items():
            for poi in poi_list:
                facility = NearbyFacility(
                    poi_id=poi['poi_id'],
                    title=poi['title'],
                    address=poi['address'],
                    category=poi['categories'],  # 使用categories字段
                    distance=poi['distance'],
                    tel=poi['tel']
                )

                # 根据categories判断属于哪个业务分类
                for business_category, category_list in self.CATEGORY_MAPPING.items():
                    if categories in category_list:
                        business_categorized_data[business_category].append(facility)
                        break

        # 每个业务分类选择最优的配套（已经按距离排序）
        facilities.transportation = business_categorized_data['transportation']
        facilities.living_services = business_categorized_data['living_services']
        facilities.commercial = business_categorized_data['commercial']
        facilities.leisure = business_categorized_data['leisure']
        facilities.industrial = business_categorized_data['industrial']

        return facilities


class EnhancedParkDataService:
    """增强园区数据服务类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.poi_service = ParkPOIService()
    
    def get_enhanced_park_data(self, park_name: str, options: Optional[DataRetrievalOptions] = None) -> Optional[EnhancedParkData]:
        """
        获取增强的园区数据，整合基础数据和详细信息
        
        Args:
            park_name (str): 园区名称
            options (DataRetrievalOptions): 数据获取选项
            
        Returns:
            EnhancedParkData: 综合园区数据，未找到返回None
        """
        if options is None:
            options = DataRetrievalOptions()
        
        try:
            basic_data = None
            detailed_info = None
            nearby_facilities = None

            # 获取基础数据
            if options.include_basic_data:
                basic_data = self._get_basic_park_data(park_name)

            # 获取详细信息
            if options.include_detailed_info:
                detailed_info = self._get_detailed_park_info(park_name)

            # 获取周边配套设施
            if options.include_nearby_facilities:
                nearby_facilities = self._get_nearby_facilities(park_name, options)

            # 如果所有数据源都没有找到，返回None
            if basic_data is None and detailed_info is None and (nearby_facilities is None or not nearby_facilities.has_facilities()):
                return None

            # 创建综合数据对象
            enhanced_data = EnhancedParkData(
                park_name=park_name,
                basic_data=basic_data,
                detailed_info=detailed_info
            )

            # 将周边配套数据临时存储（因为EnhancedParkData模型中没有这个字段）
            enhanced_data._nearby_facilities = nearby_facilities
            
            # 数据清洗
            if options.clean_text_data:
                enhanced_data = self._clean_enhanced_data(enhanced_data, options)
            
            return enhanced_data
            
        except Exception as e:
            self.logger.error(f"获取园区 '{park_name}' 的增强数据时发生错误: {str(e)}")
            return None
    
    def _get_basic_park_data(self, park_name: str) -> Optional[BasicParkData]:
        """
        从parsed_factory_data表获取基础园区数据
        
        Args:
            park_name (str): 园区名称
            
        Returns:
            BasicParkData: 基础园区数据，未找到返回None
        """
        try:
            connection = get_db_connection()
            
            with connection.cursor() as cursor:
                # 首先尝试精确匹配
                sql = """
                SELECT
                    community, floortype, Storey, slease, Planteia,
                    Plantstructure, Typewarehouse, Plantnew, isNewHouse,
                    AgentFree, years, Type4Years, Landnature, Powersupply, Loadbearing, type
                FROM parsed_factory_data
                WHERE community = %s
                LIMIT 1
                """
                cursor.execute(sql, (park_name,))
                result = cursor.fetchone()

                # 如果精确匹配没有结果，尝试模糊匹配
                if not result:
                    sql_fuzzy = """
                    SELECT
                        community, floortype, Storey, slease, Planteia,
                        Plantstructure, Typewarehouse, Plantnew, isNewHouse,
                        AgentFree, years, Type4Years, Landnature, Powersupply, Loadbearing, type
                    FROM parsed_factory_data
                    WHERE community LIKE %s
                    LIMIT 1
                    """
                    cursor.execute(sql_fuzzy, (f"%{park_name}%",))
                    result = cursor.fetchone()

                    if result:
                        pass
                
                if result:
                    # 应用数据映射转换
                    mapped_result = self._apply_data_mapping(result)
                    
                    return BasicParkData(
                        community=mapped_result.get('community'),
                        floortype=mapped_result.get('floortype'),
                        storey=mapped_result.get('Storey'),
                        slease=mapped_result.get('slease'),
                        planteia=mapped_result.get('Planteia'),
                        plantstructure=mapped_result.get('Plantstructure'),
                        typewarehouse=mapped_result.get('Typewarehouse'),
                        plantnew=mapped_result.get('Plantnew'),
                        is_new_house=mapped_result.get('isNewHouse'),
                        agent_free=mapped_result.get('AgentFree'),
                        years=mapped_result.get('years'),
                        type4years=mapped_result.get('Type4Years'),
                        landnature=mapped_result.get('Landnature'),
                        powersupply=mapped_result.get('Powersupply'),
                        loadbearing=mapped_result.get('Loadbearing'),
                        type=mapped_result.get('type')
                    )
                else:
                    return None
                    
        except Exception as e:
            self.logger.error(f"获取基础园区数据时发生错误: {str(e)}")
            return None
        finally:
            if 'connection' in locals() and connection:
                connection.close()
    
    def _get_detailed_park_info(self, park_name: str) -> Optional[DetailedParkInfo]:
        """
        从park_info表获取详细园区信息
        
        Args:
            park_name (str): 园区名称
            
        Returns:
            DetailedParkInfo: 详细园区信息，未找到返回None
        """
        try:
            connection = get_db_connection()
            
            with connection.cursor() as cursor:
                # 首先尝试精确匹配
                sql = """
                SELECT * FROM park_info
                WHERE park_name = %s
                LIMIT 1
                """
                cursor.execute(sql, (park_name,))
                result = cursor.fetchone()

                # 如果精确匹配没有结果，尝试模糊匹配
                if not result:
                    sql_fuzzy = """
                    SELECT * FROM park_info
                    WHERE park_name LIKE %s
                    LIMIT 1
                    """
                    cursor.execute(sql_fuzzy, (f"%{park_name}%",))
                    result = cursor.fetchone()

                    if result:
                        pass
                
                if result:
                    return DetailedParkInfo(
                        park_name=result.get('park_name'),
                        park_stage=result.get('park_stage'),
                        park_director=result.get('park_director'),
                        park_manager=result.get('park_mannger'),  # 注意：数据库字段名是park_mannger
                        park_province=result.get('park_province'),
                        park_city=result.get('park_city'),
                        park_country=result.get('park_country'),
                        park_area=result.get('park_area'),
                        park_type=result.get('park_type'),
                        park_structure=result.get('park_structure'),
                        park_busi_type=result.get('park_busi_type'),
                        park_property=result.get('park_property'),
                        park_support=result.get('park_support'),
                        park_depot=result.get('park_depot'),
                        park_elevator=result.get('park_elevator'),
                        park_consultid=result.get('park_consultid'),
                        park_policy=result.get('park_policy'),
                        park_industry=result.get('park_industry'),
                        park_plot=result.get('park_plot'),
                        park_fpl=result.get('park_fpl'),
                        park_ffa=result.get('park_ffa'),
                        park_stt=result.get('park_stt'),
                        park_dsd=result.get('park_dsd'),
                        park_we=result.get('park_we'),
                        park_payment=result.get('park_paymet'),  # 注意：数据库字段名是park_paymet
                        park_deli_time=result.get('park_deli_time'),
                        park_entex=result.get('park_entex'),
                        park_enreq=result.get('park_enreq'),
                        park_disbet=result.get('park_disbet'),
                        park_rategreen=result.get('park_rategreen'),
                        park_taxreq=result.get('park_taxreq'),
                        park_plotrate=result.get('park_plotrate'),
                        park_seciss=result.get('park_seciss'),
                        park_delista=result.get('park_delista'),
                        park_propercert=result.get('park_propercert'),
                        park_investtotal=result.get('park_investtotal'),
                        park_advantageanddis=result.get('park_advantageanddis'),
                        park_style=result.get('park_style'),
                        park_salecert=result.get('park_salecert'),
                        park_heiandload=result.get('park_heiandload'),
                        park_materials=result.get('park_materials'),
                        park_weconf=result.get('park_weconf'),
                        park_powersupport=result.get('park_powersupport'),
                        park_ecapacity=result.get('park_ecapacity'),
                        park_aircconf=result.get('park_aircconf'),
                        park_gas=result.get('park_gas'),
                        park_flueconf=result.get('park_flueconf'),
                        park_roadconf=result.get('park_roadconf'),
                        park_insulation=result.get('park_insulation'),
                        park_netconf=result.get('park_netconf'),
                        park_seismic=result.get('park_seismic'),
                        park_othexp=result.get('park_othexp'),
                        park_invoce=result.get('park_invoce'),
                        park_propertyinfo=result.get('park_propertyinfo'),
                        park_decoration=result.get('park_decoration'),
                        park_financialsupport=result.get('park_financialsupport'),
                        park_streetaddress=result.get('park_streetaddress'),
                        park_lat_lng=result.get('park_lat_lng'),
                        created_at=result.get('created_at'),
                        updated_at=result.get('updated_at')
                    )
                else:
                    return None
                    
        except Exception as e:
            self.logger.error(f"获取详细园区信息时发生错误: {str(e)}")
            return None
        finally:
            if 'connection' in locals() and connection:
                connection.close()

    def _get_nearby_facilities(self, park_name: str, options: DataRetrievalOptions) -> Optional[NearbyFacilities]:
        """
        获取园区周边配套设施

        Args:
            park_name (str): 园区名称
            options (DataRetrievalOptions): 数据获取选项

        Returns:
            NearbyFacilities: 周边配套设施数据，未找到返回None
        """
        return self.poi_service.get_nearby_facilities(park_name, options)

    def _apply_data_mapping(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用数据映射转换（从原get_community_data.py复制的逻辑）
        
        Args:
            result (Dict): 原始数据库查询结果
            
        Returns:
            Dict: 映射转换后的结果
        """
        mapped_result = result.copy()
        
        # 应用各种映射转换
        if 'Planteia' in mapped_result and mapped_result['Planteia'] in PLANTEIA_MAPPING:
            mapped_result['Planteia'] = PLANTEIA_MAPPING[mapped_result['Planteia']]
        
        if 'Type4Years' in mapped_result and mapped_result['Type4Years'] in TYPE4YEARS_MAPPING:
            mapped_result['Type4Years'] = TYPE4YEARS_MAPPING[mapped_result['Type4Years']]
        
        if 'Plantnew' in mapped_result and mapped_result['Plantnew'] in PLANTNEW_MAPPING:
            mapped_result['Plantnew'] = PLANTNEW_MAPPING[mapped_result['Plantnew']]
        
        if 'floortype' in mapped_result and mapped_result['floortype'] in FLOORTYPE_MAPPING:
            mapped_result['floortype'] = FLOORTYPE_MAPPING[mapped_result['floortype']]
        
        if 'AgentFree' in mapped_result and mapped_result['AgentFree'] in AGENTFREE_MAPPING:
            mapped_result['AgentFree'] = AGENTFREE_MAPPING[mapped_result['AgentFree']]

        if 'Typewarehouse' in mapped_result and mapped_result['Typewarehouse'] in TYPEWAREHOUSE_MAPPING:
            mapped_result['Typewarehouse'] = TYPEWAREHOUSE_MAPPING[mapped_result['Typewarehouse']]

        if 'isNewHouse' in mapped_result and mapped_result['isNewHouse'] in ISNEWHOUSE_MAPPING:
            mapped_result['isNewHouse'] = ISNEWHOUSE_MAPPING[mapped_result['isNewHouse']]

        if 'Landnature' in mapped_result and mapped_result['Landnature'] in LANDNATURE_MAPPING:
            mapped_result['Landnature'] = LANDNATURE_MAPPING[mapped_result['Landnature']]

        return mapped_result

    def _clean_enhanced_data(self, enhanced_data: EnhancedParkData, options: DataRetrievalOptions) -> EnhancedParkData:
        """
        清洗增强园区数据

        Args:
            enhanced_data (EnhancedParkData): 原始增强数据
            options (DataRetrievalOptions): 数据获取选项

        Returns:
            EnhancedParkData: 清洗后的数据
        """
        try:
            # 清洗基础数据
            if enhanced_data.basic_data:
                enhanced_data.basic_data = self._clean_basic_data(enhanced_data.basic_data, options)

            # 清洗详细信息
            if enhanced_data.detailed_info:
                enhanced_data.detailed_info = self._clean_detailed_info(enhanced_data.detailed_info, options)

            return enhanced_data

        except Exception as e:
            self.logger.error(f"清洗数据时发生错误: {str(e)}")
            return enhanced_data

    def _clean_basic_data(self, basic_data: BasicParkData, options: DataRetrievalOptions) -> BasicParkData:
        """清洗基础数据"""
        # 如果不包含空字段，则将空值设为None
        if not options.include_empty_fields:
            for field_name in basic_data.__dataclass_fields__:
                value = getattr(basic_data, field_name)
                if isinstance(value, str) and (not value or value.strip() == ''):
                    setattr(basic_data, field_name, None)

        return basic_data

    def _clean_detailed_info(self, detailed_info: DetailedParkInfo, options: DataRetrievalOptions) -> DetailedParkInfo:
        """清洗详细信息"""
        # 如果不包含空字段，则将空值设为None
        if not options.include_empty_fields:
            for field_name in detailed_info.__dataclass_fields__:
                value = getattr(detailed_info, field_name)
                if isinstance(value, str) and (not value or value.strip() == ''):
                    setattr(detailed_info, field_name, None)

        # 文本长度限制
        if options.max_text_length:
            for field_name in detailed_info.__dataclass_fields__:
                value = getattr(detailed_info, field_name)
                if isinstance(value, str) and len(value) > options.max_text_length:
                    setattr(detailed_info, field_name, value[:options.max_text_length] + '...')

        return detailed_info

    def get_basic_park_info(self, park_name: str) -> Optional[BasicParkData]:
        """
        仅获取基础园区信息

        Args:
            park_name (str): 园区名称

        Returns:
            BasicParkData: 基础园区数据，未找到返回None
        """
        return self._get_basic_park_data(park_name)

    def get_detailed_park_info(self, park_name: str) -> Optional[DetailedParkInfo]:
        """
        仅获取详细园区信息

        Args:
            park_name (str): 园区名称

        Returns:
            DetailedParkInfo: 详细园区信息，未找到返回None
        """
        return self._get_detailed_park_info(park_name)

    def get_ai_optimized_data(self, park_name: str) -> Optional[AIOptimizedParkData]:
        """
        获取AI优化的园区数据

        Args:
            park_name (str): 园区名称

        Returns:
            AIOptimizedParkData: AI优化的园区数据，未找到返回None
        """
        try:
            # 获取完整的增强数据
            enhanced_data = self.get_enhanced_park_data(park_name)
            if not enhanced_data:
                return None

            # 创建AI优化数据对象
            ai_data = AIOptimizedParkData(park_name=park_name)

            # 分类整理数据
            self._categorize_data_for_ai(enhanced_data, ai_data)

            # 计算数据质量评分
            ai_data.data_quality_score = self._calculate_data_quality_score(enhanced_data)

            return ai_data

        except Exception as e:
            self.logger.error(f"获取AI优化数据时发生错误: {str(e)}")
            return None

    def _categorize_data_for_ai(self, enhanced_data: EnhancedParkData, ai_data: AIOptimizedParkData):
        """
        为AI优化数据进行分类整理 - 包含所有可用字段

        Args:
            enhanced_data (EnhancedParkData): 增强园区数据
            ai_data (AIOptimizedParkData): AI优化数据对象（会被修改）
        """
        # 技术规格信息（来自基础数据）
        if enhanced_data.basic_data:
            basic = enhanced_data.basic_data

            # 为相关字段添加单位
            storey_with_unit = f"{basic.storey}米" if basic.storey else None
            slease_with_unit = f"{basic.slease}月" if basic.slease else None
            powersupply_with_unit = f"{basic.powersupply}千瓦(kW)" if basic.powersupply else None
            loadbearing_with_unit = f"{basic.loadbearing}公斤(kg)" if basic.loadbearing else None

            # 技术规格信息（包含所有可用数据，让AI根据租售类型自主选择）
            ai_data.technical_specs = {
                '租售类型': basic.type,
                '楼层类型': basic.floortype,
                '首层层高': storey_with_unit,
                '起租期': slease_with_unit,
                '可办环评': basic.planteia,
                '厂房结构': basic.plantstructure,
                '厂房类型': basic.typewarehouse,
                '厂房新旧': basic.plantnew,
                '性质': basic.is_new_house,
                '中介费': basic.agent_free,
                '建筑年代': basic.years,
                '产权年限': basic.type4years,
                '土地性质': basic.landnature,
                '供电容量': powersupply_with_unit,
                '楼板承重': loadbearing_with_unit
            }

            # 移除空值
            ai_data.technical_specs = {k: v for k, v in ai_data.technical_specs.items() if v}

        # 营销卖点信息
        if enhanced_data.detailed_info:
            detailed = enhanced_data.detailed_info
            ai_data.selling_points = {
                '园区类型': detailed.park_type,
                '园区结构': detailed.park_structure,
                '业务类型': detailed.park_busi_type,
                '园区性质': detailed.park_property,
                '优势劣势': detailed.park_advantageanddis,
                '园区风格': detailed.park_style,
                '投资总额': detailed.park_investtotal,
                '园区阶段': detailed.park_stage,
                '园区面积': detailed.park_area,
                '地块信息': detailed.park_plot,
                '容积率': detailed.park_fpl,
                '建筑面积': detailed.park_ffa,
                '结构类型': detailed.park_stt,
                '设计标准': detailed.park_dsd,
                '绿化率': detailed.park_rategreen,
                '地块容积率': detailed.park_plotrate,
                '高度和荷载': detailed.park_heiandload,
                '建筑材料': detailed.park_materials,
                '抗震等级': detailed.park_seismic,
                '保温配置': detailed.park_insulation,
                '装修情况': detailed.park_decoration
            }
            # 移除空值
            ai_data.selling_points = {k: v for k, v in ai_data.selling_points.items() if v}

        # 配套设施信息
        if enhanced_data.detailed_info:
            detailed = enhanced_data.detailed_info
            ai_data.facilities = {
                '配套设施': detailed.park_support,
                '仓储设施': detailed.park_depot,
                '电梯配置': detailed.park_elevator,
                '水电配置': detailed.park_we,
                '水电配置详细': detailed.park_weconf,
                '电力支持': detailed.park_powersupport,
                '电力容量': detailed.park_ecapacity,
                '空调配置': detailed.park_aircconf,
                '燃气配置': detailed.park_gas,
                '烟道配置': detailed.park_flueconf,
                '网络配置': detailed.park_netconf,
                '道路配置': detailed.park_roadconf,
                '物业信息': detailed.park_propertyinfo
            }
            # 移除空值
            ai_data.facilities = {k: v for k, v in ai_data.facilities.items() if v}

        # 政策和产业信息
        if enhanced_data.detailed_info:
            detailed = enhanced_data.detailed_info
            ai_data.policies_and_industries = {
                '政策支持': detailed.park_policy,
                '招商产业': detailed.park_industry,
                '环保要求': detailed.park_enreq,
                '税收要求': detailed.park_taxreq,
                '金融支持': detailed.park_financialsupport,
                '入驻要求': detailed.park_entex,
                '付款方式': detailed.park_payment,
                '交付时间': detailed.park_deli_time,
                '交付清单': detailed.park_delista,
                '产权证书': detailed.park_propercert,
                '销售证书': detailed.park_salecert,
                '发票信息': detailed.park_invoce,
                '安全问题': detailed.park_seciss,
                '其他说明': detailed.park_othexp
            }
            # 移除空值
            ai_data.policies_and_industries = {k: v for k, v in ai_data.policies_and_industries.items() if v}

        # 地理位置信息
        if enhanced_data.detailed_info:
            detailed = enhanced_data.detailed_info
            ai_data.location_info = {
                '省份': detailed.park_province,
                '城市': detailed.park_city,
                '区县': detailed.park_country,
                '详细地址': detailed.park_streetaddress,
                '经纬度': detailed.park_lat_lng,
                '距离优势': detailed.park_disbet
            }
            # 移除空值
            ai_data.location_info = {k: v for k, v in ai_data.location_info.items() if v}

        # 管理信息（新增分类）
        if enhanced_data.detailed_info:
            detailed = enhanced_data.detailed_info
            management_info = {
                '园区负责人': detailed.park_director,
                '园区管理者': detailed.park_manager,
                '咨询ID': detailed.park_consultid
            }
            # 移除空值并添加到营销卖点中（如果有数据的话）
            management_info = {k: v for k, v in management_info.items() if v}
            if management_info:
                ai_data.selling_points.update(management_info)

        # 处理周边配套设施信息
        if hasattr(enhanced_data, '_nearby_facilities') and enhanced_data._nearby_facilities:
            ai_data.nearby_facilities = enhanced_data._nearby_facilities

        # 更新可用字段列表
        ai_data.available_fields = []
        for category_name, category_data in [
            ('technical_specs', ai_data.technical_specs),
            ('selling_points', ai_data.selling_points),
            ('facilities', ai_data.facilities),
            ('policies_and_industries', ai_data.policies_and_industries),
            ('location_info', ai_data.location_info)
        ]:
            for field_name in category_data.keys():
                ai_data.available_fields.append(f"{category_name}.{field_name}")

        # 添加周边配套字段
        if ai_data.nearby_facilities and ai_data.nearby_facilities.has_facilities():
            ai_data.available_fields.append("nearby_facilities.transportation")
            ai_data.available_fields.append("nearby_facilities.living_services")
            ai_data.available_fields.append("nearby_facilities.commercial")
            ai_data.available_fields.append("nearby_facilities.leisure")
            ai_data.available_fields.append("nearby_facilities.industrial")

    def _calculate_data_quality_score(self, enhanced_data: EnhancedParkData) -> float:
        """
        计算数据质量评分

        Args:
            enhanced_data (EnhancedParkData): 增强园区数据

        Returns:
            float: 数据质量评分（0-1之间）
        """
        total_fields = 0
        filled_fields = 0

        # 统计基础数据字段
        if enhanced_data.basic_data:
            for field_name in enhanced_data.basic_data.__dataclass_fields__:
                total_fields += 1
                value = getattr(enhanced_data.basic_data, field_name)
                if value is not None and str(value).strip():
                    filled_fields += 1

        # 统计详细信息字段
        if enhanced_data.detailed_info:
            for field_name in enhanced_data.detailed_info.__dataclass_fields__:
                if field_name not in ['created_at', 'updated_at']:  # 排除时间戳字段
                    total_fields += 1
                    value = getattr(enhanced_data.detailed_info, field_name)
                    if value is not None and str(value).strip():
                        filled_fields += 1

        # 统计周边配套设施数据
        if hasattr(enhanced_data, '_nearby_facilities') and enhanced_data._nearby_facilities:
            nearby_facilities = enhanced_data._nearby_facilities
            # 每个配套分类作为一个字段
            total_fields += 5  # 5个配套分类

            if nearby_facilities.transportation:
                filled_fields += 1
            if nearby_facilities.living_services:
                filled_fields += 1
            if nearby_facilities.commercial:
                filled_fields += 1
            if nearby_facilities.leisure:
                filled_fields += 1
            if nearby_facilities.industrial:
                filled_fields += 1

        return filled_fields / total_fields if total_fields > 0 else 0.0


# 便利函数
def get_enhanced_park_data(park_name: str, options: Optional[DataRetrievalOptions] = None) -> Optional[EnhancedParkData]:
    """
    便利函数：获取增强的园区数据

    Args:
        park_name (str): 园区名称
        options (DataRetrievalOptions): 数据获取选项

    Returns:
        EnhancedParkData: 综合园区数据，未找到返回None
    """
    service = EnhancedParkDataService()
    return service.get_enhanced_park_data(park_name, options)


def get_basic_park_info(park_name: str) -> Optional[BasicParkData]:
    """
    便利函数：仅获取基础园区信息

    Args:
        park_name (str): 园区名称

    Returns:
        BasicParkData: 基础园区数据，未找到返回None
    """
    service = EnhancedParkDataService()
    return service.get_basic_park_info(park_name)


def get_detailed_park_info(park_name: str) -> Optional[DetailedParkInfo]:
    """
    便利函数：仅获取详细园区信息

    Args:
        park_name (str): 园区名称

    Returns:
        DetailedParkInfo: 详细园区信息，未找到返回None
    """
    service = EnhancedParkDataService()
    return service.get_detailed_park_info(park_name)


def get_ai_optimized_data(park_name: str) -> Optional[AIOptimizedParkData]:
    """
    便利函数：获取AI优化的园区数据

    Args:
        park_name (str): 园区名称

    Returns:
        AIOptimizedParkData: AI优化的园区数据，未找到返回None
    """
    service = EnhancedParkDataService()
    return service.get_ai_optimized_data(park_name)





def list_available_parks(limit: int = 10) -> List[str]:
    """
    获取可用的园区名称列表

    Args:
        limit (int): 返回数量限制

    Returns:
        List[str]: 园区名称列表
    """
    try:
        connection = get_db_connection()
        park_names = []

        with connection.cursor() as cursor:
            # 从两个表中获取园区名称
            sql_basic = """
            SELECT DISTINCT community as park_name
            FROM parsed_factory_data
            WHERE community IS NOT NULL AND community != ''
            LIMIT %s
            """
            cursor.execute(sql_basic, (limit // 2,))
            basic_parks = cursor.fetchall()

            sql_detailed = """
            SELECT DISTINCT park_name
            FROM park_info
            WHERE park_name IS NOT NULL AND park_name != ''
            LIMIT %s
            """
            cursor.execute(sql_detailed, (limit // 2,))
            detailed_parks = cursor.fetchall()

            # 合并结果
            for park in basic_parks:
                park_names.append(park['park_name'])

            for park in detailed_parks:
                if park['park_name'] not in park_names:
                    park_names.append(park['park_name'])

            return park_names[:limit]

    except Exception as e:
        logger.error(f"获取园区列表时发生错误: {str(e)}")
        return []
    finally:
        if 'connection' in locals() and connection:
            connection.close()


if __name__ == "__main__":
    # 测试代码
    import argparse
    import json

    parser = argparse.ArgumentParser(description='增强园区数据服务测试')
    parser.add_argument('--park_name', type=str, help='园区名称')
    parser.add_argument('--mode', type=str, choices=['basic', 'detailed', 'enhanced', 'ai'],
                       default='enhanced', help='数据获取模式')
    parser.add_argument('--list', action='store_true', help='列出可用园区')
    parser.add_argument('--limit', type=int, default=10, help='列表数量限制')

    args = parser.parse_args()

    if args.list:
        # 列出可用园区
        parks = list_available_parks(args.limit)
        print(f"可用园区列表（前{len(parks)}个）:")
        for i, park in enumerate(parks, 1):
            print(f"{i:2d}. {park}")

    elif args.park_name:
        # 获取指定园区数据
        park_name = args.park_name

        if args.mode == 'basic':
            data = get_basic_park_info(park_name)
            print(f"园区 '{park_name}' 的基础数据:")
            if data:
                for field, value in data.__dict__.items():
                    if value:
                        print(f"  {field}: {value}")
            else:
                print("  未找到数据")

        elif args.mode == 'detailed':
            data = get_detailed_park_info(park_name)
            print(f"园区 '{park_name}' 的详细信息:")
            if data:
                for field, value in data.__dict__.items():
                    if value and field not in ['created_at', 'updated_at']:
                        print(f"  {field}: {str(value)[:100]}..." if len(str(value)) > 100 else f"  {field}: {value}")
            else:
                print("  未找到数据")

        elif args.mode == 'enhanced':
            data = get_enhanced_park_data(park_name)
            print(f"园区 '{park_name}' 的增强数据:")
            if data:
                print(f"  数据完整性: {data.data_completeness}")
                print(f"  最后更新: {data.last_updated}")
                if data.basic_data:
                    print("  基础数据: 可用")
                if data.detailed_info:
                    print("  详细信息: 可用")
            else:
                print("  未找到数据")

        elif args.mode == 'ai':
            data = get_ai_optimized_data(park_name)
            print(f"园区 '{park_name}' 的AI优化数据:")
            if data:
                print(f"  数据质量评分: {data.data_quality_score:.2f}")
                print(f"  可用字段数量: {len(data.available_fields)}")
                print("  技术规格:", json.dumps(data.technical_specs, ensure_ascii=False, indent=2))
                print("  营销卖点:", json.dumps(data.selling_points, ensure_ascii=False, indent=2))
                print("  配套设施:", json.dumps(data.facilities, ensure_ascii=False, indent=2))
                print("  政策产业:", json.dumps(data.policies_and_industries, ensure_ascii=False, indent=2))
                print("  位置信息:", json.dumps(data.location_info, ensure_ascii=False, indent=2))

                # 显示周边配套信息
                if data.nearby_facilities and data.nearby_facilities.has_facilities():
                    nearby_summary = {}
                    if data.nearby_facilities.transportation:
                        nearby_summary['交通配套'] = [f"{f.title}({f.distance_text})" for f in data.nearby_facilities.transportation]
                    if data.nearby_facilities.living_services:
                        nearby_summary['生活配套'] = [f"{f.title}({f.distance_text})" for f in data.nearby_facilities.living_services]
                    if data.nearby_facilities.commercial:
                        nearby_summary['商业配套'] = [f"{f.title}({f.distance_text})" for f in data.nearby_facilities.commercial]
                    if data.nearby_facilities.leisure:
                        nearby_summary['休闲配套'] = [f"{f.title}({f.distance_text})" for f in data.nearby_facilities.leisure]
                    if data.nearby_facilities.industrial:
                        nearby_summary['产业配套'] = [f"{f.title}({f.distance_text})" for f in data.nearby_facilities.industrial]
                    print("  周边配套:", json.dumps(nearby_summary, ensure_ascii=False, indent=2))
            else:
                print("  未找到数据")



    else:
        print("请指定园区名称或使用 --list 参数列出可用园区")
        print("使用示例:")
        print("  python enhanced_park_data_service.py --list")
        print("  python enhanced_park_data_service.py --park_name '上海嘉定外冈莱尼项目（金绥）' --mode ai")
