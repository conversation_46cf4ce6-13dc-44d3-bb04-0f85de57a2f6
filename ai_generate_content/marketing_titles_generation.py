#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
房源营销标题生成模块
基于客户画像分析，生成针对性的房源营销标题
"""

import logging
import os
import sys
import re
import time
import concurrent.futures
from openai import OpenAI

# 导入客户画像生成模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ai_generate_content.customer_profile_generation import generate_customer_profiles

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 阿里云千问模型API配置
DASHSCOPE_API_KEY = "sk-61e66c39babd417da86f9f2e6b580492"
os.environ["DASHSCOPE_API_KEY"] = DASHSCOPE_API_KEY

# 创建OpenAI客户端，配置为阿里云千问模型
client = OpenAI(
    api_key=DASHSCOPE_API_KEY,
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

# 提示词模板
prompt_template = """#角色定义：你是一名非常善于在58同城\安居客等房产发布网站上进行网络营销的厂房园区的招商顾问，拥有丰富的园区知识库和客户洞察能力。你的主要工作是通过已经分析出的目标客户画像以及特征信息，结合园区的完整知识库信息，针对不同的目标客户画像以及客户特征设计出不同的房源营销点组合。

你需要深度挖掘园区知识库中的所有有价值信息，包括技术参数、配套设施、政策优势、地理位置、管理服务等各个维度，确保每一个营销点都要紧扣该目标客户的需求，然后将营销点进行组合梳理成初始的58房源帖子标题，要求标题中包含组合中所有的营销点表达，并且能够一目了然看出来该标题所想呈现的房源的营销点。

然后针对梳理出的初始标题进行改写，要求在不改变营销点表达的情况下尽可能发挥想象力，让标题变得更加吸引眼球引人注目一看到就想点击，改写可对原文中所有营销点的表达方式进行适当优化但不要偏离事实，允许调整所有营销点的前后顺序，允许改变标题中营销点表达的语气语调，允许在营销点表达中适当加入网络用语或者口语增强感染力、吸引力、亲和力。

#园区完整知识库：
{community_data}

#知识库使用指南：
**核心原则：所有营销内容必须且只能基于知识库中提供的真实数据！**

**重要：请根据知识库中的租售类型信息，智能选择合适的数据作为营销重点。**

**特别注意：如果租售类型为"出售、出租"（即既可出租也可出售），请不要在标题中提及任何租售相关的具体内容（如起租期、产权年限、租金、售价等），而应重点突出园区本身的特色、技术优势、配套设施、地理位置等中性特点。**

请从以下维度全面挖掘知识库中的所有可用信息：

1. **技术规格维度**：
   - 基础参数：租售类型、楼层类型、首层层高、起租期、厂房结构、厂房类型、厂房新旧、性质、中介费
   - 产权信息：建筑年代、产权年限、土地性质、产权证书、销售证书
   - 技术指标：供电容量、楼板承重、可办环评、抗震等级、保温配置

2. **园区特色维度**：
   - 园区定位：园区类型、园区结构、业务类型、园区性质、园区阶段、园区面积
   - 特色优势：园区风格、优势劣势、园区规模、园区负责人、园区管理者
   - 规划信息：地块信息、容积率、建筑面积、结构类型、设计标准、绿化率、地块容积率

3. **配套设施维度**：
   - 基础设施：配套设施、仓储设施、电梯配置、水电配置、水电配置详细
   - 能源配套：电力支持、电力容量、燃气配置、供电容量
   - 智能配套：空调配置、烟道配置、网络配置、道路配置
   - 服务配套：园区管理服务、装修情况

4. **政策支持维度**：
   - 政策便利：政策支持、招商产业、行政服务
   - 环保便利：环保要求、可办环评
   - 入驻服务：入驻要求、付款方式、交付时间、交付清单
   - 证照办理：产权证书、销售证书、发票信息

5. **地理位置维度**：
   - 行政区划：省份、城市、区县、详细地址、经纬度
   - 交通优势：距离优势、道路配置
   - 区位价值：根据地址信息体现的区位优势

6. **服务管理维度**：
   - 管理团队：园区负责人、园区管理者、咨询ID
   - 服务保障：园区管理服务、安全问题、其他说明
   - 建筑品质：高度和荷载、建筑材料、装修情况

7. **周边配套维度**（**重点营销维度**）：
   - **交通配套**（优先级最高）：公交站、地铁站、火车站、轻轨站、长途汽车站、机场、高速入口等
   - **产业配套**（优先级最高）：产业园区、工厂、物流仓储场地等产业集聚优势
   - **生活配套**（适当使用）：超市、医院、诊所、教育学校等生活便利设施
   - **商业配套**（适当使用）：商场、酒店、饭店、热点区域等商业服务
   - **休闲配套**（适当使用）：公园、健身房、羽毛球馆、乒乓球馆、篮球场、电影院等

**周边配套营销策略：**
- 交通配套和产业配套应作为主要营销卖点，占周边配套营销内容的70%以上
- 交通配套重点突出"便捷通达、物流便利、员工出行方便"等价值
- 产业配套重点突出"产业集聚、上下游配套、协同发展"等优势
- 其他配套作为补充卖点，体现园区的综合配套完善性
- 配套距离信息要准确标注，近距离配套（1公里内）优先使用

**知识库使用规范：**
- 每个营销点必须对应知识库中的具体数据字段，建议在营销点后标注数据来源
- 如果某个维度在知识库中没有数据，请不要在营销内容中提及该维度
- 根据知识库中的租售类型，智能选择最相关的数据作为营销重点
- **特殊处理：当租售类型为"出售、出租"时，重点使用园区特色、技术规格、配套设施、地理位置等中性信息，避免使用起租期、产权年限等具体租售相关数据**
- 优先使用知识库中的具体数据和描述，避免添加知识库中没有的信息
- 避免推测或补充知识库中没有的信息
- 将技术参数转化为客户易懂的营销语言时，请保持数据的准确性
- 建议全面覆盖知识库中的有效数据，确保信息利用率最大化
- 每个营销版本建议基于知识库中不同的数据组合，尽量避免重复使用相同信息

**营销语言优化指导：**
- 避免使用过于绝对的表述（如"100%"、"绝对"、"完全"、"最优"、"最佳"、"包过"等）
- 可以使用有吸引力的形容词，但要基于知识库中的实际数据

**时间表述优化要求：**
- **严禁使用具体时间数字**：避免"24小时"、"5分钟"、"30分钟"、"2小时"等具体时间

**敏感内容禁止要求：**
- **严禁在标题中提及任何投资、投资回报、投资收益等投资相关内容**
- **严禁在标题中提及任何物业费、物业管理费等物业费用相关内容**
- **严禁在标题中提及任何返税、税收返还、税收优惠等返税相关内容**
- **严禁在标题中提及任何具体金融收益、财务回报等财务收益内容**
- 标题应聚焦于厂房的实用功能、技术参数、配套设施、地理优势等客观属性

#输出要求：
针对目标客户{customer_type}，深度挖掘园区完整知识库中与该客户需求最匹配的信息点，给出8个不同的营销点组合版本，每个营销点组合版本至少含有6个以上的营销点，但确保每一个营销点都和目标客户的实际工作场景较好匹配，不同的营销点组合版本需要对目标客户画像的特征实现较好覆盖，原始标题和改写标题都应紧扣营销点；另外标题中不要出现"+"号，可用空格代替。

**生成前建议完成的自检清单：**
1. 检查每个营销点是否都能在上述知识库中找到对应的具体数据
2. 确认没有添加知识库中不存在的信息
3. 验证是否根据租售类型选择了最相关的知识库内容作为营销重点
4. **特别检查：如果租售类型为"出售、出租"，确认标题中没有提及起租期、产权年限、租金、售价等具体租售信息，而是突出了园区特色**
5. 验证是否避免了过于绝对化的表述
6. 确保较好地覆盖了知识库中的主要有效信息
7. 检查是否避免了重复使用相同的知识库信息
8. 验证营销语言是否适度且有吸引力
9. **重点检查：确认标题中没有涉及投资、物业费、返税等敏感内容**
10. **重点检查：确认标题聚焦于厂房实用功能和客观属性**

#输出格式：

【目标客户{customer_letter}：{customer_type}】

营销版本A营销点：XXX+XXX+XXX+XXX+XXX+XXX···原始标题：XXX···改写标题：XXX···

营销版本B营销点：XXX+XXX+XXX+XXX+XXX+XXX···原始标题：XXX···改写标题：XXX···

营销版本C营销点：XXX+XXX+XXX+XXX+XXX+XXX···原始标题：XXX···改写标题：XXX···

营销版本D营销点：XXX+XXX+XXX+XXX+XXX+XXX···原始标题：XXX···改写标题：XXX···

营销版本E营销点：XXX+XXX+XXX+XXX+XXX+XXX···原始标题：XXX···改写标题：XXX···

营销版本F营销点：XXX+XXX+XXX+XXX+XXX+XXX···原始标题：XXX···改写标题：XXX···

营销版本G营销点：XXX+XXX+XXX+XXX+XXX+XXX···原始标题：XXX···改写标题：XXX···

营销版本H营销点：XXX+XXX+XXX+XXX+XXX+XXX···原始标题：XXX···改写标题：XXX···
"""

def extract_customer_info(customer_profiles):
    """
    从客户画像文本中提取客户类型信息
    返回一个包含客户类型的字典列表
    """
    customers = []
    
    # 使用正则表达式查找所有匹配"目标客户X：XXX"的模式
    pattern = r"目标客户([A-E])：(.*?)(?=\n)"
    matches = re.findall(pattern, customer_profiles)
    
    for letter, customer_type in matches:
        customers.append({
            "letter": letter,
            "type": customer_type.strip()
        })
    
    return customers

def extract_community_data(customer_profiles):
    """
    从客户画像结果中提取园区数据部分，包括周边配套设施

    Args:
        customer_profiles (str): 客户画像生成结果

    Returns:
        str: 提取的园区数据字符串，包含周边配套信息
    """
    # 提取园区增强数据（包含周边配套）
    enhanced_pattern = r"# 园区增强数据.*?\n\n(.*?)(?=\n\n----- 园区|$)"
    enhanced_match = re.search(enhanced_pattern, customer_profiles, re.DOTALL)

    if enhanced_match:
        logger.info("成功从客户画像结果中提取园区数据（包含周边配套）")
        return enhanced_match.group(1).strip()
    else:
        logger.warning("未能从客户画像结果中提取到园区数据")
        return "未能获取到园区数据"

def format_knowledge_base_for_ai(community_data):
    """
    格式化知识库数据，明确展示所有可用信息

    Args:
        community_data (str): 原始社区数据

    Returns:
        str: 格式化后的知识库内容
    """
    # 重新格式化以突出可用数据
    formatted_data = "\n=== 园区知识库完整数据清单 ===\n"
    formatted_data += "【重要】以下是该园区的所有可用真实数据，营销内容必须且只能基于这些数据：\n\n"
    formatted_data += community_data
    formatted_data += "\n\n=== 数据使用说明 ===\n"
    formatted_data += "1. 上述每一项数据都是真实可用的\n"
    formatted_data += "2. 如果某项数据在上述清单中不存在，请不要在营销内容中提及\n"
    formatted_data += "3. 避免添加、推测或过度夸大任何信息\n"
    formatted_data += "4. 建议充分利用上述所有有效数据\n"
    formatted_data += "5. 营销语言应适度表达，避免过于绝对化的词汇\n"

    return formatted_data


def generate_marketing_titles_for_customer(task):
    """
    为特定客户类型生成营销标题

    Args:
        task: 包含(customer_letter, customer_type, customer_profiles, community_data, task_id)的元组
    """
    customer_letter, customer_type, customer_profiles, community_data, task_id = task
    
    try:
        logger.info(f"[任务 {task_id}] 开始为客户类型 '{customer_type}' 生成房源营销标题...")
        print(f"\n\n==================== 开始处理客户 '{customer_type}' 的营销标题生成 (任务 {task_id}) ====================\n", flush=True)
        
        # 添加强制思考的指令和优化约束
        system_prompt = """你是一名专业的厂房招商营销专家。你需要严格按照提供的园区知识库内容生成营销标题。

**核心约束条件：**
1. 所有营销内容必须且只能基于知识库中提供的真实数据
2. 避免添加知识库中没有的任何信息
3. 避免过度夸大知识库中的数据，保持适度表达
4. 每个营销点应能在知识库中找到对应的具体数据
5. 如果知识库中某项数据为空或不存在，请不要在营销内容中提及该项
6. 建议全面覆盖知识库中的有效信息，避免遗漏重要数据

**营销语言要求：**
- 避免使用过于绝对的表述（如"100%"、"绝对"、"完全"、"最佳"等）
- 可以使用积极正面的形容词，但要基于知识库中的实际数据
- 保持营销内容的吸引力，同时确保表述的适度性

**时间表述约束：**
- **严格禁止使用具体时间数字**：不得使用"24小时"、"5分钟"、"30分钟"等具体时间

**敏感内容严格禁止：**
- **绝对禁止在标题中提及任何投资、投资回报、投资收益、投资价值等投资相关内容**
- **绝对禁止在标题中提及任何物业费、物业管理费、物业成本等物业费用相关内容**
- **绝对禁止在标题中提及任何返税、税收返还、税收优惠、减税、免税等返税相关内容**
- **绝对禁止在标题中提及任何具体金融收益、财务回报、盈利预期等财务收益相关内容**
- 标题必须专注于厂房园区的实用功能、技术规格、配套设施、地理位置等客观属性

**周边配套使用要求：**
- 优先使用交通配套和产业配套作为主要营销卖点
- 交通配套重点体现"交通便利、物流便捷、通达性强"等优势
- 产业配套重点体现"产业集聚、配套完善、协同效应"等价值
- 生活配套、商业配套、休闲配套作为补充，体现园区综合配套完善
- 配套营销点应包含具体设施名称和距离信息，增强可信度

分析问题时，你需要先进行思考，仔细检查知识库中的每一项数据，然后再给出最终答案。
请在<thinking>标签内展示你的思考过程，在</thinking>标签后给出最终答案。"""
        
        # 格式化知识库数据
        formatted_knowledge_base = format_knowledge_base_for_ai(community_data)

        # 根据模板生成提示词
        prompt = prompt_template.format(
            customer_type=customer_type,
            customer_letter=customer_letter,
            community_data=formatted_knowledge_base
        )
        
        # 组合完整的输入内容
        input_content = f"{prompt}\n\n#参考内容：\n以下是目标客户画像和特征信息\n\n{customer_profiles}"
        
        # 调用模型生成营销标题，启用流式输出模式和思维模式
        response = client.chat.completions.create(
            model="deepseek-r1",  # 使用指定的模型
            messages=[
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": input_content
                }
            ],
            temperature=0.85,  # 控制创造性，可以根据需求调整
            max_tokens=4096,   # 设置较大的最大输出token数，确保能获取完整输出
            stream=True,       # 启用流式输出
            extra_body={"enable_thinking": True},  # 启用思考模式
        )
        
        # 处理流式输出，将所有内容合并
        complete_content = ""
        logger.info(f"[任务 {task_id}] 开始接收客户 '{customer_type}' 的流式响应...")
        
        is_thinking = False  # 标记是否处于思考部分
        thinking_content = ""  # 存储思考内容
        response_chunk_count = 0  # 跟踪接收到的响应块数量
        
        for chunk in response:
            response_chunk_count += 1
            
            # 提取每个块中的内容并添加到结果中
            if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content is not None:
                content_piece = chunk.choices[0].delta.content
                complete_content += content_piece
                
                # 输出chunk的原始内容，用于调试
                if response_chunk_count <= 5 or response_chunk_count % 100 == 0:
                    # 将replace操作移出f-string
                    safe_content = content_piece.replace('\n', '\\n')
                    logger.info(f"[任务 {task_id}] 响应块 #{response_chunk_count}: {safe_content}")
                
                # 检测思考模式的开始和结束
                if "<thinking>" in content_piece:
                    is_thinking = True
                    print("\n\033[33m----- 思考过程开始 -----\033[0m", flush=True)  # 黄色标记思考开始
                elif "</thinking>" in content_piece:
                    is_thinking = False
                    print("\033[33m----- 思考过程结束 -----\033[0m\n", flush=True)  # 黄色标记思考结束
                
                # 根据是否是思考内容，使用不同颜色显示
                if is_thinking:
                    thinking_content += content_piece
                    print(f"\033[33m{content_piece}\033[0m", end="", flush=True)  # 黄色显示思考内容
                else:
                    print(content_piece, end="", flush=True)
        
        print(f"\n\n==================== 客户 '{customer_type}' 的营销标题生成完成 (任务 {task_id}) ====================\n", flush=True)
        logger.info(f"[任务 {task_id}] 客户 '{customer_type}' 接收到 {response_chunk_count} 个响应块")
        
        # 处理结果，移除思考部分
        final_content = complete_content
        if "<thinking>" in complete_content and "</thinking>" in complete_content:
            # 找到思考部分的起始和结束位置
            thinking_start = complete_content.find("<thinking>")
            thinking_end = complete_content.find("</thinking>") + len("</thinking>")
            # 移除思考部分
            final_content = complete_content[:thinking_start] + complete_content[thinking_end:]
        
        return {"customer_letter": customer_letter, "content": final_content.strip(), "task_id": task_id}
    
    except Exception as e:
        error_msg = f"[任务 {task_id}] 为客户 '{customer_type}' 生成房源营销标题时发生错误: {e}"
        logger.error(error_msg)
        return {"customer_letter": customer_letter, "content": error_msg, "task_id": task_id}

def generate_marketing_titles(community_name="长春高新项目（金春）"):
    """
    生成所有客户类型的房源营销标题，支持多线程并行处理

    Args:
        community_name (str): 园区名称，默认为"长春高新项目（金春）"
    """
    try:
        logger.info("开始生成客户画像...")
        # 调用客户画像生成模块获取客户画像
        customer_profiles = generate_customer_profiles(community_name)
        
        # 检查客户画像是否生成成功
        if not customer_profiles or "生成客户画像时发生错误" in customer_profiles:
            logger.error("客户画像生成失败，无法继续生成营销标题")
            return "客户画像生成失败，无法继续生成营销标题"
        
        logger.info("客户画像生成成功，开始提取客户类型信息...")
        
        # 提取客户类型信息
        customers = extract_customer_info(customer_profiles)
        
        if not customers:
            logger.error("未能从客户画像中提取到客户类型信息")
            return "未能从客户画像中提取到客户类型信息"
        
        logger.info(f"成功提取到 {len(customers)} 种客户类型")
        
        # 从客户画像结果中提取园区数据
        community_data = extract_community_data(customer_profiles)
        
        # 创建任务列表
        tasks = []
        for i, customer in enumerate(customers):
            tasks.append((customer["letter"], customer["type"], customer_profiles, community_data, i+1))
        
        # 初始化结果字典，按客户字母排序
        results_dict = {}
        start_time = time.time()
        
        # 使用线程池执行并发处理
        max_workers = min(20, len(tasks))  # 最多20个并发线程，或者等于任务数量
        print(f"\n开始并发处理 {len(tasks)} 个客户类型的营销标题生成，并发数: {max_workers}", flush=True)
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务到线程池
            future_to_task = {executor.submit(generate_marketing_titles_for_customer, task): task for task in tasks}
            
            # 处理完成的任务结果
            for future in concurrent.futures.as_completed(future_to_task):
                result = future.result()
                # 按客户字母存储结果
                results_dict[result["customer_letter"]] = result["content"]
        
        # 按客户字母顺序排序结果
        sorted_results = [results_dict[letter] for letter in sorted(results_dict.keys())]
        
        # 合并所有营销标题
        combined_titles = "\n\n".join(sorted_results)
        
        total_elapsed = time.time() - start_time
        
        # 输出总结信息
        summary = f"\n\n==================== 营销标题生成总结 ====================\n"
        summary += f"- 共处理 {len(tasks)} 个客户类型\n"
        summary += f"- 园区名称: {community_name}\n"
        summary += f"- 总耗时: {total_elapsed:.1f} 秒\n"
        summary += f"- 平均处理时间: {total_elapsed/len(tasks):.1f} 秒/个\n"
        summary += f"==================== 生成完成 ====================\n"
        
        logger.info(f"所有客户类型的房源营销标题生成完成，耗时 {total_elapsed:.1f} 秒")
        print(summary, flush=True)
        
        return combined_titles
    
    except Exception as e:
        logger.error(f"生成房源营销标题时发生错误: {e}")
        return f"生成房源营销标题时发生错误: {str(e)}"

if __name__ == "__main__":
    # 获取命令行参数，支持指定园区名称
    import argparse

    parser = argparse.ArgumentParser(description='生成房源营销标题')
    parser.add_argument('--community', type=str, default="长春高新项目（金春）", help='园区名称')

    args = parser.parse_args()

    # 生成房源营销标题
    marketing_titles = generate_marketing_titles(args.community)

    # 保存结果到文件
    output_filename = f"marketing_titles_result_{args.community.replace('（', '_').replace('）', '')}.txt"
    with open(output_filename, "w", encoding="utf-8") as f:
        f.write(marketing_titles)

    print(f"房源营销标题已生成并保存到 {output_filename} 文件")