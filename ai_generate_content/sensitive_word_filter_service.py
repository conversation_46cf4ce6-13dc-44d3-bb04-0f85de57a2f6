#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏感词过滤服务
使用正则表达式进行精确匹配和替换，保持原意不变
"""

import logging
import re
import os
import asyncio
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from openai import AsyncOpenAI

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 阿里云千问模型API配置
DASHSCOPE_API_KEY = "sk-61e66c39babd417da86f9f2e6b580492"
os.environ["DASHSCOPE_API_KEY"] = DASHSCOPE_API_KEY

# 创建异步OpenAI客户端
async_client = AsyncOpenAI(
    api_key=DASHSCOPE_API_KEY,
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

# 导入热搜词重写服务
try:
    from .marketing_titles_hotword_rewrite import HotwordRewriteService
    HOTWORD_SERVICE_AVAILABLE = True
    logger.info("热搜词重写服务导入成功")
except ImportError as e:
    try:
        # 尝试绝对导入
        from ai_generate_content.marketing_titles_hotword_rewrite import HotwordRewriteService
        HOTWORD_SERVICE_AVAILABLE = True
        logger.info("热搜词重写服务导入成功")
    except ImportError as e2:
        HOTWORD_SERVICE_AVAILABLE = False
        logger.warning(f"热搜词重写服务导入失败: {e2}")
        HotwordRewriteService = None

class SensitiveWordFilter:
    """敏感词过滤器"""

    def __init__(self):
        self.sensitive_words = []
        self._load_sensitive_words()

    def _load_sensitive_words(self):
        """加载敏感词列表"""
        sensitive_words_file = Path(__file__).parent / "sensitive_words.txt"
        try:
            with open(sensitive_words_file, 'r', encoding='utf-8') as f:
                raw_content = f.read().strip()
                if raw_content:
                    # 解析敏感词，移除引号和空格
                    potential_words = raw_content.split(',')
                    for item in potential_words:
                        word = item.strip().strip("'\"")
                        if word:
                            self.sensitive_words.append(word)
            logger.info(f"成功加载 {len(self.sensitive_words)} 个敏感词")
        except FileNotFoundError:
            logger.warning(f"敏感词文件未找到: {sensitive_words_file}")
            self.sensitive_words = []

    def detect_sensitive_words(self, text: str) -> List[str]:
        """检测文本中的敏感词"""
        if not text or not self.sensitive_words:
            return []

        found_words = []
        for word in self.sensitive_words:
            # 使用正则表达式进行精确匹配
            if re.search(re.escape(word), text):
                found_words.append(word)

        return found_words

    def _split_into_sentences(self, text: str) -> List[str]:
        """将文本分割为句子，保持原有格式"""
        if not text:
            return []

        # 按换行符分割，保持每行的完整性（包括｜分隔符）
        lines = text.split('\n')

        # 过滤空行并去除首尾空格
        lines = [line.strip() for line in lines if line.strip()]

        return lines

    def detect_sensitive_sentences(self, text: str) -> List[Dict[str, any]]:
        """检测包含敏感词的句子"""
        if not text or not self.sensitive_words:
            return []

        sentences = self._split_into_sentences(text)
        sensitive_sentences = []

        for i, sentence in enumerate(sentences):
            found_words = self.detect_sensitive_words(sentence)
            if found_words:
                sensitive_sentences.append({
                    "sentence_index": i,
                    "original_sentence": sentence,
                    "sensitive_words": found_words
                })

        return sensitive_sentences

    def _mark_sensitive_words_in_sentence(self, sentence: str, sensitive_words: List[str]) -> str:
        """
        在句子中用【】标记敏感词，确保AI能明确识别需要替换的词汇

        Args:
            sentence (str): 原始句子
            sensitive_words (List[str]): 需要标记的敏感词列表

        Returns:
            str: 标记后的句子
        """
        if not sentence or not sensitive_words:
            return sentence

        marked_sentence = sentence

        # 按敏感词长度降序排列，避免短词覆盖长词的问题
        sorted_words = sorted(sensitive_words, key=len, reverse=True)

        for word in sorted_words:
            # 使用正则表达式进行精确匹配和替换，避免重复标记
            pattern = re.escape(word)
            # 检查是否已经被标记过（避免重复标记）
            if f"【{word}】" not in marked_sentence:
                marked_sentence = re.sub(pattern, f"【{word}】", marked_sentence)

        logger.info(f"敏感词标记完成: {sentence[:30]}... -> {marked_sentence[:30]}...")
        return marked_sentence

    async def _async_ai_replace_sentence(self, sentence: str, sensitive_words: List[str]) -> str:
        """使用异步AI模型重构句子以避免敏感词"""
        if not sensitive_words:
            return sentence

        # 构建强化的语句重构提示词
        sensitive_words_str = "、".join(sensitive_words)
        prompt = f"""
你是一个专业的文案重构专家。请严格按照以下要求重新表达句子：

【核心任务】：重新组织和表达以下句子，**绝对禁止**使用任何指定的敏感词，同时保持原意不变，确保语句自然通顺。

【原始句子】：{sentence}

【严禁使用的敏感词】：{sensitive_words_str}

【强制重构规则】：
1. **绝对禁止**：重构后的句子中不得出现列表中的任何敏感词，哪怕一个字都不行
2. **意思保持**：必须保持原句的核心意思和关键信息完全不变
3. **自然表达**：使用自然、通顺、符合中文习惯的表达方式
4. **结构调整**：可以大幅调整句式结构、词序、表达方式，但核心信息不变
5. **同义替换**：对敏感词使用完全不同的同义词或表达方式
6. **语法正确**：确保重构后的句子语法正确、逻辑清晰


【严禁使用的替换词汇】：
在替换敏感词时，绝对不能使用以下词汇，因为它们也是敏感词：
- 时间相关禁用词：'分钟'、'小时'、'秒' - 不能用这些词替换其他敏感词
- 经济相关禁用词：'利率'、'交易'、'增值'、'收益'、'升值'、'回报' - 不能用这些词替换其他敏感词
- 程度相关禁用词：'绝对'、'永不'、'一流'、'首选' - 不能用这些词替换其他敏感词
- 风险相关禁用词：'风险'、'潜力'、'稀缺' - 不能用这些词替换其他敏感词
- 地点相关禁用词：'花园' - 不能用这个词替换其他敏感词
- 机构相关禁用词：'政府'、'企业认证' - 不能用这些词替换其他敏感词
- 完整敏感词列表：{sensitive_words_str} - 这些词都不能作为替换词使用

【质量检查要求】：
- 重构前请在心中默念一遍敏感词列表：{sensitive_words_str}
- 重构中确保不使用任何敏感词作为替换词
- 重构后请逐字检查，确保没有任何敏感词残留
- 确保句子读起来自然流畅，不生硬

【输出要求】：请直接返回重构后的完整句子，必须满足：
- **零敏感词**：绝对不包含任何指定的敏感词
- **意思不变**：保持原句的核心意思和信息
- **表达自然**：语句通顺，符合中文表达习惯
"""

        try:
            response = await async_client.chat.completions.create(
                model="qwen-plus",
                messages=[
                    {"role": "system", "content": "你是一个专业的文案重构专家，擅长在避免敏感词的同时保持语句自然通顺。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.8,  # 适当提高温度以增加表达的多样性
                max_tokens=500
            )

            ai_reconstructed_sentence = response.choices[0].message.content.strip()

            # 验证重构后的句子是否还包含敏感词
            remaining_words = self.detect_sensitive_words(ai_reconstructed_sentence)
            if remaining_words:
                logger.warning(f"AI重构后仍包含敏感词: {remaining_words}，尝试更严格的重构")

                # 使用超级严格的重构提示词
                strict_prompt = f"""
【🚨紧急重构任务🚨】：第一轮重构失败！句子仍包含敏感词，必须立即彻底重新表达！

【失败的句子】：{ai_reconstructed_sentence}
【顽固的敏感词】：{remaining_words}
【原始参考句子】：{sentence}

【🔥超级严格要求🔥】：
1. **零容忍政策**：绝对不能包含任何敏感词：{remaining_words}，一个字都不行！
2. **彻底重写**：必须使用完全不同的词汇、句式、表达方式
3. **意思锁定**：核心意思和关键信息必须保持100%不变
4. **自然流畅**：重构后必须读起来自然，不能有AI痕迹

【🎯高级重构技巧🎯】：
- **词汇替换法**：用完全不同的同义词或近义词组替换敏感词
- **句式重组法**：改变主谓宾结构，使用被动语态、倒装句等
- **概念转换法**：将具体概念转为抽象概念，将直接表达转为间接表达
- **语境重建法**：在不同的语境下表达相同的意思

【🔧具体操作指南🔧】：
- 时间敏感词处理：用"很快/不久/片刻/瞬间/短时间内"等模糊表达
- 经济敏感词处理：用"费用/成本/价格/回馈/效果"等中性词汇
- 程度敏感词处理：用"非常/确实/相当/十分"等替代绝对化表达
- 机构敏感词处理：用"相关部门/管理机构/主管单位"等官方表达

【🚫严禁使用的替换词汇🚫】：
⚠️警告：以下词汇都是敏感词，绝对不能用来替换其他敏感词：
- 时间类：'分钟'、'小时'、'秒'
- 经济类：'利率'、'交易'、'增值'、'收益'、'升值'、'回报'
- 程度类：'绝对'、'永不'、'一流'、'首选'
- 风险类：'风险'、'潜力'、'稀缺'
- 地点类：'花园'
- 机构类：'政府'、'企业认证'
- 完整敏感词列表：{remaining_words} - 这些都不能作为替换词！

【⚡质量保证流程⚡】：
1. 重构前：逐个检查敏感词：{remaining_words}
2. 重构中：每写一个词都要确认不是敏感词
3. 重构后：逐字逐句检查，确保零敏感词残留

【❌绝对禁止❌】：
- 包含任何敏感词：{remaining_words}
- 改变原句核心意思
- 使用生硬不自然的表达

🔥请立即返回彻底重构后的句子，必须通过所有检查！🔥
"""
                retry_response = await async_client.chat.completions.create(
                    model="qwen-plus",
                    messages=[
                        {"role": "system", "content": "你是一个专业的文案重构专家，必须彻底避免敏感词，同时保持语句自然通顺。"},
                        {"role": "user", "content": strict_prompt}
                    ],
                    temperature=0.7,  # 进一步提高创造性
                    max_tokens=500
                )
                ai_reconstructed_sentence = retry_response.choices[0].message.content.strip()

                # 最终验证
                final_remaining_words = self.detect_sensitive_words(ai_reconstructed_sentence)
                if final_remaining_words:
                    logger.error(f"重试后仍包含敏感词: {final_remaining_words}，重构失败")
                else:
                    logger.info(f"重新重构成功: {ai_reconstructed_sentence[:30]}...")

            logger.info(f"异步AI重构完成: {sentence[:30]}... -> {ai_reconstructed_sentence[:30]}...")
            return ai_reconstructed_sentence

        except Exception as e:
            logger.error(f"异步AI重构失败: {e}")
            # 如果AI重构失败，返回原句子
            logger.warning(f"异步AI重构失败，保持原句: {sentence}")
            return sentence

    async def async_filter_content(self, title: str, description: str) -> Dict[str, str]:
        """
        异步过滤标题和描述中的敏感词，使用AI进行智能重构

        Args:
            title (str): 标题文本
            description (str): 描述文本

        Returns:
            Dict[str, str]: 包含重构后的标题和描述
        """
        result = {
            "original_title": title,
            "original_description": description,
            "filtered_title": title,
            "filtered_description": description,
            "title_sensitive_words": [],
            "description_sensitive_words": [],
            "title_sensitive_sentences": [],
            "description_sensitive_sentences": [],
            "has_sensitive_words": False
        }

        # 检测并处理标题中的敏感词
        title_sensitive_words = self.detect_sensitive_words(title)
        title_sensitive_sentences = self.detect_sensitive_sentences(title)

        if title_sensitive_words:
            result["title_sensitive_words"] = title_sensitive_words
            result["title_sensitive_sentences"] = title_sensitive_sentences
            result["filtered_title"] = await self._async_ai_replace_sentence(title, title_sensitive_words)
            result["has_sensitive_words"] = True

        # 检测并处理描述中的敏感词
        description_sensitive_words = self.detect_sensitive_words(description)
        description_sensitive_sentences = self.detect_sensitive_sentences(description)

        if description_sensitive_words:
            result["description_sensitive_words"] = description_sensitive_words
            result["description_sensitive_sentences"] = description_sensitive_sentences
            result["has_sensitive_words"] = True

        # 对描述进行逐行AI重构（保持原有格式）
        lines = self._split_into_sentences(description)
        filtered_lines = []

        for i, line in enumerate(lines):
            line_words = self.detect_sensitive_words(line)
            if line_words:
                # 对包含敏感词的行进行异步AI重构
                filtered_line = await self._async_ai_replace_sentence(line, line_words)
                filtered_lines.append(filtered_line)
                logger.info(f"异步AI重构第{i+1}行: {line[:50]}... -> {filtered_line[:50]}...")
            else:
                # 不包含敏感词的行保持原样
                filtered_lines.append(line)

        # 重新组装完整的描述，每行之间添加空行间距确保格式美观
        result["filtered_description"] = "\n\n".join(filtered_lines)

        logger.info(f"异步敏感词过滤完成 - 标题敏感词: {len(title_sensitive_words)}, 描述敏感词: {len(description_sensitive_words)}")
        logger.info(f"异步敏感句子检测 - 标题: {len(title_sensitive_sentences)}, 描述: {len(description_sensitive_sentences)}")

        return result

# 创建全局过滤器实例
sensitive_word_filter = SensitiveWordFilter()

async def async_filter_sensitive_words(title: str, description: str) -> Dict[str, str]:
    """
    异步过滤敏感词的便捷函数

    Args:
        title (str): 标题
        description (str): 描述内容

    Returns:
        Dict[str, str]: 过滤结果
    """
    return await sensitive_word_filter.async_filter_content(title, description)


async def async_filter_and_rewrite_content(title: str, description: str, park_name: Optional[str] = None) -> Dict[str, str]:
    """
    集成敏感词过滤和热搜词重写的完整流程
    注意：只对标题进行热搜词重写，描述只进行敏感词过滤

    Args:
        title (str): 原始标题
        description (str): 原始描述
        park_name (Optional[str]): 园区名称，用于查找热搜词

    Returns:
        Dict[str, str]: 兼容推送系统的标准JSON格式
        {
            "success": True/False,
            "message": "处理结果说明",
            "data": {
                "topic": "最终标题（兼容推送系统的topic字段）",
                "content": "过滤后的描述（兼容推送系统的content字段）",
                "original_title": "原始标题",
                "original_description": "原始描述",
                "filtered_title": "过滤敏感词后的标题",
                "filtered_description": "过滤敏感词后的描述",
                "final_title": "插入热搜词后的最终标题",
                "used_hotword": "使用的热搜词",
                "hotword_rewrite_success": True/False,
                "has_sensitive_words": True/False,
                "processing_summary": "处理过程摘要"
            }
        }
    """
    logger.info(f"开始集成处理 - 标题: {title[:50]}..., 园区: {park_name}")

    # 步骤1：敏感词过滤（标题和描述都过滤）
    filtered_result = await async_filter_sensitive_words(title, description)

    # 初始化处理结果
    final_title = filtered_result["filtered_title"]
    used_hotword = ""
    hotword_rewrite_success = False
    processing_steps = []

    # 记录敏感词过滤结果
    if filtered_result["has_sensitive_words"]:
        processing_steps.append(f"敏感词过滤：检测到{len(filtered_result.get('title_sensitive_words', []))}个标题敏感词，{len(filtered_result.get('description_sensitive_words', []))}个描述敏感词")
    else:
        processing_steps.append("敏感词过滤：未检测到敏感词")

    # 步骤2：热搜词重写（仅对标题进行）
    if park_name and HOTWORD_SERVICE_AVAILABLE:
        try:
            logger.info(f"开始热搜词重写 - 园区: {park_name}")
            hotword_service = HotwordRewriteService(park_name)

            # 对过滤后的标题进行异步热搜词重写
            rewritten_title = await hotword_service.async_rewrite_title(filtered_result["filtered_title"])

            if rewritten_title and rewritten_title != filtered_result["filtered_title"]:
                final_title = rewritten_title
                hotword_rewrite_success = True

                # 尝试提取使用的热搜词（简单的差异检测）
                original_words = set(filtered_result["filtered_title"].split())
                rewritten_words = set(rewritten_title.split())
                new_words = rewritten_words - original_words
                if new_words:
                    used_hotword = " ".join(new_words)

                processing_steps.append(f"热搜词重写：成功插入热搜词 '{used_hotword}'")
                logger.info(f"热搜词重写成功: {filtered_result['filtered_title'][:30]}... -> {rewritten_title[:30]}...")
            else:
                processing_steps.append("热搜词重写：未找到合适的热搜词或未产生变化")
                logger.warning(f"热搜词重写未产生变化 - 园区: {park_name}")

        except Exception as e:
            processing_steps.append(f"热搜词重写：失败 - {str(e)}")
            logger.error(f"热搜词重写失败 - 园区: {park_name}, 错误: {str(e)}")

    elif park_name and not HOTWORD_SERVICE_AVAILABLE:
        processing_steps.append("热搜词重写：服务不可用")
        logger.warning("热搜词重写服务不可用")

    elif not park_name:
        processing_steps.append("热搜词重写：未提供园区名称，跳过")
        logger.info("未提供园区名称，跳过热搜词重写")

    # 构建兼容推送系统的标准响应格式
    result = {
        "success": True,
        "message": "内容处理完成",
        "data": {
            # 推送系统兼容字段
            "topic": final_title,  # 推送系统使用的标题字段
            "content": filtered_result["filtered_description"],  # 推送系统使用的描述字段

            # 详细处理信息
            "original_title": title,
            "original_description": description,
            "filtered_title": filtered_result["filtered_title"],
            "filtered_description": filtered_result["filtered_description"],
            "final_title": final_title,
            "used_hotword": used_hotword,
            "hotword_rewrite_success": hotword_rewrite_success,
            "has_sensitive_words": filtered_result["has_sensitive_words"],
            "title_sensitive_words": filtered_result.get("title_sensitive_words", []),
            "description_sensitive_words": filtered_result.get("description_sensitive_words", []),
            "processing_summary": " | ".join(processing_steps)
        }
    }

    logger.info(f"集成处理完成 - 最终标题: {final_title[:50]}...")
    return result

async def main():
    """异步测试主函数"""
    # 测试代码 - 使用完整的工业厂房描述，测试新的语句重构策略
    test_title = "地铁口步行15分钟可到达 核心地段增值空间广阔 投资风险极低"
    test_description = """智慧中枢系统｜千兆光纤专线直连国家超算中心，AIoT平台实时监控温湿度/洁净度，让基因测序仪与智能显微镜无忧运转

产权魔方组合｜整栋2000㎡可拆分为300-500㎡独立单元，每个实验室享有独立产权证，融资抵押估值提升30%

政策直通车｜企业所得税“三免三减半”+研发费用175%加计扣除，另有最高500万科技专项支持补贴

70kW工业级供电搭配双回路保障，预留150%的扩容空间，可支持半导体光刻机、冷冻电镜等精密设备持续稳定运行，确保设备稳定运行

地标展示面｜12米通高玻璃幕墙+企业冠名权，LOGO与园区主标识并列呈现，客户出地铁口即可看到科技蓝光立体字

智慧实验室｜EPC级无尘车间+防静电环氧地坪，配备万向排风管井和独立废水处理系统，生物医药企业拎设备入驻

生态能量圈｜园区自建院士工作站+技术转化中心，隔壁即是国家级权威检测认证平台，研发到量产缩短数月周期

地铁4号线研发园站D口直达大堂，周边配套完善，员工公寓、科学家餐厅与全天候便利店近在咫尺，步行即可抵达，轻松享受便捷生活

资产安全有保障｜50年国有产权红本，相关方承诺回购，园区设立2亿元产业基金优先支持入驻单位

无忧服务包｜从环评备案到高企申报全程代办，专业团队提供ISO体系认证辅导+专利补贴申领

扩展缓冲区｜每层预留200㎡设备扩展区，可随时改造为P2实验室或百级洁净车间，满足爆发式增长需求

景观会客厅｜2000㎡生态中庭+星空会议室，水幕投影与垂直绿化墙营造高端商务洽谈场景"""

    # 测试园区名称（用于热搜词重写）- 使用真实存在的园区
    test_park_name = "上海嘉定外冈莱尼项目（金绥）"  # 真实园区名称

    print("=" * 80)
    print("🔥 集成敏感词过滤 + 热搜词重写服务测试")
    print("=" * 80)

    # 测试集成功能
    integrated_result = await async_filter_and_rewrite_content(test_title, test_description, test_park_name)

    print("\n📋 原始内容")
    print("-" * 40)
    print(f"标题: {integrated_result['data']['original_title']}")
    print(f"园区: {test_park_name}")

    print("\n🔍 处理结果概览")
    print("-" * 40)
    print(f"处理状态: {'✅ 成功' if integrated_result['success'] else '❌ 失败'}")
    print(f"处理信息: {integrated_result['message']}")
    print(f"处理摘要: {integrated_result['data']['processing_summary']}")

    print("\n🎯 推送系统兼容字段")
    print("-" * 40)
    print(f"topic (推送标题): {integrated_result['data']['topic']}")
    print(f"content (推送描述): {integrated_result['data']['content'][:100]}...")

    print("\n📊 详细处理信息")
    print("-" * 40)
    data = integrated_result['data']
    print(f"敏感词检测: {'是' if data['has_sensitive_words'] else '否'}")
    if data['title_sensitive_words']:
        print(f"标题敏感词: {data['title_sensitive_words']}")
    if data['description_sensitive_words']:
        print(f"描述敏感词: {data['description_sensitive_words']}")

    print(f"热搜词重写: {'成功' if data['hotword_rewrite_success'] else '未执行或失败'}")
    if data['used_hotword']:
        print(f"使用的热搜词: {data['used_hotword']}")

    print("\n📄 标题处理流程")
    print("-" * 40)
    print(f"原始标题: {data['original_title']}")
    print(f"过滤后标题: {data['filtered_title']}")
    print(f"最终标题: {data['final_title']}")

    print("\n📄 JSON输出格式示例")
    print("-" * 40)
    import json
    print(json.dumps(integrated_result, ensure_ascii=False, indent=2))

    print("\n" + "=" * 80)
    print("✨ 集成测试完成！")
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
