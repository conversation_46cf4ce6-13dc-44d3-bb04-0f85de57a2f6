#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import logging
import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Tuple
from openai import AsyncOpenAI

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ai_generate_content.enhanced_park_data_service import get_ai_optimized_data
from database.db_connection import get_db_connection

DASHSCOPE_API_KEY = "sk-61e66c39babd417da86f9f2e6b580492"
os.environ["DASHSCOPE_API_KEY"] = DASHSCOPE_API_KEY

async_client = AsyncOpenAI(
    api_key=DASHSCOPE_API_KEY,
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

def setup_hotword_logger():
    """设置热搜词重写专用的日志记录器"""
    hotword_logger = logging.getLogger('hotword_rewrite')
    hotword_logger.setLevel(logging.INFO)

    if not hotword_logger.handlers:
        log_file = os.path.join(os.path.dirname(__file__), 'logs', 'hotword_rewrite.log')
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)

        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        hotword_logger.addHandler(file_handler)
        hotword_logger.propagate = False

    return hotword_logger

logger = setup_hotword_logger()


class HotwordRewriteService:
    def __init__(self, park_name: str):
        self.park_name = park_name
        self.hot_search_keywords = []
        self.park_knowledge_base = None
        self.logger = logger

    def get_park_city_and_type(self, park_name: str) -> Tuple[Optional[str], Optional[str]]:
        try:
            connection = get_db_connection()
            with connection.cursor() as cursor:
                sql = """
                SELECT DISTINCT city, type
                FROM parsed_factory_data
                WHERE community = %s AND is_deleted = 0
                LIMIT 1
                """
                cursor.execute(sql, (park_name,))
                result = cursor.fetchone()

                if not result:
                    sql_fuzzy = """
                    SELECT DISTINCT city, type
                    FROM parsed_factory_data
                    WHERE community LIKE %s AND is_deleted = 0
                    LIMIT 1
                    """
                    cursor.execute(sql_fuzzy, (f"%{park_name}%",))
                    result = cursor.fetchone()

                if result:
                    return result.get('city'), result.get('type')
                return None, None
        except:
            return None, None
        finally:
            if connection:
                connection.close()

    def load_hot_search_keywords_from_db(self, city_code: str, zs_type: str, target_date: str) -> List[str]:
        try:
            connection = get_db_connection()
            with connection.cursor() as cursor:
                sql = """
                SELECT keyword FROM hot_search_keywords
                WHERE city_code = %s AND zs_type = %s AND DATE(create_time) = %s
                ORDER BY create_time DESC
                """
                cursor.execute(sql, (city_code, zs_type, target_date))
                results = cursor.fetchall()

                keywords = []
                seen = set()
                for row in results:
                    keyword = row['keyword']
                    if keyword and keyword not in seen:
                        keywords.append(keyword)
                        seen.add(keyword)
                return keywords
        except:
            return []
        finally:
            if connection:
                connection.close()

    def get_hotwords_with_date_fallback(self, city_code: str, zs_type: str) -> List[str]:
        today = datetime.now().strftime('%Y-%m-%d')
        keywords = self.load_hot_search_keywords_from_db(city_code, zs_type, today)
        if keywords:
            return keywords

        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        keywords = self.load_hot_search_keywords_from_db(city_code, zs_type, yesterday)
        if keywords:
            return keywords

        return self.get_latest_available_hotwords(city_code, zs_type)

    def get_latest_available_hotwords(self, city_code: str, zs_type: str) -> List[str]:
        """
        获取最近可用的热搜词数据（所有热搜词，不限制数量）

        Args:
            city_code (str): 城市代码
            zs_type (str): 租售类型（出租/出售）

        Returns:
            List[str]: 热搜词列表
        """
        try:
            connection = get_db_connection()

            with connection.cursor() as cursor:
                date_sql = """
                SELECT DATE(create_time) as date
                FROM hot_search_keywords
                WHERE city_code = %s
                AND zs_type = %s
                ORDER BY create_time DESC
                LIMIT 1
                """
                cursor.execute(date_sql, (city_code, zs_type))
                date_result = cursor.fetchone()

                if not date_result:
                    return []

                latest_date = date_result['date']

                keywords_sql = """
                SELECT keyword
                FROM hot_search_keywords
                WHERE city_code = %s
                AND zs_type = %s
                AND DATE(create_time) = %s
                ORDER BY create_time DESC
                """
                cursor.execute(keywords_sql, (city_code, zs_type, latest_date))
                results = cursor.fetchall()
                keywords = []
                seen = set()
                for row in results:
                    keyword = row['keyword']
                    if keyword and keyword not in seen:
                        keywords.append(keyword)
                        seen.add(keyword)

                self.logger.info(f"使用最近可用的热搜词数据({latest_date})，共 {len(keywords)} 个")
                return keywords

        except Exception as e:
            self.logger.error(f"获取最近可用热搜词失败: {str(e)}")
            return []
        finally:
            if connection:
                connection.close()

    def load_hot_search_keywords(self) -> List[str]:
        city_code, zs_type = self.get_park_city_and_type(self.park_name)
        logger.info(f"园区 '{self.park_name}' 查询结果 - 城市代码: {city_code}, 租售类型: {zs_type}")

        if city_code and zs_type:
            keywords = self.get_hotwords_with_date_fallback(city_code, zs_type)
            self.hot_search_keywords = keywords
            logger.info(f"从数据库获取到 {len(keywords)} 个热搜词: {keywords}")
            return keywords
        else:
            logger.warning(f"无法获取园区 '{self.park_name}' 的城市代码或租售类型")
        return []
    
    def get_park_knowledge_base(self) -> Optional[str]:
        try:
            ai_data = get_ai_optimized_data(self.park_name)
            if not ai_data:
                return None

            knowledge_base = f"园区：{self.park_name}\n"

            if ai_data.technical_specs:
                for key, value in ai_data.technical_specs.items():
                    knowledge_base += f"{key}: {value}\n"

            if ai_data.selling_points:
                for key, value in ai_data.selling_points.items():
                    knowledge_base += f"{key}: {value}\n"

            self.park_knowledge_base = knowledge_base
            return knowledge_base
        except:
            return None


    async def async_rewrite_title(self, title: str) -> str:
        """
        异步版本的标题重写方法，避免阻塞主线程

        Args:
            title (str): 原始标题

        Returns:
            str: 重写后的标题
        """
        try:
            keywords = await asyncio.to_thread(self.load_hot_search_keywords)
            if not keywords:
                self.logger.info(f"未找到热搜词，返回原标题: {title}")
                return title

            knowledge_base = await asyncio.to_thread(self.get_park_knowledge_base)
            if not knowledge_base:
                self.logger.info(f"未找到园区知识库，返回原标题: {title}")
                return title

            rewritten_title = await self.async_select_and_rewrite_title(title, keywords)
            self.logger.info(f"异步标题重写完成: {title[:30]}... -> {rewritten_title[:30]}...")
            return rewritten_title

        except Exception as e:
            self.logger.error(f"异步标题重写失败: {str(e)}")
            return title



    async def async_select_and_rewrite_title(self, title: str, all_keywords: List[str]) -> str:
        """
        异步版本的热搜词选择和标题重写合并方法

        Args:
            title (str): 原始标题
            all_keywords (List[str]): 所有可用的热搜词

        Returns:
            str: 重写后的标题
        """
        try:
            if not all_keywords:
                self.logger.warning("没有可用的热搜词进行选择和重写")
                return title

            self.logger.info(f"开始异步选择热搜词并重写标题 - 标题: '{title}', 可用热搜词: {all_keywords}")

            prompt = f"""请从热搜词列表中选择最适合的热搜词，并将其自然融入到标题中。

园区信息：{self.park_knowledge_base}
原始标题：{title}
可用热搜词：{', '.join(all_keywords)}

要求：
1. **【语句通顺性】最高优先级**：改写后的标题必须语句自然流畅，绝对避免生硬拼接
2. 从热搜词列表中选择最适合标题内容的热搜词
3. 将选中的热搜词自然融入到标题中，确保整体语句通顺连贯
4. **【强制字数限制】改写后的标题字数（包括所有符号和标点）严格不得超过30个字**
5. 如果融入热搜词后字数超过30字，必须重新组织语言结构，保持语句通顺的同时控制字数
6. 确保标题在字数限制内仍保持吸引力、营销效果和可读性
7. 绝对避免词汇生硬拼接，如"产业协同绿意税收优"这种不自然的表述
8. 直接返回改写后的完整标题，不要其他解释

请直接返回改写后的标题："""

            response = await async_client.chat.completions.create(
                model="qwen-plus",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.4,
                max_tokens=200
            )

            rewritten_title = response.choices[0].message.content.strip()
            self.logger.info(f"AI异步选择并重写标题完成 - 新标题: '{rewritten_title}'")

            # 检查字数是否超过30个字
            title_length = len(rewritten_title)
            if title_length > 30:
                self.logger.warning(f"重写后标题字数超限: {title_length}字 > 30字，开始缩减")
                reduced_title = await self.async_reduce_title_length(rewritten_title)
                self.logger.info(f"字数缩减完成: {rewritten_title[:30]}... -> {reduced_title}")
                return reduced_title
            else:
                self.logger.info(f"重写后标题字数符合要求: {title_length}字 ≤ 30字")
                return rewritten_title
        except Exception as e:
            self.logger.error(f"异步选择热搜词并重写标题时发生错误: {str(e)}")
            # 如果重写失败，返回原标题
            self.logger.info(f"重写失败，返回原标题: '{title}'")
            return title

    async def async_reduce_title_length(self, title: str) -> str:
        """
        异步缩减标题字数到30字以内，保持原意和吸引力

        Args:
            title (str): 需要缩减的标题

        Returns:
            str: 缩减后的标题
        """
        try:
            self.logger.info(f"开始缩减标题字数: '{title}' (当前{len(title)}字)")

            prompt = f"""请将以下标题缩减到30个字以内（包括所有符号和标点），要求：

原标题：{title}
当前字数：{len(title)}字

【核心要求 - 按重要性排序】：
1. **【语句通顺性】最高优先级**：缩减后的标题必须读起来自然流畅，绝对不能出现生硬拼接的词汇组合
2. **【强制字数限制】缩减后的标题字数（包括所有符号和标点）必须严格不超过30个字**
3. **【保持吸引力】确保标题仍然具有营销吸引力和可读性，不能变得平淡无奇**
4. **【保持原意】核心营销信息和卖点必须保留**

【语句通顺性具体要求】：
- 绝对避免词汇生硬拼接，如"产业协同绿意税收优"这种不自然的表述
- 确保每个词汇之间有合理的语法连接
- 使用自然的中文表达习惯
- 保持语言的流畅性和可读性

【优化策略】：
1. **重新组织语言结构**：不是简单删减，而是重新组织表达方式
2. **使用连接词**：适当使用"的"、"与"、"及"等连接词保持语句通顺
3. **合理省略**：删除不影响核心意思的修饰词和重复表达
4. **保持营销节奏**：确保标题有良好的阅读节奏感

【示例对比】：
错误示例："产业协同绿意税收优" (生硬拼接，不通顺)
正确示例："产业协同发展税收优惠" (自然流畅，有连接性)

请直接返回缩减后的标题，确保语句自然通顺："""

            response = await async_client.chat.completions.create(
                model="qwen-plus",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,  # 降低温度，确保更精确的缩减
                max_tokens=100
            )

            reduced_title = response.choices[0].message.content.strip()
            reduced_length = len(reduced_title)

            # 验证缩减结果
            if reduced_length <= 30:
                self.logger.info(f"标题缩减成功: {len(title)}字 -> {reduced_length}字")
                return reduced_title
            else:
                # 如果AI缩减后仍然超过30字，记录错误并返回原标题
                self.logger.error(f"AI缩减后仍超限({reduced_length}字)，缩减失败")
                self.logger.info(f"缩减失败，返回原标题: '{title}'")
                return title

        except Exception as e:
            self.logger.error(f"标题缩减失败: {str(e)}")
            # 如果缩减失败，返回原标题
            self.logger.info(f"缩减失败，返回原标题: '{title}'")
            return title
