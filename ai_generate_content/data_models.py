#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
园区数据模型定义
为AI内容生成提供结构化的数据模型
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any, List
from datetime import datetime


@dataclass
class BasicParkData:
    """基础园区数据模型（来自parsed_factory_data表）"""
    community: Optional[str] = None
    floortype: Optional[str] = None  # 楼层类型
    storey: Optional[str] = None  # 首层层高
    slease: Optional[str] = None  # 起租期
    planteia: Optional[str] = None  # 可办环评
    plantstructure: Optional[str] = None  # 厂房结构
    typewarehouse: Optional[str] = None  # 厂房类型
    plantnew: Optional[str] = None  # 厂房新旧
    is_new_house: Optional[str] = None  # 性质
    agent_free: Optional[str] = None  # 中介费
    years: Optional[str] = None  # 建筑年代
    type4years: Optional[str] = None  # 产权年限
    landnature: Optional[str] = None  # 土地性质
    powersupply: Optional[str] = None  # 供电容量
    loadbearing: Optional[str] = None  # 楼板承重
    type: Optional[str] = None  # 租售类型


@dataclass
class DetailedParkInfo:
    """详细园区信息模型（来自park_info表）"""
    park_name: Optional[str] = None
    park_stage: Optional[str] = None  # 园区阶段
    park_director: Optional[str] = None  # 园区负责人
    park_manager: Optional[str] = None  # 园区管理者
    park_province: Optional[str] = None  # 省份
    park_city: Optional[str] = None  # 城市
    park_country: Optional[str] = None  # 区县
    park_area: Optional[str] = None  # 园区面积
    park_type: Optional[str] = None  # 园区类型
    park_structure: Optional[str] = None  # 园区结构描述
    park_busi_type: Optional[str] = None  # 业务类型
    park_property: Optional[str] = None  # 园区性质
    park_support: Optional[str] = None  # 配套设施
    park_depot: Optional[str] = None  # 仓储设施
    park_elevator: Optional[str] = None  # 电梯配置
    park_consultid: Optional[str] = None  # 咨询ID
    park_policy: Optional[str] = None  # 政策支持
    park_industry: Optional[str] = None  # 招商产业
    park_plot: Optional[str] = None  # 地块信息
    park_fpl: Optional[str] = None  # 容积率
    park_ffa: Optional[str] = None  # 建筑面积
    park_stt: Optional[str] = None  # 结构类型
    park_dsd: Optional[str] = None  # 设计标准
    park_we: Optional[str] = None  # 水电配置
    park_payment: Optional[str] = None  # 付款方式
    park_deli_time: Optional[str] = None  # 交付时间
    park_entex: Optional[str] = None  # 入驻要求
    park_enreq: Optional[str] = None  # 环保要求
    park_disbet: Optional[str] = None  # 距离优势
    park_rategreen: Optional[str] = None  # 绿化率
    park_taxreq: Optional[str] = None  # 税收要求
    park_plotrate: Optional[str] = None  # 地块容积率
    park_seciss: Optional[str] = None  # 安全问题
    park_delista: Optional[str] = None  # 交付清单
    park_propercert: Optional[str] = None  # 产权证书
    park_investtotal: Optional[str] = None  # 总投资
    park_advantageanddis: Optional[str] = None  # 优势和劣势
    park_style: Optional[str] = None  # 园区风格
    park_salecert: Optional[str] = None  # 销售证书
    park_heiandload: Optional[str] = None  # 高度和荷载
    park_materials: Optional[str] = None  # 建筑材料
    park_weconf: Optional[str] = None  # 水电配置
    park_powersupport: Optional[str] = None  # 电力支持
    park_ecapacity: Optional[str] = None  # 电力容量
    park_aircconf: Optional[str] = None  # 空调配置
    park_gas: Optional[str] = None  # 燃气配置
    park_flueconf: Optional[str] = None  # 烟道配置
    park_roadconf: Optional[str] = None  # 道路配置
    park_insulation: Optional[str] = None  # 保温配置
    park_netconf: Optional[str] = None  # 网络配置
    park_seismic: Optional[str] = None  # 抗震等级
    park_othexp: Optional[str] = None  # 其他说明
    park_invoce: Optional[str] = None  # 发票信息
    park_propertyinfo: Optional[str] = None  # 物业信息
    park_decoration: Optional[str] = None  # 装修情况
    park_financialsupport: Optional[str] = None  # 金融支持
    park_streetaddress: Optional[str] = None  # 详细地址
    park_lat_lng: Optional[str] = None  # 经纬度
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class EnhancedParkData:
    """综合园区数据模型"""
    park_name: str
    basic_data: Optional[BasicParkData] = None
    detailed_info: Optional[DetailedParkInfo] = None
    data_completeness: Dict[str, bool] = None  # 数据完整性标记
    last_updated: Optional[datetime] = None
    
    def __post_init__(self):
        if self.data_completeness is None:
            self.data_completeness = {
                'has_basic_data': self.basic_data is not None,
                'has_detailed_info': self.detailed_info is not None
            }
        if self.last_updated is None:
            self.last_updated = datetime.now()


@dataclass
class AIOptimizedParkData:
    """AI内容生成优化的园区数据模型"""
    park_name: str

    # 基础技术信息
    technical_specs: Dict[str, Any] = None

    # 营销卖点信息
    selling_points: Dict[str, Any] = None

    # 配套设施信息
    facilities: Dict[str, Any] = None

    # 政策和产业信息
    policies_and_industries: Dict[str, Any] = None

    # 地理位置信息
    location_info: Dict[str, Any] = None

    # 周边配套设施信息
    nearby_facilities: Optional['NearbyFacilities'] = None

    # 数据质量评分
    data_quality_score: float = 0.0

    # 可用于生成的字段列表
    available_fields: List[str] = None
    
    def __post_init__(self):
        if self.available_fields is None:
            self.available_fields = []

        # 初始化各个分类字典
        if self.technical_specs is None:
            self.technical_specs = {}
        if self.selling_points is None:
            self.selling_points = {}
        if self.facilities is None:
            self.facilities = {}
        if self.policies_and_industries is None:
            self.policies_and_industries = {}
        if self.location_info is None:
            self.location_info = {}
        if self.nearby_facilities is None:
            self.nearby_facilities = NearbyFacilities()


@dataclass
class NearbyFacility:
    """周边配套设施数据模型"""
    poi_id: str
    title: str
    address: str
    category: str
    distance: int  # 距离（米）
    tel: Optional[str] = None

    def __post_init__(self):
        # 格式化距离显示
        if self.distance < 1000:
            self.distance_text = f"{self.distance}米"
        else:
            self.distance_text = f"{self.distance/1000:.1f}公里"


@dataclass
class NearbyFacilities:
    """周边配套设施分类数据模型"""
    transportation: List[NearbyFacility] = None  # 交通配套
    living_services: List[NearbyFacility] = None  # 生活配套
    commercial: List[NearbyFacility] = None  # 商业配套
    leisure: List[NearbyFacility] = None  # 休闲配套
    industrial: List[NearbyFacility] = None  # 产业配套

    def __post_init__(self):
        # 初始化各个分类列表
        if self.transportation is None:
            self.transportation = []
        if self.living_services is None:
            self.living_services = []
        if self.commercial is None:
            self.commercial = []
        if self.leisure is None:
            self.leisure = []
        if self.industrial is None:
            self.industrial = []

    def get_total_count(self) -> int:
        """获取配套设施总数"""
        return (len(self.transportation) + len(self.living_services) +
                len(self.commercial) + len(self.leisure) + len(self.industrial))

    def has_facilities(self) -> bool:
        """检查是否有配套设施"""
        return self.get_total_count() > 0


@dataclass
class DataRetrievalOptions:
    """数据获取选项配置"""
    include_basic_data: bool = True
    include_detailed_info: bool = True
    include_nearby_facilities: bool = True
    include_empty_fields: bool = False
    clean_text_data: bool = True
    optimize_for_ai: bool = False
    max_text_length: Optional[int] = None
    preferred_fields: Optional[List[str]] = None
    # POI相关配置
    poi_max_distance: int = 5000  # 最大距离（米）
    poi_max_per_category: int = 2  # 每个分类最多返回数量
    poi_categories_filter: Optional[List[str]] = None  # 指定要包含的分类
