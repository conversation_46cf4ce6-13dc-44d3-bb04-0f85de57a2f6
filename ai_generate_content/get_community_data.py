#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
园区数据获取模块
从数据库中获取园区的基本信息
"""

import logging
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database.db_connection import get_db_connection

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据映射字典
PLANTEIA_MAPPING = {
    '0': '否',
    '1': '是'
}

TYPE4YEARS_MAPPING = {
    '1': '70年',
    '2': '40年',
    '3': '50年',
    '4': '永久'
}

PLANTNEW_MAPPING = {
    '1': '全新',
    '2': '九成新',
    '3': '八成新',
    '4': '旧厂'
}

FLOORTYPE_MAPPING = {
    '1': '单层',
    '3': '多层'
}

AGENTFREE_MAPPING = {
    '0': '有',
    '1': '无'
}

def get_community_data(community_name):
    """
    从数据库获取指定园区的基础数据并进行映射转换
    
    Args:
        community_name (str): 园区名称
        
    Returns:
        dict: 包含园区数据的字典，未找到返回None
    """
    try:
        # 获取数据库连接
        connection = get_db_connection()
        
        with connection.cursor() as cursor:
            # 只查询需要的字段
            sql = """
            SELECT
                community, floortype, Storey, slease, Planteia,
                Plantstructure, Typewarehouse, Plantnew, isNewHouse,
                AgentFree, years, Type4Years, Landnature, Powersupply, Loadbearing, type
            FROM parsed_factory_data
            WHERE community = %s
            """
            cursor.execute(sql, (community_name,))
            
            # 获取查询结果
            result = cursor.fetchone()
            
            if result:
                logger.info(f"成功获取园区 '{community_name}' 的数据")
                
                # 对特定字段进行映射转换
                if 'Planteia' in result and result['Planteia'] in PLANTEIA_MAPPING:
                    result['Planteia'] = PLANTEIA_MAPPING[result['Planteia']]
                
                if 'Type4Years' in result and result['Type4Years'] in TYPE4YEARS_MAPPING:
                    result['Type4Years'] = TYPE4YEARS_MAPPING[result['Type4Years']]
                
                if 'Plantnew' in result and result['Plantnew'] in PLANTNEW_MAPPING:
                    result['Plantnew'] = PLANTNEW_MAPPING[result['Plantnew']]
                
                if 'floortype' in result and result['floortype'] in FLOORTYPE_MAPPING:
                    result['floortype'] = FLOORTYPE_MAPPING[result['floortype']]
                
                if 'AgentFree' in result and result['AgentFree'] in AGENTFREE_MAPPING:
                    result['AgentFree'] = AGENTFREE_MAPPING[result['AgentFree']]
                
                # 返回转换后的结果
                return {
                    'community': result.get('community', ''),  # 园区名称
                    'floortype': result.get('floortype', ''),  # 楼层类型
                    'Storey': result.get('Storey', ''),        # 首层层高
                    'slease': result.get('slease', ''),        # 起租期
                    'Planteia': result.get('Planteia', ''),    # 可办环评
                    'Plantstructure': result.get('Plantstructure', ''),  # 厂房结构
                    'Typewarehouse': result.get('Typewarehouse', ''),    # 厂房类型
                    'Plantnew': result.get('Plantnew', ''),    # 厂房新旧
                    'isNewHouse': result.get('isNewHouse', ''),# 性质
                    'AgentFree': result.get('AgentFree', ''),  # 中介费
                    'years': result.get('years', ''),          # 建筑年代
                    'Type4Years': result.get('Type4Years', ''),# 产权年限
                    'Landnature': result.get('Landnature', ''),# 土地性质
                    'Powersupply': result.get('Powersupply', ''),  # 供电容量
                    'Loadbearing': result.get('Loadbearing', ''),   # 楼板承重
                    'type': result.get('type', '')             # 租售类型
                }
            else:
                logger.warning(f"未找到园区 '{community_name}' 的数据")
                return None
            
    except Exception as e:
        logger.error(f"获取园区数据时发生错误: {str(e)}")
        return None
    finally:
        if 'connection' in locals() and connection:
            connection.close()
            logger.debug("数据库连接已关闭")

if __name__ == "__main__":
    # 测试代码
    import argparse
    
    parser = argparse.ArgumentParser(description='获取园区数据')
    parser.add_argument('community', type=str, help='园区名称')
    
    args = parser.parse_args()
    
    data = get_community_data(args.community)
    if data:
        print(f"园区 '{args.community}' 的基础数据:")
        for key, value in data.items():
            # 添加字段说明
            if key == 'floortype':
                print(f"楼层类型: {value}")
            elif key == 'Storey':
                print(f"首层层高: {value}")
            elif key == 'slease':
                print(f"起租期: {value}")
            elif key == 'Planteia':
                print(f"可办环评: {value}")
            elif key == 'Plantstructure':
                print(f"厂房结构: {value}")
            elif key == 'Typewarehouse':
                print(f"厂房类型: {value}")
            elif key == 'Plantnew':
                print(f"厂房新旧: {value}")
            elif key == 'isNewHouse':
                print(f"性质: {value}")
            elif key == 'AgentFree':
                print(f"中介费: {value}")
            elif key == 'years':
                print(f"建筑年代: {value}")
            elif key == 'Type4Years':
                print(f"产权年限: {value}")
            elif key == 'Landnature':
                print(f"土地性质: {value}")
            elif key == 'Powersupply':
                print(f"供电容量: {value}")
            elif key == 'Loadbearing':
                print(f"楼板承重: {value}")
            elif key == 'community':
                print(f"园区名称: {value}")
            elif key == 'type':
                print(f"租售类型: {value}")
            else:
                print(f"{key}: {value}")
    else:
        print(f"未找到园区 '{args.community}' 的数据")
