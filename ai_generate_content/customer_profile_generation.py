#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户画像生成模块
基于通话录音文本分析，生成工业厂房意向客户画像
"""
import logging
import os
import sys
from openai import OpenAI

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加自定义模块的导入
from ai_generate_content.enhanced_park_data_service import get_ai_optimized_data  # 新的增强数据获取
from database.db_connection import get_db_connection

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 阿里云千问模型API配置
DASHSCOPE_API_KEY = "sk-61e66c39babd417da86f9f2e6b580492"
os.environ["DASHSCOPE_API_KEY"] = DASHSCOPE_API_KEY

# 创建OpenAI客户端，配置为阿里云千问模型
client = OpenAI(
    api_key=DASHSCOPE_API_KEY,
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

# 从数据库获取园区通话录音记录
def get_conversations_by_proj_name(proj_name):
    """
    根据园区名称从数据库获取通话录音
    
    Args:
        proj_name (str): 园区名称
        
    Returns:
        str: 所有通话录音文本拼接而成的字符串
    """
    try:
        logger.info(f"正在获取园区 '{proj_name}' 的通话录音...")
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # 查询指定园区的通话录音，忽略asr_txt为空的记录
        query = """
        SELECT id, asr_txt, proj_name
        FROM call_records 
        WHERE proj_name = %s AND asr_txt IS NOT NULL AND asr_txt != ''
        LIMIT 20
        """
        cursor.execute(query, (proj_name,))
        records = cursor.fetchall()
        
        # 如果没有找到指定园区的通话录音，随机获取其他园区的通话录音
        if not records:
            logger.warning(f"未找到园区 '{proj_name}' 的有效通话录音，将随机使用其他园区的通话录音数据")
            
            # 随机获取其他园区有通话录音的数据
            random_query = """
            SELECT id, asr_txt, proj_name
            FROM call_records 
            WHERE asr_txt IS NOT NULL AND asr_txt != ''
            ORDER BY RAND()
            LIMIT 10
            """
            cursor.execute(random_query)
            records = cursor.fetchall()
            
            if records:
                # 获取随机选择的园区名称
                random_proj_name = records[0].get('proj_name', '未知园区')
                logger.info(f"使用随机选择的园区 '{random_proj_name}' 的通话录音数据作为替代")
            else:
                logger.error("数据库中没有任何有效的通话录音数据")
        
        # 关闭数据库连接
        cursor.close()
        connection.close()
        
        if not records:
            logger.warning("未找到任何有效的通话录音数据")
            return ""
        
        logger.info(f"找到 {len(records)} 条通话录音记录")

        # 打印使用的通话录音ID
        used_call_ids = [record['id'] for record in records]
        print(f"使用的通话录音ID: {used_call_ids}")
        logger.info(f"使用的通话录音ID: {used_call_ids}")

        # 将所有通话记录拼接成一个字符串
        all_conversations = ""
        for i, record in enumerate(records, 1):
            # 如果是随机获取的数据，添加原始园区名称标记
            if 'proj_name' in record and record['proj_name'] != proj_name:
                original_proj = record.get('proj_name', '未知园区')
                all_conversations += f"\n通话{i}（ID: {record['id']}, 来自园区：{original_proj}）：{record['asr_txt']}\n\n"
            else:
                all_conversations += f"\n通话{i}（ID: {record['id']}）：{record['asr_txt']}\n\n"
        
        return all_conversations
        
    except Exception as e:
        logger.error(f"获取通话录音时发生错误: {str(e)}")
        return ""

# 用户查询的提示词
prompt = """#角色定义：你是一名非常善于在58同城\安居客等房产发布网站上进行网络营销的厂房园区的招商顾问，主要工作是通过分析潜在客户需求描摹出目标客户画像和特征，你需要针对和大量客户的历史通话录音内容分析总结出目标客户的群体画像，客户画像包括不限于所属行业、厂房面积需求、厂房价格需求、厂房承重需求、厂房消防需求、厂房交通需求、厂房配套需求、厂房层高需求、厂房结构需求、厂房产权需求、厂房位置需求等，即所有能够反映出客户当前状态和厂房购买行为相关的直接和间接特征信息。

#输出要求：
按照上述要求梳理出不同的主要群体目标客户画像，并给出不同目标客户画像不同维度的客户特征信息，对于同类型客户进行客户画像以及特征合并，对于通话录音文本中未提及到的特征信息，请通过已经提取出的客户其余的特征信息（行业、产品等）参考进行推理后补充，最终形成所有特征信息完善的客户画像及特征信息，其中客户画像以及特征信息的描述可以自定义不需要按照固定的格式模板，只要能够全面完整表达信息即可。

#输出格式：

目标客户A：XXX
客户所属行业：XXX
厂房面积需求: XXX
厂房价格需求: XXX
厂房承重需求: XXX
厂房消防需求: XXX
厂房交通需求: XXX
厂房配套需求: XXX
厂房层高需求: XXX
厂房结构需求: XXX
厂房产权需求: XXX
厂房位置需求: XXX
厂房政策需求：XXX
厂房环评需求：XXX
厂房XX需求：XXX（如有以上未提及的其他需求分析出后，可以自由补充）
"""

def generate_customer_profiles(proj_name="沈阳和平项目"):
    """
    使用阿里云千问模型生成客户画像

    Args:
        proj_name (str): 园区名称，默认为"长春经开项目（金承）"

    Returns:
        str: 生成的客户画像文本
    """
    try:
        logger.info(f"开始为园区 '{proj_name}' 生成客户画像...")
        
        # 从数据库获取指定园区的通话录音
        conversations = get_conversations_by_proj_name(proj_name)
        
        if not conversations:
            return f"未能获取到园区 '{proj_name}' 的有效通话录音，无法生成客户画像"
        
        # 添加强制思考的指令
        system_prompt = """你是一个专业的客户画像分析师。分析问题时，你需要先进行思考，探索不同的角度和可能性，然后再给出最终答案。
请在<thinking>标签内展示你的思考过程，在</thinking>标签后给出最终答案。"""
        
        # 组合完整的输入内容
        input_content = f"{prompt}\n\n#参考内容：\n以下是收集到的历史客户通话录音文本：\n\n{conversations}"
        
        # 调用模型生成客户画像，启用流式输出模式和思维模式
        response = client.chat.completions.create(
            model="deepseek-r1",  # 使用指定的qwen3-235b-a22b模型
            messages=[
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": input_content
                }
            ],
            temperature=0.7,  # 控制创造性，可以根据需求调整
            stream=True,  # 启用流式输出
            extra_body={"enable_thinking": True},  # 启用思考模式
        )
        
        logger.info("API请求配置: model=qwen3-235b-a22b, enable_thinking=True")
        
        # 处理流式输出，将所有内容合并
        complete_content = ""
        logger.info("开始接收流式响应...")
        print(f"\n----- 为园区 '{proj_name}' 生成的客户画像开始 -----\n")
        
        is_thinking = False  # 标记是否处于思考部分
        thinking_content = ""  # 存储思考内容
        response_chunk_count = 0  # 跟踪接收到的响应块数量
        
        for chunk in response:
            response_chunk_count += 1
            
            # 提取每个块中的内容并添加到结果中
            if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content is not None:
                content_piece = chunk.choices[0].delta.content
                complete_content += content_piece
                
                # 输出chunk的原始内容，用于调试
                if response_chunk_count <= 5 or response_chunk_count % 50 == 0:
                    # 修复：将replace操作移出f-string
                    safe_content = content_piece.replace('\n', '\\n')
                    logger.info(f"响应块 #{response_chunk_count}: {safe_content}")
                
                # 检测思考模式的开始和结束
                if "<thinking>" in content_piece:
                    is_thinking = True
                    print("\n\033[33m----- 思考过程开始 -----\033[0m", flush=True)  # 黄色标记思考开始
                elif "</thinking>" in content_piece:
                    is_thinking = False
                    print("\033[33m----- 思考过程结束 -----\033[0m\n", flush=True)  # 黄色标记思考结束
                
                # 根据是否是思考内容，使用不同颜色显示
                if is_thinking:
                    thinking_content += content_piece
                    print(f"\033[33m{content_piece}\033[0m", end="", flush=True)  # 黄色显示思考内容
                else:
                    print(content_piece, end="", flush=True)
        
        print(f"\n\n----- 为园区 '{proj_name}' 生成的客户画像结束 -----\n")
        logger.info(f"接收到 {response_chunk_count} 个响应块")
        
        # 检查是否捕获到思考内容
        if thinking_content:
            logger.info("成功捕获思考内容")
        else:
            logger.warning("未捕获到思考内容，可能模型没有返回思考过程或格式不符合预期")
            
            # 检查完整内容中是否包含思考标记
            if "<thinking>" in complete_content:
                logger.info("在完整响应中发现<thinking>标记")
                thinking_start = complete_content.find("<thinking>")
                thinking_end = complete_content.find("</thinking>")
                if thinking_end > thinking_start:
                    logger.info(f"思考内容位于位置 {thinking_start} 到 {thinking_end}")
                    # 提取并打印思考部分
                    extracted_thinking = complete_content[thinking_start:thinking_end+11]
                    print(f"\n\033[33m重新提取的思考内容:\033[0m\n")
                    print(f"\033[33m{extracted_thinking}\033[0m\n")
        
        logger.info("客户画像生成完成")
        
        # 处理结果，移除思考部分
        final_content = complete_content
        if "<thinking>" in complete_content and "</thinking>" in complete_content:
            # 找到思考部分的起始和结束位置
            thinking_start = complete_content.find("<thinking>")
            thinking_end = complete_content.find("</thinking>") + len("</thinking>")
            # 移除思考部分
            final_content = complete_content[:thinking_start] + complete_content[thinking_end:]
        
        # 获取园区增强数据
        logger.info(f"开始获取园区 '{proj_name}' 的增强数据...")
        ai_optimized_data = get_ai_optimized_data(proj_name)

        if ai_optimized_data:
            logger.info(f"成功获取园区增强数据，数据质量评分: {ai_optimized_data.data_quality_score:.2f}")

            # 将园区数据格式化为更丰富的信息
            community_info = f"\n\n# 园区增强数据 - {proj_name}\n\n"

            # 技术规格信息
            if ai_optimized_data.technical_specs:
                community_info += "## 技术规格\n"
                for key, value in ai_optimized_data.technical_specs.items():
                    community_info += f"{key}: {value}\n"
                community_info += "\n"

            # 营销卖点信息
            if ai_optimized_data.selling_points:
                community_info += "## 园区特色\n"
                for key, value in ai_optimized_data.selling_points.items():
                    community_info += f"{key}: {value}\n"
                community_info += "\n"

            # 配套设施信息
            if ai_optimized_data.facilities:
                community_info += "## 配套设施\n"
                for key, value in ai_optimized_data.facilities.items():
                    community_info += f"{key}: {value}\n"
                community_info += "\n"

            # 政策和产业信息
            if ai_optimized_data.policies_and_industries:
                community_info += "## 政策支持与产业导向\n"
                for key, value in ai_optimized_data.policies_and_industries.items():
                    community_info += f"{key}: {value}\n"
                community_info += "\n"

            # 位置信息
            if ai_optimized_data.location_info:
                community_info += "## 地理位置\n"
                for key, value in ai_optimized_data.location_info.items():
                    community_info += f"{key}: {value}\n"
                community_info += "\n"

            # 周边配套设施信息
            if ai_optimized_data.nearby_facilities and ai_optimized_data.nearby_facilities.has_facilities():
                community_info += "## 周边配套设施\n"

                if ai_optimized_data.nearby_facilities.transportation:
                    community_info += "### 交通配套\n"
                    for facility in ai_optimized_data.nearby_facilities.transportation:
                        community_info += f"- {facility.title} ({facility.distance_text})\n"
                        community_info += f"  地址: {facility.address}\n"
                        if facility.tel:
                            community_info += f"  电话: {facility.tel}\n"
                    community_info += "\n"

                if ai_optimized_data.nearby_facilities.living_services:
                    community_info += "### 生活配套\n"
                    for facility in ai_optimized_data.nearby_facilities.living_services:
                        community_info += f"- {facility.title} ({facility.distance_text})\n"
                        community_info += f"  地址: {facility.address}\n"
                        if facility.tel:
                            community_info += f"  电话: {facility.tel}\n"
                    community_info += "\n"

                if ai_optimized_data.nearby_facilities.commercial:
                    community_info += "### 商业配套\n"
                    for facility in ai_optimized_data.nearby_facilities.commercial:
                        community_info += f"- {facility.title} ({facility.distance_text})\n"
                        community_info += f"  地址: {facility.address}\n"
                        if facility.tel:
                            community_info += f"  电话: {facility.tel}\n"
                    community_info += "\n"

                if ai_optimized_data.nearby_facilities.leisure:
                    community_info += "### 休闲配套\n"
                    for facility in ai_optimized_data.nearby_facilities.leisure:
                        community_info += f"- {facility.title} ({facility.distance_text})\n"
                        community_info += f"  地址: {facility.address}\n"
                        if facility.tel:
                            community_info += f"  电话: {facility.tel}\n"
                    community_info += "\n"

                if ai_optimized_data.nearby_facilities.industrial:
                    community_info += "### 产业配套\n"
                    for facility in ai_optimized_data.nearby_facilities.industrial:
                        community_info += f"- {facility.title} ({facility.distance_text})\n"
                        community_info += f"  地址: {facility.address}\n"
                        if facility.tel:
                            community_info += f"  电话: {facility.tel}\n"
                    community_info += "\n"

            # 数据质量信息
            community_info += f"## 数据质量\n"
            community_info += f"数据完整度评分: {ai_optimized_data.data_quality_score:.2f}\n"
            community_info += f"可用字段数量: {len(ai_optimized_data.available_fields)}\n"

            # 添加园区数据到客户画像结果
            final_content += community_info
            print(f"\n----- 园区 '{proj_name}' 增强数据 -----\n")
            print(community_info)
        else:
            logger.warning(f"未能获取到园区 '{proj_name}' 的增强数据")
            final_content += f"\n\n# 园区增强数据 - {proj_name}\n\n未找到相关园区数据。"
        
        return final_content
    
    except Exception as e:
        logger.error(f"生成客户画像时发生错误: {e}")
        return f"生成客户画像时发生错误: {str(e)}"

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='生成客户画像')
    parser.add_argument('--proj_name', type=str, default="长春经开项目（金承）", help='园区名称')

    args = parser.parse_args()

    # 生成客户画像
    customer_profiles = generate_customer_profiles(args.proj_name)

    # 保存结果到文件
    output_filename = f"{args.proj_name}_customer_profiles_result.txt"
    with open(output_filename, "w", encoding="utf-8") as f:
        f.write(customer_profiles)

    print(f"客户画像已生成并保存到 {output_filename} 文件")