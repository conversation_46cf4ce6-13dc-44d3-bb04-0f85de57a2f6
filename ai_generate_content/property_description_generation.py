#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import os
import sys
import re
import time
import concurrent.futures
from openai import OpenAI

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ai_generate_content.marketing_titles_generation import generate_marketing_titles, format_knowledge_base_for_ai
from ai_generate_content.enhanced_park_data_service import get_ai_optimized_data
from database.db_connection import get_db_connection

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

DASHSCOPE_API_KEY = "sk-61e66c39babd417da86f9f2e6b580492"
os.environ["DASHSCOPE_API_KEY"] = DASHSCOPE_API_KEY

client = OpenAI(
    api_key=DASHSCOPE_API_KEY,
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

# 基础提示词模板
base_prompt_template = """#角色定义：你是一名非常善于在58同城\安居客等房产发布网站上进行网络营销的厂房园区的招商顾问，拥有丰富的园区知识库和客户洞察能力。你的主要工作是根据梳理出的针对目标客户的画像以及特征匹配的营销点组合以及对应的标题，结合园区的完整知识库信息，对每一个营销点进行详细展开描述形成房源的完整介绍，撰写房源描述内容时可对营销点拆分出的细分营销场景，再结合客户画像和该细分营销点进行场景化描述，让用户看到后有代入感和场景感，所输出的内容是为潜在客户提供定制化的解决方案而不是单向的输入营销，要让目标客户看到就有为自己量身定制的感觉。

你需要深度挖掘园区知识库中的所有有价值信息，包括技术参数、配套设施、政策优势、地理位置、管理服务等各个维度，确保每一个房源描述都要紧扣该目标客户的需求和园区的真实优势。

#园区完整知识库：
{community_data}

#知识库使用指南：
**核心原则：所有房源描述内容必须且只能基于知识库中提供的真实数据！**

**重要：请根据知识库中的租售类型信息，智能选择合适的数据作为描述重点。**

**特别注意：如果租售类型为"出售、出租"（即既可出租也可出售），请不要在描述中提及任何租售相关的具体内容（如起租期、产权年限、租金、售价等），而应重点突出园区本身的特色、技术优势、配套设施、地理位置等中性特点。**

请从以下维度全面挖掘知识库中的所有可用信息：

1. **技术规格维度**：
   - 基础参数：租售类型、楼层类型、首层层高、起租期、厂房结构、厂房类型、厂房新旧、性质、中介费
   - 产权信息：建筑年代、产权年限、土地性质、产权证书、销售证书
   - 技术指标：供电容量、楼板承重、可办环评、抗震等级、保温配置

2. **园区特色维度**：
   - 园区定位：园区类型、园区结构、业务类型、园区性质、园区阶段、园区面积
   - 特色优势：园区风格、优势劣势、园区规模、园区负责人、园区管理者
   - 规划信息：地块信息、容积率、建筑面积、结构类型、设计标准、绿化率、地块容积率

3. **配套设施维度**：
   - 基础设施：供电、供水、供气、网络、消防、安防
   - 服务配套：物业管理、餐饮、住宿、会议、培训
   - 周边配套：交通、商业、教育、医疗、休闲

4. **政策产业维度**：
   - 政策支持：招商政策、税收优惠、金融支持、环保政策
   - 产业导向：主导产业、入驻要求、环评要求、税收要求

5. **地理位置维度**：
   - 位置信息：省市区县、详细地址、交通便利性
   - 距离优势：到重要地标、交通枢纽的距离

**重要提醒**：
- 严格基于知识库真实数据，不得添加、夸大或虚构任何信息
- 如果知识库中某项数据不存在，请不要在描述中提及
- 根据知识库中的租售类型，智能选择最相关的数据作为描述重点
- **特殊处理：当租售类型为"出售、出租"时，重点使用园区特色、技术规格、配套设施、地理位置等中性信息，避免使用起租期、产权年限等具体租售相关数据**
- 充分利用知识库中的所有有效数据
- 描述语言应适度表达，避免过于绝对化的词汇
- **严禁在描述中提及物业费、投资回报、返税优惠等敏感内容**
- **重点突出厂房实用功能和客观属性，避免过度营销**

#输出要求：
针对已梳理出的营销点拆分出多个维度的细分营销场景展开房源描述，用户需求和园区卖点匹配的总结要完全基于用户画像需求分析和园区卖点分析（该部分输出字数需要保证在70字以上），细分营销场景描述中每一行房源描述开头需要有一个总结词或短句对该行的房源描述内容进行概括，房源描述总行数内容必须超过8行，每一行必须超过20字，房源描述必须紧扣营销点和标题以及符合目标客户画像特征，且符合园区卖点信息不能胡乱编造

#输出格式要求：
**【关键】严格格式约束 - 违反格式将导致解析失败**：

1. **格式完全一致性**：输出必须与下方模板100%一致，不允许任何字符、标点、空格的变化
2. **关键标识符不可变**：以下标识符必须原样出现，不得修改：
   - "【目标客户"和"】"
   - "营销版本"
   - "营销点："（必须有冒号）
   - "原始标题："（必须有冒号）
   - "改写标题："（必须有冒号）
   - "房源描述："（必须有冒号，这是解析的关键标识符）
3. **换行规则**：每个字段必须独占一行，不得合并到同一行
4. **禁止添加内容**：不得添加任何格式说明、占位符、示例文字或额外的标识符
5. **空行处理**：需要空行的地方直接空行，绝对不要写"（空行）"、"（此处为空行）"等文字

#【强制】输出格式模板（必须100%严格遵守，一个字符都不能改变）：

【目标客户{customer_letter}：{customer_type}】
营销版本{version}
营销点：{selling_points}
原始标题：{original_title}
改写标题：{rewritten_title}
房源描述：
「这里必须写客户画像和园区卖点匹配的总结描述，必须用「」符号包围」
【XXXXXX】写具体描述内容
【XXXXXX】写具体描述内容
【XXXXXX】写具体描述内容

#【关键】格式细节要求：
1. **「」符号是必需的**：客户画像匹配总结必须用「」符号包围，不能省略
2. **【】符号是必需的**：每个描述点总结必须用【】符号包围
3. **冒号是必需的**：所有标识符后面必须有中文冒号「：」
4. **换行是必需的**：每个部分必须独占一行

#格式自检要求：
在输出前，请务必检查：
- 每个标识符是否完全正确（包括冒号、中文符号）
- 客户画像总结是否用「」符号包围
- 每个描述点是否用【】符号包围
- 每个字段是否独占一行
- 是否有多余的格式说明文字
- "房源描述："后是否正确换行
"""


def save_to_database(park_name, title, selling_points, description, status=1):
    try:
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                sql = """
                INSERT INTO property_marketing_content 
                (park_name, title, selling_points, description, created_at, status) 
                VALUES (%s, %s, %s, %s, NOW(), %s)
                """
                cursor.execute(sql, (park_name, title, selling_points, description, status))
            connection.commit()
            logger.info(f"成功将{park_name}的营销内容保存到数据库")
            return True
        finally:
            connection.close()
    except Exception as e:
        logger.error(f"保存到数据库时发生错误: {e}")
        return False


def extract_marketing_info(marketing_titles):
    logger.info("开始提取营销信息...")
    customer_info = {}

    try:
        lines = marketing_titles.split('\n')
        current_customer = None
        current_version = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            customer_match = re.search(r'【目标客户([A-E])：(.*?)】', line)
            if customer_match:
                current_customer = customer_match.group(1).strip()
                customer_type = customer_match.group(2).strip()

                logger.info(f"找到客户类型: {current_customer} - {customer_type}")

                if current_customer not in customer_info:
                    customer_info[current_customer] = {
                        "type": customer_type,
                        "marketing_versions": {}
                    }
                continue

            version_match = re.search(r'营销版本([A-H])', line)
            if version_match and current_customer:
                current_version = version_match.group(1).strip()

                logger.info(f"找到营销版本: {current_version} (客户: {current_customer})")

                if current_version not in customer_info[current_customer]["marketing_versions"]:
                    customer_info[current_customer]["marketing_versions"][current_version] = {}
                continue

            selling_points_match = re.search(r'营销点：(.*?)$', line)
            if selling_points_match and current_customer and current_version:
                selling_points = selling_points_match.group(1).strip()
                customer_info[current_customer]["marketing_versions"][current_version][
                    "selling_points"] = selling_points
                logger.info(f"提取营销点: {selling_points[:30]}... (客户: {current_customer}, 版本: {current_version})")
                continue

            original_title_match = re.search(r'原始标题：(.*?)$', line)
            if original_title_match and current_customer and current_version:
                original_title = original_title_match.group(1).strip()
                customer_info[current_customer]["marketing_versions"][current_version][
                    "original_title"] = original_title
                logger.info(f"提取原始标题: {original_title[:30]}... (客户: {current_customer}, 版本: {current_version})")
                continue

            rewritten_title_match = re.search(r'改写标题：(.*?)$', line)
            if rewritten_title_match and current_customer and current_version:
                rewritten_title = rewritten_title_match.group(1).strip()
                customer_info[current_customer]["marketing_versions"][current_version][
                    "rewritten_title"] = rewritten_title
                logger.info(f"提取改写标题: {rewritten_title[:30]}... (客户: {current_customer}, 版本: {current_version})")
                continue

            titles_match = re.search(r'原始标题：(.*?)改写标题：(.*?)$', line)
            if titles_match and current_customer and current_version:
                original_title = titles_match.group(1).strip()
                rewritten_title = titles_match.group(2).strip()
                customer_info[current_customer]["marketing_versions"][current_version][
                    "original_title"] = original_title
                customer_info[current_customer]["marketing_versions"][current_version][
                    "rewritten_title"] = rewritten_title
                logger.info(
                    f"提取标题(同行): 原始: {original_title[:20]}..., 改写: {rewritten_title[:20]}... (客户: {current_customer}, 版本: {current_version})")
                continue

        customer_count = len(customer_info)
        version_count = sum(len(data["marketing_versions"]) for data in customer_info.values())
        logger.info(f"成功提取到 {customer_count} 个客户类型，总共 {version_count} 个营销版本")

        return customer_info

    except Exception as e:
        logger.error(f"提取营销信息时发生错误: {e}")
        return {}


def generate_property_description_for_version(task):
    customer_letter, customer_type, version, version_info, marketing_titles, park_name, task_id = task

    try:
        selling_points = version_info.get("selling_points", "")
        original_title = version_info.get("original_title", "")
        rewritten_title = version_info.get("rewritten_title", "")

        logger.info(f"[任务 {task_id}] 开始为客户类型 '{customer_type}' 的营销版本 {version} 生成房源详细描述...")

        logger.info(f"[任务 {task_id}] 开始获取园区 '{park_name}' 的完整知识库数据...")
        ai_optimized_data = get_ai_optimized_data(park_name)

        if ai_optimized_data:
            logger.info(
                f"[任务 {task_id}] 成功获取园区知识库数据，数据质量评分: {ai_optimized_data.data_quality_score:.2f}")

            community_data = f"=== 园区知识库：{park_name} ===\n\n"

            if ai_optimized_data.technical_specs:
                community_data += "## 技术规格\n"
                for key, value in ai_optimized_data.technical_specs.items():
                    community_data += f"{key}: {value}\n"
                community_data += "\n"

            if ai_optimized_data.selling_points:
                community_data += "## 园区特色\n"
                for key, value in ai_optimized_data.selling_points.items():
                    community_data += f"{key}: {value}\n"
                community_data += "\n"

            if ai_optimized_data.facilities:
                community_data += "## 配套设施\n"
                for key, value in ai_optimized_data.facilities.items():
                    community_data += f"{key}: {value}\n"
                community_data += "\n"

            if ai_optimized_data.policies_and_industries:
                community_data += "## 政策支持与产业导向\n"
                for key, value in ai_optimized_data.policies_and_industries.items():
                    community_data += f"{key}: {value}\n"
                community_data += "\n"

            if ai_optimized_data.location_info:
                community_data += "## 地理位置\n"
                for key, value in ai_optimized_data.location_info.items():
                    community_data += f"{key}: {value}\n"
                community_data += "\n"

            if ai_optimized_data.nearby_facilities and ai_optimized_data.nearby_facilities.has_facilities():
                community_data += "## 周边配套设施\n"

                if ai_optimized_data.nearby_facilities.transportation:
                    community_data += "### 交通配套\n"
                    for facility in ai_optimized_data.nearby_facilities.transportation:
                        community_data += f"- {facility.title} ({facility.distance_text})\n"

                if ai_optimized_data.nearby_facilities.industrial:
                    community_data += "### 产业配套\n"
                    for facility in ai_optimized_data.nearby_facilities.industrial:
                        community_data += f"- {facility.title} ({facility.distance_text})\n"

                if ai_optimized_data.nearby_facilities.commercial:
                    community_data += "### 商业配套\n"
                    for facility in ai_optimized_data.nearby_facilities.commercial:
                        community_data += f"- {facility.title} ({facility.distance_text})\n"

                if ai_optimized_data.nearby_facilities.leisure:
                    community_data += "### 休闲配套\n"
                    for facility in ai_optimized_data.nearby_facilities.leisure:
                        community_data += f"- {facility.title} ({facility.distance_text})\n"
                community_data += "\n"

            community_data += f"## 数据质量\n"
            community_data += f"数据完整度评分: {ai_optimized_data.data_quality_score:.2f}\n"
            community_data += f"可用字段数量: {len(ai_optimized_data.available_fields)}\n"

        else:
            logger.error(f"[任务 {task_id}] 无法获取园区 '{park_name}' 的知识库数据")
            community_data = f"园区名称: {park_name}\n未能获取到详细园区数据"

        formatted_knowledge_base = format_knowledge_base_for_ai(community_data)
        logger.info(f"[任务 {task_id}] 园区知识库数据格式化完成")

        system_prompt = """你是一名专业的厂房招商营销专家。你需要严格按照提供的园区知识库内容生成房源描述。

**【关键】内容约束**：
1. 必须且只能使用园区知识库中提供的真实数据
2. 不得添加、推测或夸大任何未在知识库中明确提及的信息
3. 如果知识库中缺少某项信息，请不要在描述中提及该项
4. 避免使用过于绝对化的表述，如"最好的"、"完美的"等
5. 确保所有描述内容都能在知识库中找到对应的数据支撑
6. **严禁在描述中提及物业费、投资回报、返税优惠等敏感内容**
7. **重点突出厂房实用功能和客观属性，避免过度营销**
8. **根据租售类型智能选择相关数据：当租售类型为"出售、出租"时，避免提及具体租售信息，重点突出园区特色**

**【关键】格式约束 - 违反将导致系统解析失败**：
6. 输出格式必须与提供的模板100%一致，一个字符都不能改变
7. 关键标识符必须原样输出：
   - "【目标客户X：客户类型】" - X为字母，客户类型为具体类型
   - "营销版本X" - X为版本字母
   - "营销点：" - 必须有冒号，后面跟营销点内容
   - "原始标题：" - 必须有冒号，后面跟原始标题
   - "改写标题：" - 必须有冒号，后面跟改写标题
   - "房源描述：" - 必须有冒号，这是系统解析的关键标识符
8. 每个字段必须独占一行，不得合并
9. **【重要】换行要求**：房源描述部分必须正确换行，确保：
   - 「客户画像匹配总结」独占一行
   - 每个【描述点总结】及其内容独占一行
   - 不同描述点之间正确换行
10. 需要空行的地方直接空行，绝对不要写"（空行）"、"（此处为空行）"等任何文字
11. 禁止添加任何格式说明、占位符、示例文字或自创的标识符
12. 禁止修改标点符号（冒号、中文符号等）

**【关键】输出完整性要求**：
12. 必须包含模板中的所有必需部分
13. 房源描述部分必须包含：客户画像匹配总结 + 多个【描述点总结】条目
14. 每个【描述点总结】必须包含具体描述内容

**【关键】格式自检指令**：
在输出前，你必须进行格式自检：
- 检查每个标识符是否完全正确（包括冒号、中文符号）
- 检查每个字段是否独占一行
- 检查房源描述部分是否正确换行分隔不同内容
- 检查「客户画像匹配总结」是否用「」符号包围且独占一行
- 检查每个【描述点总结】是否用【】符号包围且独占一行
- 检查是否有多余的格式说明文字
- 检查"房源描述："后是否正确换行
- 检查是否严格按照模板格式输出

**思考要求**：
分析问题时，你需要先进行思考，仔细检查知识库中的每一项数据，然后再给出最终答案。
请在<thinking>标签内展示你的思考过程，在</thinking>标签后给出严格按照格式要求的最终答案。

**重要提醒**：格式错误将导致系统无法解析你的输出，请务必严格遵守格式要求！"""

        prompt = base_prompt_template.format(
            customer_letter=customer_letter,
            customer_type=customer_type,
            version=version,
            selling_points=selling_points,
            original_title=original_title,
            rewritten_title=rewritten_title,
            community_data=formatted_knowledge_base
        )

        input_content = f"{prompt}\n\n#参考内容：\n以下是目标客户画像和特征以及所有营销标题信息\n\n{marketing_titles}"

        response = client.chat.completions.create(
            model="deepseek-r1",
            messages=[
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": input_content
                }
            ],
            temperature=0.75,
            max_tokens=4096,
            stream=True,
            extra_body={"enable_thinking": True},
        )

        complete_content = ""
        logger.info(f"[任务 {task_id}] 开始接收客户 '{customer_type}' 营销版本 {version} 的流式响应...")

        is_thinking = False
        thinking_content = ""
        response_chunk_count = 0

        for chunk in response:
            response_chunk_count += 1

            if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content is not None:
                content_piece = chunk.choices[0].delta.content
                complete_content += content_piece

                if response_chunk_count <= 5 or response_chunk_count % 100 == 0:
                    safe_content = content_piece.replace('\n', '\\n')
                    logger.info(f"[任务 {task_id}] 响应块 #{response_chunk_count}: {safe_content}")

                if "<thinking>" in content_piece:
                    is_thinking = True
                elif "</thinking>" in content_piece:
                    is_thinking = False

                if is_thinking:
                    thinking_content += content_piece

        logger.info(
            f"[任务 {task_id}] 客户 '{customer_type}' 营销版本 {version} 接收到 {response_chunk_count} 个响应块")

        final_content = complete_content
        if "<thinking>" in complete_content and "</thinking>" in complete_content:
            thinking_start = complete_content.find("<thinking>")
            thinking_end = complete_content.find("</thinking>") + len("</thinking>")
            final_content = complete_content[:thinking_start] + complete_content[thinking_end:]



        return {
            "customer_letter": customer_letter,
            "version": version,
            "content": final_content.strip(),
            "task_id": task_id,
            "selling_points": selling_points,
            "title": rewritten_title
        }

    except Exception as e:
        error_msg = f"[任务 {task_id}] 为客户 '{customer_type}' 营销版本 {version} 生成房源详细描述时发生错误: {e}"
        logger.error(error_msg)
        logger.error(f"[任务 {task_id}] 园区名称: {park_name}, 错误详情: {str(e)}")
        return {
            "customer_letter": customer_letter,
            "version": version,
            "content": error_msg,
            "task_id": task_id,
            "selling_points": "",
            "title": ""
        }


def generate_all_property_descriptions(park_name="默认园区", save_to_db=True):
    try:
        from services.factory_upload_service import update_task_status

        update_task_status(park_name, 1, 5)

        marketing_titles = generate_marketing_titles(park_name)

        if not marketing_titles or "生成营销标题时发生错误" in marketing_titles:
            logger.error("营销标题获取失败，无法继续生成房源详细描述")
            update_task_status(park_name, 3, 5, "营销标题获取失败")
            return "营销标题获取失败，无法继续生成房源详细描述"

        update_task_status(park_name, 1, 30)
        logger.info("营销标题获取成功，开始提取营销信息...")

        marketing_info = extract_marketing_info(marketing_titles)

        if not marketing_info:
            logger.error("营销信息提取失败，无法继续生成房源详细描述")
            update_task_status(park_name, 3, 30, "营销信息提取失败")
            return "营销信息提取失败，无法继续生成房源详细描述"

        update_task_status(park_name, 1, 40)
        logger.info(f"成功提取到 {len(marketing_info)} 种客户类型的营销信息，开始生成房源详细描述...")

        tasks = []
        task_id = 1

        for customer_letter, customer_data in marketing_info.items():
            customer_type = customer_data["type"]

            for version, version_info in customer_data["marketing_versions"].items():
                tasks.append((
                    customer_letter,
                    customer_type,
                    version,
                    version_info,
                    marketing_titles,
                    park_name,
                    task_id
                ))
                task_id += 1

        results_dict = {}
        database_results = []
        start_time = time.time()

        max_workers = min(10, len(tasks))

        total_tasks = len(tasks)
        completed_tasks = 0

        update_task_status(park_name, 1, 50)

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_task = {executor.submit(generate_property_description_for_version, task): task for task in tasks}

            for future in concurrent.futures.as_completed(future_to_task):
                result = future.result()

                completed_tasks += 1
                progress = 50 + int((completed_tasks / total_tasks) * 40)
                update_task_status(park_name, 1, progress)

                customer_letter = result["customer_letter"]
                version = result["version"]
                content = result["content"]

                task_info = f"客户{customer_letter}版本{version}"
                parsed_content = parse_description_content(content, park_name, task_info)
                logger.info(
                    f"解析结果 - 标题长度: {len(parsed_content['title'])}, 营销点长度: {len(parsed_content['selling_points'])}, 描述长度: {len(parsed_content['description'])}")

                if parsed_content['title'] and parsed_content['description']:
                    logger.info(f"更新园区 '{park_name}' 的标题和描述到数据库")
                    update_success = update_factory_data(
                        park_name=park_name,
                        title=parsed_content['title'],
                        description=parsed_content['description']
                    )
                    if update_success:
                        logger.info(f"成功更新园区 '{park_name}' 的数据")

                if customer_letter not in results_dict:
                    results_dict[customer_letter] = {}

                results_dict[customer_letter][version] = content

                if save_to_db:
                    database_results.append({
                        "park_name": park_name,
                        "title": parsed_content['title'] or result["title"] or f"{park_name}厂房",
                        "selling_points": parsed_content['selling_points'] or result["selling_points"] or "园区厂房",
                        "description": parsed_content['description'] or content
                    })

        all_descriptions = []

        for customer_letter in sorted(results_dict.keys()):
            versions_dict = results_dict[customer_letter]
            customer_descriptions = []

            for version in sorted(versions_dict.keys()):
                customer_descriptions.append(versions_dict[version])

            combined_customer_descriptions = "\n\n".join(customer_descriptions)
            all_descriptions.append(combined_customer_descriptions)

        combined_descriptions = "\n\n".join(all_descriptions)

        success_count = 0

        update_task_status(park_name, 1, 95)

        if save_to_db and database_results:
            logger.info(f"开始将 {len(database_results)} 条营销内容保存到数据库...")

            for item in database_results:
                if save_to_database(
                        park_name=item["park_name"],
                        title=item["title"],
                        selling_points=item["selling_points"],
                        description=item["description"]
                ):
                    success_count += 1

            logger.info(f"数据库保存完成: 成功 {success_count}/{len(database_results)} 条")

        update_task_status(park_name, 1, 100)
        total_elapsed = time.time() - start_time

        summary = f"\n\n==================== 房源描述生成总结 ====================\n"
        summary += f"- 共处理 {len(tasks)} 个房源描述（来自 {len(marketing_info)} 种客户类型）\n"
        summary += f"- 总耗时: {total_elapsed:.1f} 秒\n"
        if len(tasks) > 0:
            summary += f"- 平均处理时间: {total_elapsed / len(tasks):.1f} 秒/个\n"
        else:
            summary += "- 平均处理时间: 0 秒/个\n"
        if save_to_db:
            summary += f"- 成功保存到数据库: {success_count}/{len(database_results)} 条\n"
        summary += f"==================== 生成完成 ====================\n"

        logger.info(f"所有客户类型和营销版本的房源详细描述生成完成，耗时 {total_elapsed:.1f} 秒")

        return combined_descriptions

    except Exception as e:
        logger.error(f"生成房源详细描述时发生错误: {e}")
        update_task_status(park_name, 3, 50, str(e))
        return f"生成房源详细描述时发生错误: {str(e)}"


def parse_description_content(content, park_name="未知园区", task_info=""):
    try:
        result = {
            'title': '',
            'selling_points': '',
            'description': ''
        }

        log_prefix = f"[{park_name}]{task_info}"
        logger.info(f"{log_prefix} 开始解析AI生成内容，内容长度: {len(content)} 字符")

        lines = content.split('\n')
        current_section = None
        description_lines = []

        found_title_line = False
        found_selling_points_line = False
        found_description_marker = False
        title_line_content = ""
        selling_points_line_content = ""

        for i, line in enumerate(lines):
            line = line.strip()

            if not line:
                continue

            if '改写标题：' in line:
                title_part = line.split('改写标题：')[-1].strip()
                result['title'] = title_part
                found_title_line = True
                title_line_content = line
                logger.info(f"{log_prefix} 找到改写标题行 (第{i+1}行): {line}")

            elif '营销点：' in line:
                points_part = line.split('营销点：')[-1].strip()
                result['selling_points'] = points_part
                found_selling_points_line = True
                selling_points_line_content = line
                logger.info(f"{log_prefix} 找到营销点行 (第{i+1}行): {line}")

            elif line == '房源描述：':
                current_section = 'description'
                found_description_marker = True
                logger.info(f"{log_prefix} 找到房源描述标识符 (第{i+1}行)")
                continue

            elif current_section == 'description':
                if line.startswith('【目标客户') and '】' in line:
                    logger.info(f"{log_prefix} 遇到新客户类型标识，停止收集描述 (第{i+1}行): {line}")
                    break

                if '｜' in line:
                    description_lines.append(line)
                    logger.debug(f"{log_prefix} 收集描述行(原格式) (第{i+1}行): {line[:50]}...")
                elif line.startswith('【') and '】' in line:
                    description_lines.append(line)
                    logger.debug(f"{log_prefix} 收集描述行(新格式) (第{i+1}行): {line[:50]}...")
                elif line.startswith('「') and line.endswith('」'):
                    description_lines.append(line)
                    logger.debug(f"{log_prefix} 收集描述行(总体描述) (第{i+1}行): {line[:50]}...")
                elif line.strip():
                    if not (line.startswith('营销版本') or line.startswith('营销点：') or
                            line.startswith('原始标题：') or line.startswith('改写标题：')):
                        description_lines.append(line)
                        logger.debug(f"{log_prefix} 收集描述行(其他格式) (第{i+1}行): {line[:50]}...")

            elif not current_section and (line.startswith('「') or line.startswith('【')):
                if result['title'] and result['selling_points']:
                    current_section = 'description'
                    description_lines.append(line)
                    logger.info(f"{log_prefix} 未找到房源描述标识符，但根据内容特征开始收集描述 (第{i+1}行): {line[:50]}...")

        if description_lines:
            result['description'] = '\n'.join(description_lines)

        logger.info(f"{log_prefix} 解析完成 - 标题: {'✓' if result['title'] else '✗'}, "
                   f"营销点: {'✓' if result['selling_points'] else '✗'}, "
                   f"描述: {'✓' if result['description'] else '✗'}")

        if not result['title'] or not result['selling_points'] or not result['description']:
            logger.warning(f"{log_prefix} 解析失败，记录详细调试信息:")
            logger.warning(f"{log_prefix} - 找到改写标题行: {found_title_line}, 内容: {title_line_content}")
            logger.warning(f"{log_prefix} - 找到营销点行: {found_selling_points_line}, 内容: {selling_points_line_content}")
            logger.warning(f"{log_prefix} - 找到房源描述标识符: {found_description_marker}")
            logger.warning(f"{log_prefix} - 收集到的描述行数: {len(description_lines)}")

        if not result['title']:
            logger.warning(f"{log_prefix} 未能从内容中提取到改写标题")
        if not result['selling_points']:
            logger.warning(f"{log_prefix} 未能从内容中提取到营销点")
        if not result['description']:
            logger.warning(f"{log_prefix} 未能从内容中提取到房源描述")

        return result
    except Exception as e:
        logger.error(f"{log_prefix} 解析描述内容时发生错误: {str(e)}")
        return {'title': '', 'selling_points': '', 'description': ''}


def update_factory_data(park_name, title, description):
    try:
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                sql = """
                UPDATE parsed_factory_data
                SET topic = %s, content = %s, update_time = NOW()
                WHERE community = %s AND is_deleted = 0
                """
                result = cursor.execute(sql, (title, description, park_name))
                if result > 0:
                    logger.info(f"成功更新{result}条{park_name}的房源数据到parsed_factory_data表")
                else:
                    logger.warning(f"未找到园区 '{park_name}' 的记录，更新0条记录")
            connection.commit()
            return True
        finally:
            connection.close()
    except Exception as e:
        logger.error(f"更新parsed_factory_data表时发生错误: {e}")
        return False


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="生成房源详细描述并保存到数据库")
    parser.add_argument("--park_name", type=str, default="默认园区", help="园区名称")
    parser.add_argument("--no-save-db", action="store_true", help="不保存到数据库")
    args = parser.parse_args()

    property_descriptions = generate_all_property_descriptions(
        park_name=args.park_name,
        save_to_db=not args.no_save_db
    )


