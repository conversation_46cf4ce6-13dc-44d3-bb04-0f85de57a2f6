#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter, HTTPException, Response
from pydantic import BaseModel, Field
from typing import List, Optional, Union
from datetime import datetime
from enum import Enum
from tasks.task_manager import TaskManager, execute_shell_script, execute_python_script
import logging
import json

router = APIRouter(prefix="/api/tasks", tags=["tasks"])

# 执行频率枚举
class FrequencyType(str, Enum):
    MINUTES = "minutes"    # 每x分钟
    HOURLY = "hourly"      # 每x小时
    DAILY = "daily"        # 每天x点
    WEEKLY = "weekly"      # 每周x天的y点
    MONTHLY = "monthly"    # 每月x日的y点
    CUSTOM = "custom"      # 自定义cron表达式

# 星期枚举
class WeekDay(int, Enum):
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 0

# 房源类型枚举
class PropertyType(str, Enum):
    RENT = "rent"  # 出租
    SALE = "sale"  # 出售

# 数据模型
class TaskBase(BaseModel):
    task_name: str
    task_type: str  # python_script/api/function/shell_script
    command: str
    cron_expression: str
    description: Optional[str] = None

class TaskCreate(TaskBase):
    task_code: str

class FrequencySettings(BaseModel):
    frequency_type: FrequencyType
    interval: int = Field(gt=0, description="间隔值，如每5分钟的5")
    at_hour: Optional[int] = Field(None, ge=0, le=23, description="小时(0-23)")
    at_minute: Optional[int] = Field(None, ge=0, le=59, description="分钟(0-59)")
    at_day: Optional[int] = Field(None, ge=1, le=31, description="日期(1-31)")
    at_weekday: Optional[WeekDay] = Field(None, description="星期几(0-6，0=周日)")

class TaskUpdate(BaseModel):
    task_name: Optional[str] = None
    task_type: Optional[str] = None
    command: Optional[str] = None
    cron_expression: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[int] = None
    frequency_settings: Optional[FrequencySettings] = None

class TaskResponse(TaskBase):
    id: int
    task_code: str
    is_active: int
    last_run_time: Optional[datetime] = None
    next_run_time: Optional[datetime] = None
    create_time: datetime
    update_time: datetime

    class Config:
        orm_mode = True

def frequency_to_cron(settings: FrequencySettings) -> str:
    """将频率设置转换为cron表达式"""
    if settings.frequency_type == FrequencyType.MINUTES:
        return f"*/{settings.interval} * * * *"

    elif settings.frequency_type == FrequencyType.HOURLY:
        at_minute = settings.at_minute if settings.at_minute is not None else 0
        return f"{at_minute} */{settings.interval} * * *"

    elif settings.frequency_type == FrequencyType.DAILY:
        at_hour = settings.at_hour if settings.at_hour is not None else 0
        at_minute = settings.at_minute if settings.at_minute is not None else 0
        return f"{at_minute} {at_hour} * * *"

    elif settings.frequency_type == FrequencyType.WEEKLY:
        at_hour = settings.at_hour if settings.at_hour is not None else 0
        at_minute = settings.at_minute if settings.at_minute is not None else 0
        at_weekday = settings.at_weekday.value if settings.at_weekday is not None else 1
        return f"{at_minute} {at_hour} * * {at_weekday}"

    elif settings.frequency_type == FrequencyType.MONTHLY:
        at_hour = settings.at_hour if settings.at_hour is not None else 0
        at_minute = settings.at_minute if settings.at_minute is not None else 0
        at_day = settings.at_day if settings.at_day is not None else 1
        return f"{at_minute} {at_hour} {at_day} * *"

    elif settings.frequency_type == FrequencyType.CUSTOM:
        # 对于自定义类型，不生成cron表达式，应该使用用户提供的表达式
        return None

    return "0 0 * * *"

# API路由
@router.get("/", response_model=List[TaskResponse])
async def get_all_tasks(task_type: Optional[str] = None):
    """获取所有定时任务"""
    tasks = TaskManager.get_all_tasks()

    # 如果指定了任务类型，进行筛选
    if task_type:
        tasks = [task for task in tasks if task.get('task_type') == task_type]

    return tasks

@router.get("/{task_code}", response_model=TaskResponse)
async def get_task(task_code: str):
    """获取指定任务"""
    task = TaskManager.get_task_by_code(task_code)
    if not task:
        raise HTTPException(status_code=404, detail=f"Task {task_code} not found")
    return task

@router.post("/", response_model=TaskResponse)
async def create_task(task: Union[TaskCreate, dict]):
    """创建新任务"""
    if isinstance(task, dict) and "frequency_settings" in task:
        frequency_settings = FrequencySettings(**task["frequency_settings"])
        # 只有当频率类型不是自定义时才重新生成cron表达式
        if frequency_settings.frequency_type != FrequencyType.CUSTOM:
            cron_expression = frequency_to_cron(frequency_settings)
            if cron_expression:  # 确保生成的表达式有效
                task["cron_expression"] = cron_expression

    if isinstance(task, dict):
        task_data = TaskCreate(**task)
    else:
        task_data = task

    existing_task = TaskManager.get_task_by_code(task_data.task_code)
    if existing_task:
        raise HTTPException(status_code=400, detail=f"Task {task_data.task_code} already exists")

    valid_task_types = ['shell_script', 'python_script']
    if task_data.task_type not in valid_task_types:
        raise HTTPException(status_code=400, detail=f"Invalid task type: {task_data.task_type}. Valid types: {', '.join(valid_task_types)}")

    success = TaskManager.create_task(
        task_name=task_data.task_name,
        task_code=task_data.task_code,
        task_type=task_data.task_type,
        command=task_data.command,
        cron_expression=task_data.cron_expression,
        description=task_data.description
    )

    if not success:
        raise HTTPException(status_code=500, detail="Failed to create task")

    return TaskManager.get_task_by_code(task_data.task_code)

@router.put("/{task_code}", response_model=TaskResponse)
async def update_task(task_code: str, task_update: Union[TaskUpdate, dict]):
    """更新任务"""
    if isinstance(task_update, dict) and "frequency_settings" in task_update:
        frequency_settings = FrequencySettings(**task_update["frequency_settings"])
        # 只有当频率类型不是自定义时才重新生成cron表达式
        if frequency_settings.frequency_type != FrequencyType.CUSTOM:
            cron_expression = frequency_to_cron(frequency_settings)
            if cron_expression:  # 确保生成的表达式有效
                task_update["cron_expression"] = cron_expression

    if isinstance(task_update, dict):
        update_data = TaskUpdate(**task_update)
    else:
        update_data = task_update

        if update_data.frequency_settings:
            # 只有当频率类型不是自定义时才重新生成cron表达式
            if update_data.frequency_settings.frequency_type != FrequencyType.CUSTOM:
                cron_expression = frequency_to_cron(update_data.frequency_settings)
                if cron_expression:  # 确保生成的表达式有效
                    update_data.cron_expression = cron_expression

    existing_task = TaskManager.get_task_by_code(task_code)
    if not existing_task:
        raise HTTPException(status_code=404, detail=f"Task {task_code} not found")

    if update_data.task_type:
        valid_task_types = ['shell_script', 'python_script']
        if update_data.task_type not in valid_task_types:
            raise HTTPException(status_code=400, detail=f"Invalid task type: {update_data.task_type}. Valid types: {', '.join(valid_task_types)}")

    success = TaskManager.update_task(
        task_code=task_code,
        task_name=update_data.task_name,
        task_type=update_data.task_type,
        command=update_data.command,
        cron_expression=update_data.cron_expression,
        description=update_data.description,
        is_active=update_data.is_active
    )

    if not success:
        raise HTTPException(status_code=500, detail="Failed to update task")

    return TaskManager.get_task_by_code(task_code)

@router.delete("/{task_code}")
async def delete_task(task_code: str):
    """删除任务"""
    existing_task = TaskManager.get_task_by_code(task_code)
    if not existing_task:
        raise HTTPException(status_code=404, detail=f"Task {task_code} not found")

    success = TaskManager.delete_task(task_code)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete task")

    return {"message": f"Task {task_code} deleted successfully"}

@router.post("/{task_code}/pause")
async def pause_task(task_code: str):
    """暂停任务"""
    existing_task = TaskManager.get_task_by_code(task_code)
    if not existing_task:
        raise HTTPException(status_code=404, detail=f"Task {task_code} not found")

    success = TaskManager.update_task(task_code=task_code, is_active=0)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to pause task")

    return {"message": f"Task {task_code} paused successfully"}

@router.post("/{task_code}/resume")
async def resume_task(task_code: str):
    """恢复任务"""
    existing_task = TaskManager.get_task_by_code(task_code)
    if not existing_task:
        raise HTTPException(status_code=404, detail=f"Task {task_code} not found")

    success = TaskManager.update_task(task_code=task_code, is_active=1)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to resume task")

    return {"message": f"Task {task_code} resumed successfully"}

@router.post("/{task_code}/run_now")
async def run_task_now(task_code: str):
    """立即执行任务"""
    task = TaskManager.get_task_by_code(task_code)
    if not task:
        raise HTTPException(status_code=404, detail=f"Task {task_code} not found")

    try:
        task_type = task['task_type']
        command = task['command']
        task_name = task['task_name']

        if task_type == 'shell_script':
            execute_shell_script(command, task_code, task_name)
            TaskManager.update_last_run_time(task_code)
            return {"message": f"Task {task_code} executed successfully"}
        elif task_type == 'python_script':
            execute_python_script(command, task_code, task_name)
            TaskManager.update_last_run_time(task_code)
            return {"message": f"Task {task_code} executed successfully"}
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported task type: {task_type}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to execute task: {str(e)}")

@router.get("/frequency/options")
async def get_frequency_options():
    """获取支持的频率设置选项"""
    weekdays = [{"value": day.value, "label": day.name.capitalize()} for day in WeekDay]

    return {
        "frequency_types": [
            {"value": freq.value, "label": freq_to_label(freq)}
            for freq in FrequencyType
        ],
        "weekdays": weekdays,
        "hour_options": [{"value": i, "label": f"{i:02d}点"} for i in range(24)],
        "minute_options": [{"value": i, "label": f"{i:02d}分"} for i in range(0, 60, 5)],
        "day_options": [{"value": i, "label": f"{i}日"} for i in range(1, 32)]
    }

def freq_to_label(freq_type: FrequencyType) -> str:
    """将频率类型转换为友好的标签"""
    labels = {
        FrequencyType.MINUTES: "每X分钟",
        FrequencyType.HOURLY: "每X小时",
        FrequencyType.DAILY: "每天",
        FrequencyType.WEEKLY: "每周",
        FrequencyType.MONTHLY: "每月",
        FrequencyType.CUSTOM: "自定义表达式"
    }
    return labels.get(freq_type, str(freq_type))

# 厂房爬虫数据相关路由
class SpiderDataFileResponse(BaseModel):
    """爬虫数据文件响应模型"""
    files: List[str]
    count: int

@router.get("/factory/spider-data-files", response_model=SpiderDataFileResponse)
async def get_factory_spider_data_files(
    city: Optional[str] = None,
    area: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None
):
    """
    获取58同城厂房爬虫数据城市列表 (从数据库中获取)

    参数:
        city: 城市名称过滤 (可选)
        area: 区域过滤 (可选) - 注意：现在数据库中没有区域字段，此参数将被忽略
        start_time: 开始时间过滤，格式为 'YYYYMMDD_HHMMSS' (可选)
        end_time: 结束时间过滤，格式为 'YYYYMMDD_HHMMSS' (可选)

    返回:
        城市列表和总数
    """
    try:
        # 导入数据库连接模块
        from database.db_connection import get_db_connection
        
        # 构建SQL查询条件
        where_conditions = []
        params = []
        
        if city:
            where_conditions.append("city = %s")
            params.append(city)
        
        # 转换时间格式进行过滤（如果提供）
        if start_time:
            try:
                # 将 'YYYYMMDD_HHMMSS' 转换为 MySQL datetime 格式
                dt_parts = start_time.split('_')
                mysql_start_time = f"{dt_parts[0][:4]}-{dt_parts[0][4:6]}-{dt_parts[0][6:8]} {dt_parts[1][:2]}:{dt_parts[1][2:4]}:{dt_parts[1][4:6]}"
                where_conditions.append("created_at >= %s")
                params.append(mysql_start_time)
            except Exception as e:
                logging.error(f"开始时间格式转换错误: {e}")
        
        if end_time:
            try:
                # 将 'YYYYMMDD_HHMMSS' 转换为 MySQL datetime 格式
                dt_parts = end_time.split('_')
                mysql_end_time = f"{dt_parts[0][:4]}-{dt_parts[0][4:6]}-{dt_parts[0][6:8]} {dt_parts[1][:2]}:{dt_parts[1][2:4]}:{dt_parts[1][4:6]}"
                where_conditions.append("created_at <= %s")
                params.append(mysql_end_time)
            except Exception as e:
                logging.error(f"结束时间格式转换错误: {e}")
        
        # 构建完整的SQL查询
        sql_where = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        # 连接数据库
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 只查询不同的城市，不再按日期分组
        sql = f"""
            SELECT DISTINCT
                city
            FROM 
                property_listing
            {sql_where}
            ORDER BY 
                city
        """
        
        # 只有在有参数时才传递参数
        if params:
            cursor.execute(sql, params)
        else:
            cursor.execute(sql)
        
        results = cursor.fetchall()
        
        # 只返回城市名称列表
        cities = [row['city'] for row in results if row['city']]
        
        cursor.close()
        conn.close()
        
        return SpiderDataFileResponse(files=cities, count=len(cities))
    except Exception as e:
        logging.error(f"获取爬虫数据列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取爬虫数据列表失败: {str(e)}")

@router.get("/factory/spider-data-content/{city}")
async def get_factory_spider_data_content(
    city: str,
    type: Optional[PropertyType] = PropertyType.SALE,  # 默认展示出售
    is_managed: Optional[bool] = None,  # 筛选托管帖子
    date: Optional[str] = None,  # 可选日期参数，格式为YYYYMMDD
    timestamp: Optional[str] = None  # 可选时间戳参数，优先级高于date
):
    """
    获取58同城厂房爬虫数据内容 (从数据库中获取)

    参数:
        city: 城市名称
        type: 物业类型，可选值：rent(出租)、sale(出售)，默认为sale
        is_managed: 是否筛选托管帖子，True表示只返回托管帖子，False表示只返回非托管帖子，None表示不筛选
        date: 可选的日期过滤，格式为YYYYMMDD，默认为最新数据
        timestamp: 可选的时间戳过滤，直接根据task_id进行精确匹配，优先级高于date

    返回:
        查询结果的JSON数据，包含排名信息和是否为平台托管的标记
    """
    try:
        # 导入数据库连接模块
        from database.db_connection import get_db_connection
        
        # 设置过滤关键词，根据请求的类型确定
        type_filter = "出租" if type == PropertyType.RENT else "出售"
        
        # 连接数据库
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 构建SQL查询
        where_conditions = ["city = %s", "type = %s"]
        params = [city, type_filter]
        
        # 优先使用时间戳进行精确匹配
        if timestamp:
            where_conditions.append("task_id = %s")
            params.append(timestamp)
        # 如果没有提供时间戳但提供了日期，则使用日期匹配
        elif date and len(date) == 8:  # 确保日期格式为YYYYMMDD
            try:
                formatted_date = f"{date[:4]}-{date[4:6]}-{date[6:8]}"
                where_conditions.append("DATE(created_at) = %s")
                params.append(formatted_date)
            except Exception as e:
                logging.error(f"日期格式转换错误: {e}")
        
        # 构建完整的WHERE子句
        where_clause = " AND ".join(where_conditions)
        
        # 完整SQL查询，添加ranking字段
        sql = f"""
            SELECT 
                id, type, title, url, price, building_area, 
                publish_time, image_url, agent_name, agent_company,
                created_at, city, ranking, task_id
            FROM 
                property_listing
            WHERE 
                {where_clause}
            ORDER BY
                ranking ASC, created_at DESC
        """
        
        cursor.execute(sql, params)
        results = cursor.fetchall()
        
        # 构建返回数据列表
        data_list = []
        for row in results:
            item = {
                "id": row['id'],
                "type": row['type'],
                "title": row['title'],
                "url": row['url'],
                "price": row['price'],
                "building_area": float(row['building_area']) if row['building_area'] else None,
                "publish_time": row['publish_time'],
                "image_url": row['image_url'],
                "agent_name": row['agent_name'],
                "agent_company": row['agent_company'],
                "city": row['city'],
                "created_at": row['created_at'].strftime('%Y-%m-%d %H:%M:%S') if row['created_at'] else None,
                "ranking": row['ranking'],  # 使用数据库中的ranking字段
                "rank": row['ranking'] if row['ranking'] else None,  # 保持与原有rank字段兼容
                "task_id": row['task_id']  # 添加task_id字段以便于前端根据时间戳过滤
            }
            data_list.append(item)
        
        # 如果有匹配到的数据，与数据库数据匹配标记是否为平台托管的
        if data_list:
            # 提取所有销售名称用于批量查询
            all_agent_names = [item.get("agent_name", "") for item in data_list if item.get("agent_name")]
            
            if all_agent_names:
                # 创建一个销售名称到是否托管的映射
                agent_name_is_managed_map = {}
                
                try:
                    # 构建参数化查询，一次性查询所有销售名称
                    placeholders = ', '.join(['%s'] * len(all_agent_names))
                    sql = f"SELECT name FROM account_info WHERE name IN ({placeholders})"
                    
                    # 只有在有销售名称时才执行查询
                    if all_agent_names:
                        cursor.execute(sql, all_agent_names)
                        
                        # 获取所有匹配的销售名称
                        managed_agent_names = {row['name'] for row in cursor.fetchall()}
                        
                        # 创建映射
                        for agent_name in all_agent_names:
                            agent_name_is_managed_map[agent_name] = agent_name in managed_agent_names
                    else:
                        # 无销售名称时创建空集合
                        managed_agent_names = set()
                    
                    # 为每条数据添加是否为平台托管的标记
                    for item in data_list:
                        agent_name = item.get("agent_name", "")
                        agent_company = item.get("agent_company", "")
                        # 如果agent_company包含"联东"字样，视为托管帖子
                        is_managed_by_company = "联东" in agent_company if agent_company else False
                        # 保留原有的销售名称判断逻辑，如果销售名称在托管列表或公司名称含"联东"，都视为托管
                        item["is_managed"] = agent_name_is_managed_map.get(agent_name, False) or is_managed_by_company
                        
                except Exception as e:
                    logging.error(f"查询托管数据失败: {str(e)}")
                    # 查询失败时，仍然检查agent_company是否包含"联东"
                    for item in data_list:
                        agent_company = item.get("agent_company", "")
                        item["is_managed"] = "联东" in agent_company if agent_company else False
            else:
                # 没有销售名称时，仍然通过agent_company判断是否托管
                for item in data_list:
                    agent_company = item.get("agent_company", "")
                    item["is_managed"] = "联东" in agent_company if agent_company else False
                    
        # 根据is_managed参数对数据进行筛选
        if is_managed is not None:
            data_list = [item for item in data_list if item.get("is_managed", False) == is_managed]
        
        cursor.close()
        conn.close()
        
        content = json.dumps(data_list, ensure_ascii=False)
        # 返回JSON响应
        return Response(content=content, media_type="application/json")
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"获取爬虫数据内容失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取爬虫数据内容失败: {str(e)}")

@router.get("/factory/spider-data-dates/{city}")
async def get_factory_spider_data_dates(
    city: str,
    type: Optional[PropertyType] = PropertyType.SALE  # 默认查询出售类型
):
    """
    获取指定城市的爬虫数据可用日期和时间列表
    
    参数:
        city: 城市名称
        type: 物业类型，可选值：rent(出租)、sale(出售)，默认为sale
    
    返回:
        可用日期时间列表，按时间戳降序排序
    """
    try:
        # 导入数据库连接模块
        from database.db_connection import get_db_connection
        import time
        from datetime import datetime
        
        # 设置过滤关键词，根据请求的类型确定
        type_filter = "出租" if type == PropertyType.RENT else "出售"
        
        # 连接数据库
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询不同的task_id，按降序排列，确保最新日期在前面
        sql = """
            SELECT DISTINCT task_id
            FROM property_listing
            WHERE city = %s AND type = %s AND task_id IS NOT NULL AND task_id != ''
            ORDER BY task_id DESC
        """
        
        cursor.execute(sql, [city, type_filter])
        results = cursor.fetchall()
        
        # 提取时间戳和完整日期时间信息
        time_list = []
        for row in results:
            task_id = row['task_id']
            if task_id and task_id.isdigit():
                try:
                    # 保存原始时间戳
                    timestamp = int(task_id)
                    
                    # 转换为日期时间对象
                    date_obj = datetime.fromtimestamp(timestamp)
                    
                    # 同时保存日期和完整格式化时间
                    date_str = date_obj.strftime('%Y%m%d')
                    datetime_str = date_obj.strftime('%Y-%m-%d %H:%M:%S')
                    
                    # 添加到结果列表，确保不会有重复的时间戳
                    if not any(item["timestamp"] == timestamp for item in time_list):
                        time_list.append({
                            "timestamp": timestamp,
                            "task_id": task_id,
                            "date": date_str,
                            "datetime": datetime_str
                        })
                except (ValueError, OverflowError) as e:
                    logging.warning(f"无法将task_id转换为日期时间，task_id: {task_id}, 错误: {str(e)}")
                    continue
        
        cursor.close()
        conn.close()
        
        return {
            "times": time_list,
            "count": len(time_list)
        }
    except Exception as e:
        logging.error(f"获取爬虫数据时间列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取爬虫数据时间列表失败: {str(e)}")