#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.pool import Thread<PERSON>oolExecutor
from apscheduler.triggers.cron import CronTrigger
import importlib
import subprocess
import sys
import os
from datetime import datetime

# 导入数据库连接信息
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.settings import DB_CONFIG

# 创建日志记录器（使用全局日志配置）
logger = logging.getLogger("scheduler")

class TaskScheduler:
    """定时任务调度器"""
    
    def __init__(self, use_memory_jobstore=False):
        """初始化调度器"""
        # 配置作业存储和执行器
        if use_memory_jobstore:
            # 使用内存存储(系统重启后任务状态会丢失，但不影响数据库中的任务定义)
            jobstores = {
                'default': MemoryJobStore()
            }
            logger.info("使用内存作为任务存储")
        else:
            # 使用MySQL存储(与系统其他部分使用同一数据库)
            # 构建MySQL连接URL
            db_url = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"
            
            # 创建调度器表名
            jobstores = {
                'default': SQLAlchemyJobStore(url=db_url, tablename='apscheduler_jobs')
            }
            logger.info(f"使用MySQL作为任务存储: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
            
        executors = {
            'default': ThreadPoolExecutor(20)
        }
        job_defaults = {
            'coalesce': False,
            'max_instances': 3
        }
        
        # 创建后台调度器
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults
        )
        
        # 设置项目根目录
        self.root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
    def start(self):
        """启动调度器"""
        try:
            self.scheduler.start()
            logger.info("调度器已启动")
        except Exception as e:
            logger.error(f"启动调度器失败: {str(e)}")
            
    def shutdown(self):
        """关闭调度器"""
        self.scheduler.shutdown()
        logger.info("调度器已关闭")
    
    def add_job(self, task_code, task_type, command, cron_expression, task_name=None, description=None):
        """
        添加定时任务
        
        参数:
            task_code: 任务代码
            task_type: 任务类型(python_script/api/function/shell_script)
            command: 执行命令或路径
            cron_expression: cron表达式
            task_name: 任务名称
            description: 任务描述
        """
        try:
            # 根据任务类型选择执行方法
            if task_type == 'python_script':
                job_func = self._run_script
                job_args = [command]
            elif task_type == 'function':
                # 动态导入函数
                module_path, func_name = command.rsplit('.', 1)
                module = importlib.import_module(module_path)
                job_func = getattr(module, func_name)
                job_args = []
            elif task_type == 'api':
                job_func = self._call_api
                job_args = [command]
            elif task_type == 'shell_script':
                job_func = self._run_shell_script
                job_args = [command]
            else:
                raise ValueError(f"不支持的任务类型: {task_type}")
            
            # 解析cron表达式
            trigger = CronTrigger.from_crontab(cron_expression)
            
            # 添加任务
            job = self.scheduler.add_job(
                job_func,
                trigger=trigger,
                args=job_args,
                id=task_code,
                name=task_name or task_code,
                replace_existing=True
            )
            
            logger.info(f"成功添加定时任务: {task_code}, 下次执行时间: {job.next_run_time}")
            return True
            
        except Exception as e:
            logger.error(f"添加定时任务 {task_code} 失败: {str(e)}")
            return False
    
    def remove_job(self, task_code):
        """
        移除定时任务
        
        参数:
            task_code: 任务代码
        """
        try:
            self.scheduler.remove_job(task_code)
            logger.info(f"成功移除定时任务: {task_code}")
            return True
        except Exception as e:
            logger.error(f"移除定时任务 {task_code} 失败: {str(e)}")
            return False
    
    def pause_job(self, task_code):
        """暂停定时任务"""
        try:
            self.scheduler.pause_job(task_code)
            logger.info(f"成功暂停定时任务: {task_code}")
            return True
        except Exception as e:
            logger.error(f"暂停定时任务 {task_code} 失败: {str(e)}")
            return False
    
    def resume_job(self, task_code):
        """恢复定时任务"""
        try:
            self.scheduler.resume_job(task_code)
            logger.info(f"成功恢复定时任务: {task_code}")
            return True
        except Exception as e:
            logger.error(f"恢复定时任务 {task_code} 失败: {str(e)}")
            return False
    
    def get_jobs(self):
        """获取所有任务"""
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.strftime('%Y-%m-%d %H:%M:%S') if job.next_run_time else None
            })
        return jobs
    
    def _run_script(self, script_path):
        """执行Python脚本"""
        try:
            # 构建脚本的完整路径
            full_path = os.path.join(self.root_dir, script_path)
            logger.info(f"开始执行脚本: {full_path}")
            
            # 执行脚本
            result = subprocess.run(
                [sys.executable, full_path],
                capture_output=True, 
                text=True
            )
            
            if result.returncode == 0:
                logger.info(f"脚本 {script_path} 执行成功")
                logger.debug(f"输出: {result.stdout}")
            else:
                logger.error(f"脚本 {script_path} 执行失败")
                logger.error(f"错误: {result.stderr}")
                
        except Exception as e:
            logger.error(f"执行脚本 {script_path} 出错: {str(e)}")
    
    def _run_shell_script(self, script_path, task_code=None):
        """执行Shell脚本"""
        try:
            # 任务开始时间
            start_time = datetime.now()
            
            # 构建脚本的完整路径
            full_path = os.path.join(self.root_dir, script_path)
            logger.info(f"开始执行Shell脚本: {full_path}")
            
            # 确保脚本有执行权限
            os.chmod(full_path, 0o755)
            
            # 执行脚本
            result = subprocess.run(
                ["/bin/bash", full_path],
                capture_output=True, 
                text=True
            )
            
            # 更新任务的最后执行时间
            if task_code:
                try:
                    from tasks.task_manager import TaskManager
                    TaskManager.update_last_run_time(task_code)
                except Exception as e:
                    logger.error(f"更新任务执行时间失败: {str(e)}")
            
            if result.returncode == 0:
                logger.info(f"Shell脚本 {script_path} 执行成功")
                logger.debug(f"输出: {result.stdout}")
            else:
                logger.error(f"Shell脚本 {script_path} 执行失败")
                logger.error(f"错误: {result.stderr}")
                
        except Exception as e:
            logger.error(f"执行Shell脚本 {script_path} 出错: {str(e)}")
    
    def _call_api(self, api_url):
        """调用API接口"""
        import requests
        try:
            logger.info(f"开始调用API: {api_url}")
            response = requests.get(api_url, timeout=60)
            
            if response.status_code == 200:
                logger.info(f"API调用成功: {api_url}")
                logger.debug(f"返回: {response.text[:100]}")
            else:
                logger.error(f"API调用失败: {api_url}, 状态码: {response.status_code}")
                
        except Exception as e:
            logger.error(f"调用API {api_url} 出错: {str(e)}")

# 创建全局调度器实例
# 如果要使用内存存储而非MySQL，将参数设为True
scheduler = TaskScheduler(use_memory_jobstore=False) 