#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from tasks.scheduler import scheduler
from tasks.task_manager import TaskManager

logger = logging.getLogger("tasks")

def init_scheduler():
    """初始化定时任务系统"""
    try:
        # 从数据库加载所有激活状态的任务到调度器
        TaskManager.load_all_active_tasks()
        
        # 启动调度器
        scheduler.start()
        
        logger.info("定时任务系统已初始化并启动")
        return True
    except Exception as e:
        logger.error(f"初始化定时任务系统失败: {str(e)}")
        return False 