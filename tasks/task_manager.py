#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import subprocess
import sys
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

# 导入数据库连接
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database.db_connection import db_cursor

# 导入调度器
from tasks.scheduler import scheduler

logger = logging.getLogger("task_manager")

# 定义独立的执行函数
def execute_shell_script(script_path, task_code=None, task_name=None):
    """执行Shell脚本"""
    try:
        root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        full_path = os.path.join(root_dir, script_path)

        # 创建日志目录
        logs_dir = os.path.join(root_dir, 'scripts', 'logs')
        os.makedirs(logs_dir, exist_ok=True)

        # 创建日志文件，优先使用任务名称，每次执行创建新文件
        from datetime import datetime
        start_time = datetime.now()
        timestamp = start_time.strftime('%Y%m%d_%H%M%S')

        if task_name:
            # 清理任务名称中的特殊字符，确保文件名安全
            safe_task_name = "".join(c for c in task_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_task_name = safe_task_name.replace(' ', '_')
            log_file = os.path.join(logs_dir, f"{safe_task_name}_{timestamp}.log")
        elif task_code:
            log_file = os.path.join(logs_dir, f"{task_code}_{timestamp}.log")
        else:
            script_name = os.path.splitext(os.path.basename(script_path))[0]
            log_file = os.path.join(logs_dir, f"{script_name}_{timestamp}.log")

        # 记录任务开始
        start_msg = f"[{start_time.strftime('%Y-%m-%d %H:%M:%S')}] 开始执行Shell脚本: {script_path}\n"

        # 写入日志文件（新建模式）
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"{'='*60}\n")
            f.write(start_msg)
            f.write(f"{'='*60}\n")

        # 打印到控制台
        print(start_msg.strip())

        os.chmod(full_path, 0o755)

        # 执行脚本，实时输出到控制台和日志文件
        process = subprocess.Popen(
            ["/bin/bash", full_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        # 实时读取输出并写入日志文件和控制台
        end_msg = ""
        try:
            with open(log_file, 'a', encoding='utf-8') as log_f:
                for line in iter(process.stdout.readline, ''):
                    if line:
                        # 添加时间戳
                        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        log_line = f"[{current_time}] {line}"

                        # 实时写入日志文件
                        log_f.write(log_line)
                        log_f.flush()  # 强制刷新到磁盘

                        # 输出到控制台
                        print(line.rstrip())

                # 等待进程完成
                process.wait()

                # 记录任务结束
                end_time = datetime.now()
                duration = end_time - start_time
                end_msg = f"[{end_time.strftime('%Y-%m-%d %H:%M:%S')}] Shell脚本执行完成，耗时: {duration}"

                # 写入结束日志
                log_f.write(end_msg + "\n")
                log_f.write(f"{'='*60}\n")
                log_f.flush()

        except Exception as log_error:
            print(f"日志写入错误: {log_error}")
            end_msg = f"Shell脚本执行出现日志错误: {log_error}"

        if end_msg:
            print(end_msg)

        if task_code:
            TaskManager.update_last_run_time(task_code)

    except Exception as e:
        logger.error(f"执行Shell脚本出错: {str(e)}")
        print(f"[ERROR] 执行Shell脚本出错: {str(e)}")

def execute_python_script(script_path, task_code=None, task_name=None):
    """执行Python脚本"""
    try:
        root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # 检测命令格式：支持完整Python命令或脚本路径
        if script_path.strip().startswith('python '):
            # 完整命令格式：python script.py args
            import shlex
            command_parts = shlex.split(script_path.strip())
            # 将python替换为完整的python解释器路径
            command_parts[0] = sys.executable
            # 如果脚本路径是相对路径，转换为绝对路径
            if len(command_parts) > 1 and not os.path.isabs(command_parts[1]):
                command_parts[1] = os.path.join(root_dir, command_parts[1])
            full_command = command_parts
            script_display_name = script_path  # 用于日志显示
        else:
            # 脚本路径格式：scripts/example.py
            full_path = os.path.join(root_dir, script_path)
            full_command = [sys.executable, full_path]
            script_display_name = script_path

        # 创建日志目录
        logs_dir = os.path.join(root_dir, 'scripts', 'logs')
        os.makedirs(logs_dir, exist_ok=True)

        # 创建日志文件，优先使用任务名称，每次执行创建新文件
        from datetime import datetime
        start_time = datetime.now()
        timestamp = start_time.strftime('%Y%m%d_%H%M%S')

        if task_name:
            # 清理任务名称中的特殊字符，确保文件名安全
            safe_task_name = "".join(c for c in task_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_task_name = safe_task_name.replace(' ', '_')
            log_file = os.path.join(logs_dir, f"{safe_task_name}_{timestamp}.log")
        elif task_code:
            log_file = os.path.join(logs_dir, f"{task_code}_{timestamp}.log")
        else:
            script_name = os.path.splitext(os.path.basename(script_path))[0]
            log_file = os.path.join(logs_dir, f"{script_name}_{timestamp}.log")

        # 记录任务开始
        start_msg = f"[{start_time.strftime('%Y-%m-%d %H:%M:%S')}] 开始执行Python脚本: {script_display_name}\n"

        # 写入日志文件（新建模式）
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"{'='*60}\n")
            f.write(start_msg)
            f.write(f"{'='*60}\n")

        # 打印到控制台
        print(start_msg.strip())

        # 执行脚本，实时输出到控制台和日志文件
        process = subprocess.Popen(
            full_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True,
            cwd=root_dir  # 设置工作目录为项目根目录
        )

        # 实时读取输出并写入日志文件和控制台
        end_msg = ""
        try:
            with open(log_file, 'a', encoding='utf-8') as log_f:
                for line in iter(process.stdout.readline, ''):
                    if line:
                        # 添加时间戳
                        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        log_line = f"[{current_time}] {line}"

                        # 实时写入日志文件
                        log_f.write(log_line)
                        log_f.flush()  # 强制刷新到磁盘

                        # 输出到控制台
                        print(line.rstrip())

                # 等待进程完成
                process.wait()

                # 记录任务结束
                end_time = datetime.now()
                duration = end_time - start_time
                end_msg = f"[{end_time.strftime('%Y-%m-%d %H:%M:%S')}] Python脚本执行完成，耗时: {duration}"

                # 写入结束日志
                log_f.write(end_msg + "\n")
                log_f.write(f"{'='*60}\n")
                log_f.flush()

        except Exception as log_error:
            print(f"日志写入错误: {log_error}")
            end_msg = f"Python脚本执行出现日志错误: {log_error}"

        if end_msg:
            print(end_msg)

        if task_code:
            TaskManager.update_last_run_time(task_code)

    except Exception as e:
        logger.error(f"执行Python脚本出错: {str(e)}")
        print(f"[ERROR] 执行Python脚本出错: {str(e)}")

class TaskManager:
    """定时任务管理器"""
    
    @staticmethod
    def get_all_tasks() -> List[Dict[str, Any]]:
        """获取所有定时任务"""
        try:
            with db_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM scheduled_tasks
                    ORDER BY create_time DESC
                """)
                tasks = cursor.fetchall()
                
                from tasks.scheduler import scheduler
                scheduler_jobs = {job.id: job for job in scheduler.scheduler.get_jobs()}

                for task in tasks:
                    task_code = task['task_code']
                    if task_code in scheduler_jobs and task['is_active'] == 1:
                        job = scheduler_jobs[task_code]
                        if job.next_run_time:
                            task['next_run_time'] = job.next_run_time
                
                return tasks
        except Exception as e:
            logger.error(f"获取所有任务失败: {str(e)}")
            return []
    
    @staticmethod
    def get_task_by_code(task_code: str) -> Optional[Dict[str, Any]]:
        """根据任务代码获取任务"""
        try:
            with db_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM scheduled_tasks
                    WHERE task_code = %s
                """, [task_code])
                task = cursor.fetchone()
                
                if task and task['is_active'] == 1:
                    from tasks.scheduler import scheduler
                    try:
                        job = scheduler.scheduler.get_job(task_code)
                        if job and job.next_run_time:
                            task['next_run_time'] = job.next_run_time
                    except Exception:
                        pass
                
                return task
        except Exception as e:
            logger.error(f"获取任务 {task_code} 失败: {str(e)}")
            return None
    
    @staticmethod
    def create_task(task_name: str, task_code: str, task_type: str, 
                   command: str, cron_expression: str, description: str = None) -> bool:
        """创建新任务"""
        try:
            with db_cursor() as cursor:
                cursor.execute("""
                    INSERT INTO scheduled_tasks (
                        task_name, task_code, task_type, command,
                        cron_expression, description, is_active
                    ) VALUES (%s, %s, %s, %s, %s, %s, 1)
                """, [
                    task_name, task_code, task_type, command,
                    cron_expression, description
                ])

                success = TaskManager._add_job_to_scheduler(
                    task_code=task_code,
                    task_type=task_type,
                    command=command,
                    cron_expression=cron_expression,
                    task_name=task_name
                )
                
                return success
        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            return False
    
    @staticmethod
    def update_task(task_code: str, task_name: str = None, 
                   task_type: str = None, command: str = None, 
                   cron_expression: str = None, description: str = None,
                   is_active: int = None) -> bool:
        """更新任务"""
        try:
            task = TaskManager.get_task_by_code(task_code)
            if not task:
                logger.error(f"任务 {task_code} 不存在")
                return False

            updates = {}
            if task_name is not None:
                updates['task_name'] = task_name
            if task_type is not None:
                updates['task_type'] = task_type
            if command is not None:
                updates['command'] = command
            if cron_expression is not None:
                updates['cron_expression'] = cron_expression
            if description is not None:
                updates['description'] = description
            if is_active is not None:
                updates['is_active'] = is_active

            if not updates:
                return True

            with db_cursor() as cursor:
                update_fields = ", ".join([f"{k} = %s" for k in updates.keys()])
                query = f"UPDATE scheduled_tasks SET {update_fields} WHERE task_code = %s"
                
                values = list(updates.values())
                values.append(task_code)
                
                cursor.execute(query, values)
            
            if is_active is not None:
                if is_active == 1:
                    actual_task_type = updates.get('task_type', task['task_type'])
                    actual_command = updates.get('command', task['command'])
                    actual_cron = updates.get('cron_expression', task['cron_expression'])
                    actual_name = updates.get('task_name', task['task_name'])

                    TaskManager._add_job_to_scheduler(
                        task_code=task_code,
                        task_type=actual_task_type,
                        command=actual_command,
                        cron_expression=actual_cron,
                        task_name=actual_name
                    )
                else:
                    try:
                        scheduler.scheduler.remove_job(task_code)
                    except:
                        pass

            elif cron_expression is not None or command is not None or task_type is not None:
                if task['is_active'] == 1:
                    actual_task_type = updates.get('task_type', task['task_type'])
                    actual_command = updates.get('command', task['command'])
                    actual_cron = updates.get('cron_expression', task['cron_expression'])
                    actual_name = updates.get('task_name', task['task_name'])

                    TaskManager._add_job_to_scheduler(
                        task_code=task_code,
                        task_type=actual_task_type,
                        command=actual_command,
                        cron_expression=actual_cron,
                        task_name=actual_name
                    )
            
            return True
        except Exception as e:
            logger.error(f"更新任务 {task_code} 失败: {str(e)}")
            return False
    
    @staticmethod
    def delete_task(task_code: str) -> bool:
        """删除任务"""
        try:
            with db_cursor() as cursor:
                cursor.execute("DELETE FROM scheduled_tasks WHERE task_code = %s", [task_code])

            try:
                scheduler.scheduler.remove_job(task_code)
            except:
                pass
            
            return True
        except Exception as e:
            logger.error(f"删除任务 {task_code} 失败: {str(e)}")
            return False
    
    @staticmethod
    def update_last_run_time(task_code: str) -> bool:
        """更新上次运行时间"""
        try:
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            with db_cursor() as cursor:
                cursor.execute("""
                    UPDATE scheduled_tasks 
                    SET last_run_time = %s
                    WHERE task_code = %s
                """, [now, task_code])
            return True
        except Exception as e:
            logger.error(f"更新任务 {task_code} 运行时间失败: {str(e)}")
            return False
    
    @staticmethod
    def load_all_active_tasks():
        """从数据库加载所有激活状态的任务到调度器"""
        try:
            with db_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM scheduled_tasks
                    WHERE is_active = 1
                """)
                tasks = cursor.fetchall()

                for task in tasks:
                    TaskManager._add_job_to_scheduler(
                        task_code=task['task_code'],
                        task_type=task['task_type'],
                        command=task['command'],
                        cron_expression=task['cron_expression'],
                        task_name=task['task_name']
                    )
                
                return True
        except Exception as e:
            logger.error(f"加载任务失败: {str(e)}")
            return False
            
    @staticmethod
    def _add_job_to_scheduler(task_code, task_type, command, cron_expression, task_name=None):
        """添加任务到调度器"""
        try:
            scheduler.scheduler.remove_job(task_code)
        except:
            pass

        try:

            from apscheduler.triggers.cron import CronTrigger
            trigger = CronTrigger.from_crontab(cron_expression)

            if task_type == 'shell_script':
                scheduler.scheduler.add_job(
                    execute_shell_script,
                    trigger=trigger,
                    args=[command, task_code, task_name],
                    id=task_code,
                    name=task_name or task_code,
                    replace_existing=True
                )
                return True
            elif task_type == 'python_script':
                scheduler.scheduler.add_job(
                    execute_python_script,
                    trigger=trigger,
                    args=[command, task_code, task_name],
                    id=task_code,
                    name=task_name or task_code,
                    replace_existing=True
                )
                return True
            else:
                return False

        except Exception as e:
            logger.error(f"添加任务到调度器失败: {str(e)}")
            return False