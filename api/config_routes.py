#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理API路由
"""

import os
import json
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Body, Path
from pydantic import BaseModel, Field
from pathlib import Path as FilePath

router = APIRouter(prefix="/config", tags=["配置管理"])

# 获取cities.json的绝对路径
cities_json_path = FilePath(__file__).parent.parent / "config" / "cities.json"

# 定义请求和响应模型
class DomainResponse(BaseModel):
    domains: Dict[str, str]
    names: Dict[str, str]

class AccountsResponse(BaseModel):
    accounts: Dict[str, Dict[str, str]]

class CityRequest(BaseModel):
    code: str = Field(..., description="城市代码，如'bj'、'cc'等")
    name: str = Field(..., description="城市名称，如'北京'、'长春'等")
    domain: str = Field(..., description="城市域名，如'bj.youtui01.com'")
    user_id: str = Field(..., description="用户ID")
    user_key: str = Field(..., description="用户密钥")

class CityUpdateRequest(BaseModel):
    name: Optional[str] = Field(None, description="城市名称，如'北京'、'长春'等")
    domain: Optional[str] = Field(None, description="城市域名，如'bj.youtui01.com'")
    user_id: Optional[str] = Field(None, description="用户ID")
    user_key: Optional[str] = Field(None, description="用户密钥")

class CityResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

# 通用函数：从JSON文件加载配置
def load_config():
    try:
        if not cities_json_path.exists():
            # 如果配置文件不存在，创建一个空配置
            default_config = {
                "domains": {},
                "names": {},
                "accounts": {}
            }
            with open(cities_json_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            return default_config
        
        with open(cities_json_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"加载配置文件失败: {str(e)}")

# 更新JSON配置文件
def save_config(config):
    try:
        with open(cities_json_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存配置文件失败: {str(e)}")

@router.get("/domains", response_model=DomainResponse)
async def get_domains():
    """获取所有城市的域名配置和名称映射"""
    config = load_config()
    
    return {
        "domains": config.get("domains", {}),
        "names": config.get("names", {})
    }

@router.get("/accounts", response_model=AccountsResponse)
async def get_accounts():
    """获取所有城市的账号配置"""
    config = load_config()
    return {
        "accounts": config.get("accounts", {})
    }

@router.post("/cities", response_model=CityResponse)
async def add_city(city: CityRequest):
    """添加新城市配置"""
    config = load_config()
    
    # 检查城市代码是否已存在
    if city.code in config.get("domains", {}):
        raise HTTPException(status_code=400, detail=f"城市代码 '{city.code}' 已存在")
    
    # 更新配置
    if "domains" not in config:
        config["domains"] = {}
    config["domains"][city.code] = city.domain
    
    if "names" not in config:
        config["names"] = {}
    config["names"][city.code] = city.name
    
    if "accounts" not in config:
        config["accounts"] = {}
    config["accounts"][city.code] = {
        "user_id": city.user_id,
        "user_key": city.user_key
    }
    
    # 保存配置
    save_config(config)
    
    return {
        "success": True,
        "message": f"成功添加城市 '{city.name}({city.code})'",
        "data": {
            "code": city.code,
            "name": city.name,
            "domain": city.domain,
            "user_id": city.user_id,
            "user_key": city.user_key
        }
    }

@router.put("/cities/{city_code}", response_model=CityResponse)
async def update_city(
    city_code: str = Path(..., description="城市代码"),
    city_data: CityUpdateRequest = Body(...)
):
    """更新城市配置"""
    config = load_config()
    
    # 检查城市代码是否存在
    if city_code not in config.get("domains", {}):
        raise HTTPException(status_code=404, detail=f"城市代码 '{city_code}' 不存在")
    
    # 准备更新数据
    if city_data.domain is not None:
        config["domains"][city_code] = city_data.domain
    
    if city_data.name is not None:
        config["names"][city_code] = city_data.name
    
    # 更新账号信息
    if city_code not in config.get("accounts", {}):
        config["accounts"][city_code] = {}
    
    if city_data.user_id is not None:
        config["accounts"][city_code]["user_id"] = city_data.user_id
    
    if city_data.user_key is not None:
        config["accounts"][city_code]["user_key"] = city_data.user_key
    
    # 保存配置
    save_config(config)
    
    # 获取更新后的城市数据
    updated_data = {
        "code": city_code,
        "name": config["names"].get(city_code, ""),
        "domain": config["domains"].get(city_code, ""),
        "user_id": config["accounts"].get(city_code, {}).get("user_id", ""),
        "user_key": config["accounts"].get(city_code, {}).get("user_key", "")
    }
    
    return {
        "success": True,
        "message": f"成功更新城市 '{updated_data['name']}({city_code})'",
        "data": updated_data
    }

@router.delete("/cities/{city_code}", response_model=CityResponse)
async def delete_city(city_code: str = Path(..., description="城市代码")):
    """删除城市配置"""
    config = load_config()
    
    # 检查城市代码是否存在
    if city_code not in config.get("domains", {}):
        raise HTTPException(status_code=404, detail=f"城市代码 '{city_code}' 不存在")
    
    # 删除城市配置
    city_name = config["names"].get(city_code, city_code)
    
    if city_code in config.get("domains", {}):
        del config["domains"][city_code]
    
    if city_code in config.get("names", {}):
        del config["names"][city_code]
    
    if city_code in config.get("accounts", {}):
        del config["accounts"][city_code]
    
    # 保存配置
    save_config(config)
    
    return {
        "success": True,
        "message": f"成功删除城市 '{city_name}({city_code})'",
        "data": None
    } 