#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
发布统计相关 API 路由
"""

import logging
from fastapi import APIRouter, HTTPException, Request, Query
from database.db_connection import db_cursor
from services.push_service import PushService
import json
import math

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["Statistics"]
)

def calculate_pagination_info(total_records: int, current_page: int, page_size: int) -> dict:
    """
    计算分页信息

    Args:
        total_records: 总记录数
        current_page: 当前页码
        page_size: 每页记录数

    Returns:
        包含分页信息的字典
    """
    total_pages = math.ceil(total_records / page_size) if total_records > 0 else 1
    has_next_page = current_page < total_pages
    has_prev_page = current_page > 1

    return {
        "current_page": current_page,
        "total_pages": total_pages,
        "total_records": total_records,
        "has_next_page": has_next_page,
        "has_prev_page": has_prev_page
    }

def update_single_push_status(record_id: int, process_id: str, city: str):
    """直接调用服务获取单个推送状态并更新数据库"""
    if not process_id or not city:
        logger.warning(f"记录ID {record_id} 缺少 process_id 或 city，无法更新状态")
        return

    new_final_status = None

    try:
        service = PushService(city=city)
        push_info_result = service.get_push_info(process_id=process_id)

        if push_info_result:
            if isinstance(push_info_result, list) and push_info_result:
                 status_info = push_info_result[0]
            elif isinstance(push_info_result, dict):
                status_info = push_info_result
            else:
                status_info = None

            if status_info and isinstance(status_info, dict):
                status = status_info.get('status')
                if status and isinstance(status, str):
                    status_lower = status.lower()
                    if '成功' in status_lower or '已上架' in status_lower:
                        new_final_status = 1
                    elif '失败' in status_lower or '错误' in status_lower or '已下架' in status_lower:
                        new_final_status = 2
                else:
                    logger.warning(f"记录ID {record_id} 的 get_push_info 返回值中缺少 status 字段")
            else:
                logger.warning(f"记录ID {record_id} 的 get_push_info 返回值格式不正确")
        else:
             logger.warning(f"调用 service.get_push_info 失败 (记录ID: {record_id})")

    except Exception as e:
        logger.error(f"处理记录ID {record_id} 时发生错误: {str(e)}")

    if new_final_status is not None:
        try:
            with db_cursor() as cursor:
                sql = "UPDATE push_history SET final_status = %s WHERE id = %s AND final_status = 0"
                cursor.execute(sql, (new_final_status, record_id))
        except Exception as e:
            logger.error(f"更新数据库失败 (记录ID: {record_id}): {str(e)}")

@router.get("/publish-statistics")
async def get_publish_statistics(
    success_page: int = Query(1, ge=1, description="成功记录页码，从1开始"),
    failure_page: int = Query(1, ge=1, description="失败记录页码，从1开始")
):
    """
    更新待处理推送记录的状态，然后获取发布成功/失败统计及失败记录列表

    参数:
    - success_page: 成功记录页码，从1开始，每页10条
    - failure_page: 失败记录页码，从1开始，每页10条

    返回:
    - success_count: 成功推送的数量 (每个园区只计算最新记录)
    - failure_count: 失败推送的数量 (每个园区只计算最新记录)
    - successful_records: 成功推送记录的分页数据
    - failed_records: 失败推送记录的分页数据
    """
    # 分页配置
    page_size = 10

    try:
        with db_cursor() as cursor:
            sql_select_pending = "SELECT id, process_id, city FROM push_history WHERE final_status = 0"
            cursor.execute(sql_select_pending)
            pending_records = cursor.fetchall()

        if pending_records:
            for record in pending_records:
                if record and 'id' in record and 'process_id' in record and 'city' in record:
                     update_single_push_status(record['id'], record['process_id'], record['city'])
                else:
                     logger.warning(f"查询到的待更新记录格式不正确: {record}")

    except Exception as e:
        logger.error(f"查询或更新推送状态过程中发生错误: {str(e)}")

    success_count = 0
    failure_count = 0
    successful_records_data = []
    failed_records_data = []

    try:
        with db_cursor() as cursor:
            success_offset = (success_page - 1) * page_size
            failure_offset = (failure_page - 1) * page_size

            sql_combined = """
                WITH latest_records AS (
                    SELECT
                        ph.id, ph.push_time, ph.city, ph.website_name,
                        ph.erp_house_id, ph.final_status, ph.process_id,
                        pfd.community,
                        ROW_NUMBER() OVER (
                            PARTITION BY pfd.community
                            ORDER BY ph.id DESC
                        ) as rn
                    FROM
                        push_history ph
                    JOIN
                        parsed_factory_data pfd ON ph.erp_house_id = pfd.erp_id
                    WHERE
                        ph.final_status IN (1, 2)
                        AND pfd.community IS NOT NULL
                ),
                filtered_records AS (
                    SELECT * FROM latest_records WHERE rn = 1
                )
                SELECT
                    id, push_time, city, website_name, erp_house_id,
                    final_status, process_id, community
                FROM filtered_records
                ORDER BY push_time DESC
            """

            cursor.execute(sql_combined)
            all_records = cursor.fetchall()

            success_records_all = []
            failure_records_all = []

            for record in all_records:
                record_copy = {
                    'id': record['id'],
                    'push_time': record['push_time'].isoformat() if hasattr(record['push_time'], 'isoformat') else str(record['push_time']),
                    'city': record['city'],
                    'website_name': record['website_name'],
                    'erp_house_id': record['erp_house_id'],
                    'final_status': record['final_status'],
                    'process_id': record['process_id'] if record['process_id'] else 'N/A',
                    'community': record['community'] if record['community'] else ''
                }

                if record['final_status'] == 1:
                    success_records_all.append(record_copy)
                elif record['final_status'] == 2:
                    failure_records_all.append(record_copy)

            successful_records_data = success_records_all[success_offset:success_offset + page_size]
            failed_records_data = failure_records_all[failure_offset:failure_offset + page_size]

            success_count = len(success_records_all)
            failure_count = len(failure_records_all)

        success_pagination = calculate_pagination_info(success_count, success_page, page_size)
        failure_pagination = calculate_pagination_info(failure_count, failure_page, page_size)
        return {
            "success_count": success_count,
            "failure_count": failure_count,
            "successful_records": {
                "data": successful_records_data,
                "pagination": success_pagination
            },
            "failed_records": {
                "data": failed_records_data,
                "pagination": failure_pagination
            }
        }

    except Exception as e:
        logger.error(f"统计最终发布数据或获取失败记录时失败: {str(e)}")
        raise HTTPException(status_code=500, detail="统计最终数据或获取失败记录失败")

@router.post("/mark-for-push")
async def mark_record_for_push(request: Request):
    """
    为指定园区重新生成营销内容或重置推送状态

    请求体参数：
    - park_name: 园区名称
    - status: "failed" | "success" | ...

    流程：
    - 如果 status == "failed": 只更新 parsed_factory_data 表的 ts_status = 0
    - 其他状态: 执行完整重新生成流程
      1. 清空 parsed_factory_data 表中该园区的 topic 和 content 字段
      2. 删除 property_marketing_content 表中该园区的所有记录
      3. 调用 generate_community_content 服务生成新的营销内容
      4. 等待生成任务完成并验证结果
    """
    try:
        # 解析请求体获取参数
        data = await request.json()
        park_name = data.get('park_name', '').strip()
        status = data.get('status', '').strip()

        # 参数验证
        if not park_name:
            raise HTTPException(status_code=400, detail="请求体中必须包含 'park_name' 参数")

        from services.factory_upload_service import FactoryUploadService

        if status == "failed":
            with db_cursor() as cursor:
                sql_check_park = """
                    SELECT COUNT(*) as count,
                           SUM(CASE WHEN ts_status = 0 THEN 1 ELSE 0 END) as pending_count
                    FROM parsed_factory_data
                    WHERE community = %s
                """
                cursor.execute(sql_check_park, (park_name,))
                park_result = cursor.fetchone()

                if not park_result or park_result.get('count', 0) == 0:
                    return {"success": False, "message": f"未找到园区 '{park_name}' 的房源数据"}

                total_count = park_result.get('count', 0)
                pending_count = park_result.get('pending_count', 0)

                if pending_count == total_count:
                    return {"success": True, "message": f"园区 '{park_name}' 的所有房源已经是待推送状态"}
                else:
                    sql_reset_ts_status = """
                        UPDATE parsed_factory_data
                        SET ts_status = 0
                        WHERE community = %s AND ts_status != 0
                    """
                    rows_updated = cursor.execute(sql_reset_ts_status, (park_name,))
                    return {"success": True, "message": f"园区 '{park_name}' 已重置为待推送状态（更新了 {rows_updated} 条记录）"}

        else:
            with db_cursor() as cursor:
                sql_reset_factory_data = """
                    UPDATE parsed_factory_data
                    SET topic = NULL, content = NULL
                    WHERE community = %s
                """
                cursor.execute(sql_reset_factory_data, (park_name,))

                sql_delete_marketing_content = """
                    DELETE FROM property_marketing_content
                    WHERE park_name = %s
                """
                cursor.execute(sql_delete_marketing_content, (park_name,))

            generation_result = await FactoryUploadService.generate_community_content(park_name)

            if generation_result.get('success'):
                return {"success": True, "message": f"园区 '{park_name}' 营销内容生成任务已启动，正在后台处理中"}
            else:
                logger.warning(f"园区 {park_name} 内容生成服务调用失败: {generation_result.get('message')}")
                return {"success": False, "message": f"园区 '{park_name}' 内容生成任务启动失败: {generation_result.get('message')}"}

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(f"处理园区 {park_name} 时失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成内容时发生内部错误")

@router.get("/get-matched-posts/{park_name}")
async def get_matched_posts_for_park(park_name: str):
    """根据园区名称获取营销内容数据"""
    try:
        with db_cursor() as cursor:
            sql_get_marketing_content = """
                SELECT id, park_name, title, selling_points, description, created_at, status
                FROM property_marketing_content
                WHERE park_name = %s
                ORDER BY created_at DESC
            """
            cursor.execute(sql_get_marketing_content, (park_name,))
            marketing_contents = cursor.fetchall()

            if not marketing_contents:
                logger.warning(f"未找到园区 '{park_name}' 对应的营销内容")

            posts = []
            for content in marketing_contents:
                created_at = content.get('created_at')
                if created_at and hasattr(created_at, 'isoformat'):
                    created_at = created_at.isoformat()

                post = {
                    "id": content.get('id'),
                    "标题": content.get('title'),
                    "营销点": content.get('selling_points'),
                    "内容": content.get('description'),
                    "创建时间": created_at,
                    "状态": content.get('status'),
                    "园区": content.get('park_name')
                }
                posts.append(post)

            all_matched_posts_json = json.dumps(posts, ensure_ascii=False)

            return {
                "success": True,
                "park_name": park_name,
                "all_matched_posts": all_matched_posts_json
            }

    except Exception as e:
        logger.error(f"获取园区 '{park_name}' 的营销内容数据时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取园区营销内容数据时发生内部错误")

