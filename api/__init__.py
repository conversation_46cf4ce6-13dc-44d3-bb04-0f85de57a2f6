#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API模块
包含所有API路由和客户端
"""

from api.client import YoutuiApiClient

__all__ = ['YoutuiApiClient']

from fastapi import APIRouter
from api import (
    youtui_routes, 
    excel_routes, 
    push_routes, 
    config_routes, 
    system_routes, 
    file_routes,
    factory_data_routes,
    dingtalk_routes,
    listing_analysis_routes
)

# 创建主路由器
api_router = APIRouter()

# 添加路由
api_router.include_router(youtui_routes.router)
api_router.include_router(excel_routes.router)
api_router.include_router(push_routes.router)
api_router.include_router(config_routes.router)
api_router.include_router(system_routes.router)
api_router.include_router(file_routes.router)
api_router.include_router(factory_data_routes.router)
api_router.include_router(dingtalk_routes.router)
api_router.include_router(listing_analysis_routes.router) 