#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优推科技API客户端
实现与优推科技API的交互
"""

import sys
import os
import logging
from pathlib import Path
from urllib.parse import urljoin

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from config.settings import API_BASE_URLS, API_ENDPOINTS, ENVIRONMENT, PLATFORM_ID, COMPANY_ID, DEBUG, CITY_DOMAINS, DEFAULT_CITY
from utils.http_utils import HttpClient, AsyncHttpClient
from utils.xml_utils import dict_to_xml, xml_to_dict

# 配置日志
logging.basicConfig(
    level=logging.DEBUG if DEBUG else logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class YoutuiApiClient:
    """优推科技API客户端类"""

    def __init__(self, environment=ENVIRONMENT, city=DEFAULT_CITY):
        """
        初始化API客户端

        Args:
            environment: 环境，'test'或'production'
            city: 城市代码，如'bj'(北京)、'cc'(长春)等
        """
        # 根据城市动态构建API基础URL
        self.city = city
        if city not in CITY_DOMAINS:
            logger.warning(f"城市代码 '{city}' 未在配置中找到，使用默认城市 '{DEFAULT_CITY}'")
            self.city = DEFAULT_CITY

        city_domain = CITY_DOMAINS[self.city]
        self.base_url = f"http://{city_domain}/youtui/"

        logger.info(f"使用城市: {self.city}, 域名: {city_domain}")
        logger.info(f"完整基础URL: {self.base_url}")

        self.http_client = HttpClient(self.base_url)
        self.platform_id = PLATFORM_ID
        self.company_id = COMPANY_ID
        self.debug = 'TRUE' if DEBUG else 'FALSE'
        self.environment = environment

    def get_push_info(self, user_id, userkey, process_id):
        """
        获取房源推送/外网下架详情

        Args:
            user_id: 用户标识ID
            userkey: 优推科技用户登录密码
            process_id: 推送/外网下架进程ID

        Returns:
            推送/外网下架详情，字典格式
        """
        print(f"\n===== 调用接口信息 =====")
        print(f"接口名称: 获取房源推送/外网下架详情接口")
        print(f"接口环境: {self.environment}")
        print(f"接口城市: {self.city}")
        print(f"接口端点: {API_ENDPOINTS.get('website_push_info', '?api/website/pushinfo.html')}")
        print(f"完整URL: {urljoin(self.base_url, API_ENDPOINTS.get('website_push_info', '?api/website/pushinfo.html'))}")
        print(f"========================\n")

        # 构建请求数据
        data = {
            'platformID': self.platform_id,
            'companyID': self.company_id,
            'userID': user_id,
            'userkey': userkey,
            'processID': process_id,
            'dbug': self.debug
        }

        # 转换为XML
        xml_data = dict_to_xml(data)

        # 发送请求
        endpoint = API_ENDPOINTS.get('website_push_info', '?api/website/pushinfo.html')
        response_xml = self.http_client.post(endpoint, xml_data)

        # 解析响应
        if response_xml:
            return xml_to_dict(response_xml)
        return None


class AsyncYoutuiApiClient:
    """异步优推科技API客户端类"""

    def __init__(self, environment=ENVIRONMENT, city=DEFAULT_CITY):
        """
        初始化异步优推科技API客户端

        Args:
            environment: 环境，'test'或'production'
            city: 城市代码，如'bj'(北京)、'cc'(长春)等
        """
        # 根据城市动态构建API基础URL
        self.city = city
        if city not in CITY_DOMAINS:
            logger.warning(f"城市代码 '{city}' 未在配置中找到，使用默认城市 '{DEFAULT_CITY}'")
            self.city = DEFAULT_CITY

        city_domain = CITY_DOMAINS[self.city]
        self.base_url = f"http://{city_domain}/youtui/"

        logger.info(f"使用城市: {self.city}, 域名: {city_domain}")
        logger.info(f"完整基础URL: {self.base_url}")

        self.http_client = AsyncHttpClient(self.base_url)
        self.platform_id = PLATFORM_ID
        self.company_id = COMPANY_ID
        self.debug = 'TRUE' if DEBUG else 'FALSE'
        self.environment = environment

    async def async_get_agent_post_stats(self, user_id, webcontent=None, timeout=30):
        """
        异步获取经纪人名下所有帖子的点击量

        Args:
            user_id: 用户标识ID，即经纪人ID
            webcontent: 网站与账号信息的Base64编码JSON (可选)
            timeout: 超时时间（秒），设置为None表示永不超时

        Returns:
            帖子点击量信息，字典格式
        """
        logger.info(f"异步获取经纪人(ID: {user_id})名下所有帖子点击量")
        logger.info(f"超时设置: {'永不超时' if timeout is None else f'{timeout}秒'}")

        # 构建请求数据
        data = {
            'platformID': self.platform_id,
            'companyID': self.company_id,
            'userID': user_id,
            'dbug': self.debug
        }

        # 添加webcontent参数（如果提供）
        if webcontent:
            data['webcontent'] = webcontent
            logger.info(f"使用自定义webcontent，长度：{len(webcontent)}")

        # 转换为XML
        xml_data = dict_to_xml(data)

        # 发送异步请求
        endpoint = API_ENDPOINTS['house_gethouse']
        response_xml = await self.http_client.post(endpoint, xml_data, timeout=timeout)

        # 解析响应
        if response_xml:
            return xml_to_dict(response_xml)
        return None

    async def async_get_website_accounts(self, user_id, userkey, timeout=30):
        """
        异步获取网站账号信息

        Args:
            user_id: 用户标识ID
            userkey: 优推科技用户登录密码
            timeout: 超时时间（秒），设置为None表示永不超时

        Returns:
            网站账号信息，字典格式
        """
        logger.info(f"异步获取网站账号信息 - 用户ID: {user_id}")
        logger.info(f"超时设置: {'永不超时' if timeout is None else f'{timeout}秒'}")

        # 构建请求数据
        data = {
            'platformID': self.platform_id,
            'companyID': self.company_id,
            'userID': user_id,
            'userkey': userkey,
            'dbug': self.debug
        }

        # 转换为XML
        xml_data = dict_to_xml(data)

        # 发送异步请求
        endpoint = API_ENDPOINTS.get('website_info', '?api/website/info.html')
        response_xml = await self.http_client.post(endpoint, xml_data, timeout=timeout)

        # 解析响应
        if response_xml:
            result = xml_to_dict(response_xml)
            logger.info(f"异步获取网站账号信息结果状态: {result.get('status', 'unknown')}")
            return result

        logger.error("异步获取网站账号信息失败")
        return None

    async def async_sync_house(self, user_id, house_data, action='INSERT', ttid=0):
        """
        异步同步/更新房源

        Args:
            user_id: 用户标识ID
            house_data: 房源数据，字典格式
            action: 动作类型，'INSERT'、'UPDATE'或'PUSH'
            ttid: 优推科技房源ID，更新房源时需要

        Returns:
            同步结果，字典格式
        """
        logger.info(f"异步房源同步 - 用户ID: {user_id}, 操作类型: {action}")
        logger.info(f"接口环境: {self.environment}")
        logger.info(f"接口城市: {self.city}")
        logger.info(f"接口端点: {API_ENDPOINTS['house_sync']}")
        logger.info(f"完整URL: {urljoin(self.base_url, API_ENDPOINTS['house_sync'])}")

        # 构建请求数据
        data = {
            'platformID': self.platform_id,
            'companyID': self.company_id,
            'userID': user_id,
            'act': action,
            'ttid': ttid,
            'dbug': self.debug
        }

        # 合并房源数据
        data.update(house_data)

        # 记录关键字段
        logger.info(f"标题(topic): {data.get('topic', '未设置')}")
        logger.info(f"内容(content): {data.get('content', '未设置')}")

        # 记录图片字段
        image_fields = ['thumb', 'floorplans', 'photointerior', 'photooutdoor']
        image_fields_log = {field: data.get(field, '') for field in image_fields}
        logger.info(f"图片字段: {image_fields_log}")

        # 确保图片字段存在，即使为空
        for field in image_fields:
            if field not in data:
                data[field] = ''
                logger.info(f"添加缺失的图片字段: {field}")

        # 转换为XML
        xml_data = dict_to_xml(data)

        # 记录XML数据中的图片字段
        for field in image_fields:
            if f"<{field}>" in xml_data:
                logger.info(f"字段 {field} 存在于XML中")
            else:
                logger.info(f"字段 {field} 不存在于XML中")

        # 发送异步请求
        endpoint = API_ENDPOINTS.get('house_sync')
        response_xml = await self.http_client.post(endpoint, xml_data)

        # 解析响应
        if response_xml:
            return xml_to_dict(response_xml)
        return None