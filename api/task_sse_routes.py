#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
任务状态和SSE事件流API路由
统一管理所有任务状态查询和实时事件推送
"""

import time
import logging
import difflib
import re
from typing import Dict, Any, Optional
from fastapi import APIRouter, Request
from pydantic import BaseModel, Field

from services.task_status_manager import get_all_tasks_status, get_task_status
from services.sse_service import create_event_stream
from services.push_progress_service import push_progress_service

logger = logging.getLogger(__name__)

def normalize_community_name(name):
    """
    标准化园区名称，用于模糊匹配
    """
    if not name:
        return ""

    # 全角转半角
    name = name.replace('（', '(').replace('）', ')')
    name = name.replace('【', '[').replace('】', ']')
    name = name.replace('「', '"').replace('」', '"')

    # 去除多余空格
    name = re.sub(r'\s+', '', name)

    # 转换为小写（用于比较）
    return name.lower()

def fuzzy_match_community(task_communities, frontend_communities, threshold=0.8):
    """
    模糊匹配园区名称

    Args:
        task_communities: 任务状态中的园区名称列表
        frontend_communities: 前端数据中的园区名称列表
        threshold: 相似度阈值

    Returns:
        Dict: {任务园区名称: 前端园区名称} 的映射
    """
    mapping = {}

    for task_name in task_communities:
        best_match = None
        best_score = 0

        # 标准化任务园区名称
        normalized_task = normalize_community_name(task_name)

        for frontend_name in frontend_communities:
            # 标准化前端园区名称
            normalized_frontend = normalize_community_name(frontend_name)

            # 计算相似度
            similarity = difflib.SequenceMatcher(None, normalized_task, normalized_frontend).ratio()

            # 如果完全匹配，直接使用
            if similarity == 1.0:
                best_match = frontend_name
                best_score = 1.0
                break

            # 记录最佳匹配
            if similarity > best_score and similarity >= threshold:
                best_match = frontend_name
                best_score = similarity

        if best_match:
            mapping[task_name] = best_match
            logger.info(f"模糊匹配成功: '{task_name}' -> '{best_match}' (相似度: {best_score:.3f})")
        else:
            logger.warning(f"未找到匹配的园区名称: '{task_name}'")

    return mapping

# 创建路由
router = APIRouter(
    prefix="/tasks",
    tags=["任务状态管理"],
    responses={404: {"description": "接口不存在"}},
)

# 响应模型
class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="提示信息")
    data: Optional[Dict] = Field(None, description="任务状态数据")

class TaskListResponse(BaseModel):
    """任务列表响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="提示信息")
    data: Optional[Dict[str, Dict[str, Any]]] = Field(None, description="任务状态列表")

@router.get("/status", response_model=TaskListResponse, summary="获取所有任务状态")
async def get_all_tasks():
    """
    获取所有任务的状态信息

    返回:
    - **success**: 操作是否成功
    - **message**: 提示信息
    - **data**: 所有任务状态数据，格式为 {task_key: {status, progress, start_time, ...}}
    """
    try:
        all_tasks = get_all_tasks_status()

        # 为每个任务添加可读的时间格式
        for task_key, task_info in all_tasks.items():
            if 'start_time' in task_info and 'start_time_str' not in task_info:
                start_time = task_info['start_time']
                task_info['start_time_str'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))

            if 'completed_at' in task_info and 'completed_at_str' not in task_info:
                completed_at = task_info['completed_at']
                task_info['completed_at_str'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(completed_at))

        return {
            "success": True,
            "message": f"获取所有任务状态成功，共 {len(all_tasks)} 个任务",
            "data": all_tasks
        }
    except Exception as e:
        logger.error(f"获取所有任务状态失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取所有任务状态失败: {str(e)}",
            "data": {}
        }

@router.get("/status/{task_key}", response_model=TaskStatusResponse, summary="获取特定任务状态")
async def get_single_task_status(task_key: str):
    """
    获取指定任务的状态信息

    参数:
    - **task_key**: 任务唯一标识

    返回:
    - **success**: 操作是否成功
    - **message**: 提示信息
    - **data**: 任务状态信息
    """
    try:
        task_status = get_task_status(task_key)

        if not task_status:
            return {
                "success": False,
                "message": f"任务 '{task_key}' 不存在",
                "data": None
            }

        # 添加可读的时间格式
        if 'start_time' in task_status and 'start_time_str' not in task_status:
            start_time = task_status['start_time']
            task_status['start_time_str'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))

        if 'completed_at' in task_status and 'completed_at_str' not in task_status:
            completed_at = task_status['completed_at']
            task_status['completed_at_str'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(completed_at))

        return {
            "success": True,
            "message": f"获取任务 '{task_key}' 状态成功",
            "data": task_status
        }
    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取任务状态失败: {str(e)}",
            "data": None
        }



@router.get("/content-status", response_model=TaskListResponse, summary="获取所有内容生成任务状态")
async def get_content_tasks_status():
    """
    获取所有内容生成任务的状态

    返回:
    - **success**: 操作是否成功
    - **message**: 提示信息
    - **data**: 内容生成任务状态数据
    """
    try:
        # 获取所有非视频任务状态（主要是内容生成任务）
        all_tasks = get_all_tasks_status()
        content_tasks = {}

        # 如果有任务，需要建立园区名称映射
        if all_tasks:
            # 导入数据库连接
            from database.db_connection import get_db_connection

            # 获取所有园区名称的映射关系
            community_mapping = {}
            try:
                connection = get_db_connection()
                with connection.cursor() as cursor:
                    # 查询所有园区名称
                    cursor.execute("""
                        SELECT DISTINCT community
                        FROM parsed_factory_data
                        WHERE community IS NOT NULL AND community != ''
                    """)
                    communities = cursor.fetchall()

                    # 获取前端园区名称列表
                    frontend_communities = [row['community'] for row in communities]

                    # 获取任务状态中的园区名称列表（排除视频任务）
                    task_communities = [task_key for task_key in all_tasks.keys() if not task_key.startswith('video_')]

                    # 使用模糊匹配建立映射
                    community_mapping = fuzzy_match_community(task_communities, frontend_communities)

                connection.close()
                logger.info(f"模糊匹配建立园区名称映射，共 {len(community_mapping)} 个园区")
                logger.info(f"前端园区数量: {len(frontend_communities)}, 任务园区数量: {len(task_communities)}")

            except Exception as e:
                logger.error(f"建立园区名称映射失败: {str(e)}")
                # 如果映射失败，使用原始逻辑
                community_mapping = {}

        for task_key, task_info in all_tasks.items():
            # 排除视频任务，其他都视为内容生成任务
            if not task_key.startswith('video_'):
                # 确保task_info是普通字典
                task_info = dict(task_info) if hasattr(task_info, 'copy') else task_info

                # 添加可读的状态名称（如果还没有）
                status_code = task_info.get('status', 0)
                if 'status_text' not in task_info:
                    status_texts = {0: '等待中', 1: '进行中', 2: '已完成', 3: '失败'}
                    task_info['status_text'] = status_texts.get(status_code, '未知')

                # 将时间戳转换为可读时间
                if 'start_time' in task_info and 'start_time_str' not in task_info:
                    start_time = task_info['start_time']
                    task_info['start_time_str'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))

                if 'completed_at' in task_info and 'completed_at_str' not in task_info:
                    completed_at = task_info['completed_at']
                    task_info['completed_at_str'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(completed_at))

                # 使用模糊匹配的前端园区名称作为键，如果找不到映射则使用任务键
                frontend_key = community_mapping.get(task_key, task_key)
                content_tasks[frontend_key] = task_info

        return {
            "success": True,
            "message": f"获取内容生成任务状态成功，共 {len(content_tasks)} 个任务",
            "data": content_tasks
        }
    except Exception as e:
        logger.error(f"获取内容生成任务状态失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取内容生成任务状态失败: {str(e)}",
            "data": {}
        }

@router.get("/latest", response_model=TaskStatusResponse, summary="获取最新任务状态")
async def get_latest_task_status():
    """
    获取最新的任务状态（按更新时间排序）

    返回:
    - **success**: 操作是否成功
    - **message**: 提示信息
    - **data**: 最新任务状态信息，若无任务则返回空
    """
    try:
        all_tasks = get_all_tasks_status()

        if not all_tasks:
            return {
                "success": True,
                "message": "当前没有任何任务",
                "data": None
            }

        # 找到最新更新的任务
        latest_task = None
        latest_time = 0
        latest_key = None

        for task_key, task_info in all_tasks.items():
            updated_at = task_info.get('updated_at', task_info.get('start_time', 0))
            if updated_at > latest_time:
                latest_time = updated_at
                latest_task = task_info
                latest_key = task_key

        if latest_task:
            # 添加可读的时间格式
            if 'start_time' in latest_task and 'start_time_str' not in latest_task:
                start_time = latest_task['start_time']
                latest_task['start_time_str'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))

            if 'completed_at' in latest_task and 'completed_at_str' not in latest_task:
                completed_at = latest_task['completed_at']
                latest_task['completed_at_str'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(completed_at))

            # 添加任务key信息
            latest_task['task_key'] = latest_key

        return {
            "success": True,
            "message": f"获取最新任务状态成功: {latest_key}",
            "data": latest_task
        }
    except Exception as e:
        logger.error(f"获取最新任务状态失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取最新任务状态失败: {str(e)}",
            "data": None
        }

@router.get("/events", summary="SSE事件流接口，用于实时获取任务状态更新")
async def task_events(request: Request):
    """
    建立SSE连接以接收任务状态更新

    客户端可以通过此接口建立持久连接，接收服务器推送的任务状态更新事件

    返回:
        EventSourceResponse: SSE事件流响应
    """
    # 获取初始任务状态作为初始数据
    initial_tasks = get_all_tasks_status()
    initial_data = {"task_update": initial_tasks} if initial_tasks else None

    # 使用SSE服务创建事件流
    return await create_event_stream(request, initial_data)


@router.post("/push/set-background", summary="设置推送任务为后台执行")
async def set_background_push(request: dict):
    """设置推送任务为后台执行"""
    factory_id = request.get('factory_id')
    task_id = request.get('task_id')

    if not factory_id or not task_id:
        from fastapi import HTTPException
        raise HTTPException(status_code=400, detail="缺少必要参数")

    success = await push_progress_service.set_background_push(factory_id)

    if success:
        return {"success": True, "message": "已设置为后台推送"}
    else:
        from fastapi import HTTPException
        raise HTTPException(status_code=500, detail="设置后台推送失败")


@router.get("/push/progress/{factory_id}", summary="获取推送进度")
async def get_push_progress(factory_id: str):
    """获取指定园区的推送进度"""
    progress_data = push_progress_service.get_push_progress(factory_id)

    if progress_data:
        return progress_data
    else:
        from fastapi import HTTPException
        raise HTTPException(status_code=404, detail="未找到推送进度")


@router.get("/push/active-tasks", summary="获取所有活跃的推送任务")
async def get_active_push_tasks():
    """获取所有活跃的推送任务"""
    active_tasks = push_progress_service.get_all_active_pushes()
    return active_tasks


@router.delete("/push/clear/{factory_id}", summary="清理推送进度状态")
async def clear_push_progress(factory_id: str):
    """清理指定园区的推送进度，用于重新推送"""
    success = push_progress_service.clear_push_progress(factory_id)

    if success:
        return {"success": True, "message": "推送状态已清理"}
    else:
        from fastapi import HTTPException
        raise HTTPException(status_code=404, detail="未找到推送进度或清理失败")
