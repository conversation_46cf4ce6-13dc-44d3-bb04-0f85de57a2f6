#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
报告生成API路由
提供生成和导出报告的API端点
"""

from fastapi import APIRouter, HTTPException, Body
from fastapi.responses import Response
from pydantic import BaseModel
from typing import Dict, Any, Optional
from services.report_service import ReportService
import logging
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter(
    prefix="/reports",
    tags=["报告生成"],
    responses={404: {"description": "Not found"}},
)

# 初始化服务
report_service = ReportService()

class ReportRequest(BaseModel):
    """报告请求模型"""
    title: str
    date: str
    data: Dict[str, Any]

@router.post("/competitor-analysis", response_class=Response)
async def generate_competitor_analysis_report(report_data: ReportRequest):
    """
    Generate competitor analysis report

    Parameters:
    - report_data: Report data including title, date and analysis data

    Returns:
    - PDF file
    """
    try:
        logger.info(f"Received request to generate competitor analysis report: {report_data.title}")

        # 检查是否包含中文字符
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in report_data.title)

        # 如果标题包含中文，使用ASCII标题
        safe_title = "Competitor_Analysis_Report"
        if not has_chinese:
            # 如果标题不包含中文，使用原标题但替换空格和特殊字符
            safe_title = report_data.title.replace(' ', '_').replace('/', '_').replace('\\', '_')

        # 生成PDF报告
        pdf_data = report_service.generate_competitor_analysis_report(report_data.dict())

        # 设置安全的文件名
        filename = f"{safe_title}.pdf"

        # 返回PDF文件
        return Response(
            content=pdf_data,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f'attachment; filename="{filename}"'
            }
        )

    except Exception as e:
        logger.error(f"Failed to generate competitor analysis report: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate report: {str(e)}")
