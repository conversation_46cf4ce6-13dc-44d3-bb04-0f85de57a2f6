#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
钉钉相关API路由
提供钉钉用户管理、消息发送等功能
"""

from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from services.dingtalk_service import DingTalkService

# 创建路由器
router = APIRouter(prefix="/dingtalk", tags=["钉钉"])

# 实例化钉钉服务
dingtalk_service = DingTalkService()

class DepartmentUser(BaseModel):
    """部门用户模型"""
    department_id: str = "1"
    size: int = 100
    cursor: int = 0

class CertificationNotification(BaseModel):
    """房本认证通知模型"""
    mobile: Optional[str] = None
    name: Optional[str] = None
    url: str
    to_all_user: bool = False

@router.get("/users", response_model=Dict[str, Any])
async def get_users(
    department_id: str = Query("1", description="部门ID，默认为1（根部门）"),
    size: int = Query(100, description="分页大小，最大100"),
    cursor: int = Query(0, description="分页游标，默认为0"),
):
    """
    获取钉钉部门用户列表
    
    - **department_id**: 部门ID，默认为1（根部门）
    - **size**: 分页大小，最大100
    - **cursor**: 分页游标，默认为0
    
    返回:
    - success: 是否成功
    - message: 消息
    - user_list: 用户列表(如果成功)
    - has_more: 是否有更多数据(如果成功)
    - next_cursor: 下一页游标(如果成功)
    """
    try:
        result = dingtalk_service.get_user_list(department_id, size, cursor)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取钉钉部门用户列表失败: {str(e)}")

@router.get("/all-users", response_model=Dict[str, Any])
async def get_all_users():
    """
    获取企业所有用户并与ERP系统账号绑定
    
    此接口会执行以下操作:
    1. 获取所有钉钉用户
    2. 通过手机号匹配ERP系统中的account_info表记录
    3. 如果匹配成功，则将钉钉用户ID(userid)更新到account_info表的dingtalk_userid字段
    
    返回:
    - success: 是否成功
    - message: 消息
    - total_count: 用户总数(如果成功)
    - bind_count: 成功绑定的用户数量
    - unbind_count: 未能绑定的用户数量
    """
    try:
        result = dingtalk_service.get_all_users()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取企业所有用户失败: {str(e)}")

@router.post("/send-certification", response_model=Dict[str, Any])
async def send_certification_notification(notification: CertificationNotification):
    """
    发送房本认证通知消息
    
    通过手机号或名字查询account_info表获取dingtalk_userid，然后发送钉钉工作通知
    
    请求体:
    - **mobile**: 销售人员手机号（与name至少提供一个）
    - **name**: 销售人员姓名（与mobile至少提供一个）
    - **url**: 二维码链接地址
    - **to_all_user**: 是否发送给所有用户，默认为false
    
    返回:
    - success: 是否成功
    - message: 消息
    - task_id: 钉钉任务ID(如果成功)
    """
    try:
        # 验证参数
        if not notification.mobile and not notification.name and not notification.to_all_user:
            raise HTTPException(status_code=400, detail="请至少提供手机号或姓名")
        
        # 获取用户钉钉ID
        user_ids = []
        recipient_name = notification.name
        
        # 如果不是发送给所有用户，则查询数据库获取dingtalk_userid
        if not notification.to_all_user:
            from database.db_connection import get_db_connection
            
            # 获取数据库连接
            conn = get_db_connection()
            cursor = conn.cursor()
            
            try:
                # 构建查询条件
                query_conditions = []
                query_params = []
                
                if notification.mobile:
                    query_conditions.append("account = %s")
                    query_params.append(notification.mobile)
                    
                if notification.name:
                    query_conditions.append("name = %s")
                    query_params.append(notification.name)
                
                # 构建完整查询语句
                query = f"""
                    SELECT dingtalk_userid, name 
                    FROM account_info 
                    WHERE {" OR ".join(query_conditions)} 
                    AND dingtalk_userid IS NOT NULL AND dingtalk_userid != ''
                """
                
                # 执行查询
                cursor.execute(query, query_params)
                results = cursor.fetchall()
                
                # 提取钉钉用户ID和名字
                for row in results:
                    user_id = row.get("dingtalk_userid")
                    if user_id:
                        user_ids.append(user_id)
                        
                    # 如果未提供名字，则使用数据库中的名字
                    if not recipient_name and row.get("name"):
                        recipient_name = row.get("name")
                        
            finally:
                cursor.close()
                conn.close()
                
            # 检查是否找到用户
            if not user_ids and not notification.to_all_user:
                return {
                    "success": False,
                    "message": "未找到匹配的钉钉用户ID，无法发送消息"
                }
        
        # 构建消息模板 - 使用链接卡片消息类型，可直接展示二维码图片
        message_template = f"""## 58账号房本认证通知
        
尊敬的销售 **{recipient_name or '用户'}**，您好！
        
您的58账号需要进行**房本认证**，请尽快完成以下操作：

1. 打开58同城APP
2. 使用APP扫描以下二维码
3. 按提示完成房本认证流程

![房本认证二维码]({notification.url})

如有疑问，请联系涂经理。
"""
        
        # 发送消息
        result = dingtalk_service.send_work_notification(
            message=message_template,
            user_ids=user_ids,
            to_all_user=notification.to_all_user,
            msg_type="markdown",
            title="58账号房本认证通知"
        )
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送房本认证通知失败: {str(e)}")