#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""Excel操作相关API路由"""

from fastapi import APIRouter, UploadFile, File, Body, Request
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
import logging
import time

from services.factory_upload_service import FactoryUploadService

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/excel",
    tags=["Excel操作"],
    responses={404: {"description": "接口不存在"}},
)

class GenerateContentRequest(BaseModel):
    """生成园区内容请求模型"""
    community_name: str = Field(..., description="园区名称")

class GenerateContentResponse(BaseModel):
    """生成园区内容响应模型"""
    success: bool
    message: str
    community_name: str
    warning_type: Optional[str] = None
    warning_message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None

class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""
    success: bool
    message: str
    data: Dict[str, Any]

class FactoryDetailResponse(BaseModel):
    """厂房详细数据响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class FactoryDataItem(BaseModel):
    """房源数据项模型"""
    id: Optional[int] = None
    erp_id: Optional[str] = None
    community: Optional[str] = None
    city: Optional[str] = None
    zone: Optional[str] = None
    has_marketing_content: bool
    has_gallery: Optional[bool] = None
    has_knowledge_base: Optional[bool] = None
    has_poi_data: Optional[bool] = None
    created_at: Optional[str] = None
    is_published: Optional[int] = None
    youtui_house_id: Optional[str] = None

class CommunityStats(BaseModel):
    """园区统计数据模型"""
    total: int
    with_content: int
    without_content: int
    with_content_percent: float
    city: Optional[str] = None
    zone: Optional[str] = None

class ImportedFactoryDataResponse(BaseModel):
    """已导入房源数据响应模型"""
    success: bool
    message: str
    data: Optional[List[FactoryDataItem]] = None
    pagination: Optional[Dict[str, Any]] = None
    statistics: Optional[Dict[str, Any]] = None

@router.post("/parse-excel", summary="解析Excel文件并保存到数据库")
async def parse_excel(
    file: UploadFile = File(...)
):
    """
    解析Excel文件并直接保存到parsed_factory_data表中

    - **file**: Excel文件，文件内容要求：
      - 第一行必须是字段名
      - 支持的字段：标题, 城市, 区域, 街道, 租售类型, 园区名称, 描述, 租金, 租金单位, 面积（平方米）, 地址, 建筑类型,
        封面图, 户型图, 内部图, 外部图, 配套, 起租期, 支付方式, 楼层, 首层层高, 价格, 首付百分比, 厂房结构, 厂房类型,
        厂房标签, 厂房新旧, 可办环评, 性质, 中介费, 产权, 建筑年代, 产权年限, 土地性质, 供电容量, 楼板承重

    返回:
    - 解析结果，包含成功和失败的记录数
    """
    return await FactoryUploadService.parse_and_save_excel_file(file)

@router.post("/generate-content", response_model=GenerateContentResponse, summary="为指定园区生成标题和描述")
async def generate_community_content(request: GenerateContentRequest):
    """
    为指定园区生成标题和描述内容

    此接口会调用AI服务为园区生成客户画像、营销标题和详细描述。
    生成的内容会保存到文件并更新到数据库。

    参数:
        community_name: 园区名称

    返回:
        success: 是否成功
        message: 提示信息
        community_name: 园区名称
        data: 生成的内容数据（如果成功）
    """
    try:
        logger.info(f"开始为园区 '{request.community_name}' 生成内容...")

        # 调用服务生成内容
        result = await FactoryUploadService.generate_community_content(request.community_name)

        # 返回结果
        return GenerateContentResponse(
            success=result["success"],
            message=result["message"],
            community_name=result["community_name"],
            warning_type=result.get("warning_type"),
            warning_message=result.get("warning_message"),
            data=result["data"]
        )

    except Exception as e:
        logger.error(f"生成园区内容失败: {str(e)}")
        return GenerateContentResponse(
            success=False,
            message=f"生成园区内容失败: {str(e)}",
            community_name=request.community_name
        )

@router.post("/generate-content-force", response_model=GenerateContentResponse, summary="强制为指定园区生成标题和描述")
async def generate_community_content_force(request: GenerateContentRequest):
    """
    强制为指定园区生成标题和描述内容，跳过数据检查

    此接口会跳过知识库和周边配套数据的检查，直接调用AI服务生成内容。
    用于用户确认后的强制生成场景。

    参数:
        community_name: 园区名称

    返回:
        success: 是否成功
        message: 提示信息
        community_name: 园区名称
        data: 生成的内容数据（如果成功）
    """
    try:
        logger.info(f"强制为园区 '{request.community_name}' 生成内容...")

        # 调用强制生成服务
        result = await FactoryUploadService.generate_community_content_force(request.community_name)

        # 返回结果
        return GenerateContentResponse(
            success=result["success"],
            message=result["message"],
            community_name=result["community_name"],
            data=result["data"]
        )

    except Exception as e:
        logger.error(f"强制生成园区内容失败: {str(e)}")
        return GenerateContentResponse(
            success=False,
            message=f"强制生成园区内容失败: {str(e)}",
            community_name=request.community_name
        )

@router.get("/imported-factory-data", response_model=ImportedFactoryDataResponse, summary="获取已导入的房源数据")
async def get_imported_factory_data(
    page: int = 1,
    page_size: int = 10,
    is_published: Optional[int] = None,
    has_content: Optional[int] = None,
    has_gallery: Optional[int] = None,
    has_knowledge_base: Optional[int] = None,
    has_poi_data: Optional[int] = None,
    city: Optional[str] = None
):
    """
    获取数据库中已导入的房源数据，支持分页和筛选

    参数:
    - page: 页码，从1开始
    - page_size: 每页记录数，默认10
    - is_published: 发布状态筛选，1=已发布，0=未发布，不传表示不筛选
    - has_content: 内容生成状态筛选，1=已生成内容，0=未生成内容，不传表示不筛选
    - has_gallery: 图库状态筛选，1=有图库，0=无图库，不传表示不筛选
    - has_knowledge_base: 知识库状态筛选，1=有知识库，0=无知识库，不传表示不筛选
    - has_poi_data: 周边配套状态筛选，1=有周边配套，0=无周边配套，不传表示不筛选
    - city: 城市筛选，传入城市名称进行筛选，不传表示不筛选

    返回信息包括：
    - 当前页的房源数据列表
    - 分页信息
    - 每个园区是否已生成营销内容（标题和描述）
    - 每个园区是否有知识库数据
    - 每个园区是否有周边配套数据
    - 园区分组统计信息

    返回:
        success: 是否成功
        message: 提示信息
        data: 当前页的房源数据列表
        pagination: 分页信息
        statistics: 统计信息，包含园区分组统计
    """
    try:
        logger.info(f"开始获取已导入的房源数据，页码：{page}，每页记录数：{page_size}，发布状态：{is_published}，内容状态：{has_content}，图库状态：{has_gallery}，知识库状态：{has_knowledge_base}，周边配套状态：{has_poi_data}，城市：{city}")

        # 调用服务获取数据
        result = await FactoryUploadService.get_imported_factory_data(page, page_size, is_published, has_content, has_gallery, has_knowledge_base, has_poi_data, city)

        # 返回结果
        return ImportedFactoryDataResponse(
            success=result["success"],
            message=result["message"],
            data=result.get("data"),
            pagination=result.get("pagination"),
            statistics=result.get("statistics")
        )

    except Exception as e:
        logger.error(f"获取房源数据失败: {str(e)}")
        return ImportedFactoryDataResponse(
            success=False,
            message=f"获取房源数据失败: {str(e)}"
        )


@router.get("/cities", summary="获取可用城市列表")
async def get_available_cities():
    """
    获取数据库中所有可用的城市列表

    返回:
        success: 是否成功
        message: 提示信息
        data: 城市列表
    """
    try:
        logger.info("开始获取可用城市列表")

        # 调用服务获取城市列表
        result = await FactoryUploadService.get_available_cities()

        if result["success"]:
            logger.info(f"获取城市列表成功，共 {len(result['data'])} 个城市")
        else:
            logger.error(f"获取城市列表失败: {result['message']}")

        return result

    except Exception as e:
        logger.error(f"获取城市列表失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取城市列表失败: {str(e)}"
        }


@router.post("/publish-unpublished", summary="发布指定的厂房数据到优推平台")
async def publish_unpublished(request: Dict[str, Any] = Body(...)):
    """
    将指定的厂房数据发送到优推平台

    参数:
    - erp_ids: 要发布的erp_id列表，如果为空则按limit参数获取未发布数据
    - limit: 当erp_ids为空时，每次处理的最大记录数，默认10条

    返回:
    - 发布结果，包含成功和失败的记录数
    """
    try:
        erp_ids = request.get("erp_ids", [])
        limit = request.get("limit", 10)

        if erp_ids:
            logger.info(f"开始发布指定的厂房数据，erp_ids: {erp_ids}")
        else:
            logger.info(f"开始发布未发布的厂房数据，最大处理数量: {limit}")

        # 调用服务发布数据
        result = await FactoryUploadService.publish_unpublished_data(erp_ids, limit)

        return result
    except Exception as e:
        logger.error(f"发布数据失败: {str(e)}")
        return {
            "success": False,
            "message": f"发布数据失败: {str(e)}",
            "total": 0,
            "success_count": 0,
            "failed_count": 0,
            "success_ids": [],
            "failed_items": []
        }

# SSE事件流接口已迁移到 /tasks/events 端点

@router.get("/factory-detail", response_model=FactoryDetailResponse, summary="获取园区的基本数据")
async def get_factory_detail(
    community_name: str
):
    """
    获取指定园区的基本数据

    参数:
    - community_name: 园区名称

    返回:
    - 包含园区厂房基本数据的信息
    """
    try:
        if not community_name:
            return {
                "success": False,
                "message": "请提供园区名称参数",
                "data": None
            }

        logger.info(f"开始获取园区基本数据，园区名称：{community_name}")

        # 调用服务获取数据
        result = await FactoryUploadService.get_factory_detail(community_name)

        return {
            "success": result["success"],
            "message": result["message"],
            "data": result.get("data")
        }
    except Exception as e:
        logger.error(f"获取园区基本数据失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取园区基本数据失败: {str(e)}",
            "data": None
        }