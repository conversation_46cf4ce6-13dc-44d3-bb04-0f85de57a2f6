#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统信息API路由
"""

import os
import time
import json
import datetime
import platform
import subprocess
import re
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Header, Body
from pydantic import BaseModel
from pathlib import Path as FilePath

router = APIRouter(tags=["系统信息"])

# 应用启动时间
START_TIME = datetime.datetime.now()

# 获取cities.json的绝对路径
cities_json_path = FilePath(__file__).parent.parent / "config" / "cities.json"
settings_path = FilePath(__file__).parent.parent / "config" / "settings.py"

# 项目根目录路径
PROJECT_ROOT = FilePath(__file__).parent.parent

# 远程仓库地址
REPO_URL = "https://e.coding.net/g-sjiu2679/liando_house/liando_erp_integration"

# 访问密钥 - 简单验证机制
ADMIN_KEY = "Meiyoumima333."

# 版本文件路径
VERSION_FILE = PROJECT_ROOT / "version.txt"

# 基础版本号
BASE_VERSION = "1.0."

class UpdateSystemRequest(BaseModel):
    admin_key: str

def verify_admin(admin_key: str = Body(..., embed=True)):
    """验证管理员密钥"""
    if admin_key != ADMIN_KEY:
        raise HTTPException(status_code=401, detail="管理员密钥验证失败，无权执行此操作")
    return True

def get_current_version():
    """获取当前系统版本号"""
    # 如果版本文件存在，则从文件中读取版本号
    if VERSION_FILE.exists():
        try:
            with open(VERSION_FILE, 'r') as f:
                return f.read().strip()
        except:
            pass
    
    # 否则使用Git提交历史作为版本号
    try:
        # 获取当前提交的短哈希值
        git_hash = subprocess.check_output(
            ["git", "rev-parse", "--short", "HEAD"],
            cwd=PROJECT_ROOT,
            stderr=subprocess.PIPE,
            text=True
        ).strip()
        
        # 获取提交数量作为修订版本号
        commit_count = subprocess.check_output(
            ["git", "rev-list", "--count", "HEAD"],
            cwd=PROJECT_ROOT,
            stderr=subprocess.PIPE,
            text=True
        ).strip()
        
        # 返回格式化的版本号
        version = f"{BASE_VERSION}{commit_count}"
        
        # 保存到版本文件
        with open(VERSION_FILE, 'w') as f:
            f.write(version)
        
        return version
    except Exception as e:
        # 如果无法获取Git信息，则返回默认版本号
        return "1.0.5"

@router.get("/system-info", response_model=Dict[str, Any])
async def get_system_info():
    """获取系统信息"""
    # 计算运行时间
    uptime = datetime.datetime.now() - START_TIME
    uptime_str = f"{uptime.days}天 {uptime.seconds // 3600}小时 {(uptime.seconds % 3600) // 60}分钟"
    
    try:
        # 从settings.py中读取环境配置
        environment = "production"  # 默认值
        default_city = "cc"  # 默认值
        
        # 简单地解析settings.py文件，获取环境和默认城市设置
        if settings_path.exists():
            with open(settings_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.startswith('ENVIRONMENT'):
                        parts = line.split('=')
                        if len(parts) > 1:
                            environment = parts[1].strip().strip("'\"")
                    elif line.startswith('DEFAULT_CITY'):
                        parts = line.split('=')
                        if len(parts) > 1:
                            default_city = parts[1].strip().strip("'\"")
        
        # 从cities.json文件中读取城市配置
        cities = []
        if cities_json_path.exists():
            with open(cities_json_path, 'r', encoding='utf-8') as f:
                city_config = json.load(f)
                cities = list(city_config.get("domains", {}).keys())
        
        # 获取当前版本号
        version = get_current_version()
        
        return {
            "version": version,
            "start_time": START_TIME.isoformat(),
            "uptime": uptime_str,
            "status": "所有API均通过安全检测",
            "cities": cities,
            "environment": environment,
            "default_city": default_city,
            "system": platform.system(),
            "python_version": platform.python_version()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")

@router.post("/update-system", response_model=Dict[str, Any])
async def update_system(admin_verified: bool = Depends(verify_admin)):
    """
    更新系统
    
    从远程仓库拉取最新代码(master分支)
    """
    try:
        # 确保当前目录是Git仓库
        if not os.path.exists(os.path.join(PROJECT_ROOT, ".git")):
            raise HTTPException(
                status_code=500, 
                detail="当前目录不是Git仓库，无法执行更新操作"
            )
        
        # 记录当前版本号和Git提交
        current_version = get_current_version()
        try:
            current_commit = subprocess.check_output(
                ["git", "rev-parse", "HEAD"], 
                cwd=PROJECT_ROOT,
                stderr=subprocess.PIPE,
                text=True
            ).strip()
        except subprocess.CalledProcessError:
            current_commit = "无法获取当前版本"
        
        # 拉取远程最新代码
        try:
            # 使用Coding用户名和密码创建远程URL
            git_username = "13225521581"  # 替换为实际用户名
            git_password = "Meiyoumima333."  # 替换为实际密码
            auth_repo_url = REPO_URL.replace("https://", f"https://{git_username}:{git_password}@")
            
            # 使用带凭据的URL拉取代码
            pull_result = subprocess.check_output(
                ["git", "pull", auth_repo_url, "master"], 
                cwd=PROJECT_ROOT,
                stderr=subprocess.STDOUT,
                text=True
            ).strip()
        except subprocess.CalledProcessError as e:
            raise HTTPException(
                status_code=500, 
                detail=f"拉取远程代码失败: {e.output}"
            )
        
        # 获取更新后的版本号和Git提交
        try:
            new_commit = subprocess.check_output(
                ["git", "rev-parse", "HEAD"], 
                cwd=PROJECT_ROOT,
                stderr=subprocess.PIPE,
                text=True
            ).strip()
        except subprocess.CalledProcessError:
            new_commit = "无法获取更新后版本"
        
        # 判断是否有更新
        is_updated = current_commit != new_commit
        
        # 如果有更新，则更新版本号
        if is_updated:
            # 删除版本文件，强制重新生成版本号
            if VERSION_FILE.exists():
                os.remove(VERSION_FILE)
            new_version = get_current_version()
        else:
            new_version = current_version
        
        return {
            "success": True,
            "message": "系统更新成功" if is_updated else "系统已是最新版本",
            "details": pull_result,
            "previous_commit": current_commit[:8] if len(current_commit) > 8 else current_commit,
            "current_commit": new_commit[:8] if len(new_commit) > 8 else new_commit,
            "previous_version": current_version,
            "current_version": new_version,
            "is_updated": is_updated,
            "update_time": datetime.datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新系统时发生错误: {str(e)}") 