#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
竞帖分析路由模块
提供竞帖数据分析的API接口
"""

from fastapi import APIRouter, Query, HTTPException
from typing import List, Dict, Optional
from services.listing_analysis_service import ListingAnalysisService

# 创建路由
router = APIRouter(
    prefix="/listing-analysis",
    tags=["竞帖分析"],
    responses={404: {"description": "Not found"}},
)

# 初始化服务
listing_service = ListingAnalysisService()

@router.get("/cities", response_model=List[str])
async def get_cities():
    """
    获取所有已有数据的城市列表
    
    返回:
    - 城市名称列表
    """
    cities = listing_service.get_all_cities()
    return cities

@router.get("/districts/{city}", response_model=List[str])
async def get_districts(city: str):
    """
    获取指定城市的区列表
    
    参数:
    - city: 城市名称
    
    返回:
    - 区名称列表
    """
    districts = listing_service.get_districts_by_city(city)
    return districts

@router.get("/stats", response_model=Dict)
async def get_basic_statistics(
    city: Optional[str] = None,
    district: Optional[str] = None,
    listing_type: Optional[int] = Query(None, ge=0, le=1, description="租售类型：0-出租，1-出售"),
    year: Optional[int] = Query(None, description="年份，例如：2023"),
    month: Optional[int] = Query(None, ge=1, le=12, description="月份，1-12"),
    start_date: Optional[str] = Query(None, description="开始日期，格式为YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式为YYYY-MM-DD")
):
    """
    获取基础统计数据，包括总竞争帖子数、竞争对手数、我方帖子总数和我方帖子占比
    
    参数:
    - city: 城市名称，可选
    - district: 区名称，可选
    - listing_type: 租售类型，可选（0-出租，1-出售）
    - year: 年份，可选，例如：2023
    - month: 月份，可选，1-12
    - start_date: 开始日期，格式为YYYY-MM-DD，可选
    - end_date: 结束日期，格式为YYYY-MM-DD，可选
    
    返回:
    - 基础统计数据字典，包含以下字段：
      - totalPosts: 总帖子数
      - totalCompetitors: 竞争对手数量（不含"联东"的公司，去重后的数量）
      - newPosts: 我方帖子总数（公司名包含"联东"的帖子数量）
      - ourPostsPercentage: 我方帖子占比（百分比）
      - competitorPostsTotal: 竞争对手帖子总数（公司名不包含"联东"的帖子数量）
      - avgPostsPerAccount: 我方单账号平均帖子数（按poster_name字段统计）
      - ourAccountsCount: 我方发帖账号数量（按poster_name字段去重统计）
    """
    stats = listing_service.get_basic_statistics(
        city, 
        district, 
        listing_type, 
        year, 
        month,
        start_date,
        end_date
    )
    return stats

@router.get("/top-companies", response_model=List[Dict])
async def get_top_companies(
    city: Optional[str] = None,
    district: Optional[str] = None,
    listing_type: Optional[int] = Query(None, ge=0, le=1, description="租售类型：0-出租，1-出售"),
    limit: int = Query(5, ge=1, le=20, description="获取的公司数量，默认5个，最多20个"),
    start_date: Optional[str] = Query(None, description="开始日期，格式为YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式为YYYY-MM-DD")
):
    """
    获取帖子数量最多的前N个公司
    
    参数:
    - city: 城市名称，可选
    - district: 区名称，可选
    - listing_type: 租售类型，可选（0-出租，1-出售）
    - limit: 返回记录数量，默认5条，最多20条
    - start_date: 开始日期，格式为YYYY-MM-DD，可选
    - end_date: 结束日期，格式为YYYY-MM-DD，可选
    
    返回:
    - 公司列表，每个元素包含以下字段：
      - name: 公司名称
      - postCount: 帖子数量
      - lastPostTime: 最近发帖时间，格式为YYYY-MM-DD HH:MM:SS
    """
    companies = listing_service.get_top_companies(
        city, 
        district, 
        limit, 
        listing_type,
        start_date,
        end_date
    )
    return companies

@router.get("/time-series", response_model=Dict)
async def get_time_series_data(
    city: Optional[str] = None,
    district: Optional[str] = None,
    listing_type: Optional[int] = Query(None, ge=0, le=1, description="租售类型：0-出租，1-出售"),
    days: Optional[int] = Query(None, ge=7, le=90, description="获取的天数，已弃用，保留用于兼容"),
    start_date: Optional[str] = Query(None, description="开始日期，格式为YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式为YYYY-MM-DD")
):
    """
    获取一段时间内的每日发帖数量统计，按公司分组
    
    参数:
    - city: 城市名称，可选
    - district: 区名称，可选
    - listing_type: 租售类型，可选（0-出租，1-出售）
    - days: 天数，已弃用，保留用于兼容
    - start_date: 开始日期，格式为YYYY-MM-DD，可选
    - end_date: 结束日期，格式为YYYY-MM-DD，可选
    
    返回:
    - 时间序列数据，包含以下字段：
      - series: 时间序列数据列表，每个元素包含日期和各公司的发帖数量
      - companies: 公司名称列表，包括"我方"、实际竞争对手名称和"其他"
    """
    time_series = listing_service.get_time_series_data(
        city, 
        district, 
        days, 
        listing_type, 
        start_date, 
        end_date
    )
    return time_series