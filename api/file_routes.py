#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文件上传和管理API路由
处理文件上传、下载和管理的API请求
"""

import os
import logging
from typing import Dict, List, Optional
from fastapi import APIRouter, File, Form, UploadFile, Request, Query, HTTPException
from pydantic import BaseModel, Field
from services.upload_service import UploadService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter(
    prefix="/files",
    tags=["文件管理"],
    responses={404: {"description": "Not found"}},
)

# 请求和响应模型
class FileUploadResponse(BaseModel):
    """文件上传响应模型"""
    success: bool = Field(..., description="上传是否成功")
    message: str = Field(..., description="提示信息")
    data: Optional[Dict] = Field(None, description="上传结果数据，包含文件URL等信息")


# 园区图库响应模型
class CampusGalleryResponse(BaseModel):
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="提示信息")
    data: Optional[Dict] = Field(None, description="园区图片数据，包含园区列表及其图片视频情况")
    pagination: Optional[Dict] = Field(None, description="分页信息，包含总数、当前页、总页数等")





class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="提示信息")
    data: Optional[Dict] = Field(None, description="结果数据，包含任务状态信息")


class DeleteImageRequest(BaseModel):
    """删除图片请求模型"""
    community: str = Field(..., description="园区名称")
    image_url: str = Field(..., description="要删除的图片URL")
    image_type: str = Field(..., description="图片类型字段名")


class DeleteImageResponse(BaseModel):
    """删除图片响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="提示信息")
    data: Optional[Dict] = Field(None, description="删除结果数据")


@router.get("/cities", response_model=dict)
async def get_cities():
    """
    获取支持的城市列表

    返回:
    - **success**: 是否成功
    - **cities**: 城市列表，包含城市代码和名称
    """
    try:
        # 从城市配置中获取城市信息
        city_config = UploadService.load_city_config()

        # 构建返回的城市列表
        cities = []
        for code, name in city_config.get("names", {}).items():
            cities.append({
                "code": code,
                "name": name,
                "domain": city_config.get("domains", {}).get(code, "")
            })

        # 按城市名称排序
        cities.sort(key=lambda x: x["name"])

        return {
            "success": True,
            "cities": cities
        }

    except Exception as e:
        logger.error(f"获取城市列表异常: {str(e)}")
        return {
            "success": False,
            "message": f"获取城市列表失败: {str(e)}",
            "cities": []
        }


@router.get("/campus-gallery", response_model=CampusGalleryResponse)
async def get_campus_gallery(
    community: str = None,
    page: int = Query(1, description="页码，从1开始", ge=1),
    page_size: int = Query(10, description="每页大小", ge=1, le=50)
):
    """
    获取园区图片信息

    Args:
        community: 园区名称，不提供则返回所有园区的分页列表
        page: 页码，从1开始
        page_size: 每页大小，默认10，最大50

    Returns:
        园区图片信息，包含分页元数据
    """
    return await UploadService.get_campus_gallery(community, page, page_size)





@router.delete("/campus-gallery/image", response_model=DeleteImageResponse)
async def delete_campus_image(request: DeleteImageRequest):
    """
    删除园区图库中的指定图片

    - **community**: 园区名称
    - **image_url**: 要删除的图片URL
    - **image_type**: 图片类型字段名

    返回:
    - **success**: 操作是否成功
    - **message**: 提示信息
    - **data**: 删除结果数据
    """
    try:
        result = await UploadService.delete_campus_image(
            request.community,
            request.image_url,
            request.image_type
        )
        return result
    except Exception as e:
        logger.error(f"删除图片异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除图片失败: {str(e)}")