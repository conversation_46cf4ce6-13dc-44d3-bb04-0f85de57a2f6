#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优推科技房源推送API路由
提供房源推送和检查结果相关的API端点
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List, Union
from services.push_service import PushService, WEBSITE_NAMES
from services.base_service import BaseService
from database.db_connection import get_db_connection
import json
import logging
from pathlib import Path
from services.qrcode_oss_service import QrcodeOssService
from services.sms_service import SmsService

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/youtui/push",
    tags=["优推科技房源推送"],
    responses={404: {"description": "接口不存在"}},
)

# 获取cities.json的绝对路径
cities_json_path = Path(__file__).parent.parent / "config" / "cities.json"

# 从JSON文件加载城市配置
def load_city_config():
    try:
        if not cities_json_path.exists():
            return {"domains": {}, "names": {}, "accounts": {}}
        
        with open(cities_json_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载城市配置失败: {str(e)}")
        return {"domains": {}, "names": {}, "accounts": {}}

# 定义请求和响应模型
class PushRequest(BaseModel):
    """推送请求模型"""
    erp_house_id: str = Field(..., description="ERP系统房源ID")
    tt_id: str = Field(..., description="优推科技房源ID")
    website_id: str = Field(..., description="网站ID，必须提供")
    city: Optional[str] = Field(None, description="城市代码，如'bj'(北京)、'cc'(长春)等")
    city_name: Optional[str] = Field(None, description="城市名称，如'北京'、'长春'等")
    user_id: Optional[str] = Field(None, description="用户ID，如果为空则使用默认ID")
    user_key: Optional[str] = Field(None, description="用户密钥，如果为空则使用默认密钥")

class PushInfoRequest(BaseModel):
    """推送信息查询请求模型"""
    process_id: str = Field(..., description="推送进程ID")
    city: Optional[str] = Field(..., description="城市代码，必填，如'bj'(北京)、'cc'(长春)等")
    city_name: Optional[str] = Field(None, description="城市名称，如'北京'、'长春'等")
    user_id: Optional[str] = Field(None, description="用户ID，如果为空则使用城市默认ID")
    user_key: Optional[str] = Field(None, description="用户密钥，如果为空则使用城市默认密钥")

class PushInfoItem(BaseModel):
    """推送信息项模型"""
    process_id: Optional[str] = None
    status: Optional[str] = None
    web_id: Optional[str] = None
    web_name: Optional[str] = None
    username: Optional[str] = None
    act_id: Optional[str] = None
    action: Optional[str] = None
    house_id: Optional[str] = None
    erp_id: Optional[str] = None
    push_url: Optional[str] = None
    push_id: Optional[str] = None
    push_msg: Optional[str] = None
    message: Optional[str] = None
    renzheng: Optional[str] = None  # 房本认证二维码字段
    accountInfo: Optional[Dict[str, Any]] = Field(None, description="账号详细信息，仅在需要房本认证时返回")  # 新增账号信息字段

class PushResponse(BaseModel):
    """房源推送响应模型"""
    success: bool
    process_id: Optional[str] = None
    message: str
    push_info: Optional[Union[PushInfoItem, List[PushInfoItem]]] = None

class WebsiteResponse(BaseModel):
    """网站列表响应模型"""
    websites: Dict[str, str]
    message: str = "获取网站列表成功"

class WebsiteRequest(BaseModel):
    """网站列表请求模型"""
    city: str = Field(..., description="城市代码，必填，如'bj'(北京)、'cc'(长春)等")
    city_name: Optional[str] = Field(None, description="城市名称，如'北京'、'长春'等")
    user_id: Optional[str] = Field(None, description="用户ID，如果为空则使用城市默认ID")
    user_key: Optional[str] = Field(None, description="用户密钥，如果为空则使用城市默认密钥")

class PushRecordsRequest(BaseModel):
    """推送记录查询请求模型"""
    city: str = Field(..., description="城市代码，必填，如'bj'(北京)、'cc'(长春)等")
    city_name: Optional[str] = Field(None, description="城市名称，如'北京'、'长春'等")
    user_id: Optional[str] = Field(None, description="用户ID，如果为空则使用城市默认ID")
    user_key: Optional[str] = Field(None, description="用户密钥，如果为空则使用城市默认密钥")
    erp_house_id: Optional[str] = Field(None, description="ERP系统房源ID，可选，用于筛选")
    tt_id: Optional[str] = Field(None, description="优推科技房源ID，可选，用于筛选")
    website_id: Optional[str] = Field(None, description="网站ID，可选，用于筛选")
    limit: Optional[int] = Field(50, description="返回记录数量限制，默认50条")
    offset: Optional[int] = Field(0, description="记录偏移量，默认0")

class PushRecordsResponse(BaseModel):
    """推送记录响应模型"""
    success: bool
    message: str
    total: int
    records: List[Dict[str, Any]]

class SendSmsRequest(BaseModel):
    """发送房本认证短信请求模型"""
    process_id: str = Field(..., description="推送进程ID")
    city: str = Field(..., description="城市代码，必填，如'bj'(北京)、'cc'(长春)等")
    city_name: Optional[str] = Field(None, description="城市名称，如'北京'、'长春'等")
    user_id: Optional[str] = Field(None, description="用户ID，如果为空则使用城市默认ID")
    user_key: Optional[str] = Field(None, description="用户密钥，如果为空则使用城市默认密钥")

class SendSmsResponse(BaseModel):
    """发送短信响应模型"""
    success: bool
    message: str
    request_id: Optional[str] = None
    phone_number: Optional[str] = None

# 依赖注入
def get_push_service(
    city: str,
    city_name: Optional[str] = None,
    user_id: Optional[str] = None,
    user_key: Optional[str] = None
):
    """
    获取房源推送服务实例
    
    根据提供的城市代码或城市名称初始化服务
    
    必须提供city或city_name参数之一，用于确定服务所使用的城市和账号
    """
    # 创建一个临时BaseService实例来获取城市配置
    base_service = BaseService()
    city_config = base_service.city_config
    
    actual_city = city
    
    # 如果提供了城市名称，转换为城市代码
    if not actual_city and city_name:
        # 创建城市名称到代码的反向映射
        name_to_code = {}
        for code, name in city_config.get("names", {}).items():
            name_to_code[name] = code
        
        city_code = name_to_code.get(city_name)
        if city_code:
            actual_city = city_code
            logger.info(f"依赖注入: 将城市名称 '{city_name}' 转换为城市代码 '{actual_city}'")
        else:
            logger.error(f"依赖注入: 未找到城市名称 '{city_name}' 对应的城市代码")
            raise HTTPException(
                status_code=400,
                detail=f"未找到城市 '{city_name}' 的信息，支持的城市有: {', '.join(city_config.get('names', {}).values())}"
            )
    
    # 如果还是没有城市代码，抛出异常
    if not actual_city:
        logger.error("依赖注入: 未提供有效的城市参数")
        raise HTTPException(
            status_code=400,
            detail="必须提供city或city_name参数之一"
        )
    
    # 验证城市代码是否有效
    if actual_city not in city_config.get("domains", {}):
        logger.error(f"依赖注入: 无效的城市代码 '{actual_city}'")
        raise HTTPException(
            status_code=400,
            detail=f"无效的城市代码 '{actual_city}'，支持的城市代码有: {', '.join(city_config.get('domains', {}).keys())}"
        )
        
    logger.info(f"依赖注入: 使用城市代码 '{actual_city}'")
    
    # 获取对应城市的用户账号
    city_account = city_config.get("accounts", {}).get(actual_city, {})
    
    # 如果未提供用户ID/密钥，则使用城市账号中的值
    if not user_id:
        user_id = city_account.get('user_id')
    if not user_key:
        user_key = city_account.get('user_key')
    
    logger.info(f"依赖注入: 使用城市 '{actual_city}' 的账号，用户ID: {user_id}")
    
    # 创建一个新的服务实例，使用正确的城市代码和对应的账号
    service = PushService(city=actual_city, default_user_id=user_id, default_user_key=user_key)
    
    # 扩展服务实例的get_push_info方法
    original_get_push_info = service.get_push_info
    
    def extended_get_push_info(*args, **kwargs):
        push_info = original_get_push_info(*args, **kwargs)
        
        # 如果获取到推送信息，并且需要房本认证
        if push_info and isinstance(push_info, dict):
            if "push_msg" in push_info and "房本认证" in push_info["push_msg"]:
                try:
                    # 获取数据库连接
                    connection = get_db_connection()
                    try:
                        with connection.cursor() as cursor:
                            # 先检查push_history表中是否已有记录和image_url
                            check_sql = """
                                SELECT image_url
                                FROM push_history
                                WHERE process_id = %s
                                LIMIT 1
                            """
                            # 使用原始的加密process_id进行查询
                            original_process_id = kwargs.get('process_id')  # 获取传入的原始process_id
                            logger.info(f"检查记录: process_id={original_process_id}")
                            cursor.execute(check_sql, (original_process_id,))
                            history_result = cursor.fetchone()
                            logger.info(f"查询结果: {history_result}")

                            # 查询账号信息
                            account_sql = """
                                SELECT account, name 
                                FROM account_info 
                                WHERE account = %s 
                                AND city = %s 
                                AND is_deleted = 0
                                LIMIT 1
                            """
                            logger.info(f"查询账号信息: account={push_info['username']}, city={service.city}")
                            cursor.execute(account_sql, (push_info["username"], service.city))
                            account_info = cursor.fetchone()
                            logger.info(f"账号信息查询结果: {account_info}")
                            
                            if account_info:
                                # 添加account和name信息
                                push_info["accountInfo"] = {
                                    "account": account_info["account"],
                                    "name": account_info["name"] if account_info["name"] else "未知"
                                }
                                logger.info(f"找到需要房本认证的账号: {account_info['account']}, 姓名: {account_info['name'] if account_info['name'] else '未知'}")

                            if history_result and history_result["image_url"]:
                                # 如果已经有存储的image_url，直接使用
                                push_info["renzheng"] = history_result["image_url"]
                                logger.info(f"使用已存储的二维码图片URL: {history_result['image_url']}")
                            else:
                                # 只有在没有存储的image_url时才进行上传
                                logger.info("未找到已存储的image_url，准备上传图片")
                                qr_data = push_info["renzheng"]
                                
                                # 创建专用的房本认证二维码OSS服务实例
                                qrcode_oss_service = QrcodeOssService()
                                
                                # 处理二维码图片
                                if qr_data.startswith('data:image'):
                                    try:
                                        logger.info("检测到base64图片数据，开始处理")
                                        # 使用专用服务上传base64图片
                                        success, result = qrcode_oss_service.upload_qrcode_base64(
                                            base64_data=qr_data,
                                            username=push_info['username'],
                                            process_id=push_info['process_id']
                                        )
                                        
                                        if success:
                                            # 更新二维码URL为OSS地址
                                            new_url = result["url"]
                                            push_info["renzheng"] = new_url
                                            logger.info(f"二维码图片已上传到OSS: {new_url}")
                                            
                                            # 更新数据库中的image_url
                                            update_sql = """
                                                UPDATE push_history 
                                                SET image_url = %s 
                                                WHERE process_id = %s
                                            """
                                            # 使用原始的加密process_id进行更新
                                            params = (new_url, original_process_id)
                                            logger.info(f"执行SQL: {update_sql}")
                                            logger.info(f"SQL参数: {params}")
                                            
                                            cursor.execute(update_sql, params)
                                            affected_rows = cursor.rowcount
                                            connection.commit()
                                            logger.info(f"更新完成，影响行数: {affected_rows}")
                                        else:
                                            logger.error(f"二维码图片上传失败: {result.get('error', '未知错误')}")
                                    except Exception as e:
                                        logger.error(f"处理base64图片时发生错误: {str(e)}")
                                elif qr_data.startswith(('http://', 'https://')):
                                    # 处理URL图片
                                    try:
                                        # 使用专用服务上传URL图片
                                        success, result = qrcode_oss_service.upload_qrcode_url(
                                            image_url=qr_data,
                                            username=push_info['username'],
                                            process_id=push_info['process_id']
                                        )
                                        
                                        if success:
                                            # 更新二维码URL为OSS地址
                                            new_url = result["url"]
                                            push_info["renzheng"] = new_url
                                            logger.info(f"二维码图片已上传到OSS: {new_url}")
                                            
                                            # 更新数据库中的image_url
                                            update_sql = """
                                                UPDATE push_history 
                                                SET image_url = %s 
                                                WHERE process_id = %s
                                            """
                                            # 使用原始的加密process_id进行更新
                                            original_process_id = kwargs.get('process_id')  # 获取传入的原始process_id
                                            logger.info("查询参数详情:")
                                            logger.info(f"- 原始加密process_id: '{original_process_id}'")
                                            logger.info(f"- 解密后process_id: '{push_info['process_id']}'")
                                            
                                            # 打印完整的SQL和参数
                                            params = (new_url, original_process_id)
                                            logger.info(f"执行SQL: {update_sql}")
                                            logger.info(f"SQL参数: {params}")
                                            
                                            cursor.execute(update_sql, params)
                                            affected_rows = cursor.rowcount
                                            connection.commit()
                                            logger.info(f"更新完成，影响行数: {affected_rows}")
                                        else:
                                            logger.error(f"二维码图片上传失败: {result.get('error', '未知错误')}")
                                    except Exception as e:
                                        logger.error(f"下载二维码图片失败: {str(e)}")
                    finally:
                        connection.close()
                except Exception as e:
                    logger.error(f"查询账号信息失败: {str(e)}")
        
        return push_info
    
    # 替换原有的get_push_info方法
    service.get_push_info = extended_get_push_info
    
    return service

@router.post("/websites", response_model=WebsiteResponse, summary="获取用户可推送的网站列表")
async def get_websites(
    request: WebsiteRequest
):
    """
    获取当前用户可推送房源的网站列表（需要已绑定网站账号）
    
    此接口使用异步方式调用第三方API，避免阻塞其他接口。
    
    请求体示例:
    ```json
    {
        "city": "bj",          // 城市代码，必填
        "city_name": "北京",     // 城市名称，可选
        "user_id": null,       // 用户ID，可选，为空则使用城市默认ID
        "user_key": null       // 用户密钥，可选，为空则使用城市默认密钥
    }
    ```
    
    - **city**: 城市代码，必填，如'bj'(北京)、'cc'(长春)等
    - **city_name**: 城市名称，如'北京'、'长春'等，可选
    - **user_id**: 用户ID，如果为空则使用城市默认ID
    - **user_key**: 用户密钥，如果为空则使用城市默认密钥
    
    返回已绑定账号的网站列表，仅返回用户可实际使用的网站
    """
    logger.info(f"获取网站列表请求 - 城市: {request.city}")
    
    # 创建服务实例
    service = get_push_service(
        city=request.city,
        city_name=request.city_name,
        user_id=request.user_id,
        user_key=request.user_key
    )
    
    # 异步获取用户的网站账号信息
    available_websites = await service.async_get_website_accounts(
        request.user_id if request.user_id else service.default_user_id,
        request.user_key if request.user_key else service.default_user_key
    )
    
    if available_websites and isinstance(available_websites, dict) and len(available_websites) > 0:
        logger.info(f"成功异步获取用户可用网站列表，城市: {service.city}，共{len(available_websites)}个网站")
        return {
            "websites": available_websites,
            "message": f"成功异步获取城市 '{service.city}' 的用户可用网站列表，共{len(available_websites)}个网站"
        }
    else:
        # 如果未获取到可用网站，返回默认网站列表
        logger.warning(f"未异步获取到城市 '{service.city}' 的用户特定网站列表，返回所有支持的网站列表")
        return {
            "websites": WEBSITE_NAMES,
            "message": f"未异步获取到城市 '{service.city}' 的用户特定网站列表，返回所有支持的网站（可能需要先绑定网站账号）"
        }

@router.post("/get-push-info", response_model=PushResponse, summary="获取推送结果详情")
async def get_push_info(
    request: PushInfoRequest
):
    """
    获取指定推送进程ID的推送结果详情
    
    - **必须提供 city 或 city_name 参数，指定查询的城市**
    
    每个城市使用不同的默认账号：
    - 长春(cc): 使用长春账号
    - 北京(bj): 使用北京账号
    - 其他城市: 根据配置使用对应账号
    
    请求体示例:
    ```json
    {
        "process_id": "1234567890",  // 推送进程ID
        "city": "bj",                // 城市代码，必填
        "city_name": "北京",          // 城市名称，可选
        "user_id": null,             // 用户ID，可选，为空则使用城市默认ID
        "user_key": null             // 用户密钥，可选，为空则使用城市默认密钥
    }
    ```
    """
    logger.info(f"获取推送结果请求 - 城市: {request.city}, 进程ID: {request.process_id}")
    
    # 创建服务实例
    service = get_push_service(
        city=request.city,
        city_name=request.city_name,
        user_id=request.user_id,
        user_key=request.user_key
    )
    
    # 使用请求中的用户ID和密钥，如果未提供则使用服务实例的默认值
    user_id = request.user_id if request.user_id else service.default_user_id
    user_key = request.user_key if request.user_key else service.default_user_key
    
    # 获取推送信息
    push_info = service.get_push_info(
        process_id=request.process_id,
        user_id=user_id,
        user_key=user_key
    )
    
    # 构建响应
    if push_info:
        return {
            "success": True,
            "process_id": request.process_id,
            "message": "获取推送详情成功",
            "push_info": push_info
        }
    else:
        return {
            "success": False,
            "process_id": request.process_id,
            "message": "获取推送详情失败",
            "push_info": None
        }

@router.post("/records", response_model=PushRecordsResponse)
async def get_push_records(request: PushRecordsRequest):
    """
    获取房源推送记录
    
    - 查询指定城市的推送记录
    - 可以通过房源ID、优推科技房源ID、网站ID等条件筛选
    - 支持分页查询
    """
    logger.info(f"获取推送记录请求 - 城市: {request.city}")
    
    try:
        # 创建服务实例
        service = get_push_service(
            city=request.city,
            city_name=request.city_name,
            user_id=request.user_id,
            user_key=request.user_key
        )
        
        # 获取推送记录
        result = service.get_push_records(
            erp_house_id=request.erp_house_id,
            tt_id=request.tt_id,
            website_id=request.website_id,
            limit=request.limit,
            offset=request.offset
        )
        
        logger.info(f"成功获取推送记录 - 城市: {service.city}, 条数: {result['total']}")
        
        return {
            "success": True,
            "message": f"成功获取城市 '{service.city}' 的推送记录，共 {result['total']} 条",
            "total": result['total'],
            "records": result['records']
        }
    
    except Exception as e:
        logger.error(f"获取推送记录失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取推送记录失败: {str(e)}"
        )

@router.post("/send-auth-sms", response_model=SendSmsResponse, summary="发送房本认证短信通知")
async def send_auth_sms(
    request: SendSmsRequest
):
    """
    发送房本认证短信通知
    
    - 根据推送进程ID获取推送信息
    - 如果推送信息中包含房本认证信息，则发送短信通知给对应销售
    - 短信包含销售姓名和房本认证二维码图片路径
    
    请求体示例:
    ```json
    {
        "process_id": "1234567890",  // 推送进程ID
        "city": "bj",                // 城市代码，必填
        "city_name": "北京",          // 城市名称，可选
        "user_id": null,             // 用户ID，可选，为空则使用城市默认ID
        "user_key": null             // 用户密钥，可选，为空则使用城市默认密钥
    }
    ```
    """
    logger.info(f"发送房本认证短信请求 - 城市: {request.city}, 进程ID: {request.process_id}")
    
    # 创建服务实例
    service = get_push_service(
        city=request.city,
        city_name=request.city_name,
        user_id=request.user_id,
        user_key=request.user_key
    )
    
    # 使用请求中的用户ID和密钥，如果未提供则使用服务实例的默认值
    user_id = request.user_id if request.user_id else service.default_user_id
    user_key = request.user_key if request.user_key else service.default_user_key
    
    # 获取推送信息
    push_info = service.get_push_info(
        process_id=request.process_id,
        user_id=user_id,
        user_key=user_key
    )
    
    # 检查是否包含房本认证信息
    if not push_info or not isinstance(push_info, dict):
        logger.error(f"获取推送信息失败，无法发送短信: {request.process_id}")
        return {
            "success": False,
            "message": "获取推送信息失败，无法发送短信",
            "request_id": None,
            "phone_number": None
        }
    
    # 检查是否需要房本认证
    if "push_msg" not in push_info or "房本认证" not in push_info["push_msg"] or "renzheng" not in push_info:
        logger.warning(f"推送信息不包含房本认证信息，无需发送短信: {request.process_id}")
        return {
            "success": False,
            "message": "推送信息不包含房本认证信息，无需发送短信",
            "request_id": None,
            "phone_number": None
        }
    
    # 确保账号信息存在
    if "accountInfo" not in push_info or not push_info["accountInfo"]:
        logger.warning("未找到账号信息，无法发送短信通知")
        return {
            "success": False,
            "message": "未找到账号信息，无法发送短信通知",
            "request_id": None,
            "phone_number": None
        }
    
    try:
        # 获取销售姓名和图片路径
        sales_name = push_info["accountInfo"].get("name", "销售")
        image_url = push_info["renzheng"]
        
        # 直接使用username作为联系方式
        phone_number = push_info["username"]
        logger.info(f"使用账号作为联系方式: {phone_number}")
        
        # 从完整URL中提取路径参数
        path_param = image_url.split('/')[-1] if '/' in image_url else image_url
        
        # 创建短信服务实例
        sms_service = SmsService()
        
        # 准备短信模板参数
        template_param = {
            'name': sales_name,
            'path': path_param
        }
        
        # 发送短信
        logger.info(f"发送房本认证通知短信到: {phone_number}, 参数: {template_param}")
        sms_result = sms_service.send_notification(phone_number, template_param)
        
        if sms_result["success"]:
            logger.info(f"房本认证通知短信发送成功: {phone_number}")
            return {
                "success": True,
                "message": "通知短信已发送",
                "request_id": sms_result.get("request_id", ""),
                "phone_number": phone_number
            }
        else:
            logger.error(f"房本认证通知短信发送失败: {sms_result['message']}")
            return {
                "success": False,
                "message": f"通知短信发送失败: {sms_result['message']}",
                "request_id": None,
                "phone_number": phone_number
            }
    except Exception as e:
        logger.error(f"发送房本认证通知短信时发生错误: {str(e)}")
        return {
            "success": False,
            "message": f"短信通知错误: {str(e)}",
            "request_id": None,
            "phone_number": None
        }
