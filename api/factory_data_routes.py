#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from fastapi import APIRouter, HTTPException, Query, Body
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from database.db_connection import db_cursor
from services.push_service import WEBSITE_NAMES
from api.push_routes import get_push_service
from api.client import AsyncYoutuiApiClient
import logging
from datetime import datetime
import json
import base64
import html
import difflib
import uuid
import random
from ai_generate_content.sensitive_word_filter_service import async_filter_and_rewrite_content, sensitive_word_filter
from services.gallery_image_service import gallery_image_service
from services.sse_service import sse_service
from services.push_progress_service import push_progress_service

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/factory",
    tags=["厂房数据"],
    responses={404: {"description": "接口不存在"}},
)

class FactoryDataResponse(BaseModel):
    total: int
    total_pages: int
    current_page: int
    data: List[Dict[str, Any]]

class FactoryDataRequest(BaseModel):
    """厂房数据列表请求模型"""
    city: Optional[str] = None
    community: Optional[str] = None
    page: int = 1
    page_size: int = 10

class WebsiteInfo(BaseModel):
    name: str
    account_status: str
    username: str
    actions: List[str]

class WebsiteListResponse(BaseModel):
    success: bool
    message: str
    websites: Any
    website_names: Dict[str, str]
    account_names: Dict[str, str] = {}

class PushResponse(BaseModel):
    success: bool
    message: str
    process_id: Optional[str] = None

class PushFactoryRequest(BaseModel):
    factory_id: str = Field(..., description="厂房数据ID")
    website_id: str = Field(..., description="网站ID")

class PushFactoryResponse(BaseModel):
    success: bool
    message: str
    process_id: Optional[str] = None
    push_info: Optional[Dict[str, Any]] = None
    task_id: Optional[str] = None

@router.post("/list")
async def get_factory_data(request: FactoryDataRequest) -> FactoryDataResponse:
    try:
        base_query = """
            SELECT
                id,
                erp_id,
                youtui_house_id,
                city,
                community,
                ts_status,
                type
            FROM parsed_factory_data
            WHERE ts_status = 0
            AND is_deleted = 0
            AND is_published = 1
        """
        count_query = """
            SELECT COUNT(*) as total
            FROM parsed_factory_data
            WHERE ts_status = 0
            AND is_deleted = 0
            AND is_published = 1
        """

        conditions = []
        params = []

        if request.city:
            conditions.append("city = %s")
            params.append(request.city)

        if request.community:
            conditions.append("community LIKE %s")
            params.append(f"%{request.community}%")

        if conditions:
            condition_str = " AND " + " AND ".join(conditions)
            base_query += condition_str
            count_query += condition_str

        base_query += "LIMIT %s OFFSET %s"

        with db_cursor() as cursor:
            cursor.execute(count_query, params)
            total = cursor.fetchone()["total"]

            total_pages = (total + request.page_size - 1) // request.page_size
            offset = (request.page - 1) * request.page_size

            cursor.execute(base_query, params + [request.page_size, offset])
            rows = cursor.fetchall()

            data = []
            for row in rows:
                data.append({
                    "id": row["id"],
                    "erp_id": row["erp_id"],
                    "youtui_id": row["youtui_house_id"],
                    "city": row["city"],
                    "community": row["community"],
                    "ts_status": row["ts_status"],
                    "type": row["type"]
                })

            return FactoryDataResponse(
                total=total,
                total_pages=total_pages,
                current_page=request.page,
                data=data
            )

    except Exception:
        raise HTTPException(status_code=500, detail="获取厂房数据失败")

@router.post("/get-available-websites/{factory_id}")
async def get_available_websites(
    factory_id: str
) -> WebsiteListResponse:
    try:
        with db_cursor() as cursor:
            cursor.execute("""
                SELECT city, city_name
                FROM parsed_factory_data
                WHERE id = %s AND is_deleted = 0
            """, [factory_id])

            factory_data = cursor.fetchone()

            if not factory_data:
                return WebsiteListResponse(
                    success=False,
                    message=f"未找到ID为 {factory_id} 的厂房数据",
                    websites=[],
                    website_names={},
                    account_names={}
                )

            city = factory_data["city"]
            city_name = factory_data["city_name"]

            service = get_push_service(city=city, city_name=city_name)

            raw_websites = await service.async_get_website_accounts(
                service.default_user_id,
                service.default_user_key
            )

            if raw_websites:
                # 处理所有网站数据
                filtered_websites = []
                website_names = {}
                account_names = {}

                # 收集所有账号ID
                account_ids = []
                for website in raw_websites:
                    user_name_arr = website.get('userNameArr', {})
                    account_ids.extend(user_name_arr.keys())

                # 查询账号信息
                account_name_map = {}
                if account_ids:
                    try:
                        cursor.execute("""
                            SELECT account_id, name
                            FROM account_info
                            WHERE account_id IN %s AND is_deleted = 0
                        """, [tuple(account_ids)])

                        for row in cursor.fetchall():
                            account_name_map[row['account_id']] = row['name'] if row['name'] else "未知"
                    except Exception:
                        pass

                for website in raw_websites:
                    web_id = str(website.get('webID'))

                    # 获取账号信息和推广类型
                    user_name_arr = website.get('userNameArr', {})
                    act_arr = website.get('actArr', {})

                    # 创建新的网站配置
                    new_website = {
                        "webID": web_id,
                        "userNameArr": user_name_arr,  # 保持完整的账号信息
                    }

                    # 处理推广类型
                    if isinstance(act_arr, dict):
                        new_website["actArr"] = act_arr  # 保持完整的推广类型信息
                    else:
                        new_website["actArr"] = {"00": "上架"}  # 列表格式转换为字典格式

                    filtered_websites.append(new_website)
                    website_names[web_id] = WEBSITE_NAMES.get(web_id, f"网站{web_id}")

                    # 添加账号姓名信息，确保值为字符串类型
                    for account_id in user_name_arr.keys():
                        account_names[account_id] = account_name_map.get(account_id, "未知")

                return WebsiteListResponse(
                    success=True,
                    message=f"成功异步获取城市 '{city}' 的可用网站列表",
                    websites=filtered_websites,
                    website_names=website_names,
                    account_names=account_names
                )
            else:
                return WebsiteListResponse(
                    success=False,
                    message=f"未异步获取到城市 '{city}' 的可用网站列表",
                    websites=[],
                    website_names={},
                    account_names={}
                )

    except Exception:
        return WebsiteListResponse(
            success=False,
            message="获取可用网站列表失败",
            websites=[],
            website_names={},
            account_names={}
        )

def calculate_similarity(text1: str, text2: str) -> float:
    if not text1 or not text2:
        return 0.0

    # 使用difflib的SequenceMatcher计算相似度
    matcher = difflib.SequenceMatcher(None, text1, text2)
    return matcher.ratio()

def check_title_history(title: str, website_id: str, city: str, similarity_threshold: float = 0.85) -> bool:
    try:
        with db_cursor() as cursor:
            cursor.execute("""
                SELECT push_topic
                FROM push_history
                WHERE website_id = %s AND city = %s
                ORDER BY push_time DESC
                LIMIT 1000
            """, [website_id, city])

            historical_titles = cursor.fetchall()

            if not historical_titles:
                return False

            max_similarity = 0.0

            for record in historical_titles:
                historical_title = record['push_topic']
                similarity = calculate_similarity(title, historical_title)

                if similarity > max_similarity:
                    max_similarity = similarity

                if similarity >= similarity_threshold:
                    return True

            return False

    except Exception:
        return False

def get_park_marketing_content(park_name: str, exclude_ids: List[int] = None) -> Optional[Dict[str, Any]]:

    try:
        with db_cursor() as cursor:
            exclude_condition = ""
            params = [park_name]

            if exclude_ids:
                placeholders = ', '.join(['%s'] * len(exclude_ids))
                exclude_condition = f" AND id NOT IN ({placeholders})"
                params.extend(exclude_ids)

            query = f"""
                SELECT id, title, description
                FROM property_marketing_content
                WHERE park_name = %s AND status = 1{exclude_condition}
                ORDER BY created_at DESC
                LIMIT 1
            """

            cursor.execute(query, params)
            result = cursor.fetchone()

            if result:
                return {
                    'id': result['id'],
                    'title': result['title'],
                    'description': result['description']
                }
            else:
                return None

    except Exception:
        return None

def mark_marketing_content_as_used(content_id: int) -> bool:
    try:
        with db_cursor() as cursor:
            cursor.execute("""
                UPDATE property_marketing_content
                SET status = 0
                WHERE id = %s
            """, [content_id])
            return True
    except Exception:
        return False

def process_rental_type(original_type: str) -> str:
    """
    处理租售类型，当为混合类型时随机选择其中一个
    
    Args:
        original_type: 原始租售类型，可能是"出租"、"出售"或"出售、出租"
        
    Returns:
        处理后的明确租售类型："出租"或"出售"
    """
    if not original_type:
        return "出租"  # 默认返回出租
    
    # 检查是否为混合类型
    if "、" in original_type or "," in original_type:
        # 分割混合类型
        types = []
        if "、" in original_type:
            types = [t.strip() for t in original_type.split("、")]
        elif "," in original_type:
            types = [t.strip() for t in original_type.split(",")]
        
        # 过滤有效的租售类型
        valid_types = [t for t in types if t in ["出租", "出售"]]
        
        if valid_types:
            selected_type = random.choice(valid_types)
            return selected_type
        else:
            return "出租"
    else:
        # 单一类型，直接返回
        return original_type.strip()

@router.post("/push/{factory_id}", response_model=PushFactoryResponse)
async def push_factory(
    factory_id: str,
    website_id: str = Query(..., description="网站ID"),
    website_info: Dict[str, Any] = Body(..., description="网站账号信息，包含 webID, userNameArr, actArr")
):
    task_id = str(uuid.uuid4())

    try:
        # 发送推送开始状态
        await sse_service.broadcast_event("push_status", {
            "task_id": task_id,
            "factory_id": factory_id,
            "step": 0,
            "status": "started",
            "message": "推送任务已开始"
        })

        # 验证 website_info 格式
        if not isinstance(website_info, dict) or 'webID' not in website_info or 'actArr' not in website_info:
            await sse_service.broadcast_event("push_status", {
                "task_id": task_id,
                "factory_id": factory_id,
                "step": 0,
                "status": "error",
                "message": "无效的网站账号信息格式"
            })
            return PushFactoryResponse(
                success=False,
                message="无效的网站账号信息格式",
                task_id=task_id
            )

        # 查询厂房数据
        with db_cursor() as cursor:
            cursor.execute("""
                SELECT
                    id, erp_id, youtui_house_id, city, city_name,
                    topic, zone, street, type, towards, community, content,
                    total, square, address, type4property, floor,
                    floortype, Storey, rentunitdaily, slease, Planteia,
                    Plantstructure, Typewarehouse, Plantnew, isNewHouse,
                    AgentFree, ts_status, infrastructure,
                    video_url,years,Type4Years,Landnature,Powersupply,Loadbearing
                FROM parsed_factory_data
                WHERE id = %s AND is_deleted = 0
            """, [factory_id])

            factory_data = cursor.fetchone()

            if not factory_data:
                await sse_service.broadcast_event("push_status", {
                    "task_id": task_id,
                    "factory_id": factory_id,
                    "step": 0,
                    "status": "error",
                    "message": f"未找到ID为 {factory_id} 的厂房数据"
                })
                return PushFactoryResponse(
                    success=False,
                    message=f"未找到ID为 {factory_id} 的厂房数据",
                    task_id=task_id
                )

            # 获取城市信息和房源ID
            city = factory_data["city"]
            city_name = factory_data["city_name"]
            erp_id = factory_data["erp_id"]
            youtui_house_id = factory_data["youtui_house_id"]

            if not youtui_house_id:
                await sse_service.broadcast_event("push_status", {
                    "task_id": task_id,
                    "factory_id": factory_id,
                    "step": 0,
                    "status": "error",
                    "message": "该厂房数据尚未发布，请先发布后再推送"
                })
                return PushFactoryResponse(
                    success=False,
                    message="该厂房数据尚未发布，请先发布后再推送",
                    task_id=task_id
                )

            # 创建推送服务实例（用于获取默认用户ID）
            service = get_push_service(city=city, city_name=city_name)

            # 创建异步API客户端
            async_client = AsyncYoutuiApiClient(environment='production', city=city)

            # 初始化推送进度到Redis
            await push_progress_service.start_push_task(
                factory_id, task_id, 1, "正在为园区选择优质帖子"
            )

            marketing_content = get_park_marketing_content(factory_data['community'])

            if not marketing_content:
                await push_progress_service.update_push_progress(
                    factory_id, 1, "error",
                    f"该园区 '{factory_data['community']}' 没有可用的营销内容，请先生成营销内容"
                )
                return PushFactoryResponse(
                    success=False,
                    message=f"该园区 '{factory_data['community']}' 没有可用的营销内容，请先生成营销内容",
                    task_id=task_id
                )

            original_title = marketing_content['title']
            original_content = marketing_content['description']
            marketing_content_id = marketing_content['id']

            await push_progress_service.update_push_progress(
                factory_id, 1, "completed", "园区优质帖子选择完成"
            )

            await push_progress_service.update_push_progress(
                factory_id, 2, "processing", "正在进行内容过滤"
            )

            # 对营销内容进行异步敏感词过滤和热搜词重写
            try:
                await push_progress_service.update_push_progress(
                    factory_id, 2, "processing", "正在优化内容"
                )

                # 使用集成的敏感词过滤+热搜词重写功能
                filter_result = await async_filter_and_rewrite_content(original_title, original_content, factory_data['community'])

                # 从集成结果中提取数据
                if filter_result.get('success', False):
                    filtered_title = filter_result['data'].get('topic', original_title)
                    filtered_content = filter_result['data'].get('content', original_content)

                    await push_progress_service.update_push_progress(
                        factory_id, 2, "completed", "内容处理完成"
                    )
                else:
                    filtered_title = original_title
                    filtered_content = original_content

                    await push_progress_service.update_push_progress(
                        factory_id, 2, "warning", "内容处理部分失败，使用原始内容继续"
                    )

                remaining_title_words = sensitive_word_filter.detect_sensitive_words(filtered_title)
                remaining_content_words = sensitive_word_filter.detect_sensitive_words(filtered_content)

                if remaining_title_words or remaining_content_words:
                    all_remaining_words = list(set(remaining_title_words + remaining_content_words))

                    await push_progress_service.complete_push_task(
                        factory_id, False,
                        f"内容包含敏感词，无法推送。敏感词: {', '.join(all_remaining_words)}",
                        is_sensitive_word_error=True
                    )

                    return PushFactoryResponse(
                        success=False,
                        message=f"内容包含敏感词，无法推送。敏感词: {', '.join(all_remaining_words)}",
                        task_id=task_id
                    )



            except Exception:
                await push_progress_service.update_push_progress(
                    factory_id, 2, "warning", "内容处理失败，使用原始内容"
                )
                filtered_title = original_title
                filtered_content = original_content

            used_content_ids = [marketing_content_id]
            max_attempts = 5
            attempt = 1

            while check_title_history(filtered_title, website_id, city) and attempt <= max_attempts:
                alternative_content = get_park_marketing_content(factory_data['community'], exclude_ids=used_content_ids)

                if alternative_content:
                    new_title = alternative_content['title']
                    new_content = alternative_content['description']
                    new_content_id = alternative_content['id']

                    try:
                        filter_result = await async_filter_and_rewrite_content(new_title, new_content, factory_data['community'])

                        if filter_result.get('success', False):
                            filtered_title = filter_result['data'].get('topic', new_title)
                            filtered_content = filter_result['data'].get('content', new_content)
                        else:
                            filtered_title = new_title
                            filtered_content = new_content


                        remaining_title_words = sensitive_word_filter.detect_sensitive_words(filtered_title)
                        remaining_content_words = sensitive_word_filter.detect_sensitive_words(filtered_content)

                        if remaining_title_words or remaining_content_words:
                            all_remaining_words = list(set(remaining_title_words + remaining_content_words))
                            return PushFactoryResponse(
                                success=False,
                                message=f"内容包含敏感词，无法推送。敏感词: {', '.join(all_remaining_words)}"
                            )

                    except Exception:
                        filtered_title = new_title
                        filtered_content = new_content

                    marketing_content_id = new_content_id
                    used_content_ids.append(new_content_id)
                    attempt += 1
                else:
                    break

            # 检查是否仍然重复
            if check_title_history(filtered_title, website_id, city):
                await push_progress_service.update_push_progress(
                    factory_id, 2, "error",
                    f"该园区 '{factory_data['community']}' 的所有可用营销内容都已被使用，请重新生成营销内容"
                )
                return PushFactoryResponse(
                    success=False,
                    message=f"该园区 '{factory_data['community']}' 的所有可用营销内容都已被使用，请重新生成营销内容",
                    task_id=task_id
                )

            # 标记最终使用的营销内容为已使用
            mark_marketing_content_as_used(marketing_content_id)

            await push_progress_service.update_push_progress(
                factory_id, 2, "completed", "内容过滤完成"
            )

            await push_progress_service.update_push_progress(
                factory_id, 3, "processing", "正在为园区分配图片"
            )

            # 处理租售类型 - 如果是混合类型则随机选择
            processed_rental_type = process_rental_type(factory_data['type'])

            # 构建推送数据
            push_data = {
                'platformID': '10007',
                'companyID': '1013',
                'userID': service.default_user_id,
                'act': 'PUSH',
                'ttid': youtui_house_id,

                'id': erp_id,
                'topic': filtered_title,
                'zone': factory_data['zone'],
                'street': factory_data['street'],
                'type': processed_rental_type,  # 使用处理后的租售类型
                'community': factory_data['address'],
                'content': filtered_content,
                'total': factory_data['total'],
                'square': factory_data['square'],
                'address': factory_data['address'],
                'type4property': factory_data['type4property'],
                'floor': factory_data['floor'],
                'floortype': factory_data['floortype'],
                'Storey': factory_data['Storey'],
                'webid': website_id,
            }

            # 添加必填字段
            required_fields = [
                'rentunitdaily', 'slease', 'Planteia',
                'Plantstructure', 'Typewarehouse', 'Plantnew', 'isNewHouse',
                'AgentFree', 'infrastructure',
                "years", "Landnature", "Powersupply", "Loadbearing"
            ]

            push_data['towards'] = "东南朝向"
            push_data['Type4Yearend'] = "2073"

            # 处理其他必填字段
            for field in required_fields:
                push_data[field] = factory_data.get(field, '')

            community_name = factory_data.get('community')
            image_fields_from_db = {
                'thumb': '',
                'floorplans': '',
                'photointerior': '',
                'photooutdoor': ''
            }

            if community_name:
                image_fields_from_db, _ = gallery_image_service.get_assigned_gallery_images(
                    park_name=community_name,
                    strategy='random_field_table',
                    exclude_fields=['total_plan_image']
                )

            for field, url_string in image_fields_from_db.items():
                push_data[field] = html.unescape(url_string) if url_string else ''

            webarr = [website_info]
            webcontent_data = {"webarr": webarr}
            webcontent_json = json.dumps(webcontent_data, ensure_ascii=False)
            push_data['webcontent'] = base64.b64encode(webcontent_json.encode('utf-8')).decode('utf-8')

            await push_progress_service.update_push_progress(
                factory_id, 3, "completed", "园区图片分配完成"
            )

            await push_progress_service.update_push_progress(
                factory_id, 4, "processing", "正在生成视频"
            )

            community_name = factory_data.get('community')
            original_video_url = factory_data.get('video_url')

            if community_name:
                try:
                    # 提取分配的图片URL列表
                    assigned_image_urls = []
                    for field, url_string in image_fields_from_db.items():
                        if url_string and url_string.strip():
                            # 处理可能包含多个URL的字段（用逗号分隔）
                            urls = url_string.split(",")
                            for url in urls:
                                clean_url = url.strip()
                                if clean_url and not clean_url.lower().endswith('.pdf'):
                                    # 清理URL：移除@前缀
                                    if clean_url.startswith('@'):
                                        clean_url = clean_url[1:]
                                    assigned_image_urls.append(clean_url)

                    assigned_image_urls = list(dict.fromkeys(assigned_image_urls))

                    if len(assigned_image_urls) >= 2:

                        from services.async_video_service import submit_video_generation_with_images

                        task_id = submit_video_generation_with_images(
                            campus_name=community_name,
                            image_urls=assigned_image_urls,
                            factory_id=str(factory_id),
                            fps=30,
                            duration_per_image=3.0,
                            transition_type="push_right",
                            transition_duration=1.0
                        )

                        await push_progress_service.update_push_progress(
                            factory_id, 4, "processing", "等待视频生成完成"
                        )

                        from services.async_video_service import async_wait_for_task_completion

                        video_result = await async_wait_for_task_completion(task_id, timeout_seconds=300, poll_interval=5)

                        if video_result and video_result.get("success"):
                            new_video_url = video_result.get("data", {}).get("video_url")
                            if new_video_url:
                                push_data['videoUrl'] = new_video_url
                            else:
                                if original_video_url:
                                    push_data['videoUrl'] = original_video_url
                        else:
                            if original_video_url:
                                push_data['videoUrl'] = original_video_url
                    else:
                        if original_video_url:
                            push_data['videoUrl'] = original_video_url

                except Exception:
                    if original_video_url:
                        push_data['videoUrl'] = original_video_url

            await push_progress_service.update_push_progress(
                factory_id, 4, "completed", "视频生成完成"
            )

            await push_progress_service.update_push_progress(
                factory_id, 5, "processing", "正在推送"
            )

            final_title = push_data.get('topic', '')
            final_content = push_data.get('content', '')

            final_title_words = sensitive_word_filter.detect_sensitive_words(final_title)
            final_content_words = sensitive_word_filter.detect_sensitive_words(final_content)

            if final_title_words or final_content_words:
                all_final_words = list(set(final_title_words + final_content_words))
                await sse_service.broadcast_event("push_status", {
                    "task_id": task_id,
                    "factory_id": factory_id,
                    "step": 5,
                    "status": "error",
                    "message": f"内容包含敏感词，推送被阻止。敏感词: {', '.join(all_final_words)}"
                })
                return PushFactoryResponse(
                    success=False,
                    message=f"内容包含敏感词，推送被阻止。敏感词: {', '.join(all_final_words)}",
                    task_id=task_id
                )

            # 调用异步推送接口
            result = await async_client.async_sync_house(
                user_id=service.default_user_id,
                house_data=push_data,
                action='PUSH',
                ttid=youtui_house_id
            )

            if not result:
                await sse_service.broadcast_event("push_status", {
                    "task_id": task_id,
                    "factory_id": factory_id,
                    "step": 5,
                    "status": "error",
                    "message": "推送失败"
                })
                return PushFactoryResponse(
                    success=False,
                    message="推送失败",
                    task_id=task_id
                )

            if result.get('status') == '1':
                process_info = result.get('processinfo')
                house_id = result.get('houseid') or result.get('houseId') or result.get('HOUSEID')

                # 更新推送状态
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute("""
                    UPDATE parsed_factory_data
                    SET ts_status = 1,
                        update_time = %s
                    WHERE id = %s
                """, [current_time, factory_id])

                account_id = None
                try:
                    if website_info:
                        if 'actArr' in website_info and isinstance(website_info['actArr'], dict):
                            account_keys = list(website_info['actArr'].keys())
                            if account_keys:
                                account_id = account_keys[0]

                        if not account_id and 'userNameArr' in website_info and isinstance(website_info['userNameArr'], dict):
                            account_keys = list(website_info['userNameArr'].keys())
                            if account_keys:
                                account_id = account_keys[0]
                except Exception:
                    pass



                cursor.execute("""
                    INSERT INTO push_history (
                        push_time, erp_house_id, tt_id, website_id,
                        website_name, city, process_id, account_id,
                        push_topic, push_content, community
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, [
                    current_time,
                    factory_data["erp_id"],
                    factory_data["youtui_house_id"],
                    website_id,
                    WEBSITE_NAMES.get(website_id, f"网站{website_id}"),
                    factory_data["city"],
                    process_info,
                    account_id,
                    push_data["topic"],
                    push_data["content"],
                    factory_data["community"]
                ])



                if account_id and house_id:
                    try:
                        from services.youtui_service import YoutuiService
                        youtui_service = YoutuiService(city=factory_data["city"])
                        youtui_service._add_pushed_post_to_cache(
                            account_id=account_id,
                            house_id=house_id,
                            title=push_data["topic"],
                            website_id=website_id,
                            process_id=process_info,
                            city=factory_data["city"],
                            house_type=processed_rental_type
                        )
                    except Exception:
                        pass

                await push_progress_service.complete_push_task(
                    factory_id, True, f"推送成功，进程ID: {process_info}"
                )

                return PushFactoryResponse(
                    success=True,
                    message=f"推送成功，进程ID: {process_info}",
                    process_id=process_info,
                    task_id=task_id
                )
            else:
                await push_progress_service.complete_push_task(
                    factory_id, False, "推送失败"
                )
                return PushFactoryResponse(
                    success=False,
                    message="推送失败",
                    process_id=None,
                    task_id=task_id
                )

    except Exception:

        try:
            await push_progress_service.complete_push_task(
                factory_id, False, "推送失败"
            )
        except:
            pass
        return PushFactoryResponse(
            success=False,
            message="推送失败",
            task_id=task_id
        )