#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优推科技API路由
提供与优推科技相关的API端点
"""

import json
import logging
from fastapi import APIRouter, Depends, HTTPException, Query, Body, UploadFile, File, Form
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
from services.youtui_service import YoutuiService
from services.base_service import BaseService
from config.settings import DEFAULT_CITY
from pathlib import Path
from fastapi.responses import FileResponse
from database.db_connection import get_db_connection

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/youtui",
    tags=["优推科技"],
    responses={404: {"description": "接口不存在"}},
)

# 获取cities.json的绝对路径
cities_json_path = Path(__file__).parent.parent / "config" / "cities.json"

# 从JSON文件加载城市配置
def load_city_config():
    try:
        if not cities_json_path.exists():
            return {"domains": {}, "names": {}, "accounts": {}}

        with open(cities_json_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载城市配置失败: {str(e)}")
        return {"domains": {}, "names": {}, "accounts": {}}

# 解析套餐类型，返回帖子数量限制
def parse_package_limit(taocan: str) -> int:
    """
    解析套餐类型字符串，返回帖子数量限制

    支持的格式：
    - "20-0": 20额度套餐，已用0条，限制20条
    - "29-11": 已发29条，还可发11条，限制40条
    - "40-5": 40额度套餐，已用5条，限制40条

    Args:
        taocan: 套餐类型字符串

    Returns:
        int: 帖子数量限制
    """
    try:
        if not taocan or '-' not in taocan:
            return 20  # 默认限制

        parts = taocan.split('-')
        if len(parts) != 2:
            return 20  # 默认限制

        used = int(parts[0])
        remaining = int(parts[1])

        # 总限制 = 已用 + 剩余
        total_limit = used + remaining

        # 确保限制在合理范围内
        if total_limit <= 0:
            return 20
        elif total_limit > 100:  # 设置最大限制
            return 100
        else:
            return total_limit

    except (ValueError, IndexError) as e:
        logger.warning(f"解析套餐类型失败: {taocan}, 错误: {str(e)}, 使用默认限制20")
        return 20

# 定义请求和响应模型
class BindAccountResponse(BaseModel):
    user_id: str
    user_key: str
    city: str

class UnbindAccountRequest(BaseModel):
    """账号解绑请求模型"""
    city: str = Field(..., description="城市代码，必填，如'bj'(北京)、'cc'(长春)等")
    city_name: Optional[str] = Field(None, description="城市名称，如'北京'、'长春'等")
    user_id: Optional[str] = Field(None, description="用户标识ID，如果不提供则使用城市对应的默认用户ID")

class UnbindAccountResponse(BaseModel):
    """账号解绑响应模型"""
    success: bool
    message: str
    city: str


class PublishFactoryResponse(BaseModel):
    house_id: Optional[str] = None
    success: bool
    message: str

class BatchPublishResponse(BaseModel):
    """批量发布响应模型"""
    total: int = Field(..., description="总处理数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    success_ids: List[str] = Field(default_factory=list, description="成功发布的房源ID列表")
    failed_items: List[Dict[str, Any]] = Field(default_factory=list, description="失败的项目详情")
    message: str

class CityInfoResponse(BaseModel):
    """城市信息响应模型"""
    city_name: str
    city_code: str
    domain: str

class CityRequest(BaseModel):
    """城市请求模型"""
    city: str = Field(..., description="城市代码，必填，如'bj'(北京)、'cc'(长春)等")
    city_name: Optional[str] = Field(None, description="城市名称，如'北京'、'长春'等")
    
class CityInfoRequest(BaseModel):
    """城市信息请求模型"""
    city_name: str = Field(..., description="城市名称，如'北京'、'长春'等")

class BindAccountRequest(BaseModel):
    """账号绑定请求模型"""
    city: str = Field(..., description="城市代码，必填，如'bj'(北京)、'cc'(长春)等")
    city_name: Optional[str] = Field(None, description="城市名称，如'北京'、'长春'等")
    company1: str = Field(..., description="公司名称，必填")
    company2: str = Field(..., description="门店名称，必填")
    name: str = Field(..., description="姓名，必填")
    mobile: str = Field(..., description="手机号，必填")
    user_id: str = Field(..., description="用户标识ID，必填")
    userkey: str = Field(..., description="登录密码，必填")
    idcard: Optional[str] = Field(None, description="身份证号，可选")
    path: Optional[str] = Field(None, description="头像路径，可选")

class BindAccountResponse(BaseModel):
    """账号绑定响应模型"""
    success: bool
    user_id: Optional[str] = None
    user_key: Optional[str] = None
    message: str
    city: str

# 添加新的请求模型
class UpdateAccountNameRequest(BaseModel):
    """更新账号姓名请求模型"""
    web_id: str = Field(..., description="网站ID")
    account_id: str = Field(..., description="账号ID")
    name: str = Field(..., description="姓名")

class UpdateAccountNameResponse(BaseModel):
    """更新账号姓名响应模型"""
    success: bool
    message: str

# 新增模型定义
class AgentPostStatsRequest(BaseModel):
    """获取经纪人名下所有帖子点击量请求模型"""
    city: str = Field(..., description="城市代码，必填，如'bj'(北京)、'cc'(长春)等")
    city_name: Optional[str] = Field(None, description="城市名称，如'北京'、'长春'等")
    user_id: Optional[str] = Field(None, description="用户标识ID，如果不提供则使用城市对应的默认用户ID")
    webcontent: Optional[str] = Field(None, description="网站与账号信息的Base64编码JSON")

class AgentPostStatsResponse(BaseModel):
    """获取经纪人名下所有帖子点击量响应模型"""
    success: bool
    message: str
    data: Optional[List[Dict[str, Any]]] = None
    taocan: Optional[str] = Field(None, description="套餐类型，如'20-0'、'40-0'等")
    package_limit: Optional[int] = Field(None, description="套餐帖子数量限制")

# 添加新的请求和响应模型
class OfflineHouseRequest(BaseModel):
    """房源下架请求模型"""
    city: str = Field(..., description="城市代码，必填，如'bj'(北京)、'cc'(长春)等")
    city_name: Optional[str] = Field(None, description="城市名称，如'北京'、'长春'等")
    user_id: Optional[str] = Field(None, description="用户标识ID，如果不提供则使用城市对应的默认用户ID")
    remote_id: str = Field(..., description="要下架的房源远程ID")
    webcontent: Optional[str] = Field(None, description="网站与账号信息的Base64编码JSON")
    house_type: Optional[str] = Field(None, description="房源类型，6表示出售，3表示出租，默认为6")

class OfflineHouseResponse(BaseModel):
    """房源下架响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class ActivateHouseRequest(BaseModel):
    """房源激活请求模型"""
    city: str = Field(..., description="城市代码，必填，如'bj'(北京)、'cc'(长春)等")
    city_name: Optional[str] = Field(None, description="城市名称，如'北京'、'长春'等")
    user_id: Optional[str] = Field(None, description="用户标识ID，如果不提供则使用城市对应的默认用户ID")
    remote_id: str = Field(..., description="要激活的房源远程ID")
    webcontent: Optional[str] = Field(None, description="网站与账号信息的Base64编码JSON")
    house_type: Optional[str] = Field(None, description="房源类型，6表示出售，3表示出租，默认为6")

class ActivateHouseResponse(BaseModel):
    """房源激活响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

# 依赖注入
def get_youtui_service(
    city: str,
    city_name: Optional[str] = None
):
    """
    获取优推科技服务实例
    
    根据提供的城市代码或城市名称初始化服务
    
    必须提供city或city_name参数之一，用于确定服务所使用的城市和账号
    """
    # 创建一个临时BaseService实例来获取城市配置
    base_service = BaseService()
    city_config = base_service.city_config
    
    actual_city = city
    
    # 如果提供了城市名称，转换为城市代码
    if not actual_city and city_name:
        # 创建城市名称到代码的反向映射
        name_to_code = {}
        for code, name in city_config.get("names", {}).items():
            name_to_code[name] = code
        
        city_code = name_to_code.get(city_name)
        if city_code:
            actual_city = city_code
            logger.info(f"依赖注入: 将城市名称 '{city_name}' 转换为城市代码 '{actual_city}'")
        else:
            logger.error(f"依赖注入: 未找到城市名称 '{city_name}' 对应的城市代码")
            raise HTTPException(
                status_code=400,
                detail=f"未找到城市 '{city_name}' 的信息，支持的城市有: {', '.join(city_config.get('names', {}).values())}"
            )
    
    # 如果还是没有城市代码，抛出异常
    if not actual_city:
        logger.error("依赖注入: 未提供有效的城市参数")
        raise HTTPException(
            status_code=400,
            detail="必须提供city或city_name参数之一"
        )
    
    # 验证城市代码是否有效
    if actual_city not in city_config.get("domains", {}):
        logger.error(f"依赖注入: 无效的城市代码 '{actual_city}'")
        raise HTTPException(
            status_code=400,
            detail=f"无效的城市代码 '{actual_city}'，支持的城市代码有: {', '.join(city_config.get('domains', {}).keys())}"
        )
        
    logger.info(f"依赖注入: 使用城市代码 '{actual_city}'")
    
    # 创建一个新的服务实例，使用正确的城市代码
    service = YoutuiService(city=actual_city)
    
    return service

@router.post("/cities", summary="获取支持的城市列表")
async def get_cities():
    """获取系统支持的所有城市列表"""
    # 从JSON文件加载城市配置
    city_config = load_city_config()
    
    # 提取城市代码、域名和名称
    cities = []
    for code in city_config.get("domains", {}):
        city_data = {
            "city_code": code,
            "domain": city_config.get("domains", {}).get(code, ""),
            "city_name": city_config.get("names", {}).get(code, code)
        }
        cities.append(city_data)
    
    # 按城市代码排序
    cities.sort(key=lambda x: x["city_code"])
    
    return {
        "cities": cities,
        "count": len(cities),
        "status": "success"
    }

@router.post("/city-info", response_model=CityInfoResponse, summary="获取城市信息")
async def get_city_info(request: CityInfoRequest):
    """根据城市名称获取城市信息"""
    # 从JSON文件加载城市配置
    city_config = load_city_config()
    
    # 创建城市名称到代码的反向映射
    name_to_code = {}
    for code, name in city_config.get("names", {}).items():
        name_to_code[name] = code
    
    city_name = request.city_name
    city_code = name_to_code.get(city_name)
    
    if not city_code:
        logger.error(f"未找到城市名称 '{city_name}' 对应的城市代码")
        raise HTTPException(
            status_code=404,
            detail=f"未找到城市 '{city_name}' 的信息，支持的城市有: {', '.join(city_config.get('names', {}).values())}"
        )
    
    domain = city_config.get("domains", {}).get(city_code, "")
    
    logger.info(f"获取城市 '{city_name}' 的信息：代码={city_code}, 域名={domain}")
    
    return {
        "city_name": city_name,
        "city_code": city_code,
        "domain": domain
    }


@router.get("/push/websites", summary="获取可推送的网站列表")
async def get_push_websites(
    city: str = Query(..., description="城市代码"),
    user_id: Optional[str] = Query(None, description="用户ID（可选）")
):
    """
    获取指定城市下可推送的网站列表，并保存端口数据到数据库
    
    此接口使用异步方式调用第三方API，避免阻塞其他接口。
    
    请求参数:
    - **city**: 城市代码，必填，如'bj'(北京)、'cc'(长春)等
    - **user_id**: 用户ID，可选。如果不提供，将使用城市配置中的默认用户ID
    
    返回:
    - **websites**: 网站列表，包含网站ID、账号信息和推广类型等
    - **message**: 状态描述
    - **saved_ports**: 成功保存的端口数量
    - **skipped_ports**: 跳过的重复端口数量
    """
    try:
        # 创建服务实例
        service = get_youtui_service(city=city)
        
        # 如果未提供user_id，则使用城市配置中的默认值
        if not user_id:
            user_id = service.get_user_id(city)
            logger.info(f"使用默认用户ID: {user_id}")
        
        # 使用PushService异步获取网站数据
        from services.push_service import PushService
        push_service = PushService(city=city)
        website_data = await push_service.async_get_website_data()
        
        saved_count = 0
        skipped_count = 0
        
        if website_data:
            logger.info(f"成功异步获取城市 '{city}' 的网站列表，共{len(website_data)}个网站")
            
            # 保存获取到的端口数据
            try:
                # 准备要保存的数据数组
                ports_to_save = []
                
                # 遍历所有网站，提取端口数据
                for site in website_data:
                    webID = site.get('webID')
                    if not webID:
                        continue
                        
                    # 处理所有账号
                    if 'userNameArr' in site and site['userNameArr']:
                        for account_id, account_data in site['userNameArr'].items():
                            if account_data and len(account_data) > 0:
                                username = account_data[0] if account_data[0] else '未知'
                                
                                # 构建端口数据对象
                                port_data = {
                                    "account": username,
                                    "account_id": account_id,
                                    "city": city,
                                    "port_type": str(webID),
                                    "is_deleted": 0
                                }
                                
                                ports_to_save.append(port_data)
                
                # 如果有数据需要保存
                if ports_to_save:
                    logger.info(f"准备保存 {len(ports_to_save)} 条端口数据")
                    
                    # 获取数据库连接
                    connection = get_db_connection()
                    try:
                        with connection.cursor() as cursor:
                            for port in ports_to_save:
                                # 检查是否已存在相同记录
                                check_sql = """
                                    SELECT id, name FROM account_info 
                                    WHERE account_id = %s 
                                    AND city = %s 
                                    AND port_type = %s 
                                    AND is_deleted = 0
                                """
                                cursor.execute(check_sql, (
                                    port["account_id"],
                                    port["city"],
                                    port["port_type"]
                                ))
                                
                                existing_record = cursor.fetchone()
                                
                                if existing_record:
                                    # 记录已存在，更新userNameArr中的姓名
                                    skipped_count += 1
                                    logger.debug(f"跳过已存在记录: {port['account_id']} - {port['city']} - {port['port_type']}")
                                    
                                    # 更新网站数据中的姓名
                                    for site in website_data:
                                        if str(site.get('webID')) == port["port_type"]:
                                            if 'userNameArr' in site and port["account_id"] in site['userNameArr']:
                                                account_data = site['userNameArr'][port["account_id"]]
                                                if len(account_data) >= 3:
                                                    # 添加或更新姓名字段
                                                    if len(account_data) == 3:
                                                        account_data.append(existing_record["name"] or "")
                                                    else:
                                                        account_data[3] = existing_record["name"] or ""
                                else:
                                    # 插入新记录
                                    insert_sql = """
                                        INSERT INTO account_info (account, account_id, city, port_type, create_time, is_deleted)
                                        VALUES (%s, %s, %s, %s, NOW(), %s)
                                    """
                                    
                                    cursor.execute(insert_sql, (
                                        port["account"],
                                        port["account_id"],
                                        port["city"],
                                        port["port_type"],
                                        port.get("is_deleted", 0)
                                    ))
                                    
                                    saved_count += 1
                            
                            # 提交事务
                            connection.commit()
                            
                        logger.info(f"端口数据保存完成，共保存{saved_count}条记录，跳过{skipped_count}条重复记录")
                    
                    finally:
                        # 关闭连接
                        connection.close()
                
            except Exception as e:
                logger.error(f"保存端口数据失败: {str(e)}")
                # 保存失败不影响原来的接口返回
            
            # 返回原接口数据以及保存结果
            return {
                "websites": website_data,
                "message": f"成功异步获取城市 '{city}' 的用户可用网站列表，共{len(website_data)}个网站",
                "saved_ports": saved_count,
                "skipped_ports": skipped_count
            }
        else:
            logger.error(f"城市 '{city}' 未获取到网站数据")
            return {
                "websites": [],
                "message": f"城市 '{city}' 未获取到网站数据",
                "saved_ports": 0,
                "skipped_ports": 0
            }
            
    except Exception as e:
        logger.error(f"获取推送网站列表失败: {str(e)}")
        return {
            "websites": [],
            "message": f"获取推送网站列表失败: {str(e)}",
            "saved_ports": 0,
            "skipped_ports": 0
        } 

@router.post("/push/account/name", response_model=UpdateAccountNameResponse, summary="更新账号姓名")
async def update_account_name(request: UpdateAccountNameRequest):
    """
    更新账号姓名
    
    参数:
    - **web_id**: 网站ID
    - **account_id**: 账号ID
    - **name**: 姓名
    
    返回:
    - **success**: 是否成功
    - **message**: 状态描述
    """
    try:
        # 获取数据库连接
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 检查是否存在该记录
                check_sql = """
                    SELECT id FROM account_info 
                    WHERE account_id = %s 
                    AND port_type = %s 
                    AND is_deleted = 0
                """
                cursor.execute(check_sql, (request.account_id, request.web_id))
                existing_record = cursor.fetchone()
                
                if not existing_record:
                    return {
                        "success": False,
                        "message": "未找到对应的账号记录"
                    }
                
                # 更新姓名
                update_sql = """
                    UPDATE account_info 
                    SET name = %s
                    WHERE account_id = %s 
                    AND port_type = %s 
                    AND is_deleted = 0
                """
                cursor.execute(update_sql, (
                    request.name,
                    request.account_id,
                    request.web_id
                ))
                
                # 提交事务
                connection.commit()
                
                return {
                    "success": True,
                    "message": "姓名更新成功"
                }
        
        finally:
            # 关闭连接
            connection.close()
            
    except Exception as e:
        logger.error(f"更新账号姓名失败: {str(e)}")
        return {
            "success": False,
            "message": f"更新账号姓名失败: {str(e)}"
        } 

@router.post("/agent-post-stats", response_model=AgentPostStatsResponse, summary="获取经纪人名下所有帖子的点击量")
async def get_agent_post_stats(
    request: AgentPostStatsRequest,
    youtui_service: YoutuiService = Depends(get_youtui_service)
):
    """
    获取经纪人名下所有帖子的点击量

    此接口调用优推科技的服务获取经纪人名下所有帖子的点击量信息。

    - **city**: 城市代码，如'bj'(北京)、'cc'(长春)等
    - **city_name**: 城市名称（可选），如'北京'、'长春'等
    - **user_id**: 用户标识ID，即经纪人ID（可选，如果不提供则使用城市对应的默认用户ID）
    - **webcontent**: 网站与账号信息的Base64编码JSON（可选）

    返回:
    - **success**: 是否成功
    - **message**: 操作信息
    - **data**: 帖子信息列表（成功时返回），每个帖子包含以下字段：
        - **clickM**: 月点击量
        - **Retime**: 激活时间（格式：YYYY-MM-DD HH:MM:SS）
        - **Potime**: 发布时间（格式：YYYY-MM-DD HH:MM:SS）
        - **Title**: 帖子标题
        - **RID**: 帖子ID
        - **其他字段**: 根据优推API返回的完整帖子信息
    - **taocan**: 套餐类型（如'20-0'、'40-0'等）
    - **package_limit**: 套餐帖子数量限制
    """
    logger.info(f"请求获取经纪人帖子点击量: 城市={request.city}, 用户ID={request.user_id}")
    
    try:
        # 获取用户ID
        user_id = request.user_id
        
        # 如果未提供用户ID，尝试获取城市对应的默认用户ID
        if not user_id:
            city_config = load_city_config()
            accounts = city_config.get("accounts", {})
            city_accounts = accounts.get(request.city, {})
            user_id = city_accounts.get("user_id")
            
            if not user_id:
                logger.error(f"未提供用户ID，且城市 '{request.city}' 没有默认用户账号")
                return {
                    "success": False,
                    "message": f"未提供用户ID，且城市 '{request.city}' 没有默认用户账号",
                    "data": None
                }
        
        # 调用异步服务获取帖子点击量
        # 如果提供了webcontent，则传递给服务
        if request.webcontent:
            logger.info("使用提供的webcontent调用优推API")
            result = await youtui_service.async_get_agent_post_stats(user_id, request.webcontent, timeout=None)
        else:
            logger.info("未提供webcontent，使用默认参数调用优推API")
            result = await youtui_service.async_get_agent_post_stats(user_id, timeout=None)
        
        if result:
            # 确保结果是列表形式
            if not isinstance(result, list):
                result = [result]

            processed_data = []
            tao_can_value = None

            for item in result:
                if "taocan" in item:
                    tao_can_value = item["taocan"]

                if "msg" in item and isinstance(item["msg"], str):
                    try:
                        house_list = json.loads(item["msg"])
                        processed_data.extend(house_list)
                    except json.JSONDecodeError as e:
                        logger.error(f"解析 msg 字段 JSON 数据失败: {str(e)}")
                        processed_data.append(item)
                else:
                    processed_data.append(item)

            try:
                def safe_clickm_to_int(item):
                    try:
                        clickm_value = item.get('clickM', '0')
                        return int(clickm_value) if clickm_value else 0
                    except (ValueError, TypeError):
                        return 0

                processed_data = sorted(processed_data, key=safe_clickm_to_int, reverse=True)

            except Exception as sort_error:
                logger.warning(f"排序帖子点击量数据时发生错误: {str(sort_error)}，将返回未排序的数据")

            # 解析套餐限制
            package_limit = parse_package_limit(tao_can_value or "20-0")

            response_data = {
                "success": True,
                "message": f"成功获取 {len(processed_data)} 条帖子点击量信息",
                "data": processed_data,
                "taocan": tao_can_value or "20-0",
                "package_limit": package_limit
            }

            return response_data
        else:
            logger.warning("获取帖子点击量信息失败或无数据")
            return {
                "success": False,
                "message": "获取帖子点击量信息失败或无数据",
                "data": None
            }
    except Exception as e:
        logger.error(f"获取帖子点击量时发生错误: {str(e)}")
        return {
            "success": False,
            "message": f"获取帖子点击量时发生错误: {str(e)}",
            "data": None
        } 

@router.post("/house/offline", response_model=OfflineHouseResponse, summary="下架房源")
async def offline_house(
    request: OfflineHouseRequest
):
    """
    下架指定的房源
    
    接收房源远程ID，调用优推科技API下架房源。此接口使用异步方式调用第三方API，避免阻塞其他接口。
    
    传入的请求体应包含以下字段：
    - city: 城市代码，如'bj'(北京)、'cc'(长春)等
    - city_name: (可选)城市名称，如"北京"、"长春"等
    - user_id: (可选)用户标识ID，如果不提供则使用城市对应的默认用户ID
    - remote_id: 要下架的房源远程ID
    - webcontent: (可选)网站与账号信息的Base64编码JSON
    """
    logger.info(f"接收到房源下架请求: city={request.city}, city_name={request.city_name}, remote_id={request.remote_id}")
    if request.webcontent:
        logger.info(f"提供了webcontent参数，长度: {len(request.webcontent)}")
    
    try:
        # 创建服务实例
        service = get_youtui_service(city=request.city, city_name=request.city_name)
        
        # 异步调用服务，避免阻塞
        result = await service.async_offline_house(
            user_id=request.user_id,
            city=request.city,
            remote_id=request.remote_id,
            webcontent=request.webcontent,
            house_type=request.house_type
        )
        
        # 返回结果
        return result
    except Exception as e:
        logger.error(f"房源下架过程中发生错误: {str(e)}")
        return {
            "success": False,
            "message": f"房源下架失败: {str(e)}",
            "data": None
        }

@router.post("/house/activate", response_model=ActivateHouseResponse, summary="激活房源")
async def activate_house(
    request: ActivateHouseRequest
):
    """
    激活指定的房源
    
    接收房源远程ID，调用优推科技API激活房源。此接口使用异步方式调用第三方API，避免阻塞其他接口。
    
    传入的请求体应包含以下字段：
    - city: 城市代码，如'bj'(北京)、'cc'(长春)等
    - city_name: (可选)城市名称，如"北京"、"长春"等
    - user_id: (可选)用户标识ID，如果不提供则使用城市对应的默认用户ID
    - remote_id: 要激活的房源远程ID
    - webcontent: (可选)网站与账号信息的Base64编码JSON
    - house_type: (可选)房源类型，6表示出售，3表示出租，默认为6
    """
    logger.info(f"接收到房源激活请求: city={request.city}, city_name={request.city_name}, remote_id={request.remote_id}")
    if request.webcontent:
        logger.info(f"提供了webcontent参数，长度: {len(request.webcontent)}")
    
    try:
        # 创建服务实例
        service = get_youtui_service(city=request.city, city_name=request.city_name)
        
        # 异步调用服务，避免阻塞
        result = await service.async_activate_house(
            user_id=request.user_id,
            city=request.city,
            remote_id=request.remote_id,
            webcontent=request.webcontent,
            house_type=request.house_type
        )
        
        # 返回结果
        return result
    except Exception as e:
        logger.error(f"房源激活过程中发生错误: {str(e)}")
        return {
            "success": False,
            "message": f"房源激活失败: {str(e)}",
            "data": None
        } 