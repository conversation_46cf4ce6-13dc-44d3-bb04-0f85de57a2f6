#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
钉钉消息发送简化示例
直接发送消息给指定用户，无需命令行参数
"""

import os
import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 直接从模块导入，避免通过__init__.py导入
from services.dingtalk_service import DingTalkService

def main():
    """主函数：演示如何简单发送钉钉消息给指定用户"""
    
    # 创建钉钉服务实例
    dingtalk = DingTalkService()
    
    # 要发送消息的用户ID，根据data/dingtalk_users.json中的信息设置
    # 例如：["manager7981", "02340820426926150752"]
    user_ids = ["manager7981"]
    
    # 消息内容
    message = "尊敬的销售name，您好。您的58账号需要进行房本认证，请尽快使用58扫描二维码进行房本认证"
    
    # 如果是想发送给所有用户，可以设置 to_all_user=True
    to_all_user = False
    
    # 发送消息
    result = dingtalk.send_work_notification(
        message=message,
        user_ids=user_ids,
        to_all_user=to_all_user,
        msg_type="text"  # 还可以是 "markdown", "link", "action_card"
    )
    
    if result["success"]:
        task_id = result.get("task_id")
        logger.info(f"消息发送成功，任务ID: {task_id}")
        
        # 检查发送状态
        check_send_status(dingtalk, task_id)
    else:
        logger.error(f"消息发送失败: {result.get('message')}")

def check_send_status(dingtalk, task_id):
    """检查消息发送状态"""
    if not task_id:
        return
    
    # 检查发送进度
    progress_result = dingtalk.get_send_progress(task_id)
    if progress_result["success"]:
        progress = progress_result.get("progress", {})
        progress_percent = progress.get("progress_in_percent", 0)
        status = progress.get("status", 0)
        
        logger.info(f"消息发送进度: {progress_percent}%, 状态: {status}")
    else:
        logger.error(f"获取消息发送进度失败: {progress_result.get('message')}")
    
    # 检查发送结果
    result_result = dingtalk.get_send_result(task_id)
    if result_result["success"]:
        # 显示无效的用户ID
        invalid_users = result_result.get("invalid_user_id_list", [])
        if invalid_users:
            logger.warning(f"以下用户ID无效，无法接收消息: {', '.join(invalid_users)}")
        
        # 显示已读/未读用户
        read_users = result_result.get("read_user_id_list", [])
        unread_users = result_result.get("unread_user_id_list", [])
        
        if read_users:
            logger.info(f"已读用户: {', '.join(read_users)}")
        if unread_users:
            logger.info(f"未读用户: {', '.join(unread_users)}")
    else:
        logger.error(f"获取消息发送结果失败: {result_result.get('message')}")

def send_markdown_example():
    """发送Markdown消息的示例"""
    dingtalk = DingTalkService()
    
    # 要发送消息的用户ID
    user_ids = ["manager7981"]
    
    # Markdown消息内容
    markdown_content = """
# 测试Markdown消息
## 二级标题

**这是加粗文本**

- 项目1
- 项目2

[这是一个链接](https://www.dingtalk.com)
    """
    
    # 发送Markdown消息
    result = dingtalk.send_work_notification(
        message=markdown_content,
        title="测试Markdown消息",
        user_ids=user_ids,
        msg_type="markdown"
    )
    
    if result["success"]:
        logger.info(f"Markdown消息发送成功，任务ID: {result.get('task_id')}")
    else:
        logger.error(f"Markdown消息发送失败: {result.get('message')}")

if __name__ == "__main__":
    main()
    # 如果想发送Markdown消息，取消下面这行的注释
    # send_markdown_example() 