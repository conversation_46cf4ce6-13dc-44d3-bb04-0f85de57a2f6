#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阿里云通知短信发送示例 - 房本认证通知
自动化版本 - 使用预设账号和固定值
"""

import os
import sys
import logging
import random
import argparse
from pathlib import Path

# 添加项目根目录到系统路径
current_dir = Path(__file__).parent
root_dir = current_dir.parent
sys.path.append(str(root_dir))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from services.sms_service import SmsService

# 预设账号列表 - 包含10个账号信息
PRESET_ACCOUNTS = [
    {
        "name": "童波长",
        "phone": "***********",
        "path": "qcbedc4b"  # 二维码路径
    },
    {
        "name": "李心爱",
        "phone": "***********",
        "path": "qcbedc4b"
    },
    {
        "name": "李培鑫",
        "phone": "***********",
        "path": "qcbedc4b"
    },
    {
        "name": "朱天奇",
        "phone": "***********",
        "path": "qcbedc4b"
    },
    {
        "name": "曹文彬",
        "phone": "***********",
        "path": "qcbedc4b"
    },
    {
        "name": "马利国",
        "phone": "***********",
        "path": "qcbedc4b"
    },
    {
        "name": "苑全增",
        "phone": "***********",
        "path": "qcbedc4b"
    },
    {
        "name": "张一帆",
        "phone": "***********",
        "path": "qcbedc4b"
    },
    {
        "name": "陈天阳",
        "phone": "***********",
        "path": "qcbedc4b"
    },
    {
        "name": "周双",
        "phone": "***********",
        "path": "qcbedc4b"
    }
]

def send_single_notification_auto(account_index=0):
    """自动发送单条房本认证通知短信，使用预设账号"""
    sms_service = SmsService()
    
    # 使用预设账号
    if account_index >= len(PRESET_ACCOUNTS):
        account_index = 0
    
    account = PRESET_ACCOUNTS[account_index]
    phone_number = account["phone"]
    
    # 设置内容参数
    content = {
        'name': account["name"],
        'path': account["path"]
    }
    
    print(f"发送房本认证通知到 {phone_number}，内容参数: {content}")
    result = sms_service.send_notification(phone_number, content)
    
    if result['success']:
        print(f"通知发送成功! RequestID: {result['request_id']}")
    else:
        print(f"通知发送失败: {result['message']}")
    
    return result

def batch_send_notifications_auto(count=None, random_selection=False):
    """自动批量发送房本认证通知短信，使用预设账号"""
    sms_service = SmsService()
    
    # 确定要发送的账号数量和范围
    if count is None or count > len(PRESET_ACCOUNTS):
        count = len(PRESET_ACCOUNTS)
    
    # 决定使用哪些账号
    if random_selection:
        selected_accounts = random.sample(PRESET_ACCOUNTS, count)
    else:
        selected_accounts = PRESET_ACCOUNTS[:count]
    
    # 构建电话号码到内容的映射
    phone_dict = {}
    for account in selected_accounts:
        phone_dict[account["phone"]] = {
            'name': account["name"],
            'path': account["path"]
        }
    
    print(f"批量发送房本认证通知短信到 {list(phone_dict.keys())}")
    results = sms_service.batch_send_notification(phone_dict)
    
    # 输出发送结果
    success_count = 0
    for phone, result in results.items():
        if result['success']:
            success_count += 1
            print(f"发送到 {phone} 成功! RequestID: {result['request_id']}")
        else:
            print(f"发送到 {phone} 失败: {result['message']}")
    
    print(f"总计发送: {len(phone_dict)}条，成功: {success_count}条，失败: {len(phone_dict) - success_count}条")
    
    return results

def test_single_account(account_index):
    """测试单个特定账号发送短信"""
    if account_index < 0 or account_index >= len(PRESET_ACCOUNTS):
        print(f"账号索引无效! 必须在0-{len(PRESET_ACCOUNTS)-1}之间")
        return
    
    account = PRESET_ACCOUNTS[account_index]
    print(f"测试账号 {account_index+1}: {account['name']} ({account['phone']})")
    
    return send_single_notification_auto(account_index)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="房本认证通知短信发送工具")
    parser.add_argument('--mode', choices=['single', 'batch', 'test'], default='single',
                       help='单条发送(single)、批量发送(batch)或测试特定账号(test)')
    parser.add_argument('--count', type=int, default=None,
                       help='批量发送时的数量，默认为所有预设账号')
    parser.add_argument('--random', action='store_true',
                       help='批量发送时随机选择账号')
    parser.add_argument('--index', type=int, default=0,
                       help='测试单个账号时的账号索引(0-9)')
    
    args = parser.parse_args()
    
    print("房本认证通知短信发送示例 (自动化版本)")
    print("==================================")
    
    if args.mode == 'single':
        print("发送单条房本认证通知")
        send_single_notification_auto()
    elif args.mode == 'batch':
        print(f"批量发送房本认证通知 (数量: {args.count or '全部'}, 随机选择: {'是' if args.random else '否'})")
        batch_send_notifications_auto(args.count, args.random)
    elif args.mode == 'test':
        print(f"测试特定账号 (索引: {args.index})")
        test_single_account(args.index)
    else:
        print("无效的模式选择")

if __name__ == "__main__":
    main() 