#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图片处理脚本
直接使用PIL库处理根目录下的图片，调整曝光度和锐度等参数

使用方法：
    python examples/process_images.py  # 直接运行，使用默认参数处理项目根目录下的图片

默认参数配置：
在脚本中的DEFAULT_PARAMS变量中配置了默认参数，包括：
    # 基本图像调整
    brightness: 1.2  # 亮度调整因子 (0.0-2.0, 1.0为原始亮度)
    contrast: 1.3    # 对比度调整因子 (0.0-2.0, 1.0为原始对比度)
    sharpness: 1.5   # 锐度调整因子 (0.0-2.0, 1.0为原始锐度)
    saturation: 1.1  # 饱和度调整因子 (0.0-2.0, 1.0为原始饱和度)

    # 裁剪相关
    crop_mode: 'center'  # 裁剪模式: center(居中裁剪), golden(黄金比例), custom(自定义)
    crop_ratio: 1.5      # 裁剪比例，宽高比，默认为1.5(宽度是高度的1.5倍)

如果需要修改默认参数，请直接编辑脚本中的DEFAULT_PARAMS变量。

支持的参数：
    # 基本图像调整
    brightness: 亮度调整因子 (0.0-2.0, 1.0为原始亮度)
    contrast: 对比度调整因子 (0.0-2.0, 1.0为原始对比度)
    sharpness: 锐度调整因子 (0.0-2.0, 1.0为原始锐度)
    saturation: 饱和度调整因子 (0.0-2.0, 1.0为原始饱和度)
    color: 色彩平衡调整因子 (0.0-2.0, 1.0为原始色彩)

    # 自动增强
    auto_enhance: 是否自动增强图片
    equalize: 是否直方图均衡化处理

    # 模糊和滤镜
    gamma: 伽马校正值 (0.1-10.0, 1.0为原始值)

    # 尺寸调整
    crop: 裁剪区域，格式为(left, top, right, bottom)
    crop_mode: 裁剪模式，可选值为'center'(居中裁剪), 'golden'(黄金比例), 'custom'(自定义)
    crop_ratio: 裁剪比例，宽高比，默认为1.0(正方形)
    resize_after_crop: 裁剪后是否调整回原始尺寸

预设模式：
    standard: 标准模式 - 适度增强锐度和对比度
    vivid: 鲜明模式 - 增强饱和度和对比度
    clear: 清晰模式 - 大幅增强锐度
    soft: 柔和模式 - 降低锐度和对比度
"""

import os
import sys
import argparse
from pathlib import Path
from PIL import Image, ImageEnhance, ImageOps
import logging
import time

# 获取项目根目录的绝对路径
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def process_image_file(image_path, output_dir, brightness=None, contrast=None, sharpness=None,
                      saturation=None, color=None, auto_enhance=False, equalize=False,
                      gamma=None, crop=None, crop_mode='center', crop_ratio=1.0,
                      resize_after_crop=False, quality=95):
    """
    处理单个图片文件

    Args:
        image_path: 图片文件路径
        output_dir: 输出目录

        # 基本图像调整
        brightness: 亮度调整因子 (0.0-2.0, 1.0为原始亮度)
        contrast: 对比度调整因子 (0.0-2.0, 1.0为原始对比度)
        sharpness: 锐度调整因子 (0.0-2.0, 1.0为原始锐度)
        saturation: 饱和度调整因子 (0.0-2.0, 1.0为原始饱和度)
        color: 色彩平衡调整因子 (0.0-2.0, 1.0为原始色彩)

        # 自动增强
        auto_enhance: 是否自动增强图片
        equalize: 是否直方图均衡化处理

        # 模糊和滤镜
        gamma: 伽马校正值 (0.1-10.0, 1.0为原始值)

        # 尺寸调整
        crop: 裁剪区域，格式为(left, top, right, bottom)
        crop_mode: 裁剪模式，可选值为'center'(居中裁剪), 'golden'(黄金比例), 'custom'(自定义)
        crop_ratio: 裁剪比例，宽高比，默认为1.0(正方形)
        resize_after_crop: 裁剪后是否调整回原始尺寸

        # 其他参数
        quality: 保存质量 (1-100, 只对JPEG格式有效)

    Returns:
        处理结果
    """
    try:
        # 获取文件名（不含路径）
        filename = os.path.basename(image_path)

        # 创建输出目录（如果不存在）
        os.makedirs(output_dir, exist_ok=True)

        # 输出文件路径
        output_path = os.path.join(output_dir, f"processed_{filename}")

        logger.info(f"处理图片: {image_path}")

        # 打开图片
        img = Image.open(image_path)

        # 确保图片是RGB模式（如果是PNG等带透明通道的图片，转换为RGBA）
        if img.mode == 'P':
            img = img.convert('RGBA')
        elif img.mode != 'RGB' and img.mode != 'RGBA':
            img = img.convert('RGB')

        # 尺寸调整
        original_size = img.size  # 保存原始尺寸
        crop_applied = False  # 标记是否应用了裁剪

        if crop is not None:
            # 自定义裁剪
            img = img.crop(crop)
            crop_applied = True
        elif crop_mode in ['center', 'golden']:
            # 自动裁剪
            width, height = img.size

            if crop_mode == 'center':
                # 居中裁剪
                if width > height * crop_ratio:  # 宽图，裁剪宽度
                    new_width = int(height * crop_ratio)
                    left = (width - new_width) // 2
                    right = left + new_width
                    img = img.crop((left, 0, right, height))
                    crop_applied = True
                elif height > width / crop_ratio:  # 长图，裁剪高度
                    new_height = int(width / crop_ratio)
                    top = (height - new_height) // 2
                    bottom = top + new_height
                    img = img.crop((0, top, width, bottom))
                    crop_applied = True
            elif crop_mode == 'golden':
                # 黄金比例裁剪 (1:1.618)
                golden_ratio = 1.618

                if width / height > golden_ratio:  # 宽图，裁剪宽度
                    new_width = int(height * golden_ratio)
                    left = (width - new_width) // 2
                    right = left + new_width
                    img = img.crop((left, 0, right, height))
                    crop_applied = True
                elif height / width > golden_ratio:  # 长图，裁剪高度
                    new_height = int(width * golden_ratio)
                    top = (height - new_height) // 2
                    bottom = top + new_height
                    img = img.crop((0, top, width, bottom))
                    crop_applied = True

        # 如果应用了裁剪并需要调整回原始尺寸
        if crop_applied and resize_after_crop:
            img = img.resize(original_size, Image.LANCZOS)

        if auto_enhance:
            # 自动增强
            img = ImageOps.autocontrast(img)

            # 适度锐化
            enhancer = ImageEnhance.Sharpness(img)
            img = enhancer.enhance(1.3)
        else:
            # 应用各种调整
            if brightness is not None:
                enhancer = ImageEnhance.Brightness(img)
                img = enhancer.enhance(brightness)

            if contrast is not None:
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(contrast)

            if sharpness is not None:
                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(sharpness)

            if saturation is not None:
                enhancer = ImageEnhance.Color(img)
                img = enhancer.enhance(saturation)

            if color is not None:
                enhancer = ImageEnhance.Color(img)
                img = enhancer.enhance(color)

        # 颜色效果
        if equalize:
            if img.mode == 'RGBA':
                r, g, b, a = img.split()
                r, g, b = map(ImageOps.equalize, [r, g, b])
                img = Image.merge('RGBA', (r, g, b, a))
            else:
                img = ImageOps.equalize(img)

        # 伽马校正
        if gamma is not None:
            if img.mode == 'RGBA':
                r, g, b, a = img.split()
                r = r.point(lambda i: ((i / 255) ** (1/gamma)) * 255)
                g = g.point(lambda i: ((i / 255) ** (1/gamma)) * 255)
                b = b.point(lambda i: ((i / 255) ** (1/gamma)) * 255)
                img = Image.merge('RGBA', (r, g, b, a))
            else:
                img = img.point(lambda i: ((i / 255) ** (1/gamma)) * 255)

        # 保存处理后的图片
        if img.mode == 'RGBA':
            img.save(output_path, quality=quality)
        else:
            img.save(output_path, quality=quality)

        logger.info(f"图片已保存到: {output_path}")

        return True, output_path

    except Exception as e:
        logger.error(f"处理图片时发生异常: {str(e)}")
        return False, str(e)

def process_images_in_directory(input_dir, output_dir, brightness=None, contrast=None,
                               sharpness=None, saturation=None, color=None,
                               auto_enhance=False, extensions=None, gamma=None,
                               equalize=False, crop=None, crop_mode='center', crop_ratio=1.0,
                               resize_after_crop=False, quality=95):
    """
    处理目录中的所有图片

    Args:
        input_dir: 输入目录
        output_dir: 输出目录

        # 基本图像调整
        brightness: 亮度调整因子 (0.0-2.0, 1.0为原始亮度)
        contrast: 对比度调整因子 (0.0-2.0, 1.0为原始对比度)
        sharpness: 锐度调整因子 (0.0-2.0, 1.0为原始锐度)
        saturation: 饱和度调整因子 (0.0-2.0, 1.0为原始饱和度)
        color: 色彩平衡调整因子 (0.0-2.0, 1.0为原始色彩)

        # 自动增强
        auto_enhance: 是否自动增强图片
        equalize: 是否直方图均衡化处理

        # 模糊和滤镜
        gamma: 伽马校正值 (0.1-10.0, 1.0为原始值)

        # 尺寸调整
        crop: 裁剪区域，格式为(left, top, right, bottom)
        crop_mode: 裁剪模式，可选值为'center'(居中裁剪), 'golden'(黄金比例), 'custom'(自定义)
        crop_ratio: 裁剪比例，宽高比，默认为1.0(正方形)
        resize_after_crop: 裁剪后是否调整回原始尺寸

        # 其他参数
        quality: 保存质量 (1-100, 只对JPEG格式有效)
        extensions: 图片扩展名列表，如['.jpg', '.png', '.webp', '.bmp', '.tiff']

    Returns:
        处理结果统计
    """
    if extensions is None:
        extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']

    # 确保扩展名都是小写的
    extensions = [ext.lower() for ext in extensions]

    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)

    # 统计结果
    results = {
        'total': 0,
        'success': 0,
        'failed': 0,
        'processed_files': [],
        'failed_files': []
    }

    # 获取输出目录的绝对路径
    output_dir_abs = os.path.abspath(output_dir)

    # 遍历目录中的所有文件
    for root, dirs, files in os.walk(input_dir):
        # 如果当前目录是输出目录或其子目录，则跳过
        if os.path.abspath(root).startswith(output_dir_abs):
            logger.info(f"跳过输出目录: {root}")
            continue

        for file in files:
            # 检查文件扩展名
            ext = os.path.splitext(file)[1].lower()
            if ext in extensions:
                # 构建完整的文件路径
                file_path = os.path.join(root, file)


                # 增加总数
                results['total'] += 1

                # 处理图片
                success, result = process_image_file(
                    file_path,
                    output_dir,
                    brightness,
                    contrast,
                    sharpness,
                    saturation,
                    color,
                    auto_enhance,
                    equalize,
                    gamma,
                    crop,
                    crop_mode,
                    crop_ratio,
                    resize_after_crop,
                    quality
                )

                if success:
                    results['success'] += 1
                    results['processed_files'].append({
                        'input': file_path,
                        'output': result
                    })
                else:
                    results['failed'] += 1
                    results['failed_files'].append({
                        'input': file_path,
                        'error': result
                    })

    return results

# 预设参数配置
# 在这里配置您的默认参数，这样只需要直接运行脚本即可
DEFAULT_PARAMS = {
    # 基本路径参数
    'input': ROOT_DIR,  # 输入目录，默认为项目根目录
    'output': os.path.join(ROOT_DIR, 'processed_images'),  # 输出目录

    # 基本图像调整参数
    'brightness': 1.2,  # 亮度调整因子 (0.0-2.0, 1.0为原始亮度)
    'contrast': 1.3,    # 对比度调整因子 (0.0-2.0, 1.0为原始对比度)
    'sharpness': 1.5,   # 锐度调整因子 (0.0-2.0, 1.0为原始锐度)
    'saturation': 1.1,  # 饱和度调整因子 (0.0-2.0, 1.0为原始饱和度)
    'color': None,      # 色彩平衡调整因子 (0.0-2.0, 1.0为原始色彩)

    # 自动增强参数
    'auto_enhance': False,  # 是否自动增强图片
    'equalize': False,      # 是否直方图均衡化处理

    # 模糊和滤镜参数
    'gamma': None,  # 伽马校正值 (0.1-10.0, 1.0为原始值)

    # 尺寸调整参数
    'crop': None,           # 自定义裁剪区域，格式为[left, top, right, bottom]
    'crop_mode': 'center',  # 裁剪模式: center(居中裁剪), golden(黄金比例), custom(自定义)
    'crop_ratio': 1.5,      # 裁剪比例，宽高比，默认为1.5(宽度是高度的1.5倍)
    'resize_after_crop': False,  # 裁剪后是否调整回原始尺寸

    # 其他参数
    'quality': 95,  # 保存质量 (1-100, 只对JPEG格式有效)
    'extensions': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],  # 图片扩展名列表

    # 预设参数
    'preset': 'standard',  # 预设参数：standard(标准), vivid(鲜明), clear(清晰), soft(柔和)
}

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='图片处理脚本')

    # 基本路径参数
    parser.add_argument('--input', '-i', type=str, default=DEFAULT_PARAMS['input'], help='输入目录，默认为项目根目录')
    parser.add_argument('--output', '-o', type=str, default=DEFAULT_PARAMS['output'], help='输出目录，默认为项目根目录下的processed_images目录')

    # 基本图像调整参数
    parser.add_argument('--brightness', '-b', type=float, default=DEFAULT_PARAMS['brightness'], help='亮度调整因子 (0.0-2.0, 1.0为原始亮度)')
    parser.add_argument('--contrast', '-c', type=float, default=DEFAULT_PARAMS['contrast'], help='对比度调整因子 (0.0-2.0, 1.0为原始对比度)')
    parser.add_argument('--sharpness', '-s', type=float, default=DEFAULT_PARAMS['sharpness'], help='锐度调整因子 (0.0-2.0, 1.0为原始锐度)')
    parser.add_argument('--saturation', '-sat', type=float, default=DEFAULT_PARAMS['saturation'], help='饱和度调整因子 (0.0-2.0, 1.0为原始饱和度)')
    parser.add_argument('--color', '-col', type=float, default=DEFAULT_PARAMS['color'], help='色彩平衡调整因子 (0.0-2.0, 1.0为原始色彩)')

    # 自动增强参数
    parser.add_argument('--auto-enhance', '-a', action='store_true', default=DEFAULT_PARAMS['auto_enhance'], help='自动增强图片')
    parser.add_argument('--equalize', '-eq', action='store_true', default=DEFAULT_PARAMS['equalize'], help='直方图均衡化处理')

    # 模糊和滤镜参数
    parser.add_argument('--gamma', '-g', type=float, default=DEFAULT_PARAMS['gamma'], help='伽马校正值 (0.1-10.0, 1.0为原始值)')

    # 尺寸调整参数
    parser.add_argument('--crop', '-crop', type=int, nargs=4, default=DEFAULT_PARAMS['crop'], help='裁剪区域，格式为四个数字: left top right bottom')
    parser.add_argument('--crop-mode', '-cm', type=str, choices=['center', 'golden', 'custom'], default=DEFAULT_PARAMS['crop_mode'],
                      help='裁剪模式: center(居中裁剪), golden(黄金比例), custom(自定义)')
    parser.add_argument('--crop-ratio', '-cr', type=float, default=DEFAULT_PARAMS['crop_ratio'], help='裁剪比例，宽高比，默认为1.0(正方形)')
    parser.add_argument('--resize-after-crop', '-rac', action='store_true', default=DEFAULT_PARAMS['resize_after_crop'], help='裁剪后调整回原始尺寸')

    # 其他参数
    parser.add_argument('--quality', '-q', type=int, default=DEFAULT_PARAMS['quality'], help='保存质量 (1-100, 只对JPEG格式有效)')
    parser.add_argument('--extensions', '-e', type=str, nargs='+', default=DEFAULT_PARAMS['extensions'], help='图片扩展名列表')

    # 预设参数
    parser.add_argument('--preset', '-p', type=str, choices=['standard', 'vivid', 'clear', 'soft'], default=DEFAULT_PARAMS['preset'],
                      help='预设参数：standard(标准), vivid(鲜明), clear(清晰), soft(柔和)')

    # 如果没有命令行参数，使用默认参数
    if len(sys.argv) == 1:
        logger.info("使用默认参数处理图片")
        args = parser.parse_args([])
    else:
        args = parser.parse_args()

    # 如果选择了预设参数，覆盖默认值
    if args.preset == 'vivid':
        # 鲜明模式 - 增强饱和度和对比度
        args.brightness = 1.1
        args.contrast = 1.3
        args.sharpness = 1.4
        args.saturation = 1.3
    elif args.preset == 'clear':
        # 清晰模式 - 增强锐度和对比度
        args.brightness = 1.05
        args.contrast = 1.2
        args.sharpness = 1.7
        args.saturation = 1.0
    elif args.preset == 'soft':
        # 柔和模式 - 降低锐度和对比度
        args.brightness = 1.1
        args.contrast = 0.95
        args.sharpness = 0.8
        args.saturation = 1.05

    # 处理图片
    start_time = time.time()
    results = process_images_in_directory(
        args.input,
        args.output,
        args.brightness,
        args.contrast,
        args.sharpness,
        args.saturation,
        args.color,
        args.auto_enhance,
        args.extensions,
        args.gamma,
        args.equalize,
        args.crop,
        args.crop_mode,
        args.crop_ratio,
        args.resize_after_crop,
        args.quality
    )
    end_time = time.time()

    # 打印结果
    logger.info(f"处理完成，耗时: {end_time - start_time:.2f}秒")
    logger.info(f"总共处理: {results['total']} 个文件")
    logger.info(f"成功处理: {results['success']} 个文件")
    logger.info(f"处理失败: {results['failed']} 个文件")

    if results['failed'] > 0:
        logger.info("失败的文件:")
        for failed in results['failed_files']:
            logger.info(f"  {failed['input']}: {failed['error']}")

    logger.info(f"处理后的图片保存在: {args.output}")

if __name__ == "__main__":
    main()
