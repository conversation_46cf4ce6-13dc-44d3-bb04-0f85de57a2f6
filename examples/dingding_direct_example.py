#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
钉钉群成员获取示例 - 直接导入版
使用dingtalk_config.json中的配置，直接导入钉钉服务获取群成员信息
"""

import os
import sys
import json
from pathlib import Path

# 项目根目录
current_dir = Path(__file__).parent
root_dir = current_dir.parent

# 直接导入dingding_service.py文件
sys.path.append(str(root_dir))
dingding_service_path = root_dir / 'services' / 'dingding_service.py'

# 执行文件中的代码
dingding_service_globals = {}
with open(dingding_service_path, 'r', encoding='utf-8') as f:
    exec(f.read(), dingding_service_globals)

# 获取Sample类
Sample = dingding_service_globals['Sample']

def main():
    """主函数"""
    print("钉钉群成员获取示例 - 直接导入版")
    print("=========================")
    
    # 加载钉钉配置
    config_path = root_dir / 'config' / 'dingtalk_config.json'
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            print(f"成功加载钉钉配置")
    except Exception as e:
        print(f"加载钉钉配置失败: {str(e)}")
        return
    
    # 获取访问令牌
    import requests
    app_key = config['app_key']
    app_secret = config['app_secret']
    api_host = config['api_host']
    
    try:
        url = f"{api_host}/gettoken?appkey={app_key}&appsecret={app_secret}"
        response = requests.get(url)
        result = response.json()
        
        if result.get('errcode') == 0:
            access_token = result.get('access_token')
            print(f"成功获取钉钉访问令牌")
        else:
            print(f"获取钉钉访问令牌失败: {result}")
            return
    except Exception as e:
        print(f"获取钉钉访问令牌发生异常: {str(e)}")
        return
    
    # 设置群会话ID
    conversation_id = input("请输入钉钉群会话ID (open_conversation_id): ").strip()
    if not conversation_id:
        conversation_id = "cidNnHyWT51ieitOWTCuR0V5A=="  # 示例ID
        print(f"使用示例群会话ID: {conversation_id}")
    
    # 获取群成员
    try:
        # 创建钉钉客户端
        client = Sample.create_client()
        
        # 设置请求头部
        from alibabacloud_dingtalk.im_1_0 import models as dingtalkim__1__0_models
        batch_query_group_member_headers = dingtalkim__1__0_models.BatchQueryGroupMemberHeaders()
        batch_query_group_member_headers.x_acs_dingtalk_access_token = access_token
        
        # 创建请求对象
        batch_query_group_member_request = dingtalkim__1__0_models.BatchQueryGroupMemberRequest(
            open_conversation_id=conversation_id,
            cool_app_code='COOLAPP-1-1031DAD30AA2216629D6000F',
            max_results=500,
            next_token=''
        )
        
        # 调用API获取群成员
        from alibabacloud_tea_util import models as util_models
        response = client.batch_query_group_member_with_options(
            batch_query_group_member_request, 
            batch_query_group_member_headers, 
            util_models.RuntimeOptions()
        )
        
        # 处理结果
        members = response.body.members
        next_token = response.body.next_token
        has_more = response.body.has_more
        
        print(f"\n成功获取群成员信息:")
        print(f"总人数: {len(members) if members else 0}")
        print(f"是否有更多成员: {'是' if has_more else '否'}")
        
        if members:
            print("\n成员列表 (前10名):")
            for i, member in enumerate(members[:10]):
                print(f"{i+1}. {member.nick}: {member.userid}")
            
            if len(members) > 10:
                print(f"... 等共 {len(members)} 名成员")
                
            # 保存成员信息到文件
            output_file = current_dir / f"group_members_{conversation_id}.json"
            member_data = []
            for member in members:
                member_data.append({
                    'userid': member.userid,
                    'nick': member.nick,
                    'unionid': getattr(member, 'unionid', '')
                })
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(member_data, f, ensure_ascii=False, indent=2)
            print(f"\n成员信息已保存到: {output_file}")
            
            if has_more:
                print(f"\n还有更多成员未获取，下次获取可使用 next_token: {next_token}")
    except Exception as e:
        print(f"\n获取群成员失败: {str(e)}")

if __name__ == "__main__":
    main() 