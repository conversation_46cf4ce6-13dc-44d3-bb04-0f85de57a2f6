<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联东AI内容营销托管智能体</title>
    <link rel="icon" href="images/1684755993.png" type="image/png">
    <link rel="shortcut icon" href="images/1684755993.png" type="image/png">
    <link rel="preload" href="css/styles.css" as="style">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="css/factory_upload.css">
    <link rel="stylesheet" href="css/process-flow-animation.css">
    <!-- 使用Font Awesome图标库的最新版本 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- 添加Material Icons作为补充图标库 -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <!-- 添加Animate.css进行简单动画效果 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
</head>
<body>
    <div class="container">
        <header>            <div class="header-content">
                <h1><div class="rotating-logo" aria-label="联东Logo"></div> 联东AI内容营销托管智能体</h1>
                <div class="header-subtitle">智能化房源管理与内容营销平台</div>
            </div>
                        <nav>
                <ul>
                    <li><a href="index.html" class="nav-home"><i class="fas fa-home icon-blue"></i> 首页</a></li>
                    <li><a href="cities.html" class="nav-cities"><i class="fas fa-city icon-cyan"></i> 城市管理</a></li>
                    <li><a href="ports.html" class="nav-ports"><i class="fas fa-plug icon-green"></i> 端口管理</a></li>
                    <li><a href="status.html" class="nav-status"><i class="fas fa-chart-line icon-magenta"></i> 状态查询</a></li>
                    <li><a href="factory_images_upload.html" class="nav-images"><i class="fas fa-images icon-purple"></i> 园区图库</a></li>
                    <li><a href="factory_upload.html" class="active nav-upload"><i class="fas fa-upload icon-orange"></i> 房源入库</a></li>
                    <li><a href="published_data.html" class="nav-published"><i class="fas fa-paper-plane icon-blue"></i> 房源推送</a></li>
                    <li><a href="statistics.html" class="nav-stats"><i class="fas fa-chart-bar icon-gold"></i> 发布统计</a></li>
                    <li><a href="scheduled_tasks.html" class="nav-tasks"><i class="fas fa-clock icon-green"></i> 定时任务</a></li>
                    <li><a href="competitor_analysis.html" class="nav-analysis"><i class="fas fa-search icon-cyan"></i> 竞帖分析</a></li>
                </ul>
            </nav>
        </header>

        <main>
            <div class="content-section">
                <div class="section-header animate__animated animate__fadeIn">
                    <h2><i class="fas fa-file-excel icon-orange"></i> 房源数据Excel上传</h2>
                    <p style="font-size: 12px;"><i class="fas fa-info-circle icon-blue"></i> 上传包含多个园区数据的Excel文件，直接保存到数据库并支持批量发布</p>
                </div>

                <!-- 上传流程指引 -->
                <div class="upload-process-flow animate__animated animate__fadeIn" style="margin-bottom: 30px; padding: 30px 20px;">
                    <!-- 添加性能开关 -->
                    <div class="performance-toggle">
                        <label class="switch">
                            <input type="checkbox" id="animationToggle" checked>
                            <span class="slider round"></span>
                        </label>
                        <span class="toggle-label">流程图动画</span>
                    </div>
                    <div class="process-step" id="step1">
                        <div class="step-circle">
                            <span>1</span>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-file-excel"></i>
                        </div>
                        <div class="step-title">准备Excel文件</div>
                        <div class="step-desc">准备包含所有园区数据的Excel文件</div>
                    </div>
                    <div class="step-connector"><i class="fas fa-chevron-right"></i></div>

                    <div class="process-step" id="step2">
                        <div class="step-circle">
                            <span>2</span>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-upload"></i>
                        </div>
                        <div class="step-title">上传并保存</div>
                        <div class="step-desc">选择并上传Excel文件到数据库</div>
                    </div>
                    <div class="step-connector"><i class="fas fa-chevron-right"></i></div>

                    <div class="process-step" id="step3">
                        <div class="step-circle">
                            <span>3</span>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="step-title">推送发布</div>
                        <div class="step-desc">预览数据并发布推送</div>
                    </div>
                </div>

                <form id="uploadForm" enctype="multipart/form-data" class="animate__animated animate__fadeInUp">
                    <div class="form-group file-upload-container">
                        <div class="upload-icon-container">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h3>上传园区房源Excel文件</h3>
                        <p class="upload-description">选择Excel文件直接保存到数据库，可按城市筛选查看，一键批量发布推送</p>

                        <label for="excelFile" class="file-upload-label">
                            <i class="fas fa-file-excel"></i>
                            <span class="upload-text">点击选择Excel文件</span>
                        </label>
                        <input type="file" id="excelFile" name="file" accept=".xlsx, .xls" required>
                        <div class="file-name"></div>
                        <div class="form-hint">
                            <i class="fas fa-info-circle"></i> Excel文件格式：第一行为字段名（如"园区名称"、"城市"等），后续每行为一个园区数据
                        </div>
                    </div>
                    <div class="actions">
                        <button type="button" id="parseButton" class="primary-button">
                            <i class="fas fa-upload"></i>
                            <span class="button-text">上传并入库</span>
                            <span class="button-loading-icon"></span>
                        </button>
                    </div>
                </form>

                <!-- Excel预览区域 -->
                <div id="previewArea" style="display: none;" class="animate__animated animate__fadeIn">
                    <div class="preview-header">
                        <h3><i class="fas fa-table"></i> 园区数据预览</h3>
                        <div class="preview-info">
                            <i class="fas fa-info-circle"></i> 数据已入库
                        </div>
                    </div>
                    <div class="table-container">
                        <table id="excelPreviewTable">
                            <thead>
                                <tr>
                                    <th>字段名</th>
                                    <th>值</th>
                                </tr>
                            </thead>
                            <tbody id="excelPreviewBody">
                                <!-- Excel内容将在这里显示 -->
                            </tbody>
                        </table>
                    </div>
                    <div class="actions">
                        <button type="button" id="publishButton" disabled class="primary-button">
                            <i class="fas fa-paper-plane"></i> 确认发布推送
                        </button>
                        <button type="button" id="cancelButton" class="secondary-button">
                            <i class="fas fa-times"></i> 取消
                        </button>
                    </div>
                </div>

                <!-- 处理结果区域 -->
                <div id="resultArea" style="display: none;" class="animate__animated animate__fadeIn">
                    <div class="result-header">
                        <h3><i class="fas fa-clipboard-check"></i> 处理结果</h3>
                    </div>
                    <div id="resultMessage" class="message"></div>
                    <div id="resultDetails"></div>
                    <div class="result-actions">
                        <button type="button" id="backButton" class="secondary-button">
                            <i class="fas fa-arrow-left"></i> 返回上传
                        </button>
                    </div>
                </div>

                <!-- 已导入房源数据区域 -->
                <div class="imported-data-section">
                    <div class="imported-data-header">
                        <h3><i class="fas fa-database icon-blue"></i> 已导入房源数据</h3>
                        <div class="filter-controls">
                            <div class="filter-selectors">
                                <div class="filter-group">
                                    <label for="publishFilter">发布状态：</label>
                                    <select id="publishFilter" class="filter-select">
                                        <option value="">-- 全部 --</option>
                                        <option value="1">已发布</option>
                                        <option value="0">未发布</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label for="contentFilter">内容状态：</label>
                                    <select id="contentFilter" class="filter-select">
                                        <option value="">-- 全部 --</option>
                                        <option value="1">已生成内容</option>
                                        <option value="0">未生成内容</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label for="galleryFilter">图库状态：</label>
                                    <select id="galleryFilter" class="filter-select">
                                        <option value="">-- 全部 --</option>
                                        <option value="1">有图库</option>
                                        <option value="0">无图库</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label for="knowledgeBaseFilter">知识库状态：</label>
                                    <select id="knowledgeBaseFilter" class="filter-select">
                                        <option value="">-- 全部 --</option>
                                        <option value="1">有知识库</option>
                                        <option value="0">无知识库</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label for="poiDataFilter">周边配套状态：</label>
                                    <select id="poiDataFilter" class="filter-select">
                                        <option value="">-- 全部 --</option>
                                        <option value="1">有周边配套</option>
                                        <option value="0">无周边配套</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label for="cityFilter">城市：</label>
                                    <select id="cityFilter" class="filter-select">
                                        <option value="">-- 全部 --</option>
                                        <!-- 城市选项将通过JavaScript动态加载 -->
                                    </select>
                                </div>
                            </div>
                            <div class="filter-actions">
                                <button id="applyFilterBtn" class="filter-btn">
                                    <i class="fas fa-filter"></i> 应用筛选
                                </button>
                                <button id="resetFilterBtn" class="filter-btn">
                                    <i class="fas fa-times"></i> 重置
                                </button>
                                <button id="refreshDataBtn" class="filter-btn data-refresh-btn">
                                    <i class="fas fa-sync-alt"></i> 刷新数据
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="importedDataArea">
                        <!-- 加载状态 -->
                        <div id="loadingContainer" class="loading-container">
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i>
                                <span>加载数据中...</span>
                            </div>
                        </div>

                        <!-- 数据概览卡片 -->
                        <div id="dataSummary" class="summary-cards" style="display: none;">
                            <div class="summary-card">
                                <div class="card-title">房源总数</div>
                                <div class="card-value" id="totalCount">-</div>
                                <div class="card-footer">累计入库房源数量</div>
                            </div>
                            <div class="summary-card">
                                <div class="card-title">有内容概率</div>
                                <div class="card-value" id="contentPercentage">-</div>
                                <div class="card-footer">已生成内容的数据比例</div>
                            </div>
                            <div class="summary-card">
                                <div class="card-title">有图库概率</div>
                                <div class="card-value" id="galleryPercentage">-</div>
                                <div class="card-footer">已有图库的园区比例</div>
                            </div>
                            <div class="summary-card">
                                <div class="card-title">有知识库概率</div>
                                <div class="card-value" id="knowledgeBasePercentage">-</div>
                                <div class="card-footer">已有知识库的园区比例</div>
                            </div>
                            <div class="summary-card">
                                <div class="card-title">有周边配套概率</div>
                                <div class="card-value" id="poiDataPercentage">-</div>
                                <div class="card-footer">已有周边配套的园区比例</div>
                            </div>
                            <div class="summary-card">
                                <div class="card-title">待生成营销内容</div>
                                <div class="card-value" id="pendingCount">-</div>
                                <div class="card-footer">需要生成营销内容的园区数量</div>
                            </div>
                            <div class="summary-card">
                                <div class="card-title">已生成营销内容</div>
                                <div class="card-value" id="completedCount">-</div>
                                <div class="card-footer">已有营销内容的园区数量</div>
                            </div>
                            <div class="summary-card">
                                <div class="card-title">有图库园区</div>
                                <div class="card-value" id="withGalleryCount">-</div>
                                <div class="card-footer">已有图库的园区数量</div>
                            </div>
                            <div class="summary-card">
                                <div class="card-title">有知识库园区</div>
                                <div class="card-value" id="withKnowledgeBaseCount">-</div>
                                <div class="card-footer">已有知识库的园区数量</div>
                            </div>
                            <div class="summary-card">
                                <div class="card-title">有周边配套园区</div>
                                <div class="card-value" id="withPoiDataCount">-</div>
                                <div class="card-footer">已有周边配套的园区数量</div>
                            </div>
                        </div>

                        <!-- 园区数据表格 -->
                        <div id="communitiesTableContainer" style="display: none;">
                            <h4><i class="fas fa-building icon-cyan"></i> 园区营销内容状态</h4>

                            <!-- 替换表格为卡片布局 -->
                            <div class="community-cards" id="communityCards">
                                <!-- 卡片内容将在这里动态生成 -->
                            </div>

                            <!-- 分页控件 -->
                            <div class="pagination" id="pagination">
                                <!-- 分页按钮将在这里生成 -->
                            </div>

                            <button id="generateAllBtn" class="generate-all-btn">
                                <i class="fas fa-magic"></i> 为所有未生成的园区生成营销内容
                            </button>
                        </div>

                        <!-- 空数据状态 -->
                        <div id="emptyDataContainer" class="empty-data" style="display: none;">
                            <i class="fas fa-database"></i>
                            <h4>没有已导入的房源数据</h4>
                            <p>请先使用上方的上传功能导入Excel文件</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <div class="footer-content">
                <p>&copy; 2025 联东AI内容营销托管智能体</p>
                <div class="footer-links">
                    <a href="help.html"><i class="fas fa-book"></i> 帮助文档</a>
                    <a href="https://www.liando.cn/" target="_blank"><i class="fas fa-envelope"></i> 联系我们</a>
                    <a href="#" onclick="updateSystem(); return false;"><i class="fas fa-sync-alt"></i> 系统更新</a>
                </div>
            </div>
        </footer>
    </div>

    <script src="js/main.js"></script>
    <script src="js/factory_upload.js"></script>
    <script src="js/factory_data.js"></script>
</body>
</html>