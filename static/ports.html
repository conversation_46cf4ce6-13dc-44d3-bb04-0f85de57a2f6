<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联东AI内容营销托管智能体</title>
    <link rel="icon" href="images/1684755993.png" type="image/png">
    <link rel="shortcut icon" href="images/1684755993.png" type="image/png">
    <link rel="preload" href="css/styles.css" as="style">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/loading.css">
    <!-- 使用Font Awesome图标库的最新版本 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- 添加Material Icons作为补充图标库 -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <!-- 添加Animate.css进行简单动画效果 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="css/ports.css">
    
    <style>
        /* 图标彩色化样式 */
        .icon-blue { color: #1890ff !important; }
        .icon-green { color: #52c41a !important; }
        .icon-orange { color: #fa8c16 !important; }
        .icon-purple { color: #722ed1 !important; }
        .icon-cyan { color: #13c2c2 !important; }
        .icon-red { color: #f5222d !important; }
        .icon-gold { color: #faad14 !important; }
        .icon-magenta { color: #eb2f96 !important; }
    </style>
</head>
<body>
    <!-- 添加顶部进度条 -->
    <div id="topProgressBar" class="progress-bar"></div>

    <div class="container">
        <header class="animate__animated animate__fadeIn">
            <div class="header-content">
                <h1><div class="rotating-logo pulse-animation" aria-label="联东Logo"></div> 联东AI内容营销托管智能体</h1>
                <div class="header-subtitle"><i class="fas fa-robot tech-icon"></i> 智能化房源管理与内容营销平台</div>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" class="nav-home"><i class="fas fa-home icon-blue"></i> 首页</a></li>
                    <li><a href="cities.html" class="nav-cities"><i class="fas fa-city icon-cyan"></i> 城市管理</a></li>
                    <li><a href="ports.html" class="active nav-ports"><i class="fas fa-plug icon-green"></i> 端口管理</a></li>
                    <li><a href="status.html" class="nav-status"><i class="fas fa-chart-line icon-magenta"></i> 状态查询</a></li>
                    <li><a href="factory_images_upload.html" class="nav-images"><i class="fas fa-images icon-purple"></i> 园区图库</a></li>
                    <li><a href="factory_upload.html" class="nav-upload"><i class="fas fa-upload icon-orange"></i> 房源入库</a></li>
                    <li><a href="published_data.html" class="nav-published"><i class="fas fa-paper-plane icon-blue"></i> 房源推送</a></li>
                    <li><a href="statistics.html" class="nav-stats"><i class="fas fa-chart-bar icon-gold"></i> 发布统计</a></li>
                    <li><a href="scheduled_tasks.html" class="nav-tasks"><i class="fas fa-clock icon-green"></i> 定时任务</a></li>
                    <li><a href="competitor_analysis.html" class="nav-analysis"><i class="fas fa-search icon-cyan"></i> 竞帖分析</a></li>
                </ul>
            </nav>
        </header>
        
        <!-- 添加全局加载指示器 -->
        <div id="globalLoadingIndicator" class="global-loading-indicator">
            <div class="loading-spinner"></div>
            <span>数据加载中...</span>
        </div>

        <main>
            <!-- 添加统计卡片区域 -->
            <div class="ports-dashboard">
                <div class="ports-card">
                    <div class="ports-card-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="ports-card-content">
                        <h3>可用端口</h3>
                        <p class="ports-card-value" id="availablePortsCount">--</p>
                    </div>
                </div>
                <div class="ports-card">
                    <div class="ports-card-icon active-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="ports-card-content">
                        <h3>活跃端口</h3>
                        <p class="ports-card-value" id="activePortsCount">--</p>
                    </div>
                </div>
                <div class="ports-card">
                    <div class="ports-card-icon account-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="ports-card-content">
                        <h3>已认证账号</h3>
                        <p class="ports-card-value" id="verifiedAccountsCount">--</p>
                    </div>
                </div>
                <div class="ports-card">
                    <div class="ports-card-icon warning-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="ports-card-content">
                        <h3>待处理问题</h3>
                        <p class="ports-card-value" id="issuesCount">--</p>
                    </div>
                </div>
            </div>

            <div class="content-section animate__animated animate__fadeIn">
                <div class="section-header">
                    <h2><i class="fas fa-sliders-h"></i> 端口管理</h2>
                    <p><i class="fas fa-info-circle"></i> 查看和管理不同城市的可用端口及账号状态。</p>
                </div>
                
                <form id="portForm" class="port-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="cityInput"><i class="fas fa-city"></i> 选择城市</label>
                            <div class="input-with-icon">
                                <i class="fas fa-map-marker-alt input-icon"></i>
                                <input type="text" id="cityInput" list="cityList" placeholder="输入或选择城市" required>
                            </div>
                            <datalist id="cityList">
                                <!-- 城市选项将通过JavaScript动态添加 -->
                            </datalist>
                        </div>
                        <div class="form-group">
                            <label for="userIdInput"><i class="fas fa-user"></i> 用户ID</label>
                            <div class="input-with-icon">
                                <i class="fas fa-id-card input-icon"></i>
                                <input type="text" id="userIdInput" placeholder="请输入用户ID" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" id="fetchBtn" class="primary-button">
                            <span class="button-icon"><i class="fas fa-sync-alt"></i></span>
                            <span class="button-text">获取端口列表</span>
                            <span class="button-loading-icon"></span>
                        </button>
                        <button type="button" id="clearBtn" class="secondary-button">
                            <span class="button-icon"><i class="fas fa-eraser"></i></span>
                            <span class="button-text">清空表单</span>
                            <span class="button-loading-icon"></span>
                        </button>
                        <button type="button" id="refreshDingTalkBtn" class="secondary-button">
                            <span class="button-icon"><i class="fab fa-digital-ocean"></i></span>
                            <span class="button-text">刷新钉钉账号</span>
                            <span class="button-loading-icon"></span>
                        </button>
                    </div>
                </form>
            </div>
            
            <div class="content-section animate__animated animate__fadeIn" id="portsContainer">
                <div class="section-header">
                    <h2><i class="fas fa-network-wired"></i> 端口列表</h2>
                    <div class="view-options">
                        <button type="button" id="gridViewBtn" class="view-btn active" title="网格视图">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button type="button" id="listViewBtn" class="view-btn" title="列表视图">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
                <div id="portsContent" class="animate__animated">
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-plug"></i>
                        </div>
                        <p>请先选择城市和用户ID，点击"获取端口列表"按钮查看可用端口</p>
                    </div>
                </div>
                
                <!-- 骨架屏加载效果 -->
                <div id="skeleton-loading" class="port-list" style="display: none;">
                    <!-- 骨架卡片 -->
                    <div class="port-card skeleton">
                        <div class="skeleton-header"></div>
                        <div class="skeleton-line"></div>
                        <div class="skeleton-line"></div>
                        <div class="skeleton-line"></div>
                        <div class="skeleton-actions"></div>
                    </div>
                    <div class="port-card skeleton">
                        <div class="skeleton-header"></div>
                        <div class="skeleton-line"></div>
                        <div class="skeleton-line"></div>
                        <div class="skeleton-line"></div>
                        <div class="skeleton-actions"></div>
                    </div>
                    <div class="port-card skeleton">
                        <div class="skeleton-header"></div>
                        <div class="skeleton-line"></div>
                        <div class="skeleton-line"></div>
                        <div class="skeleton-line"></div>
                        <div class="skeleton-actions"></div>
                    </div>
                </div>
            </div>
        </main>
        
        <footer>
            <div class="footer-content">
                <p>&copy; 2025 联东AI内容营销托管智能体</p>
                <div class="footer-links">
                    <a href="help.html"><i class="fas fa-book"></i> 帮助文档</a>
                    <a href="https://www.liando.cn/" target="_blank"><i class="fas fa-envelope"></i> 联系我们</a>
                    <a href="#" onclick="updateSystem(); return false;"><i class="fas fa-sync-alt"></i> 系统更新</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- 添加消息提示容器 -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="js/main.js"></script>
    <script src="js/ports.js"></script>
</body>
</html>