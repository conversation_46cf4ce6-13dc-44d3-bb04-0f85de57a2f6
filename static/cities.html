<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联东AI内容营销托管智能体</title>
    <link rel="icon" href="images/1684755993.png" type="image/png">
    <link rel="shortcut icon" href="images/1684755993.png" type="image/png">
        <link rel="preload" href="css/styles.css" as="style">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <style>
        /* 确保导航栏在页面加载时立即显示 */
        nav ul li {
            opacity: 1 !important;
            transform: none !important;
        }
        
        /* 预加载字体图标 */
        .fas {
            display: inline-block;
        }
        
        /* 图标彩色化样式 */
        .icon-blue { color: #1890ff !important; }
        .icon-green { color: #52c41a !important; }
        .icon-orange { color: #fa8c16 !important; }
        .icon-purple { color: #722ed1 !important; }
        .icon-cyan { color: #13c2c2 !important; }
        .icon-red { color: #f5222d !important; }
        .icon-gold { color: #faad14 !important; }
        .icon-magenta { color: #eb2f96 !important; }
    </style>
</head>
<body>
    <div class="container">
        <header>            <div class="header-content">
                <h1><div class="rotating-logo" aria-label="联东Logo"></div> 联东AI内容营销托管智能体</h1>
                <div class="header-subtitle">智能化房源管理与内容营销平台</div>
            </div>
                        <nav>
                <ul>
                    <li><a href="index.html" class="nav-home"><i class="fas fa-home icon-blue"></i> 首页</a></li>
                    <li><a href="cities.html" class="active nav-cities"><i class="fas fa-city icon-cyan"></i> 城市管理</a></li>
                    <li><a href="ports.html" class="nav-ports"><i class="fas fa-plug icon-green"></i> 端口管理</a></li>
                    <li><a href="status.html" class="nav-status"><i class="fas fa-chart-line icon-magenta"></i> 状态查询</a></li>
                    <li><a href="factory_images_upload.html" class="nav-images"><i class="fas fa-images icon-purple"></i> 园区图库</a></li>
                    <li><a href="factory_upload.html" class="nav-upload"><i class="fas fa-upload icon-orange"></i> 房源入库</a></li>
                    <li><a href="published_data.html" class="nav-published"><i class="fas fa-paper-plane icon-blue"></i> 房源推送</a></li>
                    <li><a href="statistics.html" class="nav-stats"><i class="fas fa-chart-bar icon-gold"></i> 发布统计</a></li>
                    <li><a href="scheduled_tasks.html" class="nav-tasks"><i class="fas fa-clock icon-green"></i> 定时任务</a></li>
                    <li><a href="competitor_analysis.html" class="nav-analysis"><i class="fas fa-search icon-cyan"></i> 竞帖分析</a></li>
                </ul>
            </nav>
        </header>
        
        <main>
            <div class="content-section">
                <h2>城市信息管理</h2>
                <p>管理系统支持的城市信息，包括城市代码、域名和名称等。</p>
                
                <div class="actions">
                    <button id="addCityBtn">新增城市</button>
                    <button id="refreshBtn" class="secondary">刷新列表</button>
                </div>
                
                <div class="city-list">
                    <table id="cityTable">
                        <thead>
                            <tr>
                                <th>城市代码</th>
                                <th>城市名称</th>
                                <th>域名</th>
                                <th>默认用户ID</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="cityTableBody">
                            <tr class="skeleton-row">
                                <td><div class="skeleton-text"></div></td>
                                <td><div class="skeleton-text"></div></td>
                                <td><div class="skeleton-text"></div></td>
                                <td><div class="skeleton-text"></div></td>
                                <td><div class="skeleton-text"></div></td>
                            </tr>
                            <tr class="skeleton-row">
                                <td><div class="skeleton-text"></div></td>
                                <td><div class="skeleton-text"></div></td>
                                <td><div class="skeleton-text"></div></td>
                                <td><div class="skeleton-text"></div></td>
                                <td><div class="skeleton-text"></div></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
        
        <!-- 新增城市对话框 -->
        <div id="addCityModal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3>新增城市</h3>
                <form id="addCityForm">
                    <div class="form-group">
                        <label for="cityCode">城市代码</label>
                        <input type="text" id="cityCode" placeholder="请输入城市代码，如bj、cc" required>
                    </div>
                    <div class="form-group">
                        <label for="cityName">城市名称</label>
                        <input type="text" id="cityName" placeholder="请输入城市名称，如北京、长春" required>
                    </div>
                    <div class="form-group">
                        <label for="cityDomain">城市域名</label>
                        <input type="text" id="cityDomain" placeholder="请输入城市域名，如bj.youtui01.com" required>
                    </div>
                    <div class="form-group">
                        <label for="userId">默认用户ID</label>
                        <input type="text" id="userId" placeholder="请输入默认用户ID" required>
                    </div>
                    <div class="form-group">
                        <label for="userKey">默认用户密钥</label>
                        <input type="text" id="userKey" placeholder="请输入默认用户密钥" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit">保存</button>
                        <button type="button" class="secondary" onclick="closeModal('addCityModal')">取消</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 编辑城市对话框 -->
        <div id="editCityModal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3>编辑城市信息</h3>
                <form id="editCityForm">
                    <input type="hidden" id="editCityCode">
                    <div class="form-group">
                        <label for="editCityName">城市名称</label>
                        <input type="text" id="editCityName" placeholder="请输入城市名称，如北京、长春" required>
                    </div>
                    <div class="form-group">
                        <label for="editCityDomain">城市域名</label>
                        <input type="text" id="editCityDomain" placeholder="请输入城市域名，如bj.youtui01.com" required>
                    </div>
                    <div class="form-group">
                        <label for="editUserId">默认用户ID</label>
                        <input type="text" id="editUserId" placeholder="请输入默认用户ID" required>
                    </div>
                    <div class="form-group">
                        <label for="editUserKey">默认用户密钥</label>
                        <input type="text" id="editUserKey" placeholder="请输入默认用户密钥" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit">保存</button>
                        <button type="button" class="secondary" onclick="closeModal('editCityModal')">取消</button>
                    </div>
                </form>
            </div>
        </div>
        
        <footer>
            <div class="footer-content">
                <p>&copy; 2025 联东AI内容营销托管智能体</p>
                <div class="footer-links">
                    <a href="help.html"><i class="fas fa-book"></i> 帮助文档</a>
                    <a href="https://www.liando.cn/" target="_blank"><i class="fas fa-envelope"></i> 联系我们</a>
                    <a href="#" onclick="updateSystem(); return false;"><i class="fas fa-sync-alt"></i> 系统更新</a>
                </div>
            </div>
        </footer>
    </div>
    
    <script src="js/loading.js"></script>
    <script src="js/main.js"></script>
    <script>
        // 城市配置数据（模拟API数据）
        let cityConfig = {
            domains: {},
            names: {},
            accounts: {}
        };
        
        // 页面加载时获取配置
        document.addEventListener('DOMContentLoaded', function() {
            // 首先绑定所有事件处理程序
            bindEventHandlers();
            // 然后获取配置数据
            fetchCityConfig();
        });

        // 按钮加载状态控制函数
        function showButtonLoading(button) {
            setButtonLoading(button, true);
        }

        function hideButtonLoading(button) {
            setButtonLoading(button, false);
        }

        // 绑定所有事件处理程序
        function bindEventHandlers() {
            // 绑定新增城市按钮点击事件
            const addCityBtn = document.getElementById('addCityBtn');
            if (addCityBtn) {
                addCityBtn.addEventListener('click', function() {
                    openModal('addCityModal');
                });
            }
            
            // 绑定刷新按钮点击事件
            const refreshBtn = document.getElementById('refreshBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', async function(e) {
                    const btn = e.target;
                    showButtonLoading(btn);
                    await fetchCityConfig();
                    hideButtonLoading(btn);
                });
            }
            
            // 绑定关闭按钮点击事件
            document.querySelectorAll('.close').forEach(element => {
                element.addEventListener('click', function() {
                    closeModal(this.closest('.modal').id);
                });
            });
            
            // 绑定新增城市表单提交事件
            const addCityForm = document.getElementById('addCityForm');
            if (addCityForm) {
                addCityForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    if (validateForm(this)) {
                        addCity();
                    }
                });
            }
            
            // 绑定编辑城市表单提交事件
            const editCityForm = document.getElementById('editCityForm');
            if (editCityForm) {
                editCityForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    if (validateForm(this)) {
                        updateCity();
                    }
                });
            }
            
            // 点击页面其他地方关闭模态框
            window.addEventListener('click', function(e) {
                const addModal = document.getElementById('addCityModal');
                const editModal = document.getElementById('editCityModal');
                
                if (e.target === addModal) {
                    closeModal('addCityModal');
                } else if (e.target === editModal) {
                    closeModal('editCityModal');
                }
            });
        }
        
        // 获取城市配置信息
        async function fetchCityConfig() {
            showGlobalLoading('正在加载城市配置...');
            try {
                // 调用API获取城市配置
                const domainsResponse = await apiRequest('/config/domains', 'GET');
                const accountsResponse = await apiRequest('/config/accounts', 'GET');
                
                // 构建城市配置对象
                cityConfig = {
                    domains: domainsResponse.domains || {},
                    names: domainsResponse.names || {},
                    accounts: accountsResponse.accounts || {}
                };
                
                renderCityTable();
                showMessage('成功获取城市配置信息', 'success');
            } catch (error) {
                console.error('获取城市配置失败:', error);
                showMessage('获取城市配置失败: ' + error.message, 'error');
            } finally {
                hideGlobalLoading();
            }
        }
        
        // 渲染城市表格
        function renderCityTable() {
            const tableBody = document.getElementById('cityTableBody');
            let html = '';
            
            // 检查是否有城市数据
            const cities = Object.keys(cityConfig.domains);
            
            if (cities.length === 0) {
                html = '<tr><td colspan="5" class="empty-data">暂无城市数据</td></tr>';
            } else {
                // 按照城市代码字母顺序排序
                cities.sort().forEach(cityCode => {
                    const cityName = cityConfig.names[cityCode] || cityCode;
                    const cityDomain = cityConfig.domains[cityCode];
                    const account = cityConfig.accounts[cityCode] || {};
                    
                    html += `
                        <tr>
                            <td>${cityCode}</td>
                            <td>${cityName}</td>
                            <td><a href="http://${cityDomain}" target="_blank" class="domain-link">${cityDomain}</a></td>
                            <td>${account.user_id || '未设置'}</td>
                            <td>
                                <button type="button" data-city-code="${cityCode}" class="edit-btn">编辑</button>
                                <button type="button" data-city-code="${cityCode}" class="delete-btn">删除</button>
                            </td>
                        </tr>
                    `;
                });
            }
            
            tableBody.innerHTML = html;

            // 为编辑按钮添加事件监听器
            tableBody.querySelectorAll('.edit-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const cityCode = this.getAttribute('data-city-code');
                    openEditModal(cityCode);
                });
            });

            // 为删除按钮添加事件监听器
            tableBody.querySelectorAll('.delete-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const cityCode = this.getAttribute('data-city-code');
                    deleteCity(cityCode);
                });
            });
        }
        
        // 打开编辑城市模态框
        function openEditModal(cityCode) {
            const name = cityConfig.names[cityCode] || '';
            const domain = cityConfig.domains[cityCode] || '';
            const userId = cityConfig.accounts[cityCode]?.user_id || '';
            const userKey = cityConfig.accounts[cityCode]?.user_key || '';
            
            document.getElementById('editCityCode').value = cityCode;
            document.getElementById('editCityName').value = name;
            document.getElementById('editCityDomain').value = domain;
            document.getElementById('editUserId').value = userId;
            document.getElementById('editUserKey').value = userKey;
            
            openModal('editCityModal');
        }
        
        // 更新城市信息
        async function updateCity() {
            const btn = document.querySelector('#editCityForm button[type="submit"]');
            showButtonLoading(btn);
            showGlobalLoading('正在更新城市信息...');
            
            const cityCode = document.getElementById('editCityCode').value;
            const cityName = document.getElementById('editCityName').value;
            const cityDomain = document.getElementById('editCityDomain').value;
            const userId = document.getElementById('editUserId').value;
            const userKey = document.getElementById('editUserKey').value;
            
            try {
                // 调用API更新城市信息
                await apiRequest(`/config/cities/${cityCode}`, 'PUT', {
                    name: cityName,
                    domain: cityDomain,
                    user_id: userId,
                    user_key: userKey
                });
                
                // 重新获取城市配置
                await fetchCityConfig();
                closeModal('editCityModal');
                showMessage(`成功更新城市 "${cityName}"(${cityCode})`, 'success');
            } catch (error) {
                console.error('更新城市失败:', error);
                showMessage('更新城市失败: ' + error.message, 'error');
            } finally {
                hideButtonLoading(btn);
                hideGlobalLoading();
            }
        }
        
        // 添加城市
        async function addCity() {
            const btn = document.querySelector('#addCityForm button[type="submit"]');
            showButtonLoading(btn);
            showGlobalLoading('正在添加城市...');
            
            const cityCode = document.getElementById('cityCode').value;
            const cityName = document.getElementById('cityName').value;
            const cityDomain = document.getElementById('cityDomain').value;
            const userId = document.getElementById('userId').value;
            const userKey = document.getElementById('userKey').value;
            
            // 检查城市代码是否已存在
            if (cityConfig.domains[cityCode]) {
                showMessage(`城市代码 "${cityCode}" 已存在`, 'error');
                return;
            }
            
            try {
                // 调用API添加城市
                await apiRequest('/config/cities', 'POST', {
                    code: cityCode,
                    name: cityName,
                    domain: cityDomain,
                    user_id: userId,
                    user_key: userKey
                });
                
                // 重新获取城市配置
                await fetchCityConfig();
                document.getElementById('addCityForm').reset();
                closeModal('addCityModal');
                showMessage(`成功添加城市 "${cityName}"(${cityCode})`, 'success');
            } catch (error) {
                console.error('添加城市失败:', error);
                showMessage('添加城市失败: ' + error.message, 'error');
            } finally {
                hideButtonLoading(btn);
                hideGlobalLoading();
            }
        }
        
        // 删除城市
        async function deleteCity(cityCode) {
            const btn = event.target;
            const cityName = cityConfig.names[cityCode] || cityCode;
            
            if (!confirm(`确定要删除城市 "${cityName}"(${cityCode}) 吗？此操作不可恢复。`)) {
                return;
            }
            
            showButtonLoading(btn);
            showGlobalLoading('正在删除城市...');
            
            try {
                // 调用API删除城市
                await apiRequest(`/config/cities/${cityCode}`, 'DELETE');
                
                // 重新获取城市配置
                await fetchCityConfig();
                showMessage(`成功删除城市 "${cityName}"(${cityCode})`, 'success');
            } catch (error) {
                console.error('删除城市失败:', error);
                showMessage('删除城市失败: ' + error.message, 'error');
            } finally {
                hideButtonLoading(btn);
                hideGlobalLoading();
            }
        }
        
        // 打开模态框
        function openModal(modalId) {
            showModal(modalId);
        }
        
        // 关闭模态框
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }
    </script>
    
    <style>
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .modal.show {
            opacity: 1;
        }
        
        .modal-content {
            background-color: #fff;
            margin: 10% auto;
            padding: 20px;
            border-radius: 10px;
            width: 50%;
            max-width: 600px;
            position: relative;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transform: translateY(-20px);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .modal.show .modal-content {
            transform: translateY(0);
            opacity: 1;
        }
        
        .close {
            position: absolute;
            right: 20px;
            top: 15px;
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #1890ff;
        }
        
        .actions {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        
        .form-actions {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        
        .small-btn {
            padding: 5px 10px;
            font-size: 14px;
            border-radius: 4px;
        }

        /* 操作按钮通用样式 */
        .edit-btn, .delete-btn {
            padding: 5px 15px;
            font-size: 14px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            margin-right: 8px;
        }

        /* 编辑按钮样式 */
        .edit-btn {
            background-color: #1890ff;
            color: white;
        }

        .edit-btn:hover {
            background-color: #40a9ff;
        }

        /* 删除按钮样式 */
        .delete-btn {
            background-color: #ff4d4f !important;
            color: white !important;
        }

        .delete-btn:hover {
            background-color: #ff7875 !important;
        }
        
        /* 域名链接样式 */
        .domain-link {
            color: #1890ff;
            text-decoration: none;
        }
        
        .domain-link:hover {
            color: #40a9ff;
            text-decoration: underline;
        }
    </style>
</body>
</html> 