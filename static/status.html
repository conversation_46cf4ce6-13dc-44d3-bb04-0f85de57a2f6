<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联东AI内容营销托管智能体</title>
    <link rel="icon" href="images/1684755993.png" type="image/png">
    <link rel="shortcut icon" href="images/1684755993.png" type="image/png">
    <link rel="preload" href="css/styles.css" as="style">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="css/status.css">
    <!-- 使用Font Awesome图标库的最新版本 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- 添加Material Icons作为补充图标库 -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <!-- 添加Animate.css进行简单动画效果 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        /* 图标彩色化样式 */
        .icon-blue { color: #1890ff !important; }
        .icon-green { color: #52c41a !important; }
        .icon-orange { color: #fa8c16 !important; }
        .icon-purple { color: #722ed1 !important; }
        .icon-cyan { color: #13c2c2 !important; }
        .icon-red { color: #f5222d !important; }
        .icon-gold { color: #faad14 !important; }
        .icon-magenta { color: #eb2f96 !important; }
    </style>
</head>
<body>
    <!-- 添加顶部进度条 -->
    <div id="topProgressBar" class="progress-bar"></div>

    <div class="container">
        <header class="animate__animated animate__fadeIn">
            <div class="header-content">
                <h1><div class="rotating-logo pulse-animation" aria-label="联东Logo"></div> 联东AI内容营销托管智能体</h1>
                <div class="header-subtitle"><i class="fas fa-robot tech-icon"></i> 智能化房源管理与内容营销平台</div>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" class="nav-home"><i class="fas fa-home icon-blue"></i> 首页</a></li>
                    <li><a href="cities.html" class="nav-cities"><i class="fas fa-city icon-cyan"></i> 城市管理</a></li>
                    <li><a href="ports.html" class="nav-ports"><i class="fas fa-plug icon-green"></i> 端口管理</a></li>
                    <li><a href="status.html" class="active nav-status"><i class="fas fa-chart-line icon-magenta"></i> 状态查询</a></li>
                    <li><a href="factory_images_upload.html" class="nav-images"><i class="fas fa-images icon-purple"></i> 园区图库</a></li>
                    <li><a href="factory_upload.html" class="nav-upload"><i class="fas fa-upload icon-orange"></i> 房源入库</a></li>
                    <li><a href="published_data.html" class="nav-published"><i class="fas fa-paper-plane icon-blue"></i> 房源推送</a></li>
                    <li><a href="statistics.html" class="nav-stats"><i class="fas fa-chart-bar icon-gold"></i> 发布统计</a></li>
                    <li><a href="scheduled_tasks.html" class="nav-tasks"><i class="fas fa-clock icon-green"></i> 定时任务</a></li>
                    <li><a href="competitor_analysis.html" class="nav-analysis"><i class="fas fa-search icon-cyan"></i> 竞帖分析</a></li>
                </ul>
            </nav>
        </header>
        
        <!-- 添加全局加载指示器 -->
        <div id="globalLoadingIndicator" class="global-loading-indicator">
            <div class="loading-spinner"></div>
            <span>数据加载中...</span>
        </div>

        <main>
            <!-- 添加状态卡片区域 -->
            <div class="status-dashboard">
                <div class="status-card">
                    <div class="status-card-icon">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <div class="status-card-content">
                        <h3>今日推送</h3>
                        <p class="status-card-value" id="todayPushCount">--</p>
                    </div>
                </div>
                <div class="status-card">
                    <div class="status-card-icon success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="status-card-content">
                        <h3>推送成功</h3>
                        <p class="status-card-value" id="successPushCount">--</p>
                    </div>
                </div>
                <div class="status-card">
                    <div class="status-card-icon error-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="status-card-content">
                        <h3>推送失败</h3>
                        <p class="status-card-value" id="failedPushCount">--</p>
                    </div>
                </div>
                <div class="status-card">
                    <div class="status-card-icon warning-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="status-card-content">
                        <h3>需要认证</h3>
                        <p class="status-card-value" id="needAuthCount">--</p>
                    </div>
                </div>
            </div>

            <!-- 推送记录列表 -->
            <div class="content-section animate__animated animate__fadeIn" id="recordsSection">
                <div class="section-header">
                    <h2><i class="fas fa-list-alt"></i> 推送记录列表</h2>
                    <p><i class="fas fa-info-circle"></i> 查询已推送房源的状态和详细信息。点击记录可查看详细推送状态。</p>
                </div>
                
                <div class="basic-filter">
                    <div class="filter-header">
                        <i class="fas fa-filter"></i> 基本筛选
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="cityInput"><i class="fas fa-city"></i> 选择城市</label>
                            <div class="input-with-icon">
                                <i class="fas fa-map-marker-alt input-icon"></i>
                                <input type="text" id="cityInput" list="cityList" placeholder="输入或选择城市" required>
                            </div>
                            <datalist id="cityList">
                                <!-- 城市选项将通过JavaScript动态添加 -->
                            </datalist>
                        </div>
                        <div class="form-group">
                            <label for="userIdInput"><i class="fas fa-user"></i> 用户ID</label>
                            <div class="input-with-icon">
                                <i class="fas fa-id-card input-icon"></i>
                                <input type="text" id="userIdInput" placeholder="请输入用户ID" required>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="records-filter">
                    <div class="filter-header">
                        <i class="fas fa-sliders-h"></i> 高级筛选
                        <button class="toggle-filter-btn" id="toggleFilterBtn">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="filter-content" id="advancedFilterContent">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="filterErpId"><i class="fas fa-hashtag"></i> 按房源ID筛选</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-search input-icon"></i>
                                    <input type="text" id="filterErpId" placeholder="请输入ERP房源ID">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="filterWebsite"><i class="fas fa-globe"></i> 按网站筛选</label>
                                <div class="select-with-icon">
                                    <i class="fas fa-sitemap input-icon"></i>
                                    <select id="filterWebsite">
                                        <option value="">全部网站</option>
                                        <!-- 网站选项将通过JavaScript动态添加 -->
                                    </select>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="button" id="filterBtn" class="primary-button">
                                    <span class="button-icon"><i class="fas fa-filter"></i></span>
                                    <span class="button-text">筛选</span>
                                    <span class="button-loading-icon"></span>
                                </button>
                                <button type="button" id="resetFilterBtn" class="secondary-button">
                                    <span class="button-icon"><i class="fas fa-sync-alt"></i></span>
                                    <span class="button-text">重置</span>
                                    <span class="button-loading-icon"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="table-container">
                    <table id="recordsTable">
                        <thead>
                            <tr>
                                <th><i class="far fa-clock"></i> 时间</th>
                                <th><i class="fas fa-building"></i> 园区名称</th>
                                <th><i class="fas fa-globe"></i> 网站</th>
                                <th><i class="fas fa-fingerprint"></i> 进程ID</th>
                                <th><i class="fas fa-info-circle"></i> 推送状态</th>
                                <th><i class="fas fa-comment-alt"></i> 推送消息</th>
                                <th><i class="fas fa-cog"></i> 操作</th>
                            </tr>
                        </thead>
                        <tbody id="recordsTableBody">
                            <!-- 骨架屏加载效果 -->
                            <tr class="skeleton-row">
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-button"></div></td>
                            </tr>
                            <tr class="skeleton-row">
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-button"></div></td>
                            </tr>
                            <tr class="skeleton-row">
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-cell"></div></td>
                                <td><div class="skeleton-button"></div></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="pagination">
                    <button type="button" id="prevPageBtn" class="secondary-button" disabled>
                        <i class="fas fa-chevron-left"></i> 上一页
                    </button>
                    <span id="pageInfo" class="page-info">第 1 页</span>
                    <button type="button" id="nextPageBtn" class="secondary-button" disabled>
                        下一页 <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
            
            <div class="content-section animate__animated animate__fadeIn" id="resultSection" style="display: none;">
                <div class="section-header">
                    <h2><i class="fas fa-search-plus"></i> 查询结果</h2>
                    <button type="button" id="backToListBtn" class="secondary-button">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </button>
                </div>
                <div id="resultContent"></div>
            </div>
        </main>
        
        <footer>
            <div class="footer-content">
                <p>&copy; 2025 联东AI内容营销托管智能体</p>
                <div class="footer-links">
                    <a href="help.html"><i class="fas fa-book"></i> 帮助文档</a>
                    <a href="https://www.liando.cn/" target="_blank"><i class="fas fa-envelope"></i> 联系我们</a>
                    <a href="#" onclick="updateSystem(); return false;"><i class="fas fa-sync-alt"></i> 系统更新</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- 添加消息提示容器 -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="js/main.js"></script>
    <script src="js/status.js"></script>
</body>
</html> 