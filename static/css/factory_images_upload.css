/* 基础表单样式 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #262626;
    font-size: 14px;
    font-weight: 500;
}

/* Material Design风格的条形流程图样式 */
.process-bar-item {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    padding: 16px 24px;
    margin-bottom: 15px;
    border-radius: 8px;
    font-weight: 400;
    box-shadow: 0 3px 5px rgba(0,0,0,0.2);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
}

.process-bar-item:hover {
    box-shadow: 0 6px 12px rgba(0,0,0,0.25);
    transform: translateY(-2px);
}

.process-bar-item::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.1) 100%);
    transform: skewX(-25deg);
}

.process-bar-title {
    font-size: 18px;
    margin-bottom: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.process-bar-title .step-number {
    background-color: rgba(255,255,255,0.2);
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-weight: bold;
}

.process-bar-desc {
    font-size: 14px;
    opacity: 0.9;
    margin-left: 40px;
    position: relative;
}

/* 特定步骤的颜色变化 */
.process-bar-item:nth-child(1) {
    background: linear-gradient(135deg, #2196F3, #1565C0);
}

.process-bar-item:nth-child(2) {
    background: linear-gradient(135deg, #1E88E5, #0D47A1);
}

.process-bar-item:nth-child(3) {
    background: linear-gradient(135deg, #1976D2, #0D47A1);
}

.process-bar-flow {
    padding: 10px;
}

/* 高级图标样式 */
.process-bar-item .feature-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 40px;
    opacity: 0.2;
    color: white;
}

.process-bar-item .pattern-dots {
    position: absolute;
    bottom: 10px;
    left: 10px;
    width: 30px;
    height: 30px;
    background-image: radial-gradient(rgba(255,255,255,0.2) 1px, transparent 1px);
    background-size: 8px 8px;
}

.process-bar-item .pattern-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: rgba(255,255,255,0.3);
}

/* 添加动画效果 */
@keyframes float {
    0%, 100% { transform: translateY(-50%) translateX(0); }
    50% { transform: translateY(-50%) translateX(5px); }
}

.process-bar-item .feature-icon {
    animation: float 3s ease-in-out infinite;
}

/* 步骤标记 */
.process-bar-item .step-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #FF5722;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 3px 5px rgba(0,0,0,0.2);
    border: 2px solid white;
    z-index: 2;
}

.process-bar-item:nth-child(1) .step-badge {
    background: #FF5722;
}

.process-bar-item:nth-child(2) .step-badge {
    background: #FF9800;
}

.process-bar-item:nth-child(3) .step-badge {
    background: #4CAF50;
}

/* 输入框和选择框通用样式 */
.form-group input:not([type="file"]),
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    color: #262626;
    background-color: white;
    transition: all 0.3s;
    outline: none;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.016);
}

/* 选择框特殊样式 */
.form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L5 5L9 1' stroke='%23595959' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 32px;
}

/* 悬停和焦点状态 */
.form-group input:not([type="file"]):hover,
.form-group select:hover {
    border-color: #40a9ff;
}

.form-group input:not([type="file"]):focus,
.form-group select:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
button {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1.5;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

button:not(.secondary) {
    background-color: #1890ff;
    color: white;
}

button:not(.secondary):hover {
    background-color: #40a9ff;
}

button.secondary {
    background-color: white;
    border: 1px solid #d9d9d9;
    color: #595959;
}

button.secondary:hover {
    color: #40a9ff;
    border-color: #40a9ff;
}

/* 表单区域样式 */
#imageUploadForm {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.form-row {
    display: flex;
    gap: 24px;
    align-items: flex-end;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

/* 图片上传区域样式 */
.image-upload-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.image-upload-box {
    width: 200px;
    height: 200px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    background-color: #fafafa;
}

.image-upload-box:hover {
    border-color: #40a9ff;
    background-color: #e6f7ff;
}

.image-upload-box input[type="file"] {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.image-upload-box .icon {
    font-size: 48px;
    color: #8c8c8c;
    margin-bottom: 10px;
    transition: all 0.3s;
}

.image-upload-box:hover .icon {
    color: #40a9ff;
}

.image-upload-box .text {
    color: #595959;
    text-align: center;
    font-size: 14px;
    transition: all 0.3s;
}

.image-upload-box:hover .text {
    color: #40a9ff;
}

.image-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 6px;
}

.image-remove {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #ff4d4f;
    z-index: 1;
    transition: all 0.3s;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.image-remove:hover {
    background-color: #ff4d4f;
    color: white;
}

.image-type-label {
    color: #262626;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 12px;
    display: block;
}

/* 上传进度条样式 */
.upload-progress {
    height: 4px;
    width: 100%;
    background-color: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 12px;
}

.upload-progress-bar {
    height: 100%;
    background-color: #1890ff;
    width: 0%;
    transition: width 0.3s ease;
}

.upload-status {
    display: flex;
    align-items: center;
    margin-top: 8px;
    font-size: 14px;
    color: #8c8c8c;
}

.upload-status .status-icon {
    margin-right: 6px;
}

/* Loading状态样式 */
.loading-container {
    text-align: center;
    padding: 40px 20px;
    background-color: #fafafa;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
}

.loading-spinner {
    margin-bottom: 16px;
}

.loading-spinner i {
    font-size: 32px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #595959;
    font-size: 16px;
    margin-bottom: 20px;
    font-weight: 500;
}

.loading-progress {
    width: 100%;
    max-width: 300px;
    height: 4px;
    background-color: #e8e8e8;
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
    position: relative;
}

.loading-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #1890ff, #40a9ff, #1890ff);
    background-size: 200% 100%;
    animation: loading-wave 2s ease-in-out infinite;
    border-radius: 2px;
}

@keyframes loading-wave {
    0% {
        background-position: 200% 0;
        width: 0%;
    }
    50% {
        background-position: 0% 0;
        width: 100%;
    }
    100% {
        background-position: -200% 0;
        width: 0%;
    }
}

/* 消息提示样式 */
.message {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 1.5;
}

.message.success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
}

.message.error {
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    color: #ff4d4f;
}

.message.warning {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    color: #faad14;
}

.message.info {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    color: #1890ff;
}

/* URL表格样式 */
.url-table {
    margin-top: 24px;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.url-table table {
    width: 100%;
    border-collapse: collapse;
}

.url-table th,
.url-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.url-table th {
    background-color: #fafafa;
    font-weight: 500;
    color: #262626;
}

.url-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    color: #262626;
    background-color: white;
    transition: all 0.3s;
}

.url-input:hover {
    border-color: #40a9ff;
}

.url-input:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
}

.copy-btn {
    background-color: #1890ff;
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.copy-btn:hover {
    background-color: #40a9ff;
}

/* 添加更多按钮样式 */
.add-more-btn {
    margin-top: 10px;
    font-size: 13px;
    padding: 4px 12px;
}

.add-more-btn:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

/* 自定义弹框样式 */
.custom-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

.custom-modal.show {
    opacity: 1;
    visibility: visible;
}

.custom-modal-content {
    background: white;
    border-radius: 12px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    transform: scale(0.9) translateY(-10px);
    transition: all 0.2s ease;
}

.custom-modal.show .custom-modal-content {
    transform: scale(1) translateY(0);
}

.custom-modal-header {
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    color: white;
    padding: 20px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.custom-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.custom-modal-header h3 i {
    margin-right: 8px;
    font-size: 20px;
}

.custom-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.custom-modal-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.custom-modal-body {
    padding: 24px;
    line-height: 1.6;
}

.custom-modal-body .error-icon {
    font-size: 48px;
    color: #faad14;
    text-align: center;
    margin-bottom: 16px;
    display: block;
}

.custom-modal-body .error-title {
    font-size: 20px;
    font-weight: 500;
    color: #262626;
    text-align: center;
    margin-bottom: 16px;
}

.custom-modal-body .error-message {
    font-size: 16px;
    color: #595959;
    text-align: center;
    margin-bottom: 20px;
}

.custom-modal-body .solution-title {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.custom-modal-body .solution-title i {
    margin-right: 8px;
    color: #52c41a;
}

.custom-modal-body .solution-list {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
}

.custom-modal-body .solution-list li {
    padding: 8px 0;
    display: flex;
    align-items: flex-start;
    font-size: 14px;
    color: #595959;
}

.custom-modal-body .solution-list li i {
    margin-right: 8px;
    margin-top: 2px;
    color: #1890ff;
    font-size: 12px;
}

.custom-modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.custom-modal-footer button {
    min-width: 80px;
}

.custom-modal-footer .btn-primary {
    background-color: #1890ff;
    color: white;
    border: none;
}

.custom-modal-footer .btn-primary:hover {
    background-color: #40a9ff;
}

.custom-modal-footer .btn-secondary {
    background-color: white;
    color: #595959;
    border: 1px solid #d9d9d9;
}

.custom-modal-footer .btn-secondary:hover {
    color: #40a9ff;
    border-color: #40a9ff;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 16px;
    }

    .form-group {
        width: 100%;
    }

    .image-upload-container {
        justify-content: center;
    }

    .image-upload-box {
        width: 100%;
        max-width: 300px;
    }
}

/* 新增/修改的样式 */
#imagePreviewContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 6px;
    min-height: 100px; /* 至少给点高度 */
    background-color: #f9f9f9;
}

.image-preview-item {
    position: relative;
    width: 120px;
    height: 120px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.image-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-preview-item .remove-preview {
    position: absolute;
    top: 4px;
    right: 4px;
    background-color: rgba(255, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 14px;
    line-height: 18px;
    text-align: center;
    cursor: pointer;
    z-index: 1;
    transition: background-color 0.2s;
}
.image-preview-item .remove-preview:hover {
    background-color: rgba(255, 0, 0, 1);
}

/* 调整上传按钮 */
#uploadTriggerButton {
    margin-right: 10px;
}

/* 调整结果展示 */
.url-list {
    list-style: none;
    padding: 0;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #eee;
    padding: 10px;
    border-radius: 4px;
}
.url-list li {
    margin-bottom: 8px;
    word-break: break-all;
    font-size: 13px;
    background: #f7f7f7;
    padding: 5px 8px;
    border-radius: 3px;
}
.error-list li {
    color: #ff4d4f;
    background: #fff2f0;
}

/* 视频预览样式 */
#videoPreviewContainer {
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 6px;
    min-height: 80px;
    background-color: #f9f9f9;
    margin-top: 15px;
}

.video-preview-item {
    position: relative;
    width: 320px;
    max-width: 100%;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.video-preview-item video {
    width: 100%;
    max-height: 240px;
    display: block;
}

.video-preview-item .video-info {
    padding: 8px 12px;
    font-size: 13px;
    color: #666;
    background-color: #f5f5f5;
    border-top: 1px solid #ddd;
}

.video-preview-item .remove-preview {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: rgba(255, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
    cursor: pointer;
    z-index: 1;
    transition: background-color 0.2s;
}

.video-preview-item .remove-preview:hover {
    background-color: rgba(255, 0, 0, 1);
}

/* 分页控件样式 */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 8px;
}

.pagination button {
    min-width: 32px;
    height: 32px;
    padding: 0 10px;
    border: 1px solid #d9d9d9;
    background-color: white;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
}

.pagination button:hover {
    color: #40a9ff;
    border-color: #40a9ff;
}

.pagination button.active {
    color: #fff;
    background-color: #1890ff;
    border-color: #1890ff;
}

.pagination button:disabled {
    color: rgba(0, 0, 0, 0.25);
    border-color: #d9d9d9;
    background-color: #f5f5f5;
    cursor: not-allowed;
}

/* 操作按钮样式 */
.action-btn {
    margin-right: 6px;
    font-size: 13px;
    padding: 4px 8px;
}

.generate-video-btn {
    background-color: #13c2c2;
    color: white;
    border: none;
    transition: all 0.3s;
}

.generate-video-btn:hover {
    background-color: #36cfc9;
}

.generate-video-btn:disabled {
    background-color: #bfbfbf;
    cursor: not-allowed;
}

/* 生成中动画 */
.generating {
    position: relative;
    padding-right: 24px;
}

.generating:after {
    content: '';
    position: absolute;
    right: 8px;
    top: 50%;
    margin-top: -4px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 2px solid white;
    border-right-color: transparent;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 园区选择下拉区域样式 */
.campus-select-wrapper {
    position: relative;
    width: 100%;
}

.campus-select-wrapper .input-group {
    display: flex;
    align-items: center;
    position: relative;
}

.campus-select-wrapper input {
    flex: 1;
    padding-right: 30px;
    border-radius: 6px 0 0 6px;
}

.campus-select-wrapper .toggle-btn {
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0 6px 6px 0;
}

.campus-select-wrapper .toggle-btn i {
    border: solid #595959;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(45deg);
    margin-top: -3px;
    transition: transform 0.3s;
}

.campus-select-wrapper.open .toggle-btn i {
    transform: rotate(-135deg);
    margin-top: 3px;
}

.campus-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    background-color: white;
    border: 1px solid #d9d9d9;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 10;
    margin-top: -1px;
    display: none;
}

.campus-select-wrapper.open .campus-dropdown {
    display: block;
}

.campus-dropdown .dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: background 0.3s;
    border-bottom: 1px solid #f0f0f0;
}

.campus-dropdown .dropdown-item:hover {
    background-color: #f5f5f5;
}

.campus-dropdown .dropdown-item.active {
    background-color: #e6f7ff;
    color: #1890ff;
}

.campus-dropdown .special-item {
    color: #1890ff;
    font-weight: 500;
}

/* 流程指南的动态效果 */
.visual-process-flow {
    position: relative;
}

.process-step {
    transition: all 0.5s ease;
}

.step-circle {
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    box-shadow: 0 4px 10px rgba(24, 144, 255, 0.2);
}

.step-arrow {
    position: absolute;
    top: 25px;
    right: -30px;
    width: 60px;
    height: 2px;
    background-color: #1890ff;
    z-index: 1;
    transition: width 0.6s ease;
}

.step-arrow:after {
    content: '';
    position: absolute;
    right: 0;
    top: -4px;
    width: 10px;
    height: 10px;
    border-top: 2px solid #1890ff;
    border-right: 2px solid #1890ff;
    transform: rotate(45deg);
}

/* 自动动画效果 */
@keyframes pulse {
    0% { transform: scale(1); box-shadow: 0 4px 10px rgba(24, 144, 255, 0.2); }
    50% { transform: scale(1.15); box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4); }
    100% { transform: scale(1); box-shadow: 0 4px 10px rgba(24, 144, 255, 0.2); }
}

@keyframes arrowFlow {
    0% { width: 0; opacity: 0; }
    20% { width: 60px; opacity: 1; }
    80% { background-color: #1890ff; }
    100% { width: 60px; opacity: 1; background-color: #52c41a; }
}

@keyframes fadeInUp {
    0% { opacity: 0; transform: translateY(15px); }
    60% { opacity: 1; transform: translateY(-5px); }
    100% { opacity: 1; transform: translateY(0); }
}

@keyframes progressGrow {
    0% { width: 0%; background-color: #1890ff; }
    30% { width: 33%; background-color: #1890ff; }
    33% { background-color: #52c41a; }
    60% { width: 66%; background-color: #52c41a; }
    63% { background-color: #1890ff; }
    100% { width: 100%; background-color: #52c41a; }
}

@keyframes colorChange {
    0% { background-color: #1890ff; }
    33% { background-color: #52c41a; }
    66% { background-color: #fa8c16; }
    100% { background-color: #1890ff; }
}

@keyframes rotateIn {
    0% { transform: rotate(-45deg) scale(0.5); opacity: 0; }
    100% { transform: rotate(0) scale(1); opacity: 1; }
}

@keyframes bouncePulse {
    0%, 100% { transform: scale(1); }
    40% { transform: scale(1.2); }
    60% { transform: scale(0.9); }
    80% { transform: scale(1.1); }
}

@keyframes glowEffect {
    0% { box-shadow: 0 0 5px rgba(24, 144, 255, 0.3); }
    50% { box-shadow: 0 0 20px rgba(24, 144, 255, 0.7), 0 0 30px rgba(24, 144, 255, 0.4); }
    100% { box-shadow: 0 0 5px rgba(24, 144, 255, 0.3); }
}

@keyframes arrowPulse {
    0%, 100% { transform: scaleX(1); }
    50% { transform: scaleX(1.1); }
}

/* 添加带箭头的小图标 */
.step-circle:after {
    content: '→';
    position: absolute;
    font-size: 0;
    transition: all 0.4s;
    color: white;
}

.visual-process-flow.auto-play #step1 .step-circle:after {
    animation: iconPop 12s infinite;
    animation-delay: 1s;
}

.visual-process-flow.auto-play #step2 .step-circle:after {
    animation: iconPop 12s infinite;
    animation-delay: 5s;
}

.visual-process-flow.auto-play #step3 .step-circle:after {
    animation: iconPop 12s infinite;
    animation-delay: 9s;
}

@keyframes iconPop {
    0%, 60%, 100% { font-size: 0; transform: rotate(0); }
    5%, 25% { font-size: 24px; transform: rotate(45deg); }
    15% { transform: rotate(0); }
}

/* 3D变换效果 */
@keyframes flipIn {
    0% { transform: perspective(400px) rotateY(90deg); opacity: 0; }
    40% { transform: perspective(400px) rotateY(-10deg); }
    70% { transform: perspective(400px) rotateY(10deg); }
    100% { transform: perspective(400px) rotateY(0deg); opacity: 1; }
}

/* 添加动画序列 */
.visual-process-flow.animate .process-progress-bar {
    animation: progressGrow 6s ease-in-out forwards;
}

.visual-process-flow.animate #step1 .step-circle {
    animation: pulse 2s ease-in-out infinite, glowEffect 3s infinite;
    animation-delay: 0.3s, 0s;
}

.visual-process-flow.animate #step1 .step-title {
    animation: flipIn 1s ease-out forwards;
}

.visual-process-flow.animate #step1 .step-desc {
    animation: fadeInUp 0.8s ease-out forwards;
    animation-delay: 0.3s;
}

.visual-process-flow.animate #step1 .step-arrow {
    animation: arrowFlow 1.5s ease-out forwards, arrowPulse 2s infinite 1.5s;
    animation-delay: 1s, 1.5s;
}

.visual-process-flow.animate #step2 .step-circle {
    animation: pulse 2s ease-in-out infinite, glowEffect 3s infinite;
    animation-delay: 1.8s, 1.5s;
}

.visual-process-flow.animate #step2 .step-title {
    animation: flipIn 1s ease-out forwards;
    animation-delay: 1.5s;
}

.visual-process-flow.animate #step2 .step-desc {
    animation: fadeInUp 0.8s ease-out forwards;
    animation-delay: 1.8s;
}

.visual-process-flow.animate #step2 .step-arrow {
    animation: arrowFlow 1.5s ease-out forwards, arrowPulse 2s infinite 3s;
    animation-delay: 2.5s, 3s;
}

.visual-process-flow.animate #step3 .step-circle {
    animation: pulse 2s ease-in-out infinite, glowEffect 3s infinite;
    animation-delay: 3.3s, 3s;
}

.visual-process-flow.animate #step3 .step-title {
    animation: flipIn 1s ease-out forwards;
    animation-delay: 3s;
}

.visual-process-flow.animate #step3 .step-desc {
    animation: fadeInUp 0.8s ease-out forwards;
    animation-delay: 3.3s;
}

/* 自动轮播高亮效果 */
@keyframes highlightStep1 {
    0%, 100% { background-color: #1890ff; transform: scale(1); }
    10%, 30% { background-color: #52c41a; transform: scale(1.15); }
    15% { transform: scale(1.25) rotate(15deg); }
    20% { transform: scale(1.2) rotate(-10deg); }
    25% { transform: scale(1.15) rotate(5deg); }
}

@keyframes highlightStep2 {
    0%, 100% { background-color: #1890ff; transform: scale(1); }
    40%, 60% { background-color: #fa8c16; transform: scale(1.15); }
    45% { transform: scale(1.25) rotate(-15deg); }
    50% { transform: scale(1.2) rotate(10deg); }
    55% { transform: scale(1.15) rotate(-5deg); }
}

@keyframes highlightStep3 {
    0%, 100% { background-color: #1890ff; transform: scale(1); }
    70%, 90% { background-color: #722ed1; transform: scale(1.15); }
    75% { transform: scale(1.25) rotate(15deg); }
    80% { transform: scale(1.2) rotate(-10deg); }
    85% { transform: scale(1.15) rotate(5deg); }
}

/* 彩虹进度条效果 */
@keyframes rainbowProgress {
    0% { width: 0%; background-color: #1890ff; }
    30% { width: 33%; background-color: #52c41a; }
    60% { width: 66%; background-color: #fa8c16; }
    100% { width: 100%; background-color: #722ed1; }
}

.visual-process-flow.auto-play #step1 .step-circle {
    animation: highlightStep1 12s ease-in-out infinite, glowEffect 4s infinite;
}

.visual-process-flow.auto-play #step2 .step-circle {
    animation: highlightStep2 12s ease-in-out infinite, glowEffect 4s infinite 1s;
}

.visual-process-flow.auto-play #step3 .step-circle {
    animation: highlightStep3 12s ease-in-out infinite, glowEffect 4s infinite 2s;
}

.visual-process-flow.auto-play .process-progress-bar {
    animation: rainbowProgress 12s ease-in-out infinite alternate;
}

.visual-process-flow.auto-play .step-title {
    animation: colorPulse 12s infinite;
    transition: all 0.3s;
}

.visual-process-flow.auto-play #step1 .step-title {
    animation-delay: 0.8s;
}

.visual-process-flow.auto-play #step2 .step-title {
    animation-delay: 4.8s;
}

.visual-process-flow.auto-play #step3 .step-title {
    animation-delay: 8.8s;
}

@keyframes colorPulse {
    0%, 90%, 100% { color: #1890ff; transform: scale(1); }
    3%, 10% { color: #52c41a; transform: scale(1.05); }
}

/* 步骤初始透明效果 */
.visual-process-flow:not(.animate) .step-title,
.visual-process-flow:not(.animate) .step-desc {
    opacity: 0;
}

.visual-process-flow:not(.animate) .step-arrow {
    width: 0;
    opacity: 0;
}

/* 添加步骤图标 */
.step-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    color: white;
    opacity: 0;
}

.visual-process-flow.auto-play #step1 .step-icon {
    content: "📸";
    animation: iconRotate 12s infinite;
    animation-delay: 1s;
}

.visual-process-flow.auto-play #step2 .step-icon {
    content: "📊";
    animation: iconRotate 12s infinite;
    animation-delay: 5s;
}

.visual-process-flow.auto-play #step3 .step-icon {
    content: "🎬";
    animation: iconRotate 12s infinite;
    animation-delay: 9s;
}

@keyframes iconRotate {
    0%, 60%, 100% { opacity: 0; transform: translate(-50%, -50%) rotate(0) scale(0.5); }
    5%, 25% { opacity: 1; transform: translate(-50%, -50%) rotate(360deg) scale(1); }
}

/* 流程进度指示器 */
.process-progress {
    height: 4px;
    background-color: #f0f0f0;
    position: absolute;
    top: 25px;
    left: 25px;
    right: 25px;
    z-index: 0;
    border-radius: 2px;
    overflow: hidden;
}

/* 确保导航栏在页面加载时立即显示 */
nav ul li {
    opacity: 1 !important;
    transform: none !important;
}

/* 预加载字体图标 */
.fas {
    display: inline-block;
}