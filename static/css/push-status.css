/* 推送状态容器样式 */
.push-status-container {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    width: 350px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    opacity: 0;
    transform: translateY(-50%) translateX(100%);
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
}

.push-status-container.show {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
}

/* 推送状态头部 */
.push-status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.push-status-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.header-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
}

.background-push-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.background-push-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.close-status-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.close-status-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* 推送步骤容器 */
.push-steps {
    padding: 20px;
}

/* 单个步骤样式 */
.step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    position: relative;
    transition: all 0.3s ease;
}

.step:last-child {
    margin-bottom: 0;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 20px;
    top: 40px;
    width: 2px;
    height: 20px;
    background: #e0e0e0;
    transition: background-color 0.3s ease;
}

.step.completed:not(:last-child)::after {
    background: #4caf50;
}

/* 步骤图标 */
.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    transition: all 0.3s ease;
    border: 2px solid #e0e0e0;
}

.step-icon i {
    font-size: 16px;
    color: #666;
    transition: color 0.3s ease;
}

/* 步骤内容 */
.step-content {
    flex: 1;
    min-width: 0;
}

.step-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
}

.step-message {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 步骤状态图标 */
.step-status {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    flex-shrink: 0;
}

.step-status i {
    font-size: 14px;
}

/* 处理中状态 */
.step.processing .step-icon {
    background: #e3f2fd;
    border-color: #2196f3;
    animation: pulse 2s infinite;
}

.step.processing .step-icon i {
    color: #2196f3;
}

.step.processing .step-status i {
    color: #2196f3;
}

/* 完成状态 */
.step.completed .step-icon {
    background: #e8f5e8;
    border-color: #4caf50;
}

.step.completed .step-icon i {
    color: #4caf50;
}

.step.completed .step-status i {
    color: #4caf50;
}

/* 错误状态 */
.step.error .step-icon {
    background: #ffebee;
    border-color: #f44336;
}

.step.error .step-icon i {
    color: #f44336;
}

.step.error .step-status i {
    color: #f44336;
}

.step.error .step-message {
    color: #f44336;
}

/* 警告状态 */
.step.warning .step-icon {
    background: #fff3e0;
    border-color: #ff9800;
}

.step.warning .step-icon i {
    color: #ff9800;
}

.step.warning .step-status i {
    color: #ff9800;
}

.step.warning .step-message {
    color: #ff9800;
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(33, 150, 243, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
    }
}

/* 旋转动画 */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.fa-spin {
    animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .push-status-container {
        right: 10px;
        width: 320px;
    }
    
    .push-status-header {
        padding: 12px 16px;
    }
    
    .push-steps {
        padding: 16px;
    }
    
    .step {
        margin-bottom: 16px;
    }
    
    .step-icon {
        width: 36px;
        height: 36px;
    }
    
    .step-icon i {
        font-size: 14px;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .push-status-container {
        background: #2d2d2d;
        border-color: #404040;
    }
    
    .push-status-header {
        border-bottom-color: #404040;
    }
    
    .step-title {
        color: #e0e0e0;
    }
    
    .step-message {
        color: #b0b0b0;
    }
    
    .step-icon {
        background: #404040;
        border-color: #555;
    }
    
    .step-icon i {
        color: #b0b0b0;
    }
    
    .step:not(:last-child)::after {
        background: #555;
    }
}
