/* 为首页卡片应用与ports.html一致的样式 */
.card-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.card {
    background-color: white;
    border-radius: 16px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.card:after {
    content: '';
    position: absolute;
    right: -40px;
    top: -40px;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: rgba(24, 144, 255, 0.03);
    z-index: 0;
    transition: all 0.3s ease;
}

.card:hover:after {
    transform: scale(1.2);
    background: rgba(24, 144, 255, 0.06);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 14px;
    background-color: rgba(24, 144, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.card:hover .card-icon {
    transform: rotate(5deg) scale(1.05);
}

.card-icon i {
    font-size: 24px;
    color: #1890ff;
}

.card h3 {
    font-size: 18px;
    color: #595959;
    margin: 0 0 12px 0;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.card p {
    color: #8c8c8c;
    flex-grow: 1;
    margin-bottom: 16px;
    position: relative;
    z-index: 1;
    line-height: 1.5;
}

.card-action {
    color: #1890ff;
    font-weight: 500;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
    margin-top: auto;
}

.card-action i {
    margin-left: 6px;
    transition: transform 0.3s ease;
}

.card:hover .card-action i {
    transform: translateX(4px);
}

/* 平台卡片样式增强 */
.platform-card {
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
}

.platform-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.platform-card:after {
    content: '';
    position: absolute;
    right: -40px;
    top: -40px;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: rgba(24, 144, 255, 0.03);
    z-index: 0;
    transition: all 0.3s ease;
}

.platform-card:hover:after {
    transform: scale(1.2);
    background: rgba(24, 144, 255, 0.06);
}

/* 来自ports.css的动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate__fadeInUp {
    animation: fadeInUp 0.6s ease forwards;
}

/* 自定义卡片颜色 */
.card:nth-child(1) .card-icon {
    background-color: rgba(24, 144, 255, 0.1);
}

.card:nth-child(1) .card-icon i {
    color: #1890ff;
}

.card:nth-child(2) .card-icon {
    background-color: rgba(82, 196, 26, 0.1);
}

.card:nth-child(2) .card-icon i {
    color: #52c41a;
}

.card:nth-child(3) .card-icon {
    background-color: rgba(250, 173, 20, 0.1);
}

.card:nth-child(3) .card-icon i {
    color: #faad14;
}

.card:nth-child(4) .card-icon {
    background-color: rgba(247, 89, 171, 0.1);
}

.card:nth-child(4) .card-icon i {
    color: #f759ab;
}

/* 初始加载动画 */
#cardContainer .card {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

/* 响应式调整 */
@media (max-width: 992px) {
    .card-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .card-container {
        grid-template-columns: 1fr;
    }
} 