/* 全局加载指示器样式 */
.global-loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    backdrop-filter: blur(3px);
}

.global-loading-indicator.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(24, 144, 255, 0.2);
    border-radius: 50%;
    border-top-color: #1890ff;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 15px;
    box-shadow: 0 0 15px rgba(24, 144, 255, 0.2);
}

.global-loading-indicator span {
    font-size: 18px;
    color: #333;
    animation: fadeInUp 0.5s ease forwards;
}

/* 顶部进度条 */
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background-color: #1890ff;
    background-image: linear-gradient(to right, #1890ff, #40a9ff);
    z-index: 9999;
    transition: width 0.2s ease;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

/* 表格骨架屏样式 */
.skeleton-row {
    animation: pulse 1.5s infinite;
}

.skeleton-text {
    height: 20px;
    background: #f0f0f0;
    border-radius: 4px;
    margin: 5px 0;
}

/* 按钮加载状态 */
.primary-button,
.secondary-button {
    position: relative;
    min-width: 80px;
}

.button-loading-icon {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s linear infinite;
}

.button-text {
    transition: opacity 0.3s ease;
}

.secondary-button .button-loading-icon {
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-top-color: #666;
}

/* 功能按钮loading状态 */
.btn-search.loading .button-loading-icon,
.btn-reset.loading .button-loading-icon,
.btn-refresh.loading .button-loading-icon {
    display: block;
}

.btn-search.loading .button-text,
.btn-reset.loading .button-text,
.btn-refresh.loading .button-text {
    opacity: 0;
}

.btn-search.loading,
.btn-reset.loading,
.btn-refresh.loading {
    pointer-events: none;
}

/* 操作按钮加载状态 */
.action-button {
    position: relative;
    min-width: 60px;
}

.action-button.loading {
    color: transparent;
    pointer-events: none;
}

.action-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s linear infinite;
}

/* 表格行淡入动画 */
.fade-in-row {
    animation: fadeIn 0.5s ease forwards;
    opacity: 0;
    transform: translateY(10px);
}

/* 刷新按钮样式 */
.refresh-button {
    background-color: transparent;
    border: 1px solid #ddd;
    color: #666;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 5px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 15px;
}

.refresh-button:hover {
    background-color: #f5f5f5;
    border-color: #1890ff;
    color: #1890ff;
}

.refresh-button .refresh-icon {
    margin-right: 5px;
    transition: transform 0.3s ease;
}

.refresh-button:hover .refresh-icon {
    transform: rotate(180deg);
}

.refresh-button.loading .refresh-icon {
    display: none;
}

/* 网站列表加载效果 */
.website-list-loading,
.content-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 0;
    color: #666;
}

.loading-spinner-small {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(24, 144, 255, 0.2);
    border-radius: 50%;
    border-top-color: #1890ff;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

/* 卡片加载效果 */
.skeleton-card {
    background-color: #f0f0f0;
    border-radius: 8px;
    height: 120px;
    animation: pulse 1.5s ease-in-out 0.5s infinite;
}

/* 表单项加载效果 */
.skeleton-input {
    height: 38px;
    background-color: #f0f0f0;
    border-radius: 4px;
    width: 100%;
    animation: pulse 1.5s ease-in-out 0.5s infinite;
}

.skeleton-label {
    height: 16px;
    background-color: #f0f0f0;
    border-radius: 4px;
    width: 80px;
    margin-bottom: 8px;
    animation: pulse 1.5s ease-in-out 0.5s infinite;
}

/* 模态框动画 */
.modal {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    opacity: 1;
}

.modal-content {
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.modal.show .modal-content {
    transform: translateY(0);
    opacity: 1;
}

/* 动画 */
@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0% {
        opacity: 0.6;
    }
    50% {
        opacity: 0.3;
    }
    100% {
        opacity: 0.6;
    }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 调整表格信息栏样式 */
.table-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.table-info > span {
    display: flex;
    align-items: center;
}

/* 按钮加载样式 */
.button-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.button-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-top: -8px;
    margin-left: -8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s infinite linear;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 全局加载样式 */
.global-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.global-loading-content {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
}

.global-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s infinite linear;
    margin: 0 auto 10px;
}

.global-loading-text {
    font-size: 14px;
    color: #fff;
}