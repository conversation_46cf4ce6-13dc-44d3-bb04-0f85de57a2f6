/* 表格容器样式 */
.table-container {
    margin: 20px 0;
    overflow-x: auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    max-width: 100%;
    transition: all 0.3s ease;
}

.table-container:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    table-layout: fixed;
}

th, td {
    padding: 15px 16px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
}

/* 移除所有单元格的省略号样式 */
th, td {
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    word-break: break-all;
    word-wrap: break-word;
}

th {
    background-color: #f7f9fc;
    font-weight: 600;
    color: #1a1a1a;
    font-size: 14px;
    transition: background-color 0.3s;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

th i {
    margin-right: 8px;
    color: #1890ff;
}

td {
    font-size: 14px;
    color: #3a3a3a;
    transition: all 0.3s;
}

tr {
    transition: transform 0.2s, box-shadow 0.2s;
}

tr:hover td {
    background-color: #f5f9ff;
}

tr:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    z-index: 1;
    position: relative;
}

/* 设置各列的固定宽度 */
th:nth-child(1), td:nth-child(1) { width: 120px; }  /* 时间 */
th:nth-child(2), td:nth-child(2) { width: 150px; }  /* 园区名称 */
th:nth-child(3), td:nth-child(3) { width: 150px; }  /* 网站 */
th:nth-child(4), td:nth-child(4) { width: 180px; }  /* 进程ID */
th:nth-child(5), td:nth-child(5) { width: 80px; }   /* 推送状态 */
th:nth-child(6), td:nth-child(6) { 
    width: auto;
    min-width: 200px;
    white-space: normal;
    word-break: break-all;
    word-wrap: break-word;
}
th:nth-child(7), td:nth-child(7) { width: 100px; text-align: center; }  /* 操作 */

/* 圆角处理 */
th:first-child { border-top-left-radius: 12px; }
th:last-child { border-top-right-radius: 12px; }
tr:last-child td:first-child { border-bottom-left-radius: 12px; }
tr:last-child td:last-child { border-bottom-right-radius: 12px; }

/* 状态样式 */
.status-success {
    color: #52c41a;
    font-weight: 500;
    background: #f6ffed;
    padding: 6px 10px;
    border-radius: 20px;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    line-height: 1;
    box-shadow: 0 2px 0 rgba(82, 196, 26, 0.1);
    border: 1px solid rgba(82, 196, 26, 0.2);
}

.status-success:before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #52c41a;
    border-radius: 50%;
    margin-right: 6px;
}

.status-error {
    color: #ff4d4f;
    font-weight: 500;
    background: #fff2f0;
    padding: 6px 10px;
    border-radius: 20px;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    line-height: 1;
    box-shadow: 0 2px 0 rgba(255, 77, 79, 0.1);
    border: 1px solid rgba(255, 77, 79, 0.2);
}

.status-error:before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #ff4d4f;
    border-radius: 50%;
    margin-right: 6px;
}

/* 查看详情按钮样式 */
.view-details-btn {
    padding: 8px 14px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.3s;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.25);
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.view-details-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.3s;
}

.view-details-btn:hover {
    background-color: #40a9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.35);
}

.view-details-btn:hover:before {
    left: 100%;
}

.view-details-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

/* 空状态和加载状态样式 */
.loading-text, .empty-text {
    text-align: center;
    padding: 40px;
    color: #8c8c8c;
    font-size: 15px;
    background: #fafafa;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.loading-text:before, .empty-text:before {
    content: '';
    display: block;
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
}

.loading-text:before {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="%23e0e0e0" stroke-width="8"/><path fill="none" stroke="%231890ff" stroke-width="8" stroke-linecap="round" d="M50 10 a 40 40 0 0 1 40 40"><animateTransform attributeName="transform" type="rotate" from="0 50 50" to="360 50 50" dur="1s" repeatCount="indefinite"/></path></svg>') no-repeat center;
}

.empty-text:before {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path fill="%23e0e0e0" d="M30 20 h40 l10 10 v50 h-50 z"/><path fill="%23f5f5f5" d="M30 20 v60 h50 v-50 h-10 v-10 z"/><path fill="%23e0e0e0" d="M70 20 v10 h10 z"/><path stroke="%23d0d0d0" stroke-width="2" stroke-dasharray="5,3" fill="none" d="M40 40 h30 M40 50 h30 M40 60 h30"/></svg>') no-repeat center;
}

/* 确保表格内容垂直居中 */
td {
    vertical-align: middle;
}

/* 基础表单样式 */
.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #262626;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
}

.form-group label i {
    margin-right: 6px;
    color: #1890ff;
}

/* 输入框和选择框通用样式 */
.form-group input,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    padding-left: 40px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    font-size: 14px;
    color: #262626;
    background-color: white;
    transition: all 0.3s;
    outline: none;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.016);
    height: 42px;
}

/* 输入框和选择框的图标 */
.input-with-icon,
.select-with-icon {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #bfbfbf;
    transition: all 0.3s;
    font-size: 16px;
}

.form-group input:focus + .input-icon,
.form-group input:hover + .input-icon,
.form-group select:focus ~ .input-icon,
.form-group select:hover ~ .input-icon {
    color: #1890ff;
}

/* 选择框特殊样式 */
.form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L5 5L9 1' stroke='%23595959' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 32px;
}

/* 悬停和焦点状态 */
.form-group input:hover,
.form-group select:hover {
    border-color: #40a9ff;
}

.form-group input:focus,
.form-group select:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
button {
    padding: 10px 18px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1.5;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
    position: relative;
    overflow: hidden;
}

button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.3s;
}

button:hover:before {
    left: 100%;
}

button .button-icon {
    margin-right: 8px;
}

.primary-button {
    background-color: #1890ff;
    color: white;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.25);
}

.primary-button:hover {
    background-color: #40a9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.35);
}

.primary-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.secondary-button {
    background-color: white;
    border: 1px solid #d9d9d9;
    color: #595959;
}

.secondary-button:hover {
    color: #40a9ff;
    border-color: #40a9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.secondary-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}

/* 筛选区域样式 */
.basic-filter,
.records-filter {
    background-color: white;
    border-radius: 12px;
    padding: 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: all 0.3s ease;
}

.basic-filter:hover,
.records-filter:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.filter-header {
    background-color: #f7f9fc;
    padding: 12px 20px;
    font-weight: 500;
    color: #1a1a1a;
    font-size: 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-header i {
    color: #1890ff;
    margin-right: 8px;
}

.filter-content {
    padding: 20px;
}

.toggle-filter-btn {
    background: transparent;
    border: none;
    color: #8c8c8c;
    cursor: pointer;
    font-size: 16px;
    padding: 4px;
    box-shadow: none;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.toggle-filter-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #1890ff;
}

.toggle-filter-btn.active i {
    transform: rotate(180deg);
}

.form-row {
    display: flex;
    gap: 24px;
    align-items: flex-end;
}

.form-actions {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 16px;
    }

    .form-actions {
        margin-top: 16px;
    }

    .form-group {
        margin-bottom: 0;
    }
}

/* 禁用状态 */
.form-group input:disabled,
.form-group select:disabled,
button:disabled {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
    box-shadow: none;
}

button:disabled {
    opacity: 0.7;
}

/* 消息提示样式 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 24px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    transform: translateY(0);
    opacity: 1;
    display: flex;
    align-items: center;
}

.message:before {
    margin-right: 10px;
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
}

.message.info {
    background-color: #1890ff;
}
.message.info:before {
    content: '\f05a';
}

.message.success {
    background-color: #52c41a;
}
.message.success:before {
    content: '\f058';
}

.message.warning {
    background-color: #faad14;
}
.message.warning:before {
    content: '\f071';
}

.message.error {
    background-color: #ff4d4f;
}
.message.error:before {
    content: '\f057';
}

.message.fade-out {
    opacity: 0;
    transform: translateY(-20px);
}

/* 全局加载指示器样式 */
.global-loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    backdrop-filter: blur(4px);
}

.global-loading-indicator.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(24, 144, 255, 0.1);
    border-radius: 50%;
    border-top-color: #1890ff;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
    box-shadow: 0 0 20px rgba(24, 144, 255, 0.2);
}

.global-loading-indicator span {
    font-size: 18px;
    color: #333;
    font-weight: 500;
}

/* 顶部进度条 */
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(to right, #108ee9, #1890ff, #40a9ff);
    z-index: 9999;
    transition: width 0.3s ease;
    box-shadow: 0 2px 10px rgba(24, 144, 255, 0.3);
}

/* 骨架屏样式 */
.skeleton-row {
    animation: pulse 1.5s ease-in-out 0.5s infinite;
}

.skeleton-cell {
    height: 24px;
    background-color: #f0f0f0;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
}

.skeleton-cell:after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: shimmer 2s infinite;
}

.skeleton-button {
    height: 36px;
    width: 80px;
    background-color: #f0f0f0;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
}

.skeleton-button:after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    100% {
        left: 100%;
    }
}

/* 按钮加载状态 */
.primary-button,
.secondary-button {
    position: relative;
    min-width: 90px;
}

.button-loading-icon {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s linear infinite;
}

.button-text {
    transition: opacity 0.3s ease;
}

.secondary-button .button-loading-icon {
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-top-color: #666;
}

button.loading .button-loading-icon {
    display: block;
}

button.loading .button-text {
    opacity: 0;
}

button.loading {
    pointer-events: none;
}

/* Toast 提示样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    padding: 16px 24px;
    margin-bottom: 16px;
    border-radius: 8px;
    color: #fff;
    opacity: 0;
    transform: translateX(30px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    max-width: 300px;
}

.toast:before {
    margin-right: 12px;
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.success {
    background-color: #52c41a;
}
.toast.success:before {
    content: '\f058';
}

.toast.error {
    background-color: #ff4d4f;
}
.toast.error:before {
    content: '\f057';
}

.toast.warning {
    background-color: #faad14;
}
.toast.warning:before {
    content: '\f071';
}

.toast.info {
    background-color: #1890ff;
}
.toast.info:before {
    content: '\f05a';
}

/* 动画 */
@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 0.8; }
    100% { opacity: 0.6; }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* 确保导航栏在页面加载时立即显示 */
nav ul li {
    opacity: 1 !important;
    transform: none !important;
}

/* 预加载字体图标 */
.fas, .far, .fab, .material-icons {
    display: inline-block;
}

/* 添加状态卡片样式 */
.status-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 24px;
    margin-bottom: 30px;
}

.status-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    padding: 24px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.status-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.status-card:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, transparent 50%, rgba(0, 120, 255, 0.05) 50%);
    border-radius: 0 0 0 80px;
}

.status-card-icon {
    width: 56px;
    height: 56px;
    background-color: rgba(24, 144, 255, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.1);
}

.status-card-icon i {
    font-size: 24px;
    color: #1890ff;
}

.status-card-icon.success-icon {
    background-color: rgba(82, 196, 26, 0.1);
}

.status-card-icon.success-icon i {
    color: #52c41a;
}

.status-card-icon.error-icon {
    background-color: rgba(255, 77, 79, 0.1);
}

.status-card-icon.error-icon i {
    color: #ff4d4f;
}

.status-card-icon.warning-icon {
    background-color: rgba(250, 173, 20, 0.1);
}

.status-card-icon.warning-icon i {
    color: #faad14;
}

.status-card-content {
    flex: 1;
}

.status-card-content h3 {
    font-size: 16px;
    margin: 0 0 6px;
    color: #595959;
    font-weight: 500;
}

.status-card-value {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
}

/* 分页样式美化 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    gap: 16px;
}

.page-info {
    font-size: 14px;
    color: #595959;
    background-color: #f7f9fc;
    padding: 8px 16px;
    border-radius: 8px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 内容区域样式 */
.content-section {
    background: white;
    border-radius: 12px;
    padding: 0;
    margin-bottom: 30px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.section-header {
    background-color: #f7f9fc;
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h2 {
    font-size: 18px;
    margin: 0;
    color: #1a1a1a;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.section-header h2 i {
    margin-right: 10px;
    color: #1890ff;
}

.section-header p {
    margin: 8px 0 0;
    color: #8c8c8c;
    font-size: 14px;
}

.section-header p i {
    margin-right: 6px;
    color: #1890ff;
}

/* 技术图标 */
.tech-icon {
    margin-right: 8px;
    color: #1890ff;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-6px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* 查询结果样式美化 */
#resultSection {
    transition: all 0.3s ease;
}

#resultContent {
    padding: 24px;
}

#resultContent table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 20px;
}

#resultContent table th {
    background-color: #f7f9fc;
    color: #1a1a1a;
    font-weight: 600;
    text-align: left;
    padding: 15px 16px;
}

#resultContent table td {
    padding: 15px 16px;
    border-bottom: 1px solid #f0f0f0;
}

#resultContent table tr:last-child td {
    border-bottom: none;
}

#resultContent .status-info {
    margin-top: 15px;
}

#resultContent .view-link {
    color: #1890ff;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s;
}

#resultContent .view-link:hover {
    color: #40a9ff;
    text-decoration: underline;
}

#resultContent .view-link:before {
    content: '\f35d';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: 5px;
    font-size: 14px;
}

/* 新增状态类 */
.status-active {
    color: #1890ff;
    font-weight: 500;
    background: #e6f7ff;
    padding: 6px 10px;
    border-radius: 20px;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    line-height: 1;
    box-shadow: 0 2px 0 rgba(24, 144, 255, 0.1);
    border: 1px solid rgba(24, 144, 255, 0.2);
}

.status-active:before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #1890ff;
    border-radius: 50%;
    margin-right: 6px;
}

.status-processing {
    color: #faad14;
    font-weight: 500;
    background: #fffbe6;
    padding: 6px 10px;
    border-radius: 20px;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    line-height: 1;
    box-shadow: 0 2px 0 rgba(250, 173, 20, 0.1);
    border: 1px solid rgba(250, 173, 20, 0.2);
}

.status-processing:before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #faad14;
    border-radius: 50%;
    margin-right: 6px;
    animation: blink 1s infinite;
}

@keyframes blink {
    0% { opacity: 0.4; }
    50% { opacity: 1; }
    100% { opacity: 0.4; }
} 