/* 基础表单样式 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #262626;
    font-size: 14px;
    font-weight: 500;
}

/* 输入框和选择框通用样式 */
.form-group input,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    color: #262626;
    background-color: white;
    transition: all 0.3s;
    outline: none;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.016);
}

/* 选择框特殊样式 */
.form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L5 5L9 1' stroke='%23595959' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 32px;
}

/* 悬停和焦点状态 */
.form-group input:hover,
.form-group select:hover {
    border-color: #40a9ff;
}

.form-group input:focus,
.form-group select:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
button {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1.5;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

button:not(.secondary-button):not(.page-button):not(.refresh-button) {
    background-color: #1890ff;
    color: white;
}

button:not(.secondary-button):not(.page-button):not(.refresh-button):hover {
    background-color: #40a9ff;
}

.secondary-button {
    background-color: white;
    border: 1px solid #d9d9d9;
    color: #595959;
}

.secondary-button:hover {
    color: #40a9ff;
    border-color: #40a9ff;
}

/* 搜索区域样式 */
.search-section {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.form-row {
    display: flex;
    gap: 24px;
    align-items: flex-end;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group:last-child {
    display: flex;
    gap: 12px;
    min-width: auto;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 16px;
    }
    
    .form-group {
        width: 100%;
    }
    
    .form-group:last-child {
        flex-direction: column;
        width: 100%;
    }
    
    .form-group:last-child button {
        width: 100%;
    }
}

/* 顶部进度条 */
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background-color: #1890ff;
    z-index: 9999;
    transition: width 0.2s ease;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

/* 表格行淡入动画 */
.fade-in-row {
    animation: fadeIn 0.5s ease forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 刷新按钮样式 */
.refresh-button {
    background-color: transparent;
    border: 1px solid #ddd;
    color: #666;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 5px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 15px;
}

.refresh-button:hover {
    background-color: #f5f5f5;
    border-color: #1890ff;
    color: #1890ff;
}

.refresh-button .refresh-icon {
    margin-right: 5px;
    transition: transform 0.3s ease;
}

.refresh-button:hover .refresh-icon {
    transform: rotate(180deg);
}

.refresh-button.loading .refresh-icon {
    display: none;
}

/* 调整表格信息栏样式 */
.table-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.table-info > div:first-child {
    display: flex;
    align-items: center;
}

/* 确保导航栏在页面加载时立即显示 */
nav ul li {
    opacity: 1 !important;
    transform: none !important;
}

/* 预加载字体图标 */
.fas {
    display: inline-block;
}

/* Toast 提示样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    padding: 15px 20px;
    margin-bottom: 10px;
    border-radius: 4px;
    color: #fff;
    opacity: 0;
    transition: opacity 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toast.show {
    opacity: 1;
}

.toast.success {
    background-color: #52c41a;
}

.toast.error {
    background-color: #ff4d4f;
}

.toast.warning {
    background-color: #faad14;
}

.toast.info {
    background-color: #1890ff;
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    opacity: 0;
    transition: opacity 0.3s ease;
    overflow-y: auto;
}

.modal.show {
    opacity: 1;
}

.modal-content {
    background-color: #fefefe;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 600px;
    border-radius: 8px;
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
    margin: 30px auto;
}

.modal.show .modal-content {
    transform: translateY(0);
    opacity: 1;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: black;
}

.website-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px 0;
}

.website-item {
    padding: 15px;
    margin: 8px 0;
    border: 1px solid #ddd;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    background-color: #fff;
    user-select: none;
    transform: translateY(10px);
    opacity: 0;
    animation: fadeInUp 0.3s forwards;
}

.website-item:hover {
    background-color: #f0f7ff;
    border-color: #1890ff;
}

.website-item.selected {
    background-color: #e6f7ff;
    border-color: #1890ff;
}

.website-item input[type="radio"] {
    margin-right: 12px;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.website-item label {
    flex: 1;
    cursor: pointer;
    font-size: 16px;
    margin: 0;
    padding: 0;
    color: #333;
}

.modal-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    text-align: right;
}

.modal-footer button {
    margin-left: 10px;
    padding: 8px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.modal-footer .secondary-button {
    background-color: #fff;
    border: 1px solid #ddd;
    color: #666;
}

.modal-footer .secondary-button:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
}

.modal-footer .primary-button {
    background-color: #1890ff;
    border: 1px solid #1890ff;
    color: #fff;
}

.modal-footer .primary-button:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
}

.modal-footer .primary-button:disabled {
    background-color: #ccc;
    border-color: #ccc;
    cursor: not-allowed;
}

/* 修改网站项的样式 */
.website-item input[type="radio"]:disabled + label {
    cursor: not-allowed;
    opacity: 0.7;
}

.website-item.disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 0.6;
}

.website-item.disabled:hover {
    background-color: #f5f5f5;
    border-color: #ddd;
}

.website-item.disabled label {
    cursor: not-allowed;
    color: #999;
}

.website-item input[type="radio"]:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.website-item input[type="radio"]:disabled + label {
    cursor: not-allowed;
    color: #999;
}

/* 消息提示样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    padding: 15px 20px;
    margin-bottom: 10px;
    border-radius: 4px;
    color: #fff;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.toast.show {
    opacity: 1;
}

.toast.success {
    background-color: #52c41a;
}

.toast.error {
    background-color: #ff4d4f;
}

.toast.warning {
    background-color: #faad14;
}

.toast.info {
    background-color: #1890ff;
}

.website-item.no-website {
    background-color: #f9f9f9;
    border: 1px dashed #ddd;
    padding: 20px;
    text-align: center;
    cursor: default;
}

.website-item.no-website:hover {
    background-color: #f9f9f9;
    border-color: #ddd;
}

.website-item.no-website p {
    margin: 0;
    font-size: 14px;
    color: #999;
}

/* 全局加载指示器样式 */
.global-loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.global-loading-indicator.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(24, 144, 255, 0.2);
    border-radius: 50%;
    border-top-color: #1890ff;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 15px;
}

.global-loading-indicator span {
    font-size: 18px;
    color: #333;
}

/* 表格骨架屏样式 */
.skeleton-row {
    animation: pulse 1.5s ease-in-out 0.5s infinite;
}

.skeleton-cell {
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.skeleton-button {
    height: 32px;
    width: 60px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

/* 按钮加载状态 */
.primary-button,
.secondary-button {
    position: relative;
    min-width: 80px;
}

.button-loading-icon {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s linear infinite;
}

.button-text {
    transition: opacity 0.3s ease;
}

.secondary-button .button-loading-icon {
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-top-color: #666;
}

button.loading .button-loading-icon {
    display: block;
}

button.loading .button-text {
    opacity: 0;
}

button.loading {
    pointer-events: none;
}

/* 推送按钮加载状态 */
.push-button {
    position: relative;
    min-width: 60px;
}

.push-button.loading {
    color: transparent;
    pointer-events: none;
}

.push-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s linear infinite;
}

/* 动画 */
@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 0.8; }
    100% { opacity: 0.6; }
}

/* 网站列表加载效果 */
.website-list-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 0;
    color: #666;
}

.loading-spinner-small {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(24, 144, 255, 0.2);
    border-radius: 50%;
    border-top-color: #1890ff;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes fadeInUp {
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* 添加相关的CSS样式 */
.website-group {
    margin-bottom: 20px;
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;
    animation: fadeIn 0.3s ease-in-out forwards;
    opacity: 0;
    background: white;
}

.website-title {
    background: #f8f9fa;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
}

.website-title h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
    display: flex;
    align-items: center;
}

.website-title h3::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background: #1890ff;
    margin-right: 8px;
    border-radius: 2px;
}

.account-list {
    padding: 12px;
}

.account-item {
    margin: 8px 0;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    background: white;
}

.account-item:not(.disabled):hover {
    border-color: #40a9ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
    transform: translateY(-1px);
}

.account-item.selected {
    background: #f0f7ff;
    border-color: #1890ff;
}

.account-item.selected .account-realname {
    background-color: rgba(24, 144, 255, 0.1);
}

.account-item.disabled {
    opacity: 0.7;
    cursor: not-allowed;
    background: #fafafa;
}

.account-item input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.account-item label {
    display: block;
    padding: 16px;
    cursor: pointer;
    margin: 0;
}

.account-item.disabled label {
    cursor: not-allowed;
}

.account-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.account-user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.account-username {
    font-size: 15px;
    font-weight: 500;
    color: #262626;
    position: relative;
    padding-right: 12px;
}

.account-username::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 14px;
    background-color: #e8e8e8;
}

.account-realname {
    font-size: 14px;
    color: #666;
    background-color: #f5f5f5;
    padding: 2px 8px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
}

.account-realname::before {
    content: '👤';
    margin-right: 4px;
    font-size: 12px;
}

.account-status {
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 4px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.status-active {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.status-active::before {
    content: '●';
    font-size: 10px;
    color: #52c41a;
}

.status-inactive {
    background: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffa39e;
}

.status-inactive::before {
    content: '●';
    font-size: 10px;
    color: #ff4d4f;
}

.account-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    padding-top: 8px;
    margin-top: 8px;
    border-top: 1px dashed #f0f0f0;
}

.action-tag {
    font-size: 12px;
    padding: 2px 8px;
    background: #f5f5f5;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    color: #595959;
    transition: all 0.2s ease;
    cursor: pointer;
}

.action-tag.disabled {
    background-color: #f5f5f5;
    border-color: #ddd;
    color: #999;
    cursor: not-allowed;
    opacity: 0.6;
}

.action-tag:not(.disabled):hover {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

.account-item:not(.disabled):hover .action-tag {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

.account-item.selected .action-tag {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

/* 优化模态框样式 */
.modal-content {
    max-width: 700px;
    margin: 5% auto;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
}

.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 添加新的样式 */
.account-item input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.account-item label::before {
    content: '';
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    border: 2px solid #d9d9d9;
    border-radius: 4px;
    background-color: white;
    transition: all 0.2s ease;
}

.account-item.selected label::before {
    background-color: #1890ff;
    border-color: #1890ff;
}

.account-item.selected label::after {
    content: '';
    position: absolute;
    left: 22px;
    top: 50%;
    transform: translateY(-70%) rotate(45deg);
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 2px 2px 0;
}

.account-item label {
    padding-left: 48px;
}

.account-item.disabled label::before {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    cursor: not-allowed;
}

/* 添加强制警告弹窗样式 */
.warning-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.7);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.warning-modal.show {
    opacity: 1;
}

.warning-content {
    background-color: #fff1f0;
    border: 2px solid #ff4d4f;
    margin: 15% auto;
    padding: 30px;
    width: 80%;
    max-width: 500px;
    border-radius: 8px;
    text-align: center;
    position: relative;
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.warning-modal.show .warning-content {
    transform: translateY(0);
    opacity: 1;
}

.warning-icon {
    color: #ff4d4f;
    font-size: 48px;
    margin-bottom: 20px;
}

.warning-title {
    color: #ff4d4f;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
}

.warning-message {
    color: #cf1322;
    font-size: 16px;
    margin-bottom: 25px;
    line-height: 1.6;
}

.warning-list {
    text-align: left;
    margin: 0 auto;
    max-width: 90%;
    margin-bottom: 25px;
}

.warning-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background: rgba(255, 77, 79, 0.1);
    padding: 12px 15px;
    border-radius: 4px;
    border-left: 4px solid #ff4d4f;
}

.warning-item:last-child {
    margin-bottom: 0;
}

.warning-item::before {
    content: "⚠️";
    margin-right: 10px;
    font-size: 20px;
}

.warning-confirm-btn {
    background-color: #ff4d4f;
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.warning-confirm-btn:hover {
    background-color: #ff7875;
}

/* 帖子列表模态框样式 */
.post-modal-content {
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    margin: 20px auto;
    overflow: hidden;
}

.modal-body {
    flex: 1;
    overflow-y: auto;
    max-height: calc(80vh - 130px);
    padding: 15px;
}

.modal-footer {
    position: sticky;
    bottom: 0;
    background-color: white;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    margin-top: 0;
    padding: 15px;
    border-top: 1px solid #eee;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.posts-count-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 15px;
    background-color: #f9f9f9;
    border-radius: 6px;
    position: sticky;
    top: 0;
    z-index: 5;
}

.count-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    background-color: #e6f7ff;
    border-radius: 4px;
    font-weight: 500;
    color: #1890ff;
    border: 1px solid #91d5ff;
}

.count-badge.full {
    background-color: #fff2f0;
    color: #ff4d4f;
    border-color: #ffccc7;
}

.limit-warning {
    display: flex;
    align-items: center;
    color: #ff4d4f;
    font-size: 14px;
}

.warning-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background-color: #ff4d4f;
    color: white;
    border-radius: 50%;
    margin-right: 8px;
    font-style: normal;
    font-weight: bold;
}

.posts-loading, .posts-error {
    padding: 30px 0;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;
}

.error-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #ff4d4f;
    color: white;
    border-radius: 50%;
    margin-bottom: 15px;
    font-style: normal;
    font-weight: bold;
    font-size: 24px;
}

.posts-list {
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.no-posts {
    text-align: center;
    padding: 30px 20px;
    font-size: 15px;
    color: #8c8c8c;
    background-color: #fafafa;
    border-radius: 8px;
    border: 1px dashed #d9d9d9;
    margin: 20px 0;
}

/* 自定义滚动条样式 */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #aaa;
}

/* 优化后的帖子项样式 */
.post-item {
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 15px;
    background-color: white;
    transition: all 0.3s ease;
    animation: fadeInUp 0.3s forwards;
    opacity: 0;
    transform: translateY(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin: 0;
}

.post-item:hover {
    border-color: #d0d0d0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.post-item.removed {
    background-color: #f5f5f5;
    opacity: 0.7;
    border-style: dashed;
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.post-title {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
    flex: 1;
    margin-right: 15px;
    line-height: 1.4;
}

.post-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.remove-post-btn {
    padding: 4px 10px;
    background-color: #ff4d4f;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.3s ease;
}

.remove-post-btn:hover {
    background-color: #ff7875;
}

.remove-post-btn:disabled {
    background-color: #d9d9d9;
    cursor: not-allowed;
    opacity: 0.8;
}

.remove-post-btn.loading::after {
    content: "";
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
    margin-left: 6px;
    vertical-align: middle;
}

.view-post-btn {
    padding: 4px 10px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.view-post-btn:hover {
    background-color: #40a9ff;
}

.view-post-btn i {
    margin-right: 4px;
    font-size: 12px;
}

.post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 10px;
    font-size: 13px;
    color: #8c8c8c;
}

.post-id, .post-sid, .post-time {
    display: inline-flex;
    align-items: center;
    background-color: #f5f5f5;
    padding: 3px 8px;
    border-radius: 4px;
}

.post-info {
    margin-bottom: 12px;
}

.post-content {
    background-color: #fafafa;
    padding: 10px 12px;
    border-radius: 4px;
    font-size: 14px;
    color: #595959;
    margin-bottom: 10px;
    border-left: 3px solid #1890ff;
}

.post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
}

.post-tag {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
    border-radius: 4px;
    padding: 2px 8px;
    font-size: 12px;
}

.post-stats {
    display: flex;
    gap: 15px;
    padding-top: 10px;
    border-top: 1px dashed #f0f0f0;
}

.stat-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #8c8c8c;
}

.stat-item i {
    margin-right: 5px;
    color: #1890ff;
}

.stat-value {
    color: #262626;
    font-weight: 500;
    margin-left: 3px;
}

.footer-info {
    flex: 1;
    text-align: left;
    display: flex;
    align-items: center;
}

.footer-hint {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    color: #8c8c8c;
}

.footer-hint i {
    margin-right: 6px;
    color: #1890ff;
}

.footer-hint.warning i {
    color: #ff4d4f;
}

.footer-hint.warning {
    color: #ff4d4f;
}

.footer-buttons {
    display: flex;
    gap: 10px;
}

.warning-btn {
    background-color: #fff2f0 !important;
    color: #ff4d4f !important;
    border: 1px solid #ff4d4f !important;
    cursor: not-allowed !important;
    opacity: 0.9 !important;
}

.warning-btn:hover {
    background-color: #fff2f0 !important;
    color: #ff4d4f !important;
    border: 1px solid #ff4d4f !important;
}

.close {
    transition: all 0.3s ease;
}

/* 移动设备适配 */
@media (max-width: 768px) {
    .post-modal-content {
        width: 95%;
        margin: 10px auto;
        max-height: 95vh;
    }
    
    .modal-body {
        max-height: calc(95vh - 140px);
        padding: 10px;
    }
    
    .modal-header {
        padding: 10px;
    }
    
    .modal-footer {
        padding: 10px;
        flex-direction: column;
        gap: 10px;
    }
    
    .footer-info {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .footer-buttons {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }
    
    .footer-buttons button {
        flex: 1;
        margin: 0 5px;
    }
} 