/* 基础表单样式 */
.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #262626;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
}

.form-group label i {
    margin-right: 6px;
    color: #1890ff;
}

/* 输入框和选择框通用样式 */
.form-group input,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    padding-left: 40px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    font-size: 14px;
    color: #262626;
    background-color: white;
    transition: all 0.3s;
    outline: none;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.016);
    height: 42px;
}

/* 输入框和选择框的图标 */
.input-with-icon,
.select-with-icon {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #bfbfbf;
    transition: all 0.3s;
    font-size: 16px;
}

.form-group input:focus + .input-icon,
.form-group input:hover + .input-icon,
.form-group select:focus ~ .input-icon,
.form-group select:hover ~ .input-icon {
    color: #1890ff;
}

/* 选择框特殊样式 */
.form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L5 5L9 1' stroke='%23595959' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 32px;
}

/* 悬停和焦点状态 */
.form-group input:hover,
.form-group select:hover {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.06);
}

.form-group input:focus,
.form-group select:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
button {
    padding: 10px 18px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1.5;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
    position: relative;
    overflow: hidden;
}

button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.3s;
}

button:hover:before {
    left: 100%;
}

button .button-icon {
    margin-right: 8px;
}

.primary-button {
    background-color: #1890ff;
    color: white;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.25);
}

.primary-button:hover {
    background-color: #40a9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.35);
}

.primary-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.secondary-button {
    background-color: white;
    border: 1px solid #d9d9d9;
    color: #595959;
}

.secondary-button:hover {
    color: #40a9ff;
    border-color: #40a9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.secondary-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}

/* 筛选区域样式 */
.port-form {
    padding: 24px;
    background-color: white;
    border-radius: 12px;
    margin-bottom: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
}

.port-form:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #1890ff, #73d13d);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.port-form:hover {
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

.port-form:hover:before {
    opacity: 1;
}

.form-row {
    display: flex;
    gap: 24px;
    align-items: flex-end;
}

.form-row .form-group {
    flex: 1;
}

.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

/* 端口列表样式增强 */
.port-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.port-card {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 12px;
    padding: 20px;
    width: calc(33.33% - 20px);
    box-sizing: border-box;
    transition: all 0.35s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.port-card:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, transparent 50%, rgba(0, 120, 255, 0.05) 50%);
    border-radius: 0 0 0 60px;
    transition: all 0.3s ease;
}

.port-card:hover {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
    border-color: rgba(24, 144, 255, 0.5);
    transform: translateY(-6px) scale(1.02);
}

.port-card:hover:after {
    background: linear-gradient(135deg, transparent 50%, rgba(0, 120, 255, 0.1) 50%);
}

.port-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 12px;
    position: relative;
}

.port-header:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #1890ff, #73d13d);
    transition: width 0.3s ease;
}

.port-card:hover .port-header:after {
    width: 100%;
}

.port-header h3 {
    margin: 0;
    color: #1890ff;
    font-size: 18px;
    display: flex;
    align-items: center;
}

.port-header h3 i {
    margin-right: 8px;
    font-size: 16px;
}

.port-id {
    color: #8c8c8c;
    font-size: 13px;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
}

.port-id i {
    margin-right: 4px;
    font-size: 12px;
}

.port-info {
    margin: 10px 0;
    font-size: 14px;
    color: #595959;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.port-info p {
    margin: 0;
    display: flex;
    align-items: center;
}

.port-info i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    color: #8c8c8c;
}

.port-actions {
    display: flex;
    margin-top: 15px;
    gap: 10px;
    flex-wrap: wrap;
}

.port-btn {
    padding: 6px 12px;
    font-size: 13px;
    background: #f5f5f5;
    border: 1px solid #e8e8e8;
    color: #595959;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    cursor: pointer;
    flex: 1;
    min-width: 80px;
}

.port-btn i {
    margin-right: 4px;
    font-size: 12px;
}

.port-btn:hover {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

.port-btn.edit {
    color: #1890ff;
    background: #e6f7ff;
    border-color: #91d5ff;
}

.port-btn.delete {
    color: #ff4d4f;
    background: #fff2f0;
    border-color: #ffccc7;
}

.port-btn.edit:hover {
    background: #bae7ff;
    border-color: #69c0ff;
}

.port-btn.delete:hover {
    background: #ffd6d6;
    border-color: #ffa39e;
}

.status-active {
    display: inline-flex;
    align-items: center;
    color: #52c41a;
    font-weight: 500;
    line-height: 1.2;
}

.status-active:before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #52c41a;
    border-radius: 50%;
    margin-right: 4px;
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}

.status-inactive, .status-error {
    display: inline-flex;
    align-items: center;
    color: #ff4d4f;
    font-weight: 500;
    line-height: 1.2;
}

.status-inactive:before, .status-error:before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #ff4d4f;
    border-radius: 50%;
    margin-right: 4px;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 端口卡片骨架屏 */
.port-card.skeleton {
    background: white;
    padding: 20px;
    animation: pulse 1.5s infinite;
}

.skeleton-header {
    height: 24px;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 20px;
}

.skeleton-line {
    height: 14px;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 12px;
}

.skeleton-line:nth-child(3) {
    width: 70%;
}

.skeleton-actions {
    height: 34px;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin-top: 15px;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 50px 20px;
    color: #8c8c8c;
    background: rgba(0, 0, 0, 0.01);
    border-radius: 12px;
    border: 1px dashed #d9d9d9;
    margin: 20px 0;
}

.empty-icon {
    font-size: 60px;
    color: #d9d9d9;
    margin-bottom: 15px;
    animation: float 4s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.empty-state p {
    color: #8c8c8c;
    font-size: 16px;
    max-width: 80%;
    line-height: 1.6;
}

/* 端口配置统计卡片 */
.ports-dashboard {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 24px;
}

.ports-card {
    background-color: white;
    border-radius: 16px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
}

.ports-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.ports-card:after {
    content: '';
    position: absolute;
    right: -40px;
    top: -40px;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: rgba(24, 144, 255, 0.03);
    z-index: 0;
    transition: all 0.3s ease;
}

.ports-card:hover:after {
    transform: scale(1.2);
    background: rgba(24, 144, 255, 0.06);
}

.ports-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 14px;
    background-color: rgba(24, 144, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.ports-card:hover .ports-card-icon {
    transform: rotate(5deg) scale(1.05);
}

.ports-card-icon i {
    font-size: 24px;
    color: #1890ff;
}

.ports-card-icon.active-icon {
    background-color: rgba(82, 196, 26, 0.1);
}

.ports-card-icon.active-icon i {
    color: #52c41a;
}

.ports-card-icon.warning-icon {
    background-color: rgba(250, 173, 20, 0.1);
}

.ports-card-icon.warning-icon i {
    color: #faad14;
}

.ports-card-icon.account-icon {
    background-color: rgba(247, 89, 171, 0.1);
}

.ports-card-icon.account-icon i {
    color: #f759ab;
}

.ports-card-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

.ports-card-content h3 {
    font-size: 16px;
    color: #595959;
    margin: 0 0 6px 0;
    font-weight: 500;
}

.ports-card-value {
    font-size: 28px;
    font-weight: 600;
    color: #262626;
    margin: 0;
    line-height: 1.2;
    transition: all 0.3s ease;
    background: linear-gradient(45deg, #1890ff, #73d13d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.ports-card:hover .ports-card-value {
    transform: scale(1.05);
}

/* 内容区域样式 */
.content-section {
    background: white;
    border-radius: 12px;
    padding: 0;
    margin-bottom: 30px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.section-header {
    background-color: #f7f9fc;
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h2 {
    font-size: 18px;
    margin: 0;
    color: #1a1a1a;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.section-header h2 i {
    margin-right: 10px;
    color: #1890ff;
}

.section-header p {
    margin: 8px 0 0;
    color: #8c8c8c;
    font-size: 14px;
}

.section-header p i {
    margin-right: 6px;
    color: #1890ff;
}

.view-options {
    display: flex;
    gap: 8px;
}

.view-btn {
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8c8c8c;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn:hover {
    border-color: #40a9ff;
    color: #1890ff;
    transform: translateY(-2px);
}

.view-btn.active {
    background: #1890ff;
    color: white;
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

/* 表格视图样式 */
.ports-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-top: 20px;
}

.ports-table th,
.ports-table td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.ports-table th {
    background-color: #fafafa;
    color: #595959;
    font-weight: 500;
    font-size: 14px;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.ports-table th i {
    margin-right: 6px;
    color: #1890ff;
}

.ports-table tr:last-child td {
    border-bottom: none;
}

.ports-table tr:hover td {
    background-color: #f0f7ff;
}

/* 表格中的姓名编辑容器 */
.name-edit-container-table {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
}

/* 表格中的按钮样式 */
.table-btn {
    padding: 5px 10px;
    font-size: 12px;
    height: auto;
    min-width: auto;
}

/* 高亮输入框效果 */
.highlight-input {
    animation: highlight-pulse 1s ease-in-out;
}

@keyframes highlight-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(24, 144, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
    }
}

/* 表格行动画效果 */
.port-row {
    transition: all 0.3s ease;
}

.port-row:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
}

/* 动画辅助类 */
.animate__fadeInUp {
    animation: fadeInUp 0.6s ease forwards;
}

.animate__fadeIn {
    animation: fadeIn 0.4s ease forwards;
}

.animate__fadeOut {
    animation: fadeOut 0.3s ease forwards;
}

.animate__shakeX {
    animation: shakeX 0.8s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes shakeX {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* 增强空状态样式 */
.empty-state p {
    font-size: 16px;
    color: #8c8c8c;
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.5;
}

.empty-state .empty-icon i {
    animation: pulse 2s infinite;
}

/* 更多过渡效果 */
.content-section {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.content-section:hover {
    transform: translateY(-2px);
}

/* 初始化统计卡片状态，用于动画 */
.ports-dashboard .ports-card {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

/* 提示消息容器样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

.toast {
    background: white;
    color: #333;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.3s ease;
    position: relative;
    padding-left: 40px;
    min-width: 250px;
    font-size: 14px;
    border-left: 4px solid #1890ff;
}

.toast:before {
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.success {
    border-left-color: #52c41a;
}

.toast.success:before {
    content: "\f058";
    color: #52c41a;
}

.toast.error {
    border-left-color: #f5222d;
}

.toast.error:before {
    content: "\f057";
    color: #f5222d;
}

.toast.warning {
    border-left-color: #faad14;
}

.toast.warning:before {
    content: "\f071";
    color: #faad14;
}

.toast.info {
    border-left-color: #1890ff;
}

.toast.info:before {
    content: "\f129";
    color: #1890ff;
}

/* 响应式调整 */
@media (max-width: 992px) {
    .ports-dashboard {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .port-card {
        width: calc(50% - 20px);
    }
    
    .ports-table {
        display: block;
        overflow-x: auto;
    }
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 12px;
    }
    
    .port-card {
        width: 100%;
    }
    
    .ports-dashboard {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .form-actions {
        flex-wrap: wrap;
    }
    
    .form-actions button {
        flex: 1 0 auto;
        min-width: 120px;
    }
}

/* 技术图标 */
.tech-icon {
    margin-right: 8px;
    color: #1890ff;
    animation: float 3s ease-in-out infinite;
}

/* 全局加载指示器样式 */
.global-loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    backdrop-filter: blur(4px);
}

.global-loading-indicator.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(24, 144, 255, 0.1);
    border-radius: 50%;
    border-top-color: #1890ff;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
    box-shadow: 0 0 20px rgba(24, 144, 255, 0.2);
}

.global-loading-indicator span {
    font-size: 18px;
    color: #333;
    font-weight: 500;
}

/* 顶部进度条 */
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(to right, #108ee9, #1890ff, #40a9ff);
    z-index: 9999;
    transition: width 0.3s ease;
    box-shadow: 0 2px 10px rgba(24, 144, 255, 0.3);
}

/* 按钮加载状态 */
.primary-button,
.secondary-button {
    position: relative;
    min-width: 90px;
}

.button-loading-icon {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s linear infinite;
}

.button-text {
    transition: opacity 0.3s ease;
}

.secondary-button .button-loading-icon {
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-top-color: #666;
}

button.loading .button-loading-icon {
    display: block;
}

button.loading .button-text {
    opacity: 0;
}

button.loading {
    pointer-events: none;
}

/* 确保导航栏在页面加载时立即显示 */
nav ul li {
    opacity: 1 !important;
    transform: none !important;
}

/* 预加载字体图标 */
.fas, .far, .fab, .material-icons {
    display: inline-block;
}

/* 新增动态按钮效果 */
.view-stats-btn {
    background: linear-gradient(90deg, #1890ff, #40a9ff);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 14px;
    margin-top: 10px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
}

.view-stats-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    background: linear-gradient(90deg, #40a9ff, #69c0ff);
}

.view-stats-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: all 0.6s ease;
}

.view-stats-btn:hover:before {
    left: 100%;
}

/* 名称编辑容器增强 */
.name-edit-container {
    display: flex;
    align-items: center;
    margin: 10px 0;
    background: #f9f9f9;
    border-radius: 6px;
    padding: 5px 8px;
    transition: all 0.3s ease;
    width: 100%;
}

.name-edit-container:hover {
    background: #f0f7ff;
}

.name-label {
    font-size: 14px;
    color: #595959;
    margin-right: 8px;
    white-space: nowrap;
}

.name-edit-input {
    flex: 1;
    border: 1px solid #e8e8e8;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 13px;
    transition: all 0.3s ease;
    min-width: 60px;
}

.name-edit-input:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.name-edit-btn {
    background: #f0f0f0;
    border: none;
    padding: 5px 10px;
    margin-left: 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #595959;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: auto;
    flex-shrink: 0;
}

.name-edit-btn:hover {
    background: #1890ff;
    color: white;
}

/* 视图切换按钮增强 */
.view-options {
    display: flex;
    gap: 8px;
}

.view-btn {
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8c8c8c;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn:hover {
    border-color: #40a9ff;
    color: #1890ff;
    transform: translateY(-2px);
}

.view-btn.active {
    background: #1890ff;
    color: white;
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

/* 更多动画效果 */
@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 0.8; }
    100% { opacity: 0.6; }
}

/* 点击量模态框样式 */

/* 基本模态框样式 */
.stats-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    padding: 15px;
}

.stats-modal.show {
    opacity: 1;
    visibility: visible;
}

.stats-modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 95%;
    max-width: 1300px;
    max-height: 90vh;
    overflow: hidden;
    transform: translateY(20px);
    transition: transform 0.3s ease;
    animation: modalFadeIn 0.3s forwards;
}

.stats-modal.show .stats-modal-content {
    transform: translateY(0);
}

.stats-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
}

.stats-modal-header h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.stats-modal-close {
    background: none;
    border: none;
    font-size: 22px;
    cursor: pointer;
    color: #999;
    transition: color 0.2s;
}

.stats-modal-close:hover {
    color: #555;
}

.stats-modal-body {
    padding: 12px;
    overflow-y: auto;
    max-height: calc(90vh - 120px);
}

/* 帖子管理模态框的特殊设置 */
#statsModal.stats-modal .stats-modal-body {
    padding: 8px;
}

/* 添加模态框表格的字体大小调整 */
.stats-table-compact {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.stats-table-compact th {
    background-color: #f5f5f5;
    padding: 8px 10px;
    text-align: left;
    font-size: 12px;
    font-weight: 500;
    color: #555;
    border-bottom: 1px solid #eee;
}

.stats-table-compact td {
    padding: 6px 10px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
}

.stats-table-compact .post-title-cell {
    max-width: 280px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
}

/* 调整计数徽章样式 */
.stats-count {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 13px;
}

.count-badge {
    background-color: #f0f7ff;
    color: #1890ff;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    margin-left: 5px;
}

/* 套餐信息样式 */
.package-info {
    margin-bottom: 12px;
    padding: 8px 12px;
    background-color: #f9f9f9;
    border-radius: 6px;
    border-left: 3px solid #52c41a;
}

.package-type {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #555;
}

.package-type i {
    margin-right: 6px;
    color: #52c41a;
}

.package-badge {
    background-color: #f6ffed;
    color: #52c41a;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 500;
    margin-left: 8px;
}

/* 调整按钮字体大小 */
.view-post-btn, .remove-post-btn, .activate-post-btn {
    padding: 3px 8px;
    font-size: 11px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    margin-right: 4px;
}

.view-post-btn {
    background-color: #e6f7ff;
    color: #1890ff;
}

.remove-post-btn {
    background-color: #fff2f0;
    color: #ff4d4f;
}

.activate-post-btn {
    background-color: #f6ffed;
    color: #52c41a;
}

.view-post-btn:hover {
    background-color: #bae7ff;
}

.remove-post-btn:hover {
    background-color: #ffccc7;
}

.activate-post-btn:hover {
    background-color: #b7eb8f;
}

.view-post-btn i, .remove-post-btn i, .activate-post-btn i {
    font-size: 10px;
    margin-right: 2px;
}

/* 调整点击量显示样式 */
.clicks-count {
    font-size: 12px;
    font-weight: 500;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .stats-modal-content {
        width: 95%;
        height: 90vh;
    }
    
    .stats-modal-footer {
        flex-direction: column;
        gap: 15px;
    }
    
    .modal-actions-left, .modal-actions-right {
        width: 100%;
        justify-content: center;
    }
    
    .stats-modal-footer .primary-button,
    .stats-modal-footer .secondary-button {
        min-width: 0;
        flex: 1;
    }
}

/* 改进响应式布局 */
@media (max-width: 768px) {
    .post-detail-modal .stats-modal-content {
        width: 95%;
        height: 90vh;
    }
    
    .post-detail-modal .stats-modal-footer {
        flex-direction: column;
        gap: 15px;
    }
    
    .modal-actions-left, .modal-actions-right {
        width: 100%;
        justify-content: center;
    }
    
    .post-detail-modal .stats-modal-footer .primary-button,
    .post-detail-modal .stats-modal-footer .secondary-button {
        min-width: 0;
        flex: 1;
    }
}

/* 租售类型标签样式 */
.type-label {
    display: inline-block;
    font-size: 12px;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 10px;
    background-color: #f5f5f5;
    color: #595959;
}

/* 不同租售类型的样式 */
.type-sale {
    background-color: #e6f7ff;
    color: #1890ff;
}

.type-rent {
    background-color: #f6ffed;
    color: #52c41a;
}

.type-both {
    background-color: #fff7e6;
    color: #fa8c16;
}

.type-unknown {
    background-color: #f5f5f5;
    color: #8c8c8c;
}

tr:hover .type-label {
    opacity: 0.9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-table-compact .type-label {
    font-size: 11px;
    padding: 1px 6px;
}

/* 帖子激活高亮样式 */
.highlight-row {
    animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
    0%, 100% {
        background-color: transparent;
    }
    50% {
        background-color: rgba(82, 196, 26, 0.15);
    }
}

/* 优化激活按钮样式 */
.activate-post-btn {
    background-color: #f6ffed;
    color: #52c41a;
}

.activate-post-btn:hover {
    background-color: #b7eb8f;
}

.activate-post-btn i {
    color: #52c41a;
}

/* 时间列样式 */
.time-column {
    font-size: 11px;
    white-space: nowrap;
    min-width: 80px;
}

/* 时间显示样式 */
.time-today {
    color: #52c41a;
    font-weight: 500;
    background-color: #f6ffed;
    padding: 1px 4px;
    border-radius: 3px;
}

.time-yesterday {
    color: #fa8c16;
    font-weight: 500;
    background-color: #fff7e6;
    padding: 1px 4px;
    border-radius: 3px;
}

.time-other {
    color: #595959;
    background-color: #f5f5f5;
    padding: 1px 4px;
    border-radius: 3px;
}

.time-unknown {
    color: #bfbfbf;
    font-style: italic;
}

.time-error {
    color: #ff4d4f;
    font-size: 10px;
}