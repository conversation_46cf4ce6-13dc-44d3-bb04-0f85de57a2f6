/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
    background-image: linear-gradient(to bottom, #f5f7fa, #eef2f7);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

/* 简单旋转的logo样式 */
.rotating-logo {
    height: 38px; /* 调整大小使其更协调 */
    width: 38px;
    margin-right: 10px; /* 减小右边距 */
    margin-top: -2px; /* 微调上下对齐 */
    vertical-align: middle;
    /* 使用圆形裁剪 */
    border-radius: 50%;
    overflow: hidden;
    /* 阴影效果 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    /* 简单旋转动画 */
    animation: simple-rotate 10s infinite linear;
    transition: all 0.3s ease;
    /* 完全移除边框和内边距 */
    border: none;
    padding: 0;
    /* 使用背景图像并显著放大以裁剪白边 */
    background-size: 130% 130%;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url('../images/logo.jpg');
    /* 确保元素完全不透明 */
    display: inline-block;
    filter: brightness(1.05) contrast(1.1) saturate(1.1);
}

/* 悬停效果 */
.rotating-logo:hover {
    animation-duration: 3s; /* 悬停时加速旋转 */
    transform: scale(1.08) translate3d(0, 0, 0); /* 稍微放大，但不要太大 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    background-size: 135% 135%; /* 悬停时显著放大背景图像以裁剪更多白边 */
    filter: brightness(1.08) contrast(1.12) saturate(1.12); /* 悬停时适度增强亮度和对比度 */
}

/* 简单旋转动画 */
@keyframes simple-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

header:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.header-content {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    text-align: center;
    /* 添加3D变换上下文 */
    perspective: 1000px;
    transform-style: preserve-3d;
    position: relative;
    overflow: visible;
}

header h1 {
    color: #1890ff;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    font-size: 1.5rem; /* 稍微减小字体大小 */
    text-align: center;
    justify-content: center;
    /* 移除不必要的3D变换上下文 */
    position: relative;
    letter-spacing: 0.5px; /* 增加字间距，提高可读性 */
    font-weight: 500; /* 稍微加粗 */
}

header h1 i {
    margin-right: 10px;
    color: #1890ff;
}

.header-subtitle {
    color: #666;
    font-size: 0.9rem;
    text-align: center;
}

nav ul {
    display: flex;
    list-style: none;
    border-bottom: 1px solid #eee;
    padding-bottom: 12px;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: center;
}

nav ul li {
    margin-right: 5px;
}

nav ul li a {
    text-decoration: none;
    color: #666;
    font-size: 13px;
    padding: 6px 10px;
    border-radius: 5px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    white-space: nowrap;
    margin: 0 1px;
}

nav ul li a i {
    margin-right: 4px;
    font-size: 13px;
}

nav ul li a:hover {
    color: #1890ff;
    background-color: rgba(24, 144, 255, 0.1);
    transform: translateY(-2px);
}

nav ul li a.active {
    color: #1890ff;
    font-weight: bold;
    background-color: rgba(24, 144, 255, 0.1);
    box-shadow: 0 2px 5px rgba(24, 144, 255, 0.15);
}

/* 主体内容样式 */
main {
    margin-bottom: 30px;
}

.welcome-section {
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    background-image: linear-gradient(to right, #fff, #f8fbff);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.welcome-section:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.welcome-section::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 100%;
    background-image: linear-gradient(to right, rgba(255,255,255,0), rgba(24, 144, 255, 0.05));
    z-index: 1;
}

.welcome-content {
    position: relative;
    z-index: 2;
}

.welcome-content h2 {
    color: #1890ff;
    margin-bottom: 15px;
    border-bottom: none;
    font-size: 1.6rem;
}

.welcome-content p {
    color: #666;
    font-size: 1rem;
    max-width: 80%;
    line-height: 1.6;
}

.dashboard, .system-info, .content-section {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.dashboard:hover, .system-info:hover, .content-section:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

h2 {
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    font-size: 1.4rem;
}

h2 i {
    margin-right: 10px;
    color: #1890ff;
}

/* 卡片样式 */
.card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    margin-top: 20px;
}

.card {
    background-color: #fff;
    border-radius: 12px;
    padding: 25px;
    flex: 1;
    min-width: 250px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #eee;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: #1890ff;
}

.card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #1890ff, #40a9ff);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover::after {
    opacity: 1;
}

.card-icon {
    font-size: 36px;
    margin-bottom: 15px;
    color: #1890ff;
    transition: all 0.3s ease;
}

.card:hover .card-icon {
    transform: scale(1.1);
}

.card-icon i {
    font-size: 2.5rem;
}

.card h3 {
    color: #1890ff;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.card p {
    color: #666;
    margin-bottom: 20px;
    flex-grow: 1;
    font-size: 0.95rem;
    line-height: 1.5;
}

.card-action {
    color: #1890ff;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    margin-top: auto;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.card:hover .card-action {
    opacity: 1;
}

.card-action i {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.card:hover .card-action i {
    transform: translateX(3px);
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

input, select, textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
button {
    background-color: #1890ff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

button:hover {
    background-color: #40a9ff;
}

button.secondary {
    background-color: #f0f0f0;
    color: #333;
}

button.secondary:hover {
    background-color: #e0e0e0;
}

button.danger {
    background-color: #ff4d4f;
}

button.danger:hover {
    background-color: #ff7875;
}

/* 编辑和删除按钮样式 */
.edit-btn {
    background-color: #1890ff;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 8px;
    font-size: 14px;
}

.edit-btn:hover {
    background-color: #40a9ff;
}

.delete-btn {
    background-color: #ff4d4f;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.delete-btn:hover {
    background-color: #ff7875;
}

/* 按钮加载状态 */
button.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

button.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-top: -8px;
    margin-left: -8px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    border-top-color: white;
    animation: button-spin 0.8s linear infinite;
}

@keyframes button-spin {
    to {
        transform: rotate(360deg);
    }
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 设置特定列的宽度 */
table td:nth-child(1) { /* 时间列 */
    width: 150px;
}

table td:nth-child(2) { /* ERP房源ID列 */
    width: 280px;
}

table td:nth-child(3) { /* 网站列 */
    width: 120px;
}

table td:nth-child(4) { /* 进程ID列 */
    width: 300px;
}

table td:nth-child(5) { /* 推送状态列 */
    width: 80px;
}

table td:nth-child(6) { /* 推送消息列 */
    width: 100px;
}

table td:nth-child(7) { /* 操作列 */
    width: 100px;
    text-align: center;
}

th {
    background-color: #f5f7fa;
    font-weight: bold;
}

tr:hover {
    background-color: rgba(24, 144, 255, 0.05);
}

/* 支持平台样式 */
.supported-platforms {
    background-color: #fff;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.supported-platforms::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #1890ff, #40a9ff);
}

.supported-platforms:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.platforms-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 20px;
}

.platform-section {
    flex: 1;
    min-width: 300px;
}

.platform-section h3 {
    color: #333;
    margin-bottom: 18px;
    font-size: 1.1rem;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    position: relative;
}

.platform-section h3::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(to right, #1890ff, #40a9ff);
    border-radius: 3px;
}

.platform-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: space-between; /* 平均分配空间 */
}

.platform-card {
    background-color: #ffffff;
    border-radius: 12px;
    padding: 25px 20px;
    width: calc(50% - 10px); /* 设置固定宽度，考虑间距 */
    height: 180px; /* 设置固定高度 */
    border: 1px solid #eee;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; /* 垂直居中内容 */
    text-align: center;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
}

.platform-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    border-color: #1890ff;
}

.platform-logo {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    min-height: 50px; /* 确保最小高度一致 */
}

.platform-logo img {
    max-height: 100%;
    width: auto;
    object-fit: contain;
    transition: all 0.3s ease;
}

.platform-card:hover .platform-logo {
    transform: scale(1.05);
}

.platform-card h4 {
    color: #1890ff;
    margin-bottom: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.platform-card p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
    height: 40px; /* 固定高度，确保一致性 */
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

.platform-card.coming-soon {
    background-color: #f9f9f9;
    border: 1px dashed #ddd;
    opacity: 0.85;
}

.platform-card.coming-soon .platform-logo img {
    filter: grayscale(80%);
    opacity: 0.6;
}

.platform-card.coming-soon:hover {
    opacity: 1;
    border-color: #1890ff;
    border-style: dashed;
}

.platform-card.coming-soon:hover .platform-logo img {
    filter: grayscale(40%);
    opacity: 0.8;
}

.coming-soon-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background-color: #faad14;
    color: white;
    padding: 3px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(250, 173, 20, 0.3);
}

@media (max-width: 768px) {
    .platforms-container {
        flex-direction: column;
    }

    .platform-section {
        width: 100%;
    }

    .platform-cards {
        flex-direction: column;
    }

    .platform-card {
        width: 100%;
        height: auto;
        min-height: 160px;
    }
}

/* 系统信息样式 */
.system-info {
    margin-top: 30px;
}

.system-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.info-card {
    background-color: #f9fafc;
    border-radius: 10px;
    padding: 15px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    border: 1px solid #eee;
}

.info-card:hover {
    background-color: #f0f7ff;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.info-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(24, 144, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #1890ff;
    font-size: 1.2rem;
}

.info-content {
    flex: 1;
}

.info-label {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 5px;
}

.info-value {
    font-size: 1rem;
    color: #333;
    font-weight: 500;
}

.info-item {
    margin-bottom: 10px;
}

.status-active {
    color: #52c41a;
    font-weight: bold;
}

.error {
    color: #ff4d4f;
}

/* 页脚样式 */
footer {
    padding: 25px 20px;
    color: #999;
    font-size: 14px;
    border-top: 1px solid #eee;
    margin-top: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: #666;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: #1890ff;
}

/* 搜索区域样式 */
.search-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.form-row .form-group {
    flex: 1;
    min-width: 200px;
    margin-bottom: 0;
}

.form-row .form-group:last-child {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

/* 按钮样式扩展 */
.primary-button {
    background-color: #1890ff;
    color: white;
}

.primary-button:hover {
    background-color: #40a9ff;
}

.secondary-button {
    background-color: #f0f0f0;
    color: #666;
}

.secondary-button:hover {
    background-color: #e0e0e0;
}

/* 数据展示区域样式 */
.data-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table-container {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 20px;
}

/* 状态标签样式 */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    background-color: #f0f0f0;
    color: #666;
}

.status-badge.success {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.status-badge.warning {
    background-color: #fffbe6;
    color: #faad14;
    border: 1px solid #ffe58f;
}

.status-badge.error {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

/* 操作按钮样式 */
.action-button {
    padding: 4px 8px;
    font-size: 14px;
    margin-right: 8px;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    color: #666;
}

.action-button:hover {
    color: #1890ff;
    border-color: #1890ff;
    background-color: #fff;
}

.action-button.danger {
    color: #ff4d4f;
    border-color: #ff4d4f;
    background-color: #fff;
}

.action-button.danger:hover {
    color: #fff;
    background-color: #ff4d4f;
}

/* 分页控件样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
}

.page-button {
    padding: 8px 16px;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    color: #666;
}

.page-button:hover:not(:disabled) {
    color: #1890ff;
    border-color: #1890ff;
    background-color: #fff;
}

.page-button:disabled {
    background-color: #f5f5f5;
    color: #d9d9d9;
    cursor: not-allowed;
}

#pageInfo {
    color: #666;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 100px auto;
    padding: 0;
    width: 80%;
    max-width: 800px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-body {
    padding: 24px;
    max-height: calc(100vh - 300px);
    overflow-y: auto;
}

.close {
    font-size: 24px;
    color: #999;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #666;
}

/* 响应式设计补充 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .form-row .form-group {
        min-width: 100%;
    }

    .table-container {
        margin: 0 -20px;
    }

    .modal-content {
        width: 95%;
        margin: 50px auto;
    }
}

/* 导航栏彩色图标样式 */
.icon-blue {
    color: #1890ff !important;
}

.icon-green {
    color: #52c41a !important;
}

.icon-orange {
    color: #fa8c16 !important;
}

.icon-gold {
    color: #faad14 !important;
}

.icon-red {
    color: #f5222d !important;
}

.icon-purple {
    color: #722ed1 !important;
}

.icon-magenta {
    color: #eb2f96 !important;
}

.icon-cyan {
    color: #13c2c2 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-container {
        flex-direction: column;
    }

    nav ul {
        flex-direction: column;
        padding: 0 10px 15px;
        gap: 5px;
    }

    nav ul li {
        margin-bottom: 8px;
        margin-right: 0;
        width: 100%;
    }

    nav ul li a {
        font-size: 15px;
        padding: 10px 15px;
        justify-content: center;
        width: 100%;
    }

    nav ul li a i {
        margin-right: 8px;
        font-size: 16px;
    }
}

@media (max-width: 1100px) and (min-width: 769px) {
    nav ul {
        gap: 5px;
        padding-bottom: 12px;
    }

    nav ul li {
        margin-right: 5px;
    }

    nav ul li a {
        font-size: 13px;
        padding: 7px 10px;
        margin: 0 1px;
    }

    nav ul li a i {
        font-size: 13px;
        margin-right: 4px;
    }
}

.table-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.table-info span {
    color: #666;
    font-size: 0.9rem;
}

#loadingIndicator {
    display: flex;
    align-items: center;
    color: #666;
}

#loadingIndicator span {
    margin-left: 0.5rem;
}

#loadingIndicator::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ddd;
    border-top-color: #666;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 状态标签样式 */
.status-success {
    color: #52c41a;
}

/* 查看详情按钮样式 */
.view-details-btn {
    background-color: #1890ff;
    color: white;
    border: none;
    padding: 5px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.view-details-btn:hover {
    background-color: #40a9ff;
}

/* 定时任务管理页面样式 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.section-header h2 {
    margin: 0;
    padding: 0;
    border: none;
}

.btn-small {
    padding: 5px 10px;
    font-size: 14px;
    margin-right: 5px;
}

.btn-small:last-child {
    margin-right: 0;
}

.btn-small.warning {
    background-color: #faad14;
}

.btn-small.warning:hover {
    background-color: #ffc53d;
}

.btn-small.success {
    background-color: #52c41a;
}

.btn-small.success:hover {
    background-color: #73d13d;
}

.form-container {
    padding: 20px;
}

.form-text {
    color: #666;
    font-size: 12px;
    margin-top: 4px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.form-actions button {
    min-width: 80px;
}

/* 状态标签样式扩展 */
.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    line-height: 1.5;
    text-align: center;
    white-space: nowrap;
}

.status-badge.success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
}

.status-badge.warning {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    color: #faad14;
}

.status-badge.error {
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    color: #ff4d4f;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .section-header {
        flex-direction: column;
        gap: 10px;
    }

    .section-header button {
        width: 100%;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions button {
        width: 100%;
    }

    .btn-small {
        padding: 8px 12px;
        width: auto;
    }
}

/* 定时任务模态框样式 */
.cron-input-group {
    display: flex;
    gap: 10px;
}

.cron-input-group input,
.cron-input-group select {
    flex: 1;
}

.form-control[multiple] {
    height: auto;
    min-height: 120px;
}

.form-text {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .cron-input-group {
        flex-direction: column;
    }

    .cron-input-group input,
    .cron-input-group select {
        width: 100%;
    }
}

/* 美化多选框样式 */
select[multiple] {
    padding: 8px;
}

select[multiple] option {
    padding: 8px;
    margin: 2px 0;
    border-radius: 4px;
    transition: all 0.2s;
}

select[multiple] option:checked {
    background-color: #e6f7ff;
    color: #1890ff;
}

select[multiple] option:hover {
    background-color: #f5f5f5;
}

/* 任务类型标签样式 */
.task-type-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    line-height: 1.5;
    margin-right: 8px;
}

.task-type-badge.data_sync {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
}

.task-type-badge.content_push {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.task-type-badge.data_backup {
    background-color: #fff7e6;
    color: #faad14;
    border: 1px solid #ffd591;
}

/* 任务描述样式 */
.task-description {
    color: #666;
    font-size: 14px;
    margin-top: 8px;
    white-space: pre-wrap;
    word-break: break-word;
}

/* 任务参数预览样式 */
.task-params {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
    overflow-x: auto;
    margin-top: 8px;
}

/* 城市选择输入框样式 */
input[list] {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23666' d='M2 4l4 4 4-4'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    padding-right: 28px;
}

/* 表单验证提示样式 */
.form-group.has-error input,
.form-group.has-error select,
.form-group.has-error textarea {
    border-color: #ff4d4f;
}

.form-group.has-error .form-text {
    color: #ff4d4f;
}

.error-message {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 4px;
    display: none;
}

.form-group.has-error .error-message {
    display: block;
}