/* 流程图自动动画效果 */
.upload-process-flow {
    position: relative;
    overflow: hidden;
}

/* 步骤圆圈的简化脉冲效果 - 减少box-shadow使用 */
@keyframes enhancedPulse {
    0% { transform: scale(1); }
    40% { transform: scale(1.1); }
    80% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* 步骤数字的淡入淡出效果 */
@keyframes numberFade {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 图标圆圈的简化动画效果 */
@keyframes iconPop {
    0% { opacity: 0; }
    20% { opacity: 1; }
    80% { opacity: 1; }
    100% { opacity: 0; }
}

/* 步骤标题的简化效果 - 移除颜色变化 */
@keyframes titleGlow {
    0% { transform: translateY(0); }
    50% { transform: translateY(-2px); }
    100% { transform: translateY(0); }
}

/* 连接器的简化动画效果 */
@keyframes connectorFlow {
    0% { background-color: #e8e8e8; }
    100% { background-color: #1890ff; }
}

/* 连接器箭头的简化动画效果 */
@keyframes arrowBounce {
    0%, 100% { transform: translate(-50%, -50%); color: #1890ff; }
    50% { transform: translate(-50%, -50%); color: #52c41a; }
}

/* 应用动画到各个步骤 - 使用更简单的背景色 */
.upload-process-flow.animate-flow .step-circle {
    animation: enhancedPulse 4s infinite;
    background-color: #1890ff;
}

.upload-process-flow.animate-flow .step-circle span {
    animation: numberFade 4s infinite;
}

/* 新增图标圆圈样式 */
.upload-process-flow .icon-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #1890ff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -10px;
    right: -10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 2;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.upload-process-flow.animate-flow .icon-circle {
    animation: iconPop 4s infinite;
}

.upload-process-flow .icon-circle i {
    font-size: 18px;
}

.upload-process-flow.animate-flow .step-title {
    animation: titleGlow 4s infinite;
    font-weight: 500;
    color: #1890ff;
}

.upload-process-flow.animate-flow .step-connector {
    animation: connectorFlow 4s infinite alternate;
    height: 3px;
}

.upload-process-flow.animate-flow .step-connector i {
    animation: arrowBounce 2s infinite;
    font-size: 16px;
}

/* 简化步骤动画延迟，减少不同的延迟数量 */
.upload-process-flow.animate-flow #step1 .step-circle,
.upload-process-flow.animate-flow #step1 .step-circle span,
.upload-process-flow.animate-flow #step1 .icon-circle,
.upload-process-flow.animate-flow #step1 .step-title {
    animation-delay: 0s;
}

.upload-process-flow.animate-flow #step2 .step-circle,
.upload-process-flow.animate-flow #step2 .step-circle span,
.upload-process-flow.animate-flow #step2 .icon-circle,
.upload-process-flow.animate-flow #step2 .step-title {
    animation-delay: 0.5s;
}

.upload-process-flow.animate-flow #step3 .step-circle,
.upload-process-flow.animate-flow #step3 .step-circle span,
.upload-process-flow.animate-flow #step3 .icon-circle,
.upload-process-flow.animate-flow #step3 .step-title {
    animation-delay: 1s;
}

/* 连接器动画 */
.upload-process-flow.animate-flow .step-connector:nth-of-type(1),
.upload-process-flow.animate-flow .step-connector:nth-of-type(1) i {
    animation-delay: 0.25s;
}

.upload-process-flow.animate-flow .step-connector:nth-of-type(2),
.upload-process-flow.animate-flow .step-connector:nth-of-type(2) i {
    animation-delay: 0.75s;
}

/* 鼠标悬停时暂停动画 */
.upload-process-flow.animate-flow .process-step:hover .step-circle,
.upload-process-flow.animate-flow .process-step:hover .step-icon,
.upload-process-flow.animate-flow .process-step:hover .step-title {
    animation-play-state: paused;
}

/* 简化高亮步骤样式 */
.process-step.highlight-step .step-circle {
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
    background-color: #096dd9;
}

.process-step.highlight-step .icon-circle {
    opacity: 1;
    transform: scale(1.1);
    background-color: #52c41a;
    box-shadow: 0 0 15px rgba(82, 196, 26, 0.6);
}

.process-step.highlight-step .step-title {
    color: #1890ff;
    font-weight: bold;
    transform: translateY(-5px);
    text-shadow: 0 0 5px rgba(24, 144, 255, 0.2);
}

.process-step.highlight-step .step-desc {
    color: #40a9ff;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .upload-process-flow.animate-flow .step-connector {
        animation: none;
    }

    .upload-process-flow.animate-flow .step-connector i {
        animation: arrowBounce 2s infinite;
    }
}
