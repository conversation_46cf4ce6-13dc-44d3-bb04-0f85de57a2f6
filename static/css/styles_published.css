/* ===== 现代化设计系统 ===== */

/* 性能优化 - 减少动画在低性能设备上的影响 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* 全局性能优化 */
* {
    /* 优化重绘性能 */
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* CSS 自定义属性 - 设计令牌 */
:root {
    /* 颜色系统 */
    --color-primary: #1890ff;
    --color-primary-hover: #40a9ff;
    --color-primary-active: #096dd9;
    --color-primary-light: #e6f7ff;
    --color-primary-lighter: #f0f9ff;

    --color-success: #52c41a;
    --color-warning: #faad14;
    --color-error: #ff4d4f;
    --color-info: #1890ff;

    --color-text-primary: #262626;
    --color-text-secondary: #595959;
    --color-text-tertiary: #8c8c8c;
    --color-text-disabled: #bfbfbf;

    --color-bg-primary: #ffffff;
    --color-bg-secondary: #fafafa;
    --color-bg-tertiary: #f5f5f5;
    --color-bg-disabled: #f5f5f5;

    --color-border-primary: #d9d9d9;
    --color-border-secondary: #f0f0f0;
    --color-border-hover: #40a9ff;
    --color-border-focus: #1890ff;

    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-xxl: 24px;
    --spacing-xxxl: 32px;

    /* 字体系统 */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;

    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.6;

    /* 圆角系统 */
    --border-radius-sm: 4px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;
    --border-radius-xl: 12px;

    /* 阴影系统 */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);

    /* 动画系统 - 优化缓动函数 */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    /* Z-index 系统 */
    --z-dropdown: 1000;
    --z-modal: 1050;
    --z-tooltip: 1100;
    --z-toast: 9999;
}

/* ===== 页面头部样式 ===== */
.page-header {
    margin-bottom: var(--spacing-xxl);
}

.page-title-wrapper {
    text-align: center;
    padding: var(--spacing-xl) 0;
}

.page-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-sm);
    border: none;
    padding: 0;
}

.page-title-icon {
    color: var(--color-primary);
    font-size: var(--font-size-xl);
}

.page-description {
    color: var(--color-text-secondary);
    font-size: var(--font-size-md);
    line-height: var(--line-height-relaxed);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== 搜索区域样式 ===== */
.search-section {
    background: var(--color-bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xxl);
    margin-bottom: var(--spacing-xxl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-border-secondary);
    transition: box-shadow var(--transition-normal);
}

.search-section:hover {
    box-shadow: var(--shadow-lg);
}

.search-header {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--color-border-secondary);
}

.search-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    margin: 0;
}

.search-title i {
    color: var(--color-primary);
    font-size: var(--font-size-md);
}

.search-form {
    margin: 0;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: var(--spacing-xl);
    align-items: end;
}

/* ===== 表单控件样式 ===== */
.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    margin: 0;
}

.form-label-icon {
    color: var(--color-primary);
    font-size: var(--font-size-xs);
}

.form-help {
    font-size: var(--font-size-xs);
    color: var(--color-text-tertiary);
    margin: 0;
    line-height: var(--line-height-normal);
}

/* 输入框样式 */
.input-wrapper {
    position: relative;
}

.form-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-right: var(--spacing-xxxl);
    border: 2px solid var(--color-border-primary);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);
    background-color: var(--color-bg-primary);
    transition: all var(--transition-normal);
    outline: none;
    box-shadow: var(--shadow-sm);
}

.form-input::placeholder {
    color: var(--color-text-tertiary);
}

.form-input:hover {
    border-color: var(--color-border-hover);
    box-shadow: var(--shadow-md);
}

.form-input:focus {
    border-color: var(--color-border-focus);
    box-shadow: 0 0 0 3px var(--color-primary-light);
}

.input-icon {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-text-tertiary);
    font-size: var(--font-size-sm);
    pointer-events: none;
}

/* 选择框样式 */
.select-wrapper {
    position: relative;
}

.form-select {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-xxxl) var(--spacing-md) var(--spacing-lg);
    border: 2px solid var(--color-border-primary);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);
    background-color: var(--color-bg-primary);
    transition: all var(--transition-normal);
    outline: none;
    box-shadow: var(--shadow-sm);
    appearance: none;
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3Csvg width='12' height='8' viewBox='0 0 12 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L6 6L11 1' stroke='%23595959' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right var(--spacing-md) center;
}

.form-select:hover {
    border-color: var(--color-border-hover);
    box-shadow: var(--shadow-md);
}

.form-select:focus {
    border-color: var(--color-border-focus);
    box-shadow: 0 0 0 3px var(--color-primary-light);
}

/* ===== 按钮系统 ===== */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-xl);
    border: 2px solid transparent;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    outline: none;
    position: relative;
    min-width: 100px;
    box-shadow: var(--shadow-sm);
}

.btn:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 主要按钮 */
.btn-primary {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--color-primary-hover);
    border-color: var(--color-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary:active {
    background-color: var(--color-primary-active);
    border-color: var(--color-primary-active);
    transform: translateY(0);
}

/* 次要按钮 */
.btn-secondary {
    background-color: var(--color-bg-primary);
    border-color: var(--color-border-primary);
    color: var(--color-text-secondary);
}

.btn-secondary:hover:not(:disabled) {
    border-color: var(--color-primary);
    color: var(--color-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary:active {
    background-color: var(--color-primary-lighter);
    transform: translateY(0);
}

/* 刷新按钮 */
.btn-refresh {
    background-color: var(--color-bg-secondary);
    border-color: var(--color-border-primary);
    color: var(--color-text-secondary);
    padding: var(--spacing-sm) var(--spacing-lg);
    min-width: auto;
}

.btn-refresh:hover:not(:disabled) {
    background-color: var(--color-primary-lighter);
    border-color: var(--color-primary);
    color: var(--color-primary);
}

.btn-refresh .refresh-icon {
    transition: transform var(--transition-normal);
}

.btn-refresh:hover .refresh-icon {
    transform: rotate(180deg);
}

/* 分页按钮 */
.btn-pagination {
    background-color: var(--color-bg-primary);
    border-color: var(--color-border-primary);
    color: var(--color-text-secondary);
    padding: var(--spacing-sm) var(--spacing-lg);
    min-width: auto;
}

.btn-pagination:hover:not(:disabled) {
    background-color: var(--color-primary-lighter);
    border-color: var(--color-primary);
    color: var(--color-primary);
}

.btn-pagination:disabled {
    background-color: var(--color-bg-disabled);
    border-color: var(--color-border-secondary);
    color: var(--color-text-disabled);
}

/* 按钮图标 */
.btn-icon {
    font-size: var(--font-size-xs);
}

/* 按钮加载状态 */
.btn-text {
    transition: opacity var(--transition-fast);
}

.btn-loading {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: currentColor;
    animation: spin 1s linear infinite;
}

/* 功能按钮loading状态 */
.btn-search.loading .btn-text,
.btn-reset.loading .btn-text,
.btn-refresh.loading .btn-text {
    opacity: 0;
}

.btn-search.loading .btn-loading,
.btn-reset.loading .btn-loading,
.btn-refresh.loading .btn-loading {
    display: block;
}

.btn-search.loading,
.btn-reset.loading,
.btn-refresh.loading {
    pointer-events: none;
}

@keyframes spin {
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* ===== 数据展示区域样式 ===== */
.data-section {
    background: var(--color-bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xxl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-border-secondary);
}

.data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--color-border-secondary);
}

.data-stats {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.stats-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--color-primary-lighter);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-primary-light);
}

.stats-icon {
    color: var(--color-primary);
    font-size: var(--font-size-sm);
}

.stats-label {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    font-weight: var(--font-weight-medium);
}

.stats-value {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-primary);
}

.stats-unit {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.loading-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--color-text-tertiary);
}

.loading-spinner-mini {
    width: 16px;
    height: 16px;
    border: 2px solid var(--color-border-secondary);
    border-radius: 50%;
    border-top-color: var(--color-primary);
    animation: spin 1s linear infinite;
}

.loading-text {
    font-size: var(--font-size-sm);
}

.data-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

/* ===== 卡片网格布局 ===== */
.cards-container {
    position: relative;
    margin-bottom: var(--spacing-xl);
    /* 性能优化 */
    contain: layout style;
}

.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: var(--spacing-xxl);
    padding: var(--spacing-lg) 0;
    /* 性能优化 */
    contain: layout style;
    /* 启用GPU加速 */
    transform: translate3d(0, 0, 0);
}

/* ===== 优化后的卡片背景系统 ===== */
.card-bg-animation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    border-radius: var(--border-radius-xl);
    z-index: 0;
    /* 性能优化 */
    will-change: transform;
    contain: layout style paint;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(24, 144, 255, 0.03) 0%,
        rgba(64, 169, 255, 0.05) 50%,
        rgba(24, 144, 255, 0.02) 100%
    );
    /* 简化动画 - 移除复杂的背景位置动画 */
    opacity: 0.8;
    transition: opacity var(--transition-normal);
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(24, 144, 255, 0.06);
    /* 简化动画 - 只保留一个简单的缩放动画 */
    animation: simpleFloat 8s ease-in-out infinite;
    will-change: transform;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: -10%;
    right: -5%;
    background: radial-gradient(circle, rgba(24, 144, 255, 0.08) 0%, transparent 70%);
    animation-delay: 0s;
}

/* 移除多余的形状以减少动画负担 */
.shape-2,
.shape-3 {
    display: none;
}

/* 完全移除粒子系统以提升性能 */
.bg-particles,
.particle {
    display: none;
}

/* 简化的动画关键帧 */
@keyframes simpleFloat {
    0%, 100% {
        transform: translate3d(0, 0, 0) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translate3d(0, -10px, 0) scale(1.05);
        opacity: 0.8;
    }
}

/* ===== 优化后的数据卡片样式 ===== */
.data-card, .skeleton-card {
    position: relative;
    background: rgba(255, 255, 255, 0.98);
    /* 简化背景模糊效果 */
    backdrop-filter: blur(8px);
    border-radius: var(--border-radius-xl);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.5);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    overflow: hidden;
    cursor: pointer;
    min-height: 280px;
    /* 性能优化 */
    will-change: transform;
    contain: layout style paint;
}

.data-card:hover, .skeleton-card:hover {
    /* 简化悬停效果 */
    transform: translate3d(0, -8px, 0);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
}

.data-card:hover .card-bg-animation .bg-gradient {
    opacity: 1;
}

.data-card:hover .card-bg-animation .bg-shape {
    /* 保持简单的动画，不改变持续时间 */
    opacity: 0.9;
}

.card-content {
    position: relative;
    z-index: 1;
    padding: var(--spacing-xl);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    margin: 0;
    line-height: var(--line-height-tight);
    flex: 1;
    margin-right: var(--spacing-md);
}

.card-location {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
}

.card-location i {
    color: var(--color-primary);
}

.card-badge {
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-xl);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.badge-sale {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.badge-rent {
    background: linear-gradient(135deg, #4ecdc4, #44bd87);
    color: white;
}

.card-body {
    flex: 1;
    margin-bottom: var(--spacing-lg);
}

.card-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.5);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(24, 144, 255, 0.1);
}

.info-label {
    font-size: var(--font-size-xs);
    color: var(--color-text-tertiary);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.info-label i {
    color: var(--color-primary);
}

.info-value {
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);
    font-weight: var(--font-weight-medium);
    word-break: break-all;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(24, 144, 255, 0.1);
    margin-top: auto;
}

.card-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--color-text-tertiary);
    font-size: var(--font-size-xs);
}

.card-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.card-action-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-push {
    background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
    color: white;
}

.btn-push:hover {
    transform: translate3d(0, -2px, 0);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* ===== 骨架屏样式 ===== */
.skeleton-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
}

.skeleton-title {
    height: 24px;
    width: 70%;
    background: linear-gradient(90deg, var(--color-bg-tertiary) 25%, var(--color-bg-secondary) 50%, var(--color-bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--border-radius-sm);
}

.skeleton-badge {
    height: 20px;
    width: 60px;
    background: linear-gradient(90deg, var(--color-bg-tertiary) 25%, var(--color-bg-secondary) 50%, var(--color-bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--border-radius-xl);
}

.skeleton-card-body {
    flex: 1;
    margin-bottom: var(--spacing-lg);
}

.skeleton-info-item {
    height: 16px;
    width: 100%;
    background: linear-gradient(90deg, var(--color-bg-tertiary) 25%, var(--color-bg-secondary) 50%, var(--color-bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-md);
}

.skeleton-info-item:nth-child(2) {
    width: 80%;
}

.skeleton-info-item:nth-child(3) {
    width: 60%;
}

.skeleton-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--color-border-secondary);
    margin-top: auto;
}

.skeleton-button {
    height: 32px;
    width: 80px;
    background: linear-gradient(90deg, var(--color-bg-tertiary) 25%, var(--color-bg-secondary) 50%, var(--color-bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--border-radius-md);
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* ===== 空状态样式 ===== */
.empty-state {
    text-align: center;
    padding: var(--spacing-xxxl) var(--spacing-xl);
    color: var(--color-text-tertiary);
    grid-column: 1 / -1;
}

.empty-icon {
    font-size: 64px;
    color: var(--color-text-disabled);
    margin-bottom: var(--spacing-xl);
}

.empty-title {
    font-size: var(--font-size-xl);
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-md);
}

.empty-description {
    font-size: var(--font-size-md);
    line-height: var(--line-height-relaxed);
    max-width: 400px;
    margin: 0 auto;
}

/* ===== 分页样式 ===== */
.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-xl);
}

.pagination {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--color-bg-primary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border-secondary);
    box-shadow: var(--shadow-sm);
}

.page-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    padding: 0 var(--spacing-lg);
}

.page-current {
    font-weight: var(--font-weight-semibold);
    color: var(--color-primary);
    font-size: var(--font-size-md);
}

.page-total {
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
}

.page-separator {
    color: var(--color-text-tertiary);
    margin: 0 var(--spacing-xs);
}

/* ===== 顶部进度条 ===== */
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-primary-hover));
    z-index: var(--z-toast);
    transition: width var(--transition-normal);
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

/* ===== 动画效果 ===== */
.fade-in-row {
    animation: fadeInUp 0.5s ease forwards;
    opacity: 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .form-actions {
        justify-content: center;
    }

    .data-header {
        flex-direction: column;
        gap: var(--spacing-lg);
        align-items: stretch;
    }

    .data-stats {
        justify-content: center;
    }

    .data-actions {
        justify-content: center;
    }

    .cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: var(--spacing-xl);
    }
}

@media (max-width: 768px) {
    :root {
        --spacing-xs: 3px;
        --spacing-sm: 6px;
        --spacing-md: 10px;
        --spacing-lg: 14px;
        --spacing-xl: 18px;
        --spacing-xxl: 20px;
        --spacing-xxxl: 24px;
    }

    .search-section,
    .data-section {
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }

    .page-title {
        font-size: var(--font-size-xl);
    }

    .form-actions {
        flex-direction: column;
        width: 100%;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .data-header {
        text-align: center;
    }

    .stats-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xs);
    }

    .cards-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        padding: var(--spacing-md) 0;
    }

    .data-card, .skeleton-card {
        min-height: 240px;
    }

    .card-content {
        padding: var(--spacing-lg);
    }

    .card-info-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .pagination {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .btn-pagination {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .page-title-wrapper {
        padding: var(--spacing-md) 0;
    }

    .page-title {
        font-size: var(--font-size-lg);
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .search-section,
    .data-section {
        padding: var(--spacing-md);
        border-radius: var(--border-radius-md);
    }

    .cards-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-sm) 0;
    }

    .data-card, .skeleton-card {
        min-height: 200px;
    }

    .card-content {
        padding: var(--spacing-md);
    }

    .card-title {
        font-size: var(--font-size-md);
    }

    .card-badge {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 10px;
    }

    .info-item {
        padding: var(--spacing-sm);
    }

    .info-label {
        font-size: 10px;
    }

    .info-value {
        font-size: var(--font-size-xs);
    }

    .card-action-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 10px;
    }

    .empty-icon {
        font-size: 48px;
    }

    .empty-title {
        font-size: var(--font-size-lg);
    }

    .empty-description {
        font-size: var(--font-size-sm);
    }
}

/* ===== 模态框和弹窗样式 ===== */
.modal {
    display: none;
    position: fixed;
    z-index: var(--z-modal);
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    opacity: 0;
    transition: opacity var(--transition-normal);
    overflow-y: auto;
    backdrop-filter: blur(4px);
}

.modal.show {
    opacity: 1;
}

.modal-content {
    background-color: var(--color-bg-primary);
    padding: 0;
    border: none;
    width: 90%;
    max-width: 700px;
    border-radius: var(--border-radius-xl);
    transform: translateY(-20px) scale(0.95);
    opacity: 0;
    transition: all var(--transition-normal);
    margin: 5% auto;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal.show .modal-content {
    transform: translateY(0) scale(1);
    opacity: 1;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl) var(--spacing-xxl);
    background: linear-gradient(135deg, var(--color-primary-lighter) 0%, var(--color-primary-light) 100%);
    border-bottom: 1px solid var(--color-border-secondary);
}

.modal-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-primary);
    margin: 0;
}

.close {
    color: var(--color-text-tertiary);
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all var(--transition-fast);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.close:hover {
    color: var(--color-error);
    background-color: rgba(255, 77, 79, 0.1);
}

.modal-body {
    padding: var(--spacing-xl) var(--spacing-xxl);
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg) var(--spacing-xxl);
    background-color: var(--color-bg-secondary);
    border-top: 1px solid var(--color-border-secondary);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

/* ===== Toast 提示样式 ===== */
.toast-container {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    z-index: var(--z-toast);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    color: white;
    opacity: 0;
    transform: translateX(100%);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 300px;
    max-width: 400px;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    backdrop-filter: blur(8px);
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast::before {
    content: '';
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast.success {
    background: linear-gradient(135deg, var(--color-success) 0%, #73d13d 100%);
}

.toast.success::before {
    content: '✓';
    color: white;
    font-weight: bold;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.2);
}

.toast.error {
    background: linear-gradient(135deg, var(--color-error) 0%, #ff7875 100%);
}

.toast.error::before {
    content: '✕';
    color: white;
    font-weight: bold;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.2);
}

.toast.warning {
    background: linear-gradient(135deg, var(--color-warning) 0%, #ffc53d 100%);
}

.toast.warning::before {
    content: '⚠';
    color: white;
    font-weight: bold;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.2);
}

.toast.info {
    background: linear-gradient(135deg, var(--color-info) 0%, #40a9ff 100%);
}

.toast.info::before {
    content: 'ℹ';
    color: white;
    font-weight: bold;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.2);
}

/* ===== 警告模态框样式 ===== */
.warning-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: var(--z-toast);
    backdrop-filter: blur(8px);
}

.warning-content {
    background: var(--color-bg-primary);
    padding: var(--spacing-xxxl);
    border-radius: var(--border-radius-xl);
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--color-border-secondary);
}

.warning-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-error);
    margin-bottom: var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.warning-title::before {
    content: '⚠️';
    font-size: var(--font-size-xxl);
}

.warning-list {
    text-align: left;
    margin-bottom: var(--spacing-xl);
}

.warning-item {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    background: var(--color-bg-secondary);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--color-warning);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
}

.warning-confirm-btn {
    background: linear-gradient(135deg, var(--color-error), #ff7875);
    color: white;
    border: none;
    padding: var(--spacing-lg) var(--spacing-xxxl);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.warning-confirm-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* ===== 网站列表样式 ===== */
.website-list-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-xxxl);
    color: var(--color-text-secondary);
}

.loading-spinner-small {
    width: 32px;
    height: 32px;
    border: 3px solid var(--color-border-secondary);
    border-radius: 50%;
    border-top-color: var(--color-primary);
    animation: spin 1s linear infinite;
}

.website-list {
    max-height: 400px;
    overflow-y: auto;
    padding: var(--spacing-md);
}

.website-group {
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--color-border-secondary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    background: var(--color-bg-primary);
    box-shadow: var(--shadow-sm);
}

.website-title {
    background: var(--color-primary-lighter);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--color-border-secondary);
}

.website-title h3 {
    margin: 0;
    font-size: var(--font-size-md);
    color: var(--color-primary);
    font-weight: var(--font-weight-semibold);
}

.account-list {
    padding: var(--spacing-md);
}

.account-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    border: 1px solid var(--color-border-secondary);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    background: var(--color-bg-primary);
}

.account-item:hover {
    background: var(--color-primary-lighter);
    border-color: var(--color-primary);
}

.account-item.selected {
    background: var(--color-primary-light);
    border-color: var(--color-primary);
}

.account-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--color-bg-disabled);
}

.account-item input[type="checkbox"] {
    margin-right: var(--spacing-md);
}

.account-item label {
    flex: 1;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.account-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.account-user-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.account-username {
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
}

.account-realname {
    font-size: var(--font-size-xs);
    color: var(--color-text-tertiary);
}

.account-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.status-active {
    background: var(--color-success);
    color: white;
}

.status-inactive {
    background: var(--color-error);
    color: white;
}

.account-actions {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
    margin-top: var(--spacing-xs);
}

.action-tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--color-primary-light);
    color: var(--color-primary);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.action-tag.disabled {
    background: var(--color-bg-disabled);
    color: var(--color-text-disabled);
}

/* ===== 全局加载指示器样式 ===== */
.global-loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: var(--z-toast);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    backdrop-filter: blur(4px);
}

.global-loading-indicator.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--color-primary-light);
    border-radius: 50%;
    border-top-color: var(--color-primary);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: var(--spacing-lg);
}

.global-loading-indicator span {
    font-size: var(--font-size-lg);
    color: var(--color-text-primary);
    font-weight: var(--font-weight-medium);
}

.website-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px 0;
}

.website-item {
    padding: 15px;
    margin: 8px 0;
    border: 1px solid #ddd;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    background-color: #fff;
    user-select: none;
    transform: translateY(10px);
    opacity: 0;
    animation: fadeInUp 0.3s forwards;
}

.website-item:hover {
    background-color: #f0f7ff;
    border-color: #1890ff;
}

.website-item.selected {
    background-color: #e6f7ff;
    border-color: #1890ff;
}

.website-item input[type="radio"] {
    margin-right: 12px;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.website-item label {
    flex: 1;
    cursor: pointer;
    font-size: 16px;
    margin: 0;
    padding: 0;
    color: #333;
}

.modal-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    text-align: right;
}

.modal-footer button {
    margin-left: 10px;
    padding: 8px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.modal-footer .secondary-button {
    background-color: #fff;
    border: 1px solid #ddd;
    color: #666;
}

.modal-footer .secondary-button:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
}

.modal-footer .primary-button {
    background-color: #1890ff;
    border: 1px solid #1890ff;
    color: #fff;
}

.modal-footer .primary-button:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
}

.modal-footer .primary-button:disabled {
    background-color: #ccc;
    border-color: #ccc;
    cursor: not-allowed;
}

/* 修改网站项的样式 */
.website-item input[type="radio"]:disabled + label {
    cursor: not-allowed;
    opacity: 0.7;
}

.website-item.disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 0.6;
}

.website-item.disabled:hover {
    background-color: #f5f5f5;
    border-color: #ddd;
}

.website-item.disabled label {
    cursor: not-allowed;
    color: #999;
}

.website-item input[type="radio"]:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.website-item input[type="radio"]:disabled + label {
    cursor: not-allowed;
    color: #999;
}

/* 消息提示样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    padding: 15px 20px;
    margin-bottom: 10px;
    border-radius: 4px;
    color: #fff;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.toast.show {
    opacity: 1;
}

.toast.success {
    background-color: #52c41a;
}

.toast.error {
    background-color: #ff4d4f;
}

.toast.warning {
    background-color: #faad14;
}

.toast.info {
    background-color: #1890ff;
}

.website-item.no-website {
    background-color: #f9f9f9;
    border: 1px dashed #ddd;
    padding: 20px;
    text-align: center;
    cursor: default;
}

.website-item.no-website:hover {
    background-color: #f9f9f9;
    border-color: #ddd;
}

.website-item.no-website p {
    margin: 0;
    font-size: 14px;
    color: #999;
}

/* 全局加载指示器样式 */
.global-loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.global-loading-indicator.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(24, 144, 255, 0.2);
    border-radius: 50%;
    border-top-color: #1890ff;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 15px;
}

.global-loading-indicator span {
    font-size: 18px;
    color: #333;
}

/* 表格骨架屏样式 */
.skeleton-row {
    animation: pulse 1.5s ease-in-out 0.5s infinite;
}

.skeleton-cell {
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.skeleton-button {
    height: 32px;
    width: 60px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

/* 按钮加载状态 */
.primary-button,
.secondary-button {
    position: relative;
    min-width: 80px;
}

.button-loading-icon {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s linear infinite;
}

.button-text {
    transition: opacity 0.3s ease;
}

.secondary-button .button-loading-icon {
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-top-color: #666;
}



/* 推送按钮独立loading状态 */
.push-btn-loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.push-btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s linear infinite;
}

/* 确认推送按钮独立loading状态 */
.confirm-btn-loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.confirm-btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s linear infinite;
}

/* 下架按钮独立loading状态 */
.remove-btn-loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.remove-btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s linear infinite;
}

/* 推送中状态 */
.push-button.pushing {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.push-button.pushing:hover {
    background: linear-gradient(135deg, #f57c00, #e65100);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
}

.push-button.pushing::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 8px;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: #fff;
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
}

/* 重新推送状态 */
.push-button.retry {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.push-button.retry:hover {
    background: linear-gradient(135deg, #d32f2f, #b71c1c);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
}

/* 动画 */
@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 0.8; }
    100% { opacity: 0.6; }
}

/* 网站列表加载效果 */
.website-list-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 0;
    color: #666;
}

.loading-spinner-small {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(24, 144, 255, 0.2);
    border-radius: 50%;
    border-top-color: #1890ff;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes fadeInUp {
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* 添加相关的CSS样式 */
.website-group {
    margin-bottom: 24px;
    border: 2px solid #f0f0f0;
    border-radius: 16px;
    overflow: hidden;
    animation: fadeIn 0.4s ease-in-out forwards;
    opacity: 0;
    background: white;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.website-group:hover {
    border-color: #d6f7ff;
    box-shadow: 0 8px 24px rgba(24, 144, 255, 0.1);
    transform: translateY(-2px);
}

.website-title {
    background: linear-gradient(135deg, #f8fbff 0%, #e6f7ff 100%);
    padding: 16px 20px;
    border-bottom: 2px solid rgba(24, 144, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.website-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.website-title h3 {
    margin: 0;
    font-size: 17px;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.website-title h3::before {
    content: '🌐';
    margin-right: 10px;
    font-size: 16px;
}

.account-list {
    padding: 12px;
}

.account-item {
    margin: 8px 0;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    background: white;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.account-item:not(.disabled):hover {
    border-color: #40a9ff;
    box-shadow: 0 6px 20px rgba(24, 144, 255, 0.15);
    transform: translateY(-2px);
    background: #fafcff;
}

.account-item.selected {
    background: linear-gradient(135deg, #f0f7ff 0%, #e6f7ff 100%);
    border-color: #1890ff;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
    transform: translateY(-1px);
}

.account-item.selected .account-realname {
    background-color: rgba(24, 144, 255, 0.15);
    color: #1890ff;
    font-weight: 500;
}

.account-item.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f8f8f8;
    border-color: #e0e0e0;
}

.account-item input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.account-item label {
    display: block;
    padding: 12px;
    cursor: pointer;
    margin: 0;
}

.account-item.disabled label {
    cursor: not-allowed;
}

.account-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.account-user-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.account-username {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: 0.3px;
    margin-bottom: 1px;
}

.account-realname {
    font-size: 12px;
    color: #666;
    background-color: #f5f5f5;
    padding: 2px 8px;
    border-radius: 12px;
    display: inline-flex;
    align-items: center;
    font-weight: 500;
    transition: all 0.2s ease;
}

.account-realname::before {
    content: '👤';
    margin-right: 4px;
    font-size: 10px;
}

.account-status {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.status-active {
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
    color: #389e0d;
    border: 1px solid #95de64;
}

.status-active::before {
    content: '✓';
    font-size: 10px;
    color: #389e0d;
    font-weight: bold;
}

.status-inactive {
    background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
    color: #cf1322;
    border: 1px solid #ff7875;
}

.status-inactive::before {
    content: '✕';
    font-size: 10px;
    color: #cf1322;
    font-weight: bold;
}

.account-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    padding-top: 8px;
    margin-top: 8px;
    border-top: 1px solid rgba(24, 144, 255, 0.1);
}

.action-tag {
    font-size: 12px;
    padding: 6px 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 20px;
    color: #495057;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.action-tag.disabled {
    background: #f8f9fa;
    border-color: #e9ecef;
    color: #adb5bd;
    cursor: not-allowed;
    opacity: 0.7;
}

.action-tag:not(.disabled):hover {
    background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
    border-color: #40a9ff;
    color: #1890ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.account-item:not(.disabled):hover .action-tag {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

.account-item.selected .action-tag {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

/* 优化模态框样式 */
.modal-content {
    max-width: 800px;
    margin: 3% auto;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border: none;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #f8fbff 0%, #e6f7ff 100%);
    border-bottom: 2px solid rgba(24, 144, 255, 0.1);
    padding: 20px 24px;
}

.modal-title {
    font-size: 20px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-title::before {
    content: '🚀';
    font-size: 18px;
}

.modal-body {
    max-height: 75vh;
    overflow-y: auto;
    padding: 24px;
    background: #fafbfc;
}

.modal-footer {
    background: white;
    border-top: 2px solid rgba(24, 144, 255, 0.1);
    padding: 20px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 添加新的样式 */
.account-item input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.account-item label::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 24px;
    width: 20px;
    height: 20px;
    border: 2px solid #d9d9d9;
    border-radius: 6px;
    background-color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.account-item.selected label::before {
    background-color: #1890ff;
    border-color: #1890ff;
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

.account-item.selected label::after {
    content: '';
    position: absolute;
    left: 26px;
    top: 29px;
    transform: rotate(45deg);
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 3px 3px 0;
}

.account-item label {
    padding-left: 56px;
}

.account-item.disabled label::before {
    background-color: #f5f5f5;
    border-color: #e0e0e0;
    cursor: not-allowed;
    box-shadow: none;
}

/* 模态框按钮样式 */
.modal-footer .btn {
    min-width: 100px;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.modal-footer .btn-secondary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modal-footer .btn-secondary:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-footer .btn-primary {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.modal-footer .btn-primary:hover {
    background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
}

.modal-footer .btn:active {
    transform: translateY(0);
}

/* 添加强制警告弹窗样式 */
.warning-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.7);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.warning-modal.show {
    opacity: 1;
}

.warning-content {
    background-color: #fff1f0;
    border: 2px solid #ff4d4f;
    margin: 15% auto;
    padding: 30px;
    width: 80%;
    max-width: 500px;
    border-radius: 8px;
    text-align: center;
    position: relative;
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.warning-modal.show .warning-content {
    transform: translateY(0);
    opacity: 1;
}

.warning-icon {
    color: #ff4d4f;
    font-size: 48px;
    margin-bottom: 20px;
}

.warning-title {
    color: #ff4d4f;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
}

.warning-message {
    color: #cf1322;
    font-size: 16px;
    margin-bottom: 25px;
    line-height: 1.6;
}

.warning-list {
    text-align: left;
    margin: 0 auto;
    max-width: 90%;
    margin-bottom: 25px;
}

.warning-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background: rgba(255, 77, 79, 0.1);
    padding: 12px 15px;
    border-radius: 4px;
    border-left: 4px solid #ff4d4f;
}

.warning-item:last-child {
    margin-bottom: 0;
}

.warning-item::before {
    content: "⚠️";
    margin-right: 10px;
    font-size: 20px;
}

/* 警告弹窗复选框样式 */
.warning-checkbox-container {
    margin: 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.warning-checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    user-select: none;
}

.warning-checkbox {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    cursor: pointer;
    accent-color: #ff4d4f;
}

.warning-checkbox-text {
    font-weight: 500;
}

.warning-buttons {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.warning-confirm-btn {
    background-color: #ff4d4f;
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    min-width: 160px;
}

.warning-confirm-btn:hover {
    background-color: #ff7875;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
}

/* 帖子列表模态框样式 */
.post-modal-content {
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    margin: 20px auto;
    overflow: hidden;
}

.modal-body {
    flex: 1;
    overflow-y: auto;
    max-height: calc(80vh - 130px);
    padding: 15px;
}

.modal-footer {
    position: sticky;
    bottom: 0;
    background-color: white;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    margin-top: 0;
    padding: 15px;
    border-top: 1px solid #eee;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.posts-count-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 15px;
    background-color: #f9f9f9;
    border-radius: 6px;
    position: sticky;
    top: 0;
    z-index: 5;
}

.count-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    background-color: #e6f7ff;
    border-radius: 4px;
    font-weight: 500;
    color: #1890ff;
    border: 1px solid #91d5ff;
}

.count-badge.full {
    background-color: #fff2f0;
    color: #ff4d4f;
    border-color: #ffccc7;
}

.limit-warning {
    display: flex;
    align-items: center;
    color: #ff4d4f;
    font-size: 14px;
}

.warning-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background-color: #ff4d4f;
    color: white;
    border-radius: 50%;
    margin-right: 8px;
    font-style: normal;
    font-weight: bold;
}

.posts-loading, .posts-error {
    padding: 30px 0;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;
}

.error-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #ff4d4f;
    color: white;
    border-radius: 50%;
    margin-bottom: 15px;
    font-style: normal;
    font-weight: bold;
    font-size: 24px;
}

.posts-list {
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.no-posts {
    text-align: center;
    padding: 30px 20px;
    font-size: 15px;
    color: #8c8c8c;
    background-color: #fafafa;
    border-radius: 8px;
    border: 1px dashed #d9d9d9;
    margin: 20px 0;
}

/* 自定义滚动条样式 */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #aaa;
}

/* 优化后的帖子项样式 */
.post-item {
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 15px;
    background-color: white;
    transition: all 0.3s ease;
    animation: fadeInUp 0.3s forwards;
    opacity: 0;
    transform: translateY(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin: 0;
}

.post-item:hover {
    border-color: #d0d0d0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.post-item.removed {
    background-color: #f5f5f5;
    opacity: 0.7;
    border-style: dashed;
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.post-title {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
    flex: 1;
    margin-right: 15px;
    line-height: 1.4;
}

.post-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.remove-post-btn {
    padding: 4px 10px;
    background-color: #ff4d4f;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.3s ease;
}

.remove-post-btn:hover {
    background-color: #ff7875;
}

.remove-post-btn:disabled {
    background-color: #d9d9d9;
    cursor: not-allowed;
    opacity: 0.8;
}

.remove-post-btn.loading::after {
    content: "";
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
    margin-left: 6px;
    vertical-align: middle;
}

.view-post-btn {
    padding: 4px 10px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.view-post-btn:hover {
    background-color: #40a9ff;
}

.view-post-btn i {
    margin-right: 4px;
    font-size: 12px;
}

.post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 10px;
    font-size: 13px;
    color: #8c8c8c;
}

.post-id, .post-sid, .post-time {
    display: inline-flex;
    align-items: center;
    background-color: #f5f5f5;
    padding: 3px 8px;
    border-radius: 4px;
}

.post-info {
    margin-bottom: 12px;
}

.post-content {
    background-color: #fafafa;
    padding: 10px 12px;
    border-radius: 4px;
    font-size: 14px;
    color: #595959;
    margin-bottom: 10px;
    border-left: 3px solid #1890ff;
}

.post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
}

.post-tag {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
    border-radius: 4px;
    padding: 2px 8px;
    font-size: 12px;
}

.post-stats {
    display: flex;
    gap: 15px;
    padding-top: 10px;
    border-top: 1px dashed #f0f0f0;
}

.stat-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #8c8c8c;
}

.stat-item i {
    margin-right: 5px;
    color: #1890ff;
}

.stat-value {
    color: #262626;
    font-weight: 500;
    margin-left: 3px;
}

.footer-info {
    flex: 1;
    text-align: left;
    display: flex;
    align-items: center;
}

.footer-hint {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    color: #8c8c8c;
}

.footer-hint i {
    margin-right: 6px;
    color: #1890ff;
}

.footer-hint.warning i {
    color: #ff4d4f;
}

.footer-hint.warning {
    color: #ff4d4f;
}

.footer-buttons {
    display: flex;
    gap: 10px;
}

.warning-btn {
    background-color: #fff2f0 !important;
    color: #ff4d4f !important;
    border: 1px solid #ff4d4f !important;
    cursor: not-allowed !important;
    opacity: 0.9 !important;
}

.warning-btn:hover {
    background-color: #fff2f0 !important;
    color: #ff4d4f !important;
    border: 1px solid #ff4d4f !important;
}

.close {
    transition: all 0.3s ease;
}

/* 移动设备适配 */
@media (max-width: 768px) {
    .post-modal-content {
        width: 95%;
        margin: 10px auto;
        max-height: 95vh;
    }
    
    .modal-body {
        max-height: calc(95vh - 140px);
        padding: 10px;
    }
    
    .modal-header {
        padding: 10px;
    }
    
    .modal-footer {
        padding: 10px;
        flex-direction: column;
        gap: 10px;
    }
    
    .footer-info {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .footer-buttons {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }
    
    .footer-buttons button {
        flex: 1;
        margin: 0 5px;
    }
} 