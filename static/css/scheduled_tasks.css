/* 定时任务管理页面样式 */

/* 图标彩色化样式 */
.icon-blue { color: #1890ff !important; }
.icon-green { color: #52c41a !important; }
.icon-orange { color: #fa8c16 !important; }
.icon-purple { color: #722ed1 !important; }
.icon-cyan { color: #13c2c2 !important; }
.icon-red { color: #f5222d !important; }
.icon-gold { color: #faad14 !important; }
.icon-magenta { color: #eb2f96 !important; }

/* 表单错误样式 */
.form-error {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 5px;
}

input.error,
select.error,
textarea.error {
    border-color: #ff4d4f !important;
    background-color: #fff2f0;
}

/* 状态徽章样式 */
.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: bold;
}

.status-badge.success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
}

.status-badge.warning {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    color: #faad14;
}

.status-badge.error {
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    color: #ff4d4f;
}

/* 全局加载指示器样式 */
.global-loading-indicator {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.global-loading-indicator.active {
    display: flex;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast提示样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
}

.toast {
    padding: 12px 20px;
    margin-bottom: 10px;
    border-radius: 4px;
    color: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateX(120%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background-color: #52c41a;
}

.toast.error {
    background-color: #f5222d;
}

.toast.info {
    background-color: #1890ff;
}

.toast.warning {
    background-color: #faad14;
}

/* 顶部进度条样式 */
#topProgressBar {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    width: 0%;
    background: linear-gradient(to right, #108ee9, #1890ff);
    z-index: 9999;
    opacity: 0;
    transition: width 0.4s ease, opacity 0.3s ease;
}

/* 任务类型徽章样式 */
.task-type-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: bold;
}

.task-type-badge.shell {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    color: #1890ff;
}

.task-type-badge.python {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
}

/* 美化频率设置样式 */
.frequency-settings {
    border: 1px solid rgba(102, 126, 234, 0.2);
    padding: 20px;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    margin-top: 12px;
    position: relative;
    overflow: hidden;
}

.frequency-settings::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.frequency-row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 12px;
}

.frequency-row:last-child {
    margin-bottom: 0;
}

.frequency-row .form-group {
    flex: 1;
    min-width: 140px;
    margin-bottom: 0;
}

.cron-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.cron-input-group input {
    flex: 1;
    min-width: 300px; /* 进一步增加最小宽度 */
    font-family: 'Courier New', monospace; /* 使用等宽字体便于阅读cron表达式 */
}

#editCronBtn {
    background-color: #f0f0f0;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-height: 40px;
}

#editCronBtn:hover {
    background-color: #e6e6e6;
    border-color: #bfbfbf;
}

/* 修复后的任务类型单选框样式 */
.task-type-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 20px 0;
    width: 100%;
}

.task-type-option {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 20px 16px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    background: #ffffff;
    transition: all 0.3s ease;
    user-select: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    min-height: 90px;
}

.task-type-option:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #d0d7de;
}

.task-type-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

/* 简化的选中状态样式 */
.task-type-option input[type="radio"]:checked + .checkmark + .task-icon {
    background: #1890ff;
    color: white;
}

.task-type-option input[type="radio"]:checked ~ .task-label {
    color: #1890ff;
    font-weight: 600;
}

.task-type-option:has(input[type="radio"]:checked) {
    border-color: #1890ff;
    background: #f0f8ff;
}

/* Python脚本特殊样式 */
.task-type-option:last-child input[type="radio"]:checked + .checkmark + .task-icon {
    background: #52c41a;
    color: white;
}

.task-type-option:last-child input[type="radio"]:checked ~ .task-label {
    color: #52c41a;
    font-weight: 600;
}

.task-type-option:last-child:has(input[type="radio"]:checked) {
    border-color: #52c41a;
    background: #f6ffed;
}

.task-type-option .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    background: transparent;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    z-index: 0;
}

.task-icon {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #666;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    z-index: 2;
}

.task-label {
    position: relative;
    z-index: 2;
    font-weight: 500;
    color: #333;
    font-size: 13px;
    transition: all 0.3s ease;
    text-align: center;
}

/* 优化后的响应式设计 */
@media (max-width: 1024px) {
    .task-modal {
        width: 80%;
        max-width: none;
        min-width: 480px;
    }
}

@media (max-width: 768px) {
    .task-modal {
        width: 90%;
        max-width: none;
        min-width: 400px;
    }

    .modal-body {
        padding: 20px 24px;
    }

    .modal-header {
        padding: 16px 24px;
    }

    .modal-footer {
        padding: 16px 24px;
    }

    .frequency-row {
        flex-direction: column;
        gap: 12px;
    }

    .frequency-row .form-group {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .task-modal {
        width: 95%;
        min-width: auto;
        margin: 0;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-height: 90vh;
    }

    .modal-body {
        padding: 12px;
        max-height: calc(100vh - 140px);
    }

    .modal-header {
        padding: 12px 16px;
    }

    .modal-footer {
        padding: 12px 16px;
        flex-direction: column;
    }

    .modal-footer button {
        width: 100%;
        margin-bottom: 8px;
    }

    .modal-footer button:last-child {
        margin-bottom: 0;
    }

    .task-type-selector {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .task-type-option {
        flex-direction: row;
        justify-content: flex-start;
        padding: 12px 16px;
        min-height: 60px;
    }

    .task-icon {
        width: 28px;
        height: 28px;
        font-size: 14px;
        margin-bottom: 0;
        margin-right: 10px;
    }

    .task-label {
        text-align: left;
        font-size: 11px;
    }
}

/* 额外的样式 */
#areaFilter:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
}

#applyFilterBtn:hover {
    background-color: #40a9ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.35);
}

#resetFilterBtn:hover {
    background-color: #f0f0f0;
    border-color: #d0d0d0;
}

.city-card {
    transition: all 0.3s;
}

.city-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.pagination-btn:hover {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn:disabled:hover {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: #595959;
}

/* 确保导航栏在页面加载时立即显示 */
nav ul li {
    opacity: 1 !important;
    transform: none !important;
}

/* 预加载字体图标 */
.fas {
    display: inline-block;
}

/* 新增的样式类 */
.filter-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.filter-controls label {
    font-weight: 500;
    color: #333;
}

.filter-controls select {
    width: 200px;
}

.filter-controls button {
    padding: 8px 16px;
}

.spider-data-section {
    margin-top: 30px;
}

.filter-form {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.filter-input-group {
    display: flex;
    align-items: center;
}

.input-wrapper {
    position: relative;
    flex: 1;
}

.input-wrapper input {
    height: 42px;
    padding-left: 15px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s;
    width: 100%;
    box-shadow: none;
    font-size: 14px;
}

.button-group {
    display: flex;
    margin-left: 15px;
}

.button-group button {
    height: 42px;
    padding: 0 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.button-group button:first-child {
    background-color: #1890ff;
    border: none;
}

.button-group button:last-child {
    margin-left: 10px;
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    color: #595959;
}

.cities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.pagination-controls {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    align-items: center;
}

.pagination-btn {
    height: 36px;
    width: 36px;
    padding: 0;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    color: #595959;
    cursor: pointer;
}

.pagination-btn:first-child {
    margin-right: 10px;
}

.pagination-btn:last-child {
    margin-left: 10px;
}

.pagination-info {
    font-size: 14px;
    color: #666;
}

/* 美化后的模态框样式 */
.task-modal {
    width: 70%;
    max-width: 700px;
    min-width: 550px;
    padding: 0;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.05);
    margin: 0;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.95);
    z-index: 1000;
    background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
    overflow: visible;
    max-height: 90vh;
    animation: modalFadeIn 0.3s ease-out forwards;
}

/* 模态框动画 */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.task-modal::backdrop {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.6) 100%);
    backdrop-filter: blur(8px);
    animation: backdropFadeIn 0.3s ease-out;
}

@keyframes backdropFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(8px);
    }
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    border-radius: 16px 16px 0 0;
    position: relative;
    overflow: hidden;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    position: relative;
    z-index: 1;
}

.modal-close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 18px;
    cursor: pointer;
    color: #ffffff;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px);
}

.modal-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.modal-body {
    padding: 24px 32px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    padding: 24px 32px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid rgba(0,0,0,0.05);
    border-radius: 0 0 16px 16px;
    position: relative;
}

.modal-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.5) 50%, transparent 100%);
}

.task-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: flex;
    flex-direction: column;
}

.form-row .form-group {
    margin-bottom: 0;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    min-height: 44px;
    box-sizing: border-box;
    position: relative;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: #ffffff;
    transform: translateY(-1px);
}

.form-text {
    font-size: 12px;
    color: #666;
    margin-top: 6px;
    display: block;
}

/* 美化按钮样式 */
.btn-primary, .btn-secondary {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
    min-height: 44px;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-secondary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    border-color: #adb5bd;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
