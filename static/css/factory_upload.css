/* 基础表单样式 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #262626;
    font-size: 14px;
    font-weight: 500;
}

/* 输入框和选择框通用样式 */
.form-group input:not([type="file"]),
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    color: #262626;
    background-color: white;
    transition: all 0.3s;
    outline: none;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.016);
}

/* 选择框特殊样式 */
.form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L5 5L9 1' stroke='%23595959' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 32px;
}

/* 文件上传输入框样式 */
.form-group input[type="file"] {
    display: none;
}

.form-group .file-upload-label {
    display: inline-block;
    padding: 8px 16px;
    background-color: #f5f5f5;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    width: 100%;
    text-align: center;
    color: #595959;
}

.form-group .file-upload-label:hover {
    border-color: #40a9ff;
    color: #40a9ff;
    background-color: #e6f7ff;
}

.form-group .file-name {
    margin-top: 8px;
    font-size: 14px;
    color: #595959;
}

/* 悬停和焦点状态 */
.form-group input:not([type="file"]):hover,
.form-group select:hover {
    border-color: #40a9ff;
}

.form-group input:not([type="file"]):focus,
.form-group select:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
button {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1.5;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

button:not(.secondary) {
    background-color: #1890ff;
    color: white;
}

button:not(.secondary):hover {
    background-color: #40a9ff;
}

button.secondary {
    background-color: white;
    border: 1px solid #d9d9d9;
    color: #595959;
}

button.secondary:hover {
    color: #40a9ff;
    border-color: #40a9ff;
}

button:disabled {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: #bfbfbf;
    cursor: not-allowed;
}

/* 表单区域样式 */
#uploadForm {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.form-row {
    display: flex;
    gap: 24px;
    align-items: flex-end;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-hint {
    margin-top: 8px;
    font-size: 12px;
    color: #8c8c8c;
}

/* 预览区域样式 */
#previewArea {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    margin-top: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.info-box {
    background-color: #f5f5f5;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
}

.info-box h4 {
    margin: 0 0 12px 0;
    color: #262626;
}

.info-box ul {
    margin: 0;
    padding-left: 20px;
    color: #595959;
}

.info-box .highlight {
    color: #1890ff;
    font-weight: 500;
}

/* 表格样式 */
.table-container {
    margin-top: 16px;
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

th {
    background-color: #fafafa;
    font-weight: 500;
}

.highlight-row {
    background-color: #e6f7ff;
}

.required-mark {
    color: #ff4d4f;
    margin-right: 4px;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 16px;
    }

    .form-group {
        width: 100%;
    }

    .actions {
        flex-direction: column;
        gap: 12px;
    }

    .actions button {
        width: 100%;
    }
}

/* 确保导航栏在页面加载时立即显示 */
nav ul li {
    opacity: 1 !important;
    transform: none !important;
}

/* 预加载字体图标 */
.fas {
    display: inline-block;
}

/* 上传页面特定样式 */
.info-box {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

/* 性能开关样式 */
.performance-toggle {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    align-items: center;
    z-index: 10;
}

.toggle-label {
    margin-left: 8px;
    font-size: 12px;
    color: #666;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .3s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .3s;
}

input:checked + .slider {
    background-color: #1890ff;
}

input:focus + .slider {
    box-shadow: 0 0 1px #1890ff;
}

input:checked + .slider:before {
    transform: translateX(20px);
}

.slider.round {
    border-radius: 20px;
}

.slider.round:before {
    border-radius: 50%;
}

.info-box h4 {
    margin-top: 0;
    color: #1890ff;
}

.highlight {
    color: #ff4d4f;
    font-weight: bold;
}

.required-mark {
    color: #ff4d4f;
    font-weight: bold;
}

.highlight-row {
    background-color: #fffbe6;
}

.result-section {
    margin-bottom: 20px;
}

.result-section h4 {
    margin-bottom: 10px;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.form-hint {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

/* 新增样式 - 流程指引 */
.section-header {
    margin-bottom: 30px;
}

.section-header h2 {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.section-header h2 i {
    margin-right: 10px;
    color: #1890ff;
}

.upload-process-flow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 24px;
}

.process-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    flex: 1;
}

.step-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #1890ff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    position: relative;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
    transition: all 0.3s ease;
}

.step-circle span {
    font-size: 20px;
    font-weight: bold;
    z-index: 1;
}

.step-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    opacity: 0;
    transition: all 0.3s ease;
}

.process-step:hover .step-circle span {
    opacity: 0;
}

.process-step:hover .step-icon {
    opacity: 1;
}

.step-title {
    font-weight: 500;
    color: #262626;
    margin-bottom: 5px;
}

.step-desc {
    font-size: 12px;
    color: #8c8c8c;
    max-width: 120px;
}

.step-connector {
    height: 2px;
    background-color: #e8e8e8;
    flex: 1;
    margin: 0 10px;
    position: relative;
    max-width: 50px;
}

.step-connector i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #1890ff;
    font-size: 14px;
}

/* 新增样式 - 文件上传区域 */
.file-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 20px;
    text-align: center;
}

.upload-icon-container {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(24, 144, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

.upload-icon-container i {
    font-size: 36px;
    color: #1890ff;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
    }
}

.file-upload-container h3 {
    margin-bottom: 10px;
    color: #262626;
}

.upload-description {
    color: #8c8c8c;
    margin-bottom: 30px;
    max-width: 400px;
}

.file-upload-label {
    display: flex !important;
    align-items: center;
    justify-content: center;
    padding: 15px 30px !important;
    border: 2px dashed #d9d9d9 !important;
    border-radius: 8px !important;
    background-color: #fafafa !important;
    transition: all 0.3s ease;
    cursor: pointer;
    width: 100%;
    max-width: 400px;
}

.file-upload-label i {
    font-size: 24px;
    margin-right: 10px;
    color: #1890ff;
}

.file-upload-label:hover {
    border-color: #1890ff !important;
    background-color: rgba(24, 144, 255, 0.05) !important;
}

/* 新增样式 - 预览区域 */
.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 15px;
}

.preview-header h3 {
    display: flex;
    align-items: center;
    margin: 0;
}

.preview-header h3 i {
    margin-right: 10px;
    color: #1890ff;
}

.preview-info {
    color: #8c8c8c;
    font-size: 14px;
}

.preview-info i {
    color: #faad14;
    margin-right: 5px;
}

/* 新增样式 - 结果区域 */
.result-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 15px;
}

.result-header h3 {
    display: flex;
    align-items: center;
    margin: 0;
}

.result-header h3 i {
    margin-right: 10px;
    color: #52c41a;
}

.result-actions {
    margin-top: 30px;
    display: flex;
    justify-content: flex-end;
}

/* 按钮样式增强 */
.primary-button {
    background-color: #1890ff;
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-weight: 500;
}

.primary-button i {
    margin-right: 8px;
}

.primary-button:hover {
    background-color: #40a9ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.secondary-button {
    background-color: white;
    color: #595959;
    padding: 10px 20px;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: 1px solid #d9d9d9;
    cursor: pointer;
    font-weight: 500;
    margin-left: 10px;
}

.secondary-button i {
    margin-right: 8px;
}

.secondary-button:hover {
    color: #1890ff;
    border-color: #1890ff;
}

.actions {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

/* 新增样式 - 结果区域增强 */
.result-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.result-stat {
    padding: 12px 15px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 200px;
}

.result-stat i {
    font-size: 20px;
    margin-right: 10px;
}

.result-stat span {
    font-weight: bold;
    margin: 0 5px;
}

.id-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.id-item {
    background-color: #f5f5f5;
    padding: 8px 12px;
    border-radius: 6px;
    display: flex;
    align-items: center;
}

.id-item i {
    color: #52c41a;
    margin-right: 8px;
}

.result-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.result-table th {
    background-color: #fafafa;
    padding: 12px;
    text-align: left;
    font-weight: 500;
    border-bottom: 1px solid #f0f0f0;
}

.result-table td {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.error-icon {
    font-size: 48px;
    color: #ff4d4f;
    text-align: center;
    margin: 20px 0;
}

/* 步骤指示器状态样式 */
.process-step.active .step-circle {
    background-color: #1890ff;
    box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.2);
}

.process-step.completed .step-circle {
    background-color: #52c41a;
}

.process-step.completed .step-circle:after {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
}

.process-step.completed .step-circle span {
    opacity: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .upload-process-flow {
        flex-direction: column;
        padding: 15px;
    }

    .process-step {
        margin-bottom: 20px;
        width: 100%;
    }

    .step-connector {
        width: 2px;
        height: 20px;
        margin: 10px 0;
    }

    .preview-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .preview-info {
        margin-top: 10px;
    }

    .result-summary {
        flex-direction: column;
    }

    .id-list {
        flex-direction: column;
    }
}

/* 图标彩色化样式 */
.icon-blue { color: #1890ff !important; }
.icon-green { color: #52c41a !important; }
.icon-orange { color: #fa8c16 !important; }
.icon-purple { color: #722ed1 !important; }
.icon-cyan { color: #13c2c2 !important; }
.icon-red { color: #f5222d !important; }
.icon-gold { color: #faad14 !important; }
.icon-magenta { color: #eb2f96 !important; }

/* 已导入房源数据样式 */
.imported-data-section {
    margin-top: 40px;
    border-top: 1px dashed #ddd;
    padding-top: 30px;
}

.imported-data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.imported-data-header h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.data-refresh-btn {
    background-color: #f0f5ff;
    border: 1px solid #91d5ff;
    color: #1890ff;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.data-refresh-btn:hover {
    background-color: #e6f7ff;
}

.summary-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.summary-card {
    flex: 1;
    min-width: 200px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.card-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.card-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

.card-footer {
    font-size: 13px;
    color: #999;
    margin-top: auto;
}

.community-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.community-table th {
    background-color: #f5f7fa;
    padding: 12px 15px;
    text-align: left;
    border-bottom: 2px solid #e8e8e8;
}

.community-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e8e8e8;
}

.community-table tr:hover {
    background-color: #f9f9f9;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 12px;
    gap: 5px;
    white-space: nowrap;
}

.status-complete {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
}

.status-pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.status-progress {
    background-color: #f0f5ff;
    color: #2f54eb;
    border: 1px solid #adc6ff;
}

.status-error {
    background-color: #fff1f0;
    color: #f5222d;
    border: 1px solid #ffa39e;
}

.action-btn {
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    border: none;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s;
    font-weight: 500;
    min-width: 80px;
    justify-content: center;
}

.generate-btn {
    background-color: #1890ff;
    color: white;
    border: 1px solid #1890ff;
}

.generate-btn:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.view-btn {
    background-color: #f0f5ff;
    color: #1890ff;
    border: 1px solid #d6e4ff;
}

.view-btn:hover {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.publish-btn {
    background-color: #52c41a;
    color: white;
    border: 1px solid #52c41a;
}

.publish-btn:hover {
    background-color: #73d13d;
    border-color: #73d13d;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.generate-all-btn {
    background-color: #52c41a;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    margin-top: 20px;
}

.generate-all-btn:hover {
    background-color: #389e0d;
}

.empty-data {
    padding: 30px;
    text-align: center;
    background-color: #f9f9f9;
    border-radius: 8px;
    margin-top: 20px;
}

.empty-data i {
    font-size: 40px;
    color: #d9d9d9;
    margin-bottom: 15px;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.loading-spinner i {
    font-size: 30px;
    color: #1890ff;
    margin-bottom: 15px;
}

/* 分页组件样式 */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 5px;
}

.pagination-btn {
    min-width: 32px;
    height: 32px;
    padding: 0 8px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
}

.pagination-btn:hover {
    color: #1890ff;
    border-color: #1890ff;
}

.pagination-btn.active {
    color: #fff;
    background-color: #1890ff;
    border-color: #1890ff;
}

.pagination-btn.disabled {
    color: #d9d9d9;
    cursor: not-allowed;
}

.pagination-btn.disabled:hover {
    color: #d9d9d9;
    border-color: #d9d9d9;
}

.pagination-info {
    margin-left: 20px;
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-container {
    background-color: white;
    border-radius: 8px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.modal-close-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px;
    color: #999;
}

.modal-close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
}

.content-section {
    margin-bottom: 20px;
}

.content-section h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.content-section p {
    margin: 0;
    line-height: 1.6;
    color: #666;
}

.content-box {
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
}

/* 进度条样式 */
.progress-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.progress-box {
    background-color: white;
    border-radius: 8px;
    width: 80%;
    max-width: 500px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.progress-box h3 {
    margin: 0 0 15px 0;
    text-align: center;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.progress-bar-container {
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: #1890ff;
    transition: width 0.3s;
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    z-index: 9999;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.message-toast.show {
    transform: translateX(0);
}

.message-toast.hide {
    transform: translateX(120%);
}

.message-icon {
    margin-right: 12px;
    font-size: 20px;
}

.message-success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
}

.message-success .message-icon {
    color: #52c41a;
}

.message-error {
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
}

.message-error .message-icon {
    color: #ff4d4f;
}

.message-info {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
}

.message-info .message-icon {
    color: #1890ff;
}

.message-warning {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
}

.message-warning .message-icon {
    color: #faad14;
}

/* 园区卡片样式 */
.community-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.community-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    padding: 20px;
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.community-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.community-card-header {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.community-card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4;
}

.community-card-location {
    font-size: 13px;
    color: #888;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.community-card-location i {
    font-size: 14px;
    color: #1890ff;
}

.community-card-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.community-card-status {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.community-card-actions {
    margin-top: auto;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    flex-wrap: wrap;
    padding-top: 15px;
}

/* 计数标记 */
.status-count {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    color: white;
    z-index: 1;
}

.status-count-pending {
    background-color: #fa8c16;
}

.status-count-complete {
    background-color: #52c41a;
}

/* 筛选控件样式 */
.filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
}

.filter-selectors {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    flex: 1;
}

.filter-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 140px;
}

.filter-group label {
    font-size: 14px;
    color: #595959;
    white-space: nowrap;
    font-weight: 500;
    min-width: 60px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    min-width: 120px;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.filter-select:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
}

.filter-btn {
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background-color: #f5f5f5;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
    font-weight: 500;
}

.filter-btn:hover {
    background-color: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
}

#applyFilterBtn {
    background-color: #1890ff;
    border-color: #1890ff;
    color: white;
}

#applyFilterBtn:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
}

#resetFilterBtn {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: #595959;
}

#resetFilterBtn:hover {
    background-color: #fafafa;
    color: #ff4d4f;
    border-color: #ff4d4f;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .filter-selectors {
        flex-direction: column;
        gap: 15px;
    }

    .filter-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .filter-group {
        min-width: auto;
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .filter-group label {
        min-width: auto;
        text-align: left;
    }

    .filter-select {
        min-width: auto;
        width: 100%;
    }

    .community-cards {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .community-card-actions {
        justify-content: center;
        gap: 8px;
    }

    .action-btn {
        min-width: 70px;
        font-size: 12px;
        padding: 5px 10px;
    }
}