<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联东AI内容营销托管智能体</title>
    <link rel="icon" href="images/1684755993.png" type="image/png">
    <link rel="shortcut icon" href="images/1684755993.png" type="image/png">
    <link rel="preload" href="css/styles.css" as="style">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- 添加自定义CSS覆盖默认卡片样式 -->
    <link rel="stylesheet" href="css/index.css">

    <style>
        /* 确保导航栏在页面加载时立即显示 */
        nav ul li {
            opacity: 1 !important;
            transform: none !important;
        }

        /* 预加载字体图标 */
        .fas {
            display: inline-block;
        }
        
        /* 图标彩色化样式 */
        .icon-blue { color: #1890ff !important; }
        .icon-green { color: #52c41a !important; }
        .icon-orange { color: #fa8c16 !important; }
        .icon-purple { color: #722ed1 !important; }
        .icon-cyan { color: #13c2c2 !important; }
        .icon-red { color: #f5222d !important; }
        .icon-gold { color: #faad14 !important; }
        .icon-magenta { color: #eb2f96 !important; }

        /* 系统更新按钮样式 */
        .system-update-section {
            margin-top: 20px;
            text-align: center;
        }

        .update-system-btn {
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 16px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .update-system-btn:hover {
            background-color: #40a9ff;
        }

        .update-system-btn:active {
            background-color: #096dd9;
        }

        .update-system-btn:disabled {
            background-color: #d9d9d9;
            cursor: not-allowed;
        }

        .update-result {
            margin-top: 15px;
            padding: 12px;
            border-radius: 4px;
            font-size: 14px;
        }

        .update-result.success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }

        .update-result.error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }

        .update-details {
            margin-top: 10px;
            font-size: 12px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <h1><div class="rotating-logo" aria-label="联东Logo"></div> 联东AI内容营销托管智能体</h1>
                <div class="header-subtitle">智能化房源管理与内容营销平台</div>
            </div>
                        <nav>
                <ul>
                    <li><a href="index.html" class="active nav-home"><i class="fas fa-home icon-blue"></i> 首页</a></li>
                    <li><a href="cities.html" class="nav-cities"><i class="fas fa-city icon-cyan"></i> 城市管理</a></li>
                    <li><a href="ports.html" class="nav-ports"><i class="fas fa-plug icon-green"></i> 端口管理</a></li>
                    <li><a href="status.html" class="nav-status"><i class="fas fa-chart-line icon-magenta"></i> 状态查询</a></li>
                    <li><a href="factory_images_upload.html" class="nav-images"><i class="fas fa-images icon-purple"></i> 园区图库</a></li>
                    <li><a href="factory_upload.html" class="nav-upload"><i class="fas fa-upload icon-orange"></i> 房源入库</a></li>
                    <li><a href="published_data.html" class="nav-published"><i class="fas fa-paper-plane icon-blue"></i> 房源推送</a></li>
                    <li><a href="statistics.html" class="nav-stats"><i class="fas fa-chart-bar icon-gold"></i> 发布统计</a></li>
                    <li><a href="scheduled_tasks.html" class="nav-tasks"><i class="fas fa-clock icon-green"></i> 定时任务</a></li>
                    <li><a href="competitor_analysis.html" class="nav-analysis"><i class="fas fa-search icon-cyan"></i> 竞帖分析</a></li>
                </ul>
            </nav>
        </header>

        <main>
            <div class="welcome-section">
                <div class="welcome-content">
                    <h2>欢迎使用联东AI内容营销托管智能体</h2>
                    <p>本系统提供全方位的房源管理、内容营销和数据分析功能，助力您的业务高效运转。</p>
                </div>
            </div>

            <div class="supported-platforms">
                <h2><i class="fas fa-globe"></i> 支持平台</h2>
                <div class="platforms-container">
                    <div class="platform-section">
                        <h3>当前支持</h3>
                        <div class="platform-cards">
                            <div class="platform-card">
                                <div class="platform-logo">
                                    <img src="images/platforms/58.png" alt="58同城 logo">
                                </div>
                                <h4>58同城</h4>
                                <p>全面支持58同城平台的房源发布与管理</p>
                            </div>
                            <div class="platform-card">
                                <div class="platform-logo">
                                    <img src="images/platforms/anjuke.jpg" alt="安居客 logo">
                                </div>
                                <h4>安居客</h4>
                                <p>全面支持安居客平台的房源发布与管理</p>
                            </div>
                        </div>
                    </div>
                    <div class="platform-section">
                        <h3>即将支持</h3>
                        <div class="platform-cards">
                            <div class="platform-card coming-soon">
                                <div class="platform-logo">
                                    <img src="images/platforms/xiaohongshu.png" alt="小红书 logo">
                                </div>
                                <h4>小红书</h4>
                                <p>即将支持小红书平台的内容营销</p>
                                <div class="coming-soon-badge">开发中</div>
                            </div>
                            <div class="platform-card coming-soon">
                                <div class="platform-logo">
                                    <img src="images/platforms/douyin.jpg" alt="抖音 logo">
                                </div>
                                <h4>抖音</h4>
                                <p>即将支持抖音平台的内容营销</p>
                                <div class="coming-soon-badge">开发中</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard">
                <h2><i class="fas fa-th-large"></i> 管理控制台</h2>
                <div class="card-container">
                    <!-- 添加卡片骨架屏 -->
                    <div id="cardSkeleton" style="display: none;">
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                    </div>
                    <div id="cardContainer">
                        <div class="card" onclick="location.href='cities.html'">
                            <div class="card-icon"><i class="fas fa-city"></i></div>
                            <h3>城市管理</h3>
                            <p>查看和管理所有支持的城市信息，修改城市配置或添加新城市</p>
                            <div class="card-action">管理 <i class="fas fa-arrow-right"></i></div>
                        </div>

                        <div class="card" onclick="location.href='ports.html'">
                            <div class="card-icon"><i class="fas fa-plug"></i></div>
                            <h3>端口管理</h3>
                            <p>查看和管理不同城市的可用端口及账号状态</p>
                            <div class="card-action">管理 <i class="fas fa-arrow-right"></i></div>
                        </div>

                        <div class="card" onclick="location.href='status.html'">
                            <div class="card-icon"><i class="fas fa-chart-line"></i></div>
                            <h3>推送状态</h3>
                            <p>查询已推送房源的状态和详细信息</p>
                            <div class="card-action">查看 <i class="fas fa-arrow-right"></i></div>
                        </div>

                        <div class="card" onclick="location.href='factory_upload.html'">
                            <div class="card-icon"><i class="fas fa-upload"></i></div>
                            <h3>厂房数据上传</h3>
                            <p>通过Excel文件上传厂房数据并发布到优推平台</p>
                            <div class="card-action">上传 <i class="fas fa-arrow-right"></i></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="system-info">
                <h2><i class="fas fa-server"></i> 系统信息</h2>
                <div id="systemInfo">
                    <!-- 添加系统信息骨架屏 -->
                    <div class="skeleton-info">
                        <div class="skeleton-label"></div>
                        <div class="skeleton-input"></div>
                        <div class="skeleton-label"></div>
                        <div class="skeleton-input"></div>
                        <div class="skeleton-label"></div>
                        <div class="skeleton-input"></div>
                    </div>
                </div>
            </div>
        </main>

                <footer>
            <div class="footer-content">
                <p>&copy; 2025 联东AI内容营销托管智能体</p>
                <div class="footer-links">
                    <a href="help.html"><i class="fas fa-book"></i> 帮助文档</a>
                    <a href="https://www.liando.cn/" target="_blank"><i class="fas fa-envelope"></i> 联系我们</a>
                    <a href="#" onclick="updateSystem(); return false;"><i class="fas fa-sync-alt"></i> 系统更新</a>
                </div>
            </div>
        </footer>
    </div>

    <script src="js/main.js"></script>
    <script src="js/loading.js"></script>
    <script>
        // 当页面加载完成时执行
        document.addEventListener('DOMContentLoaded', async () => {
            // 添加加载组件
            addLoadingComponents();

            // 显示加载状态
            showGlobalLoading();
            startProgress();

            try {
                // 显示骨架屏
                document.getElementById('cardContainer').style.display = 'none';
                document.getElementById('cardSkeleton').style.display = 'flex';

                // 设置活动导航项
                setActiveNavItem();

                // 加载系统信息
                await loadSystemInfo();

                // 显示卡片内容
                document.getElementById('cardSkeleton').style.display = 'none';
                const cardContainer = document.getElementById('cardContainer');
                cardContainer.style.display = 'flex';
                cardContainer.style.flexWrap = 'wrap';
                cardContainer.style.gap = '40px';

                // 为卡片添加淡入效果
                document.querySelectorAll('.card').forEach((card, index) => {
                    addFadeInEffect(card, index);
                });
                
                // 为平台卡片添加动画效果
                document.querySelectorAll('.platform-card').forEach((card, index) => {
                    addFadeInEffect(card, index);
                });
            } catch (error) {
                console.error('页面初始化失败:', error);
                showMessage('页面初始化失败，请刷新重试', 'error');
            } finally {
                // 隐藏加载状态
                hideGlobalLoading();
                completeProgress();
            }
        });

        // 添加卡片淡入效果
        function addFadeInEffect(element, index) {
            // 设置延迟时间，每个卡片依次出现
            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
                element.classList.add('animate__fadeInUp');
            }, 100 * index);
        }

        // 加载系统信息
        async function loadSystemInfo() {
            const sysInfoElement = document.getElementById('systemInfo');

            try {
                // 调用API获取系统信息
                const response = await apiRequest('/system-info', 'GET');

                let html = '<div class="system-info-grid">';
                html += `<div class="info-card fade-in-row"><div class="info-icon"><i class="fas fa-code-branch"></i></div><div class="info-content"><div class="info-label">系统版本</div><div class="info-value">${response.version || '未知'}</div></div></div>`;
                html += `<div class="info-card fade-in-row"><div class="info-icon"><i class="fas fa-calendar-alt"></i></div><div class="info-content"><div class="info-label">启动时间</div><div class="info-value">${response.start_time ? formatDate(new Date(response.start_time)) : '未知'}</div></div></div>`;
                html += `<div class="info-card fade-in-row"><div class="info-icon"><i class="fas fa-clock"></i></div><div class="info-content"><div class="info-label">运行时长</div><div class="info-value">${response.uptime || '未知'}</div></div></div>`;
                html += `<div class="info-card fade-in-row"><div class="info-icon"><i class="fas fa-shield-alt"></i></div><div class="info-content"><div class="info-label">API状态</div><div class="info-value"><span class="status-active">所有API均通过安全检测</span></div></div></div>`;
                html += `<div class="info-card fade-in-row"><div class="info-icon"><i class="fas fa-city"></i></div><div class="info-content"><div class="info-label">已配置城市</div><div class="info-value">${response.cities ? response.cities.length : 0}个</div></div></div>`;
                html += '</div>';

                // 添加系统更新按钮
                html += '<div class="system-update-section fade-in-row">';
                html += '<button id="updateSystemBtn" class="update-system-btn"><i class="fas fa-sync-alt"></i> 系统更新</button>';
                html += '<div id="updateResult" class="update-result" style="display:none;"></div>';
                html += '</div>';

                sysInfoElement.innerHTML = html;

                // 为更新按钮添加点击事件
                document.getElementById('updateSystemBtn').addEventListener('click', updateSystem);
            } catch (error) {
                console.error('获取系统信息失败:', error);
                sysInfoElement.innerHTML = '<div class="error fade-in-row">获取系统信息失败</div>';
                showMessage('获取系统信息失败', 'error');
            }
        }

        // 系统更新函数
        async function updateSystem() {
            // 弹出密钥输入对话框
            const adminKey = prompt("请输入管理员密钥:", "");
            if (!adminKey) return; // 用户取消输入

            const updateBtn = document.getElementById('updateSystemBtn');
            const resultDiv = document.getElementById('updateResult');

            try {
                // 更改按钮状态
                updateBtn.disabled = true;
                updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 更新中...';
                resultDiv.style.display = 'none';

                // 调用API更新系统
                const response = await apiRequest('/update-system', 'POST', {
                    admin_key: adminKey
                });

                // 显示更新结果
                resultDiv.style.display = 'block';

                if (response.success) {
                    resultDiv.className = 'update-result success';
                    let resultHtml = `<div><i class="fas fa-check-circle"></i> ${response.message}</div>`;

                    if (response.is_updated) {
                        resultHtml += `<div class="update-details">`;
                        resultHtml += `<div>原版本: ${response.previous_version}</div>`;
                        resultHtml += `<div>新版本: ${response.current_version}</div>`;
                        resultHtml += `<div>Git提交: ${response.current_commit}</div>`;
                        resultHtml += `<div>更新时间: ${formatDate(new Date(response.update_time))}</div>`;
                        resultHtml += `</div>`;
                    }

                    resultDiv.innerHTML = resultHtml;

                    // 如果有更新，3秒后刷新页面
                    if (response.is_updated) {
                        setTimeout(() => {
                            resultDiv.innerHTML += '<div>系统已更新，页面将在3秒后刷新...</div>';
                        }, 1000);

                        setTimeout(() => {
                            window.location.reload();
                        }, 4000);
                    }
                } else {
                    resultDiv.className = 'update-result error';
                    resultDiv.innerHTML = `<div><i class="fas fa-times-circle"></i> 更新失败</div><div>${response.detail || '未知错误'}</div>`;
                }
            } catch (error) {
                console.error('系统更新失败:', error);

                resultDiv.style.display = 'block';
                resultDiv.className = 'update-result error';

                if (error.status === 401) {
                    resultDiv.innerHTML = '<div><i class="fas fa-times-circle"></i> 管理员密钥验证失败</div>';
                } else {
                    resultDiv.innerHTML = `<div><i class="fas fa-times-circle"></i> 系统更新失败</div><div>${error.message || '未知错误'}</div>`;
                }
            } finally {
                // 恢复按钮状态
                updateBtn.disabled = false;
                updateBtn.innerHTML = '<i class="fas fa-sync-alt"></i> 系统更新';
            }
        }
    </script>
</body>
</html>