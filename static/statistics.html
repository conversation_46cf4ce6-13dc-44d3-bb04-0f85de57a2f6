<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联东AI内容营销托管智能体</title>
    <link rel="icon" href="images/1684755993.png" type="image/png">
    <link rel="shortcut icon" href="images/1684755993.png" type="image/png">
        <link rel="preload" href="css/styles.css" as="style">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="js/vendor/echarts.min.js"></script>

    <style>
        /* 确保导航栏在页面加载时立即显示 */
        nav ul li {
            opacity: 1 !important;
            transform: none !important;
        }

        /* 预加载字体图标 */
        .fas {
            display: inline-block;
        }

        /* 图标彩色化样式 */
        .icon-blue { color: #1890ff !important; }
        .icon-green { color: #52c41a !important; }
        .icon-orange { color: #fa8c16 !important; }
        .icon-purple { color: #722ed1 !important; }
        .icon-cyan { color: #13c2c2 !important; }
        .icon-red { color: #f5222d !important; }
        .icon-gold { color: #faad14 !important; }
        .icon-magenta { color: #eb2f96 !important; }
    </style>
</head>
<body>
    <div class="container">
        <header>            <div class="header-content">
                <h1><div class="rotating-logo" aria-label="联东Logo"></div> 联东AI内容营销托管智能体</h1>
                <div class="header-subtitle">智能化房源管理与内容营销平台</div>
            </div>
                        <nav>
                <ul>
                    <li><a href="index.html" class="nav-home"><i class="fas fa-home icon-blue"></i> 首页</a></li>
                    <li><a href="cities.html" class="nav-cities"><i class="fas fa-city icon-cyan"></i> 城市管理</a></li>
                    <li><a href="ports.html" class="nav-ports"><i class="fas fa-plug icon-green"></i> 端口管理</a></li>
                    <li><a href="status.html" class="nav-status"><i class="fas fa-chart-line icon-magenta"></i> 状态查询</a></li>
                    <li><a href="factory_images_upload.html" class="nav-images"><i class="fas fa-images icon-purple"></i> 园区图库</a></li>
                    <li><a href="factory_upload.html" class="nav-upload"><i class="fas fa-upload icon-orange"></i> 房源入库</a></li>
                    <li><a href="published_data.html" class="nav-published"><i class="fas fa-paper-plane icon-blue"></i> 房源推送</a></li>
                    <li><a href="statistics.html" class="active nav-stats"><i class="fas fa-chart-bar icon-gold"></i> 发布统计</a></li>
                    <li><a href="scheduled_tasks.html" class="nav-tasks"><i class="fas fa-clock icon-green"></i> 定时任务</a></li>
                    <li><a href="competitor_analysis.html" class="nav-analysis"><i class="fas fa-search icon-cyan"></i> 竞帖分析</a></li>
                </ul>
            </nav>
        </header>

        <main>
            <div class="content-section">
                <h2>发布统计</h2>
                <div style="width: 80%; margin: 20px auto; background-color: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
                    <div id="publishChart" style="width: 100%; height: 400px;"></div>
                </div>
                <div id="statisticsSummary" style="text-align: center; margin-top: 20px;">
                    <!-- 统计摘要将在这里加载 -->
                </div>
            </div>

            <!-- 新增失败记录表格区域 -->
            <div class="content-section" id="failedRecordsSection" style="display: none;">
                <h2>推送失败记录</h2>
                <div class="table-container">
                    <table id="failedRecordsTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>推送时间</th>
                                <th>城市</th>
                                <th>网站</th>
                                <th>园区名称</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="failedRecordsTableBody">
                            <!-- 失败记录将在这里动态插入 -->
                            <tr>
                                <td colspan="6" style="text-align: center;">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- 失败记录分页控件 -->
                <div id="failurePaginationContainer" class="pagination-container">
                    <!-- 分页控件将在这里动态插入 -->
                </div>
            </div>

            <!-- 新增成功记录表格区域 -->
            <div class="content-section" id="successfulRecordsSection" style="display: none; margin-top: 30px;">
                <h2>推送成功记录</h2>
                <div class="table-container">
                    <table id="successfulRecordsTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>推送时间</th>
                                <th>城市</th>
                                <th>网站</th>
                                <th>园区名称</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="successfulRecordsTableBody">
                            <!-- 成功记录将在这里动态插入 -->
                            <tr>
                                <td colspan="6" style="text-align: center;">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- 成功记录分页控件 -->
                <div id="successPaginationContainer" class="pagination-container">
                    <!-- 分页控件将在这里动态插入 -->
                </div>
            </div>
        </main>

        <footer>
            <div class="footer-content">
                <p>&copy; 2025 联东AI内容营销托管智能体</p>
                <div class="footer-links">
                    <a href="help.html"><i class="fas fa-book"></i> 帮助文档</a>
                    <a href="https://www.liando.cn/" target="_blank"><i class="fas fa-envelope"></i> 联系我们</a>
                    <a href="#" onclick="updateSystem(); return false;"><i class="fas fa-sync-alt"></i> 系统更新</a>
                </div>
            </div>
        </footer>
    </div>



    <!-- 全局加载指示器容器 -->
    <div id="globalLoadingIndicator" style="display: none;">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
        <div class="progress-bar-container">
            <div class="progress-bar"></div>
        </div>
    </div>
    <!-- 消息提示容器 -->
    <div id="messageContainer"></div>


    <script src="js/main.js"></script>
    <script src="js/loading.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            addLoadingComponents();
            showGlobalLoading();
            startProgress();

            try {
                setActiveNavItem();
                await loadPublishStatistics();

                // 启动 SSE 连接
                connectToEventSource();
            } catch (error) {
                console.error('加载发布统计数据失败:', error);
                showMessage('加载统计数据失败，请稍后重试', 'error');
                const chartContainer = document.getElementById('publishChart').parentElement;
                chartContainer.innerHTML = '<p class="error">无法加载统计图表。</p>';
                document.getElementById('statisticsSummary').innerHTML = '<p class="error">无法加载统计摘要。</p>';
                document.getElementById('failedRecordsSection').style.display = 'block'; // 即使出错也显示表格区域
                document.getElementById('failedRecordsTableBody').innerHTML = '<tr><td colspan="6" class="error">无法加载失败记录。</td></tr>';
            } finally {
                hideGlobalLoading();
                completeProgress();
            }
        });

        // 页面卸载时断开 SSE 连接
        window.addEventListener('beforeunload', () => {
            disconnectEventSource();
        });

        async function loadPublishStatistics(successPage = 1, failurePage = 1) {
            // 构建带分页参数的API URL
            const apiUrl = `/api/publish-statistics?success_page=${successPage}&failure_page=${failurePage}`;
            const response = await apiRequest(apiUrl, 'GET');

            if (response && typeof response.success_count === 'number' && typeof response.failure_count === 'number') {
                renderPublishChart(response.success_count, response.failure_count);
                renderStatisticsSummary(response.success_count, response.failure_count);

                // 更新分页状态
                currentSuccessPage = successPage;
                currentFailurePage = failurePage;

                // 新增：渲染失败记录表格（适配新的数据结构）
                if (response.failed_records && response.failed_records.data && Array.isArray(response.failed_records.data)) {
                    failurePagination = response.failed_records.pagination || {};
                    renderFailedRecordsTable(response.failed_records.data);
                    renderFailurePagination();
                } else {
                     console.warn('API响应中缺少 failed_records.data 数组或格式不正确');
                     renderFailedRecordsTable([]); // 传递空数组以显示"无失败记录"
                     renderFailurePagination();
                }

                // 新增：渲染成功记录表格（适配新的数据结构）
                if (response.successful_records && response.successful_records.data && Array.isArray(response.successful_records.data)) {
                    successPagination = response.successful_records.pagination || {};
                    renderSuccessfulRecordsTable(response.successful_records.data);
                    renderSuccessPagination();
                } else {
                     console.warn('API响应中缺少 successful_records.data 数组或格式不正确');
                     renderSuccessfulRecordsTable([]); // 传递空数组以显示"无成功记录"
                     renderSuccessPagination();
                }

            } else {
                 throw new Error('从API获取的统计数据格式无效');
            }
        }

        // 添加一个变量来存储图表实例
        let publishChart = null;

        // 分页状态管理变量
        let currentSuccessPage = 1;
        let currentFailurePage = 1;
        let successPagination = {};
        let failurePagination = {};

        function renderPublishChart(successCount, failureCount) {
            const chartDom = document.getElementById('publishChart');

            // 如果已存在图表实例，先销毁它
            if (publishChart) {
                publishChart.dispose();
            }

            // 初始化ECharts实例
            publishChart = echarts.init(chartDom);

            // 配置ECharts选项
            const option = {
                title: {
                    text: '房源发布成功与失败统计',
                    left: 'center',
                    textStyle: {
                        fontSize: 18,
                        fontWeight: 'bold',
                        color: '#333'
                    },
                    top: 10
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        return params[0].name + '<br/>数量: ' + params[0].value;
                    }
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '15%',
                    top: '20%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['发布成功', '发布失败'],
                    axisLabel: {
                        fontSize: 14,
                        fontWeight: 'bold',
                        color: '#666'
                    },
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '数量',
                    nameTextStyle: {
                        fontSize: 14,
                        fontWeight: 'bold',
                        color: '#666'
                    },
                    axisLabel: {
                        fontSize: 12,
                        color: '#666'
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(200, 200, 200, 0.15)'
                        }
                    }
                },
                series: [{
                    name: '发布数量',
                    type: 'bar',
                    data: [
                        {
                            value: successCount,
                            itemStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0, y: 0, x2: 0, y2: 1,
                                    colorStops: [
                                        { offset: 0, color: 'rgba(34, 197, 94, 0.9)' },
                                        { offset: 1, color: 'rgba(34, 197, 94, 0.5)' }
                                    ]
                                },
                                borderColor: 'rgba(34, 197, 94, 1)',
                                borderWidth: 2
                            }
                        },
                        {
                            value: failureCount,
                            itemStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0, y: 0, x2: 0, y2: 1,
                                    colorStops: [
                                        { offset: 0, color: 'rgba(239, 68, 68, 0.9)' },
                                        { offset: 1, color: 'rgba(239, 68, 68, 0.5)' }
                                    ]
                                },
                                borderColor: 'rgba(239, 68, 68, 1)',
                                borderWidth: 2
                            }
                        }
                    ],
                    barWidth: '60%',
                    itemStyle: {
                        borderRadius: [8, 8, 0, 0]
                    },
                    emphasis: {
                        itemStyle: {
                            color: function(params) {
                                return params.dataIndex === 0 ? 'rgba(34, 197, 94, 1)' : 'rgba(239, 68, 68, 1)';
                            }
                        }
                    }
                }],
                animationDuration: 1500,
                animationEasing: 'cubicOut'
            };

            // 设置图表选项并渲染
            publishChart.setOption(option);

            // 监听窗口大小变化，自动调整图表大小
            window.addEventListener('resize', function() {
                if (publishChart) {
                    publishChart.resize();
                }
            });
        }

         function renderStatisticsSummary(successCount, failureCount) {
            const total = successCount + failureCount;
            const successRate = total > 0 ? ((successCount / total) * 100).toFixed(2) : 0;
            const summaryElement = document.getElementById('statisticsSummary');

            // 更新摘要样式，与图表颜色保持一致
            let html = `<div style="font-size: 16px; padding: 15px; background-color: rgba(250, 250, 250, 0.7); border-radius: 8px; display: inline-block; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);">`;
            html += `<strong style="margin-right: 15px;">总计: <span style="color: #333; font-size: 18px;">${total}</span></strong>`;
            html += `<strong style="margin-right: 15px; color: rgba(34, 197, 94, 1);">成功: <span style="font-size: 18px;">${successCount}</span></strong>`;
            html += `<strong style="margin-right: 15px; color: rgba(239, 68, 68, 1);">失败: <span style="font-size: 18px;">${failureCount}</span></strong>`;
            html += `<strong>成功率: <span style="color: #333; font-size: 18px;">${successRate}%</span></strong>`;
            html += `</div>`;

            summaryElement.innerHTML = html;
        }

        // 新增：渲染失败记录表格的函数
        function renderFailedRecordsTable(records) {
            const tableBody = document.getElementById('failedRecordsTableBody');
            const section = document.getElementById('failedRecordsSection');
            const columnsCount = 6; // 更新列数 (增加了操作列)

            if (!records || records.length === 0) {
                tableBody.innerHTML = `<tr><td colspan="${columnsCount}" style="text-align: center;">没有推送失败的记录。</td></tr>`;
                section.style.display = 'block'; // 保持显示
            } else {
                let tableHtml = '';
                records.forEach(record => {
                    tableHtml += `
                        <tr id="failed-record-${record.id}"> <!-- 添加行 ID -->
                            <td>${escapeHtml(record.id || '')}</td>
                            <td>${escapeHtml(record.push_time ? formatDate(new Date(record.push_time)) : '')}</td>
                            <td>${escapeHtml(record.city || '')}</td>
                            <td>${escapeHtml(record.website_name || '')}</td>
                            <td>${escapeHtml(record.community || 'N/A')}</td>
                            <td>
                                <button class="action-button" onclick="markForPush(${record.id}, this)">转为推送</button>
                                <button class="action-button refresh-button" onclick="markForPush(${record.id}, this)">重新生成</button>
                            </td>
                        </tr>
                    `;
                });
                tableBody.innerHTML = tableHtml;
                section.style.display = 'block'; // 确保区域可见
            }
        }

        // 新增：渲染成功记录表格的函数
        function renderSuccessfulRecordsTable(records) {
            const tableBody = document.getElementById('successfulRecordsTableBody');
            const section = document.getElementById('successfulRecordsSection');
            const columnsCount = 6; // 更新列数为6

            if (!records || records.length === 0) {
                // 更新 colspan
                tableBody.innerHTML = `<tr><td colspan="${columnsCount}" style="text-align: center;">没有推送成功的记录。</td></tr>`;
                section.style.display = 'block'; // 保持显示
            } else {
                let tableHtml = '';
                records.forEach(record => {
                    tableHtml += `
                        <tr>
                            <td>${escapeHtml(record.id || '')}</td>
                            <td>${escapeHtml(record.push_time ? formatDate(new Date(record.push_time)) : '')}</td>
                            <td>${escapeHtml(record.city || '')}</td>
                            <td>${escapeHtml(record.website_name || '')}</td>
                            <td>${escapeHtml(record.community || 'N/A')}</td>
                            <td>
                                <button class="action-button" onclick="markForPush(${record.id}, this)">转为推送</button>
                                <button class="action-button refresh-button" onclick="markForPush(${record.id}, this)">重新生成</button>
                            </td>
                        </tr>
                    `;
                });
                tableBody.innerHTML = tableHtml;
                section.style.display = 'block'; // 确保区域可见
            }
        }

        // -- 新增辅助函数 --
        function escapeHtml(unsafe) {
            if (unsafe === null || unsafe === undefined) {
                return '';
            }
            return unsafe
                 .toString()
                 .replace(/&/g, "&amp;")
                 .replace(/</g, "&lt;")
                 .replace(/>/g, "&gt;")
                 .replace(/"/g, "&quot;")
                 .replace(/'/g, "&#039;");
        }

        function formatDate(date) {
            if (!date || !(date instanceof Date) || isNaN(date)) {
                return '';
            }
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const seconds = date.getSeconds().toString().padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
        // -- 辅助函数结束 --

        // 分页控件渲染函数
        function renderPagination(containerId, pagination, currentPage, onPageChange) {
            const container = document.getElementById(containerId);
            if (!container) return;

            const { total_pages = 1, total_records = 0, has_prev_page = false, has_next_page = false } = pagination;

            // 计算当前显示范围
            const pageSize = 10;
            const startRecord = (currentPage - 1) * pageSize + 1;
            const endRecord = Math.min(currentPage * pageSize, total_records);

            let html = `
                <div class="pagination-info">
                    显示第 ${startRecord}-${endRecord} 条，共 ${total_records} 条记录
                </div>
                <button class="pagination-button" ${!has_prev_page ? 'disabled' : ''}
                        onclick="${onPageChange}(${currentPage - 1})">
                    上一页
                </button>
                <div class="pagination-info">
                    第 <input type="number" class="pagination-input"
                             value="${currentPage}" min="1" max="${total_pages}"
                             onchange="${onPageChange}(parseInt(this.value))"
                             onkeypress="if(event.key==='Enter') ${onPageChange}(parseInt(this.value))">
                    页，共 ${total_pages} 页
                </div>
                <button class="pagination-button" ${!has_next_page ? 'disabled' : ''}
                        onclick="${onPageChange}(${currentPage + 1})">
                    下一页
                </button>
            `;

            container.innerHTML = html;
        }

        // 失败记录分页渲染
        function renderFailurePagination() {
            renderPagination('failurePaginationContainer', failurePagination, currentFailurePage, 'loadFailurePage');
        }

        // 成功记录分页渲染
        function renderSuccessPagination() {
            renderPagination('successPaginationContainer', successPagination, currentSuccessPage, 'loadSuccessPage');
        }

        // 页码切换处理函数
        async function loadSuccessPage(page) {
            if (!page || page < 1 || page > (successPagination.total_pages || 1)) {
                showMessage('页码无效', 'error');
                return;
            }

            showGlobalLoading();
            try {
                await loadPublishStatistics(page, currentFailurePage);
            } catch (error) {
                console.error('加载成功记录页面失败:', error);
                showMessage('加载页面失败，请稍后重试', 'error');
            } finally {
                hideGlobalLoading();
            }
        }

        async function loadFailurePage(page) {
            if (!page || page < 1 || page > (failurePagination.total_pages || 1)) {
                showMessage('页码无效', 'error');
                return;
            }

            showGlobalLoading();
            try {
                await loadPublishStatistics(currentSuccessPage, page);
            } catch (error) {
                console.error('加载失败记录页面失败:', error);
                showMessage('加载页面失败，请稍后重试', 'error');
            } finally {
                hideGlobalLoading();
            }
        }

        // 新增："转为推送"按钮的点击处理函数
        async function markForPush(recordId, buttonElement) {
            // 检查按钮状态，如果是"已生成完成"则不执行任何操作
            if (buttonElement.textContent === '已生成完成') {
                showMessage('内容已生成完成，无需重复操作', 'info');
                return;
            }

            // 从表格行中获取园区名称
            const row = buttonElement.closest('tr');
            const communityCell = row.querySelector('td:nth-child(5)'); // 园区名称在第5列
            const parkName = communityCell.textContent.trim();

            if (!parkName || parkName === 'N/A') {
                showMessage('无法获取园区名称，操作失败', 'error');
                return;
            }

            // 保存原始按钮文本，以便根据不同类型的按钮显示不同的成功状态
            const originalText = buttonElement.textContent;
            const isRefreshButton = originalText === '重新生成';

            // 禁用按钮防止重复点击
            buttonElement.disabled = true;
            buttonElement.textContent = '处理中...';
            showGlobalLoading(); // 显示全局加载指示器

            try {
                const apiUrl = `/api/mark-for-push`;
                const requestData = {
                    park_name: parkName,
                    status: isRefreshButton ? 'success' : 'failed'  // 重新生成用success，转为推送用failed
                };
                const response = await apiRequest(apiUrl, 'POST', requestData);

                if (response.success) {
                    showMessage(response.message || '操作成功！', 'success');

                    // 根据响应消息判断按钮状态
                    if (response.message && response.message.includes('已经是待推送状态')) {
                        // 如果已经是待推送状态，显示相应文本但不变灰
                        buttonElement.textContent = '已是待推送';
                        buttonElement.style.backgroundColor = '#f0f0f0'; // 浅灰色
                        buttonElement.style.color = '#666'; // 深灰色文字
                    } else if (response.message && response.message.includes('正在后台处理中')) {
                        // 如果是后台生成任务，显示相应状态
                        buttonElement.textContent = '后台生成中';
                        buttonElement.style.backgroundColor = '#e3f2fd'; // 浅蓝色
                        buttonElement.style.color = '#1976d2'; // 蓝色文字
                    } else if (response.message && response.message.includes('重置为待推送状态')) {
                        // 如果是重置推送状态
                        buttonElement.textContent = '已转为推送';
                        buttonElement.style.backgroundColor = '#cccccc'; // 变灰
                    } else {
                        // 其他成功情况
                        buttonElement.textContent = isRefreshButton ? '已重新生成' : '已转为推送';
                        buttonElement.style.backgroundColor = '#cccccc'; // 变灰
                    }
                } else {
                    showMessage(response.message || '操作失败', 'error');
                    // 失败时恢复按钮状态
                    buttonElement.disabled = false;
                    buttonElement.textContent = originalText;
                }
            } catch (error) {
                console.error('生成内容操作失败:', error);
                showMessage('操作时发生错误', 'error');
                 // 失败时恢复按钮状态
                buttonElement.disabled = false;
                buttonElement.textContent = originalText;
            } finally {
                hideGlobalLoading(); // 隐藏全局加载指示器
            }
        }

        // SSE 连接相关变量
        let eventSource = null;







        // --- SSE 连接相关函数 ---
        /**
         * 连接到SSE事件流
         */
        function connectToEventSource() {
            // 如果已经有SSE连接，先断开
            disconnectEventSource();

            // 创建一个新的SSE连接
            eventSource = new EventSource('/tasks/events');

            // 监听连接建立事件
            eventSource.addEventListener('connected', function(event) {
                try {
                    const data = JSON.parse(event.data);
                    console.log("SSE连接已建立:", data);
                } catch (error) {
                    console.error("解析SSE连接事件失败:", error);
                }
            });

            // 监听消息事件
            eventSource.addEventListener('message', function(event) {
                try {
                    const eventData = JSON.parse(event.data);
                    console.log("接收到SSE消息:", eventData);

                    if (eventData.type === 'task_update') {
                        // 更新任务状态UI
                        updateTaskStatusUI(eventData.data);
                    }
                } catch (error) {
                    console.error('处理SSE消息失败:', error);
                }
            });

            // 监听ping事件（保持连接）
            eventSource.addEventListener('ping', function(event) {
                console.log("收到ping心跳");
            });

            // 添加错误事件监听器
            eventSource.onerror = function(event) {
                console.error('SSE连接错误:', event);
                // 如果连接断开，尝试重连
                setTimeout(connectToEventSource, 5000);
            };
        }

        /**
         * 断开SSE连接
         */
        function disconnectEventSource() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
        }

        /**
         * 更新任务状态UI
         * @param {Object} tasks - 任务状态数据
         */
        function updateTaskStatusUI(tasks) {
            // 遍历所有任务状态
            for (const [communityName, taskInfo] of Object.entries(tasks)) {
                const status = taskInfo.status;
                const progress = taskInfo.progress || 0;

                // 查找该园区对应的按钮
                const buttons = document.querySelectorAll(`button[onclick*="markForPush"]`);

                buttons.forEach(btn => {
                    // 从按钮所在的表格行获取园区名称
                    const row = btn.closest('tr');
                    if (row) {
                        const communityCell = row.querySelector('td:nth-child(5)');
                        const parkName = communityCell ? communityCell.textContent.trim() : '';

                        if (parkName === communityName) {
                            // 更新按钮状态和文本
                            if (status === 1) { // 进行中
                                btn.disabled = true;
                                btn.textContent = `生成中 ${progress}%`;
                                btn.style.backgroundColor = '#e3f2fd'; // 浅蓝色
                                btn.style.color = '#1976d2'; // 蓝色文字
                            } else if (status === 0) { // 等待中
                                btn.disabled = true;
                                btn.textContent = '等待中...';
                                btn.style.backgroundColor = '#fff3e0'; // 浅橙色
                                btn.style.color = '#f57c00'; // 橙色文字
                            } else if (status === 2) { // 已完成
                                btn.disabled = true;
                                btn.textContent = '已生成完成';
                                btn.style.backgroundColor = '#e8f5e9'; // 浅绿色
                                btn.style.color = '#43a047'; // 绿色文字
                            } else if (status === 3) { // 失败
                                btn.disabled = false;
                                btn.textContent = '生成失败，重试';
                                btn.style.backgroundColor = '#ffebee'; // 浅红色
                                btn.style.color = '#e53935'; // 红色文字
                                if (taskInfo.error_message) {
                                    btn.setAttribute('title', taskInfo.error_message);
                                }
                            }
                        }
                    }
                });
            }
        }
        // --- SSE 连接相关函数结束 ---

    </script>

    <style>
        /* 新增模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal.show {
            opacity: 1;
        }

        .modal-content {
            position: relative;
            background-color: #fff;
            width: 80%;
            max-width: 800px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 25px;
            overflow: hidden;
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }

        .modal.show .modal-content {
            transform: translateY(0);
        }

        .modal-content.large {
            max-width: 900px;
        }

        .modal h2 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.6rem;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .modal-body {
            max-height: 65vh;
            overflow-y: auto;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            scrollbar-width: thin;
            scrollbar-color: #bbb #f0f0f0;
        }

        .modal-body::-webkit-scrollbar {
            width: 8px;
        }

        .modal-body::-webkit-scrollbar-track {
            background: #f0f0f0;
            border-radius: 10px;
        }

        .modal-body::-webkit-scrollbar-thumb {
            background: #bbb;
            border-radius: 10px;
        }

        .close-button {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 28px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            transition: color 0.2s;
            width: 32px;
            height: 32px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
        }

        .close-button:hover {
            color: #2c3e50;
            background-color: #f0f0f0;
        }

        /* 帖子内容卡片样式 */
        .post-card {
            border: 1px solid #eaecef;
            border-radius: 12px;
            margin-bottom: 20px;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: transform 0.2s, box-shadow 0.2s;
            position: relative;
        }

        .post-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        }

        /* 自定义复选框样式 */
        .custom-checkbox {
            position: absolute;
            top: 20px;
            right: 20px;
        }

        .custom-checkbox input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        .checkmark {
            position: relative;
            display: inline-block;
            height: 28px;
            width: 28px;
            background-color: #f8f9fa;
            border: 2px solid #dfe3e8;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .custom-checkbox input[type="checkbox"]:checked ~ .checkmark {
            background-color: #4a90e2;
            border-color: #4a90e2;
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
            left: 9px;
            top: 5px;
            width: 6px;
            height: 12px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .custom-checkbox input[type="checkbox"]:checked ~ .checkmark:after {
            display: block;
        }

        /* 修改为单选按钮样式 */
        .custom-radio {
            position: absolute;
            top: 20px;
            right: 20px;
        }

        .custom-radio input[type="radio"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        .radio-checkmark {
            position: relative;
            display: inline-block;
            height: 28px;
            width: 28px;
            background-color: #f8f9fa;
            border: 2px solid #dfe3e8;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .custom-radio input[type="radio"]:checked ~ .radio-checkmark {
            background-color: #fff;
            border-color: #4a90e2;
            border-width: 2px;
        }

        .radio-checkmark:after {
            content: "";
            position: absolute;
            display: none;
            top: 50%;
            left: 50%;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background-color: #4a90e2;
            transform: translate(-50%, -50%);
        }

        .custom-radio input[type="radio"]:checked ~ .radio-checkmark:after {
            display: block;
        }

        .post-card-content-wrapper {
            padding-right: 40px; /* 为复选框留出空间 */
        }

        .post-card-title {
            font-weight: 600;
            font-size: 1.25rem;
            color: #2c3e50;
            margin-bottom: 12px;
            line-height: 1.3;
        }

        .post-card-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 16px;
        }

        .post-card-meta-item {
            background-color: #eef5ff;
            border-radius: 20px;
            padding: 6px 16px;
            font-size: 0.9rem;
            color: #4a90e2;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
        }

        .post-card-content {
            white-space: pre-line;
            margin-top: 16px;
            padding: 18px;
            background-color: #f9f9fa;
            border-radius: 10px;
            border-left: 4px solid #4a90e2;
            font-size: 1rem;
            line-height: 1.6;
            color: #3c4043;
            box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.03);
        }

        /* 添加已使用帖子的样式 */
        .post-card.used {
            background-color: #f9f9f9;
            border-color: #e0e0e0;
            box-shadow: none;
        }

        .post-card.used .post-card-content-wrapper {
            opacity: 0.75;
        }

        .used-tag {
            position: absolute;
            top: 16px;
            right: 60px;
            background-color: #ff6b6b;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(255, 107, 107, 0.25);
        }

        /* 已使用帖子的复选框样式 */
        .post-card.used .custom-checkbox .checkmark {
            background-color: #e9ecef;
            border-color: #ced4da;
            cursor: not-allowed;
        }

        /* 已使用帖子的单选按钮样式 */
        .post-card.used .custom-radio .radio-checkmark {
            background-color: #e9ecef;
            border-color: #ced4da;
            cursor: not-allowed;
        }

        /* 添加模态框底部和按钮样式 */
        .modal-footer {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eaeaea;
            text-align: center;
        }

        .btn-primary {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 10px 24px;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 6px rgba(74, 144, 226, 0.2);
        }

        .btn-primary:hover {
            background-color: #3a7bc8;
            box-shadow: 0 4px 8px rgba(74, 144, 226, 0.3);
            transform: translateY(-1px);
        }

        .btn-primary:active {
            background-color: #2d69b3;
            box-shadow: 0 1px 3px rgba(74, 144, 226, 0.2);
            transform: translateY(1px);
        }

        .btn-primary:disabled {
            background-color: #a5c6f1;
            cursor: not-allowed;
            box-shadow: none;
        }



        /* 刷新帖子按钮样式 */
        .refresh-button {
            background-color: #ff9800;
            margin-left: 0; /* 移除左边距 */
        }

        .refresh-button:hover {
            background-color: #f57c00;
        }

        /* 操作列样式调整 */
        #successfulRecordsTable td:last-child,
        #failedRecordsTable td:last-child {
            min-width: 200px; /* 确保操作列有足够宽度 */
            white-space: nowrap; /* 防止按钮换行 */
            text-align: center; /* 按钮居中显示 */
        }

        /* 按钮样式调整 */
        .action-button {
            display: inline-block; /* 确保按钮内联显示 */
            padding: 6px 10px; /* 减小按钮内边距使其更紧凑 */
            margin-right: 5px; /* 按钮之间的间距 */
            font-size: 0.9rem; /* 稍微减小字体大小 */
            min-width: 80px; /* 设置最小宽度 */
        }

        /* 分页控件样式 */
        .pagination-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            gap: 10px;
            flex-wrap: wrap;
        }

        .pagination-info {
            font-size: 0.9rem;
            color: #666;
            margin: 0 15px;
        }

        .pagination-button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background-color: #fff;
            color: #333;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
            min-width: 80px;
        }

        .pagination-button:hover:not(:disabled) {
            background-color: #4a90e2;
            color: white;
            border-color: #4a90e2;
        }

        .pagination-button:disabled {
            background-color: #f5f5f5;
            color: #ccc;
            cursor: not-allowed;
            border-color: #e0e0e0;
        }

        .pagination-input {
            width: 60px;
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
            font-size: 0.9rem;
        }

        .pagination-input:focus {
            outline: none;
            border-color: #4a90e2;
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
        }
    </style>
</body>
</html>