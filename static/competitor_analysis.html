<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>联东AI内容营销托管智能体</title>
    <link rel="icon" href="images/1684755993.png" type="image/png">
    <link rel="shortcut icon" href="images/1684755993.png" type="image/png">
        <link rel="preload" href="css/styles.css" as="style">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* 图标彩色化样式 */
        .icon-blue { color: #1890ff !important; }
        .icon-green { color: #52c41a !important; }
        .icon-orange { color: #fa8c16 !important; }
        .icon-purple { color: #722ed1 !important; }
        .icon-cyan { color: #13c2c2 !important; }
        .icon-red { color: #f5222d !important; }
        .icon-gold { color: #faad14 !important; }
        .icon-magenta { color: #eb2f96 !important; }
        
        /* 卡片样式 */
        .analysis-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .analysis-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        .analysis-card-header {
            background-color: #f7f7f7;
            padding: 15px 20px;
            border-bottom: 1px solid #eaeaea;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .analysis-card-title {
            margin: 0;
            font-size: 18px;
            color: #333;
            font-weight: bold;
        }

        .analysis-card-body {
            padding: 20px;
        }

        /* 统计数字样式 */
        .stat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-item {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            border-left: 4px solid #1890ff;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin: 5px 0;
            color: #1890ff;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
        }

        .stat-subtext {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }

        /* 图表容器样式 */
        .chart-container {
            width: 100%;
            height: 350px;
            margin-bottom: 20px;
        }

        /* 表格样式 */
        .competitor-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .competitor-table th,
        .competitor-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eaeaea;
        }

        .competitor-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            color: #333;
        }

        .competitor-table tr:hover {
            background-color: #f7f7f7;
        }

        /* 筛选表单样式 */
        .filter-form {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
        }

        .filter-form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: flex-end;
        }

        .filter-form .form-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-actions {
            display: flex;
            gap: 10px;
        }

        /* 数据比较样式 */
        .data-comparison {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }

        .comparison-item {
            flex: 1;
            min-width: 250px;
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 15px;
            position: relative;
        }

        .comparison-item::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, #1890ff, #52c41a);
        }

        .comparison-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .comparison-value-container {
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }

        .comparison-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }

        .comparison-change {
            font-size: 14px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 5px;
        }

        .comparison-change.positive {
            color: #52c41a;
            background-color: #f6ffed;
        }

        .comparison-change.negative {
            color: #f5222d;
            background-color: #fff1f0;
        }

        /* 热力图样式 */
        .heatmap-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .heatmap-table {
            min-width: 100%;
            border-collapse: collapse;
        }

        .heatmap-table th,
        .heatmap-table td {
            padding: 10px;
            text-align: center;
            border: 1px solid #eaeaea;
        }

        .heatmap-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .heatmap-cell {
            width: 35px;
            height: 35px;
            transition: all 0.3s ease;
        }

        .heatmap-cell:hover {
            transform: scale(1.1);
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
            z-index: 1;
            position: relative;
        }

        /* 竞争对手列表样式 */
        .competitor-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .competitor-item {
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .competitor-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        .competitor-header {
            padding: 15px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #eaeaea;
        }

        .competitor-name {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
        }

        .competitor-content {
            padding: 15px;
        }

        .competitor-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 10px;
        }

        .competitor-stat {
            background-color: #f9f9f9;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 14px;
        }

        .competitor-stat span {
            font-weight: bold;
            color: #1890ff;
        }

        .competitor-actions {
            margin-top: 10px;
            display: flex;
            justify-content: flex-end;
        }

        /* 进度条样式 */
        .progress-container {
            width: 100%;
            background-color: #f5f5f5;
            border-radius: 4px;
            margin: 10px 0;
        }

        .progress-bar {
            height: 8px;
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .progress-bar.low {
            background-color: #52c41a;
        }

        .progress-bar.medium {
            background-color: #faad14;
        }

        .progress-bar.high {
            background-color: #f5222d;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .stat-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .comparison-item {
                min-width: 100%;
            }
            
            .competitor-list {
                grid-template-columns: 1fr;
            }
        }

        /* 自定义城市选择下拉控件样式 */
        .custom-select-container {
            position: relative;
            width: 100%;
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            max-height: 250px;
            overflow-y: auto;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            margin-top: 2px;
        }

        .dropdown-item {
            padding: 10px 15px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .dropdown-item:hover {
            background-color: #f5f5f5;
        }

        #citySearchInput {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        #citySearchInput:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
    </style>

    <style>
        /* 确保导航栏在页面加载时立即显示 */
        nav ul li {
            opacity: 1 !important;
            transform: none !important;
        }
        
        /* 预加载字体图标 */
        .fas {
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>            <div class="header-content">
                <h1><div class="rotating-logo" aria-label="联东Logo"></div> 联东AI内容营销托管智能体</h1>
                <div class="header-subtitle">智能化房源管理与内容营销平台</div>
            </div>
                        <nav>
                <ul>
                    <li><a href="index.html" class="nav-home"><i class="fas fa-home icon-blue"></i> 首页</a></li>
                    <li><a href="cities.html" class="nav-cities"><i class="fas fa-city icon-cyan"></i> 城市管理</a></li>
                    <li><a href="ports.html" class="nav-ports"><i class="fas fa-plug icon-green"></i> 端口管理</a></li>
                    <li><a href="status.html" class="nav-status"><i class="fas fa-chart-line icon-magenta"></i> 状态查询</a></li>
                    <li><a href="factory_images_upload.html" class="nav-images"><i class="fas fa-images icon-purple"></i> 园区图库</a></li>
                    <li><a href="factory_upload.html" class="nav-upload"><i class="fas fa-upload icon-orange"></i> 房源入库</a></li>
                    <li><a href="published_data.html" class="nav-published"><i class="fas fa-paper-plane icon-blue"></i> 房源推送</a></li>
                    <li><a href="statistics.html" class="nav-stats"><i class="fas fa-chart-bar icon-gold"></i> 发布统计</a></li>
                    <li><a href="scheduled_tasks.html" class="nav-tasks"><i class="fas fa-clock icon-green"></i> 定时任务</a></li>
                    <li><a href="competitor_analysis.html" class="active nav-analysis"><i class="fas fa-search icon-cyan"></i> 竞帖分析</a></li>
                </ul>
            </nav>
        </header>

        <main>
            <div class="content-section">
                <div class="section-header">
                    <h2>竞帖分析</h2>
                    <div class="header-actions">
                        <span id="dataLastUpdateTime" style="margin-right: 15px; color: #666; font-size: 14px; display: none;"></span>
                        <button class="btn-primary refresh-data-btn">刷新数据</button>
                        <button class="btn-secondary export-data-btn">导出报告</button>
                    </div>
                </div>

                <!-- 筛选表单 -->
                <div class="filter-form">
                    <div class="filter-form-row">
                        <div class="form-group">
                            <label for="cityFilter">城市</label>
                            <select id="cityFilter" class="form-control" style="display:none;">
                                <option value="">全部城市</option>
                                <!-- 城市选项将通过JS动态加载 -->
                            </select>
                            <div class="custom-select-container">
                                <input type="text" id="citySearchInput" class="form-control" placeholder="输入城市名称或选择" autocomplete="off">
                                <div id="cityDropdown" class="custom-select-dropdown" style="display:none;">
                                    <div class="dropdown-item" data-value="">全部城市</div>
                                    <!-- 城市选项将通过JS动态加载 -->
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="districtFilter">区域</label>
                            <select id="districtFilter" class="form-control">
                                <option value="">全部区域</option>
                                <!-- 区域选项将通过JS动态加载 -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="listingTypeFilter">租售类型</label>
                            <select id="listingTypeFilter" class="form-control">
                                <option value="">全部类型</option>
                                <option value="0">出租</option>
                                <option value="1">出售</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="startDateFilter">开始日期</label>
                            <input type="date" id="startDateFilter" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="endDateFilter">结束日期</label>
                            <input type="date" id="endDateFilter" class="form-control">
                        </div>
                        <div class="filter-actions">
                            <button id="applyFilterBtn" class="btn-primary">应用筛选</button>
                            <button id="resetFilterBtn" class="btn-secondary">重置</button>
                        </div>
                    </div>
                </div>

                <!-- 概览统计 -->
                <div class="analysis-card">
                    <div class="analysis-card-header">
                        <h3 class="analysis-card-title">概览统计</h3>
                        <span id="overviewTimeRange">近30天数据</span>
                    </div>
                    <div class="analysis-card-body">
                        <div class="stat-grid">
                            <div class="stat-item">
                                <div class="stat-label">总竞争帖子数</div>
                                <div class="stat-value" id="totalPosts">0</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">竞争对手数量</div>
                                <div class="stat-value" id="totalCompetitors">0</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">我方新增帖子</div>
                                <div class="stat-value" id="newPosts">0</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">我方帖子占比</div>
                                <div class="stat-value" id="ourPostsPercentage">0%</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">我方单账号平均帖子数</div>
                                <div class="stat-value" id="avgPostsPerAccount">0</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">竞争对手单账号平均帖子数</div>
                                <div class="stat-value" id="competitorAvgPostsPerAccount">0</div>
                            </div>
                        </div>
                        
                        <div class="chart-container" id="postsOverTimeChart">
                            <!-- 图表将通过JS动态加载 -->
                        </div>
                    </div>
                </div>

                <!-- 竞争对手分析 -->
                <div class="analysis-card">
                    <div class="analysis-card-header">
                        <h3 class="analysis-card-title">竞争对手分析</h3>
                    </div>
                    <div class="analysis-card-body">
                        <div class="chart-container" id="competitorShareChart">
                            <!-- 图表将通过JS动态加载 -->
                        </div>
                        
                        <div class="table-container">
                            <table class="competitor-table" id="competitorTable">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>竞争对手</th>
                                        <th>帖子数量</th>
                                        <th>前两页帖子份额</th>
                                        <th>周增长率</th>
                                        <th>最近更新</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="competitorTableBody">
                                    <!-- 竞争对手数据将通过JS动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 内容质量对比 -->
                <div class="analysis-card">
                    <div class="analysis-card-header">
                        <h3 class="analysis-card-title">内容质量对比</h3>
                    </div>
                    <div class="analysis-card-body">
                        <div class="data-comparison">
                            <div class="comparison-item">
                                <div class="comparison-title">帖子平均收藏数</div>
                                <div class="comparison-value-container">
                                    <div class="comparison-value" id="avgPopularityOur">0</div>
                                    <div class="comparison-change positive" id="avgPopularityChange">+0%</div>
                                </div>
                                <div class="comparison-subtitle">相比竞争对手 <span id="avgPopularityCompetitor">0</span></div>
                            </div>
                            <div class="comparison-item">
                                <div class="comparison-title">帖子平均图片数</div>
                                <div class="comparison-value-container">
                                    <div class="comparison-value" id="avgImagesOur">0</div>
                                    <div class="comparison-change positive" id="avgImagesChange">+0%</div>
                                </div>
                                <div class="comparison-subtitle">相比竞争对手 <span id="avgImagesCompetitor">0</span></div>
                            </div>
                            <div class="comparison-item">
                                <div class="comparison-title">帖子平均更新频率(天/次)</div>
                                <div class="comparison-value-container">
                                    <div class="comparison-value" id="updateFrequencyOur">0</div>
                                    <div class="comparison-change positive" id="updateFrequencyChange">+0%</div>
                                </div>
                                <div class="comparison-subtitle">相比竞争对手 <span id="updateFrequencyCompetitor">0</span></div>
                            </div>
                            <div class="comparison-item">
                                <div class="comparison-title">帖子平均视频数</div>
                                <div class="comparison-value-container">
                                    <div class="comparison-value" id="avgVideosOur">0</div>
                                    <div class="comparison-change positive" id="avgVideosChange">+0%</div>
                                </div>
                                <div class="comparison-subtitle">相比竞争对手 <span id="avgVideosCompetitor">0</span></div>
                            </div>
                        </div>

                        <div class="chart-container" id="contentQualityChart">
                            <!-- 图表将通过JS动态加载 -->
                        </div>
                    </div>
                </div>

                <!-- 区域热力分析 -->
                <div class="analysis-card">
                    <div class="analysis-card-header">
                        <h3 class="analysis-card-title">区域热力分析</h3>
                    </div>
                    <div class="analysis-card-body">
                        <div class="heatmap-container">
                            <table class="heatmap-table" id="districtHeatmap">
                                <!-- 区域热力图将通过JS动态加载 -->
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 热门竞争对手列表 -->
                <div class="analysis-card">
                    <div class="analysis-card-header">
                        <h3 class="analysis-card-title">热门竞争对手</h3>
                    </div>
                    <div class="analysis-card-body">
                        <div class="competitor-list" id="topCompetitorsList">
                            <!-- 竞争对手列表将通过JS动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <div class="footer-content">
                <p>&copy; 2025 联东AI内容营销托管智能体</p>
                <div class="footer-links">
                    <a href="help.html"><i class="fas fa-book"></i> 帮助文档</a>
                    <a href="https://www.liando.cn/" target="_blank"><i class="fas fa-envelope"></i> 联系我们</a>
                    <a href="#" onclick="updateSystem(); return false;"><i class="fas fa-sync-alt"></i> 系统更新</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- 全局加载指示器 -->
    <div id="globalLoadingIndicator" style="display: none;">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
        <div class="progress-bar-container">
            <div class="progress-bar"></div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="messageContainer"></div>

    <!-- 竞争对手详情模态框 -->
    <dialog id="competitorDetailModal" style="width: 80%; max-width: 800px; padding: 0; border-radius: 8px; border: none; box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15); margin: auto;">
        <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #eaeaea; display: flex; justify-content: space-between; align-items: center; background-color: #f5f5f5;">
            <h3 id="competitorModalTitle">竞争对手详情</h3>
            <button class="modal-close-btn" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">&times;</button>
        </div>
        <div class="modal-body" style="padding: 20px; max-height: 70vh; overflow-y: auto;" id="competitorModalContent">
            <!-- 竞争对手详情将通过JS动态加载 -->
        </div>
    </dialog>

    <script src="js/main.js?v=1.0.1"></script>
    <script src="js/loading.js?v=1.0.1"></script>
    <script src="js/vendor/echarts.min.js"></script>
    <script src="js/competitor_analysis.js"></script>
</body>
</html> 