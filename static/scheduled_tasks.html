<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>定时任务管理 - 联东AI内容营销托管智能体</title>
    <meta name="description" content="智能化房源管理与内容营销平台 - 定时任务管理">
    <meta name="keywords" content="房源管理,内容营销,智能化,定时任务">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preload" href="css/styles.css" as="style">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="css/scheduled_tasks.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <h1><div class="rotating-logo" aria-label="联东Logo"></div> 联东AI内容营销托管智能体</h1>
                <div class="header-subtitle">智能化房源管理与内容营销平台</div>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" class="nav-home"><i class="fas fa-home icon-blue"></i> 首页</a></li>
                    <li><a href="cities.html" class="nav-cities"><i class="fas fa-city icon-cyan"></i> 城市管理</a></li>
                    <li><a href="ports.html" class="nav-ports"><i class="fas fa-plug icon-green"></i> 端口管理</a></li>
                    <li><a href="status.html" class="nav-status"><i class="fas fa-chart-line icon-magenta"></i> 状态查询</a></li>
                    <li><a href="factory_images_upload.html" class="nav-images"><i class="fas fa-images icon-purple"></i> 园区图库</a></li>
                    <li><a href="factory_upload.html" class="nav-upload"><i class="fas fa-upload icon-orange"></i> 房源入库</a></li>
                    <li><a href="published_data.html" class="nav-published"><i class="fas fa-paper-plane icon-blue"></i> 房源推送</a></li>
                    <li><a href="statistics.html" class="nav-stats"><i class="fas fa-chart-bar icon-gold"></i> 发布统计</a></li>
                    <li><a href="scheduled_tasks.html" class="active nav-tasks"><i class="fas fa-clock icon-green"></i> 定时任务</a></li>
                    <li><a href="competitor_analysis.html" class="nav-analysis"><i class="fas fa-search icon-cyan"></i> 竞帖分析</a></li>
                </ul>
            </nav>
        </header>

        <main>
            <div class="content-section">
                <div class="section-header">
                    <h2>定时任务管理</h2>
                    <button class="btn-primary create-task-btn">新建任务</button>
                </div>

                <!-- 任务筛选 -->
                <div class="filter-section">
                    <div class="filter-controls">
                        <label for="taskTypeFilter">任务类型:</label>
                        <select id="taskTypeFilter" class="form-control">
                            <option value="">全部类型</option>
                            <option value="shell_script">Shell脚本</option>
                            <option value="python_script">Python脚本</option>
                        </select>
                        <button id="applyTaskFilterBtn" class="btn-primary">
                            <i class="fas fa-filter"></i>筛选
                        </button>
                        <button id="resetTaskFilterBtn" class="btn-secondary">
                            <i class="fas fa-undo"></i>重置
                        </button>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="table-container">
                    <table id="tasksTable">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>任务类型</th>
                                <th>执行周期</th>
                                <th>下次执行时间</th>
                                <th>状态</th>
                                <th>最后执行时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="tasksTableBody">
                            <!-- 任务列表将在这里动态插入 -->
                        </tbody>
                    </table>
                </div>

                <!-- 爬虫数据查询 -->
                <div class="content-section spider-data-section">
                    <div class="section-header">
                        <h2>爬虫数据查询</h2>
                    </div>

                    <!-- 筛选表单 -->
                    <div class="filter-form">
                        <div class="filter-input-group">
                            <div class="input-wrapper">
                                <input type="text" id="areaFilter" class="form-control" placeholder="输入城市名称">
                            </div>
                            <div class="button-group">
                                <button id="applyFilterBtn" class="btn-primary">
                                    <i class="fas fa-search"></i> 应用筛选
                                </button>
                                <button id="resetFilterBtn" class="btn-secondary">
                                    <i class="fas fa-redo-alt"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 城市卡片列表 -->
                    <div id="citiesContainer" class="cities-grid">
                        <!-- 城市卡片将在这里动态插入 -->
                    </div>

                    <!-- 分页控件 -->
                    <div id="paginationControls" class="pagination-controls">
                        <button id="prevPageBtn" class="pagination-btn">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div id="paginationInfo" class="pagination-info">
                            第 <span id="currentPage">1</span> 页，共 <span id="totalPages">1</span> 页
                        </div>
                        <button id="nextPageBtn" class="pagination-btn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <div class="footer-content">
                <p>&copy; 2025 联东AI内容营销托管智能体</p>
                <div class="footer-links">
                    <a href="help.html"><i class="fas fa-book"></i> 帮助文档</a>
                    <a href="https://www.liando.cn/" target="_blank"><i class="fas fa-envelope"></i> 联系我们</a>
                    <a href="#" onclick="updateSystem(); return false;"><i class="fas fa-sync-alt"></i> 系统更新</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- 新建/编辑任务对话框 -->
    <dialog id="taskModal" class="task-modal">
        <div class="modal-header">
            <h3 id="modalTitle">新建定时任务</h3>
            <button class="modal-close-btn" type="button">&times;</button>
        </div>
        <div class="modal-body">
            <form class="task-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="taskName">任务名称</label>
                        <input type="text" id="taskName" class="form-control" placeholder="请输入任务名称">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>任务类型</label>
                        <div class="task-type-selector">
                            <label class="task-type-option">
                                <input type="radio" name="taskType" value="shell_script" checked>
                                <span class="checkmark"></span>
                                <div class="task-icon">
                                    <i class="fas fa-terminal"></i>
                                </div>
                                <span class="task-label">Shell脚本</span>
                            </label>
                            <label class="task-type-option">
                                <input type="radio" name="taskType" value="python_script">
                                <span class="checkmark"></span>
                                <div class="task-icon">
                                    <i class="fab fa-python"></i>
                                </div>
                                <span class="task-label">Python脚本</span>
                            </label>
                        </div>
                        <input type="hidden" id="taskType" name="taskType" value="shell_script">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>执行频率设置</label>
                        <div class="frequency-settings">
                            <div class="frequency-row">
                                <div class="form-group">
                                    <label for="frequencyType">频率类型</label>
                                    <select id="frequencyType" class="form-control">
                                        <!-- 频率类型选项将通过JS动态加载 -->
                                    </select>
                                </div>
                            </div>

                            <div class="frequency-row">
                                <!-- 间隔设置 (用于每X分钟/小时) -->
                                <div class="form-group interval-group" style="display: none;">
                                    <label for="intervalValue">间隔值</label>
                                    <input type="number" id="intervalValue" min="1" class="form-control" placeholder="请输入间隔值">
                                    <small class="form-text" id="intervalUnit">分钟</small>
                                </div>

                                <!-- 小时设置 (用于每天/每周/每月) -->
                                <div class="form-group time-group" style="display: none;">
                                    <label for="atHour">小时</label>
                                    <select id="atHour" class="form-control">
                                        <!-- 小时选项将通过JS动态加载 -->
                                    </select>
                                </div>

                                <!-- 分钟设置 (用于所有需要具体时间的频率) -->
                                <div class="form-group time-group" style="display: none;">
                                    <label for="atMinute">分钟</label>
                                    <select id="atMinute" class="form-control">
                                        <!-- 分钟选项将通过JS动态加载 -->
                                    </select>
                                </div>

                                <!-- 星期几设置 (用于每周) -->
                                <div class="form-group weekday-group" style="display: none;">
                                    <label for="atWeekday">星期</label>
                                    <select id="atWeekday" class="form-control">
                                        <!-- 星期几选项将通过JS动态加载 -->
                                    </select>
                                </div>

                                <!-- 日期设置 (用于每月) -->
                                <div class="form-group day-group" style="display: none;">
                                    <label for="atDay">日期</label>
                                    <select id="atDay" class="form-control">
                                        <!-- 日期选项将通过JS动态加载 -->
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="cronExpression">Cron表达式</label>
                        <div class="cron-input-group">
                            <input type="text" id="cronExpression" class="form-control" placeholder="0 6,12,18 * * * (每天6点、12点、18点)" readonly>
                            <button type="button" id="editCronBtn" class="btn-small">编辑</button>
                        </div>
                        <small class="form-text">支持多时间点格式，如：<code>0 6,12,18 * * *</code></small>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="taskParams">命令</label>
                        <textarea id="taskParams" class="form-control" rows="3" placeholder="scripts/example.sh 或 python script.py args"></textarea>
                        <small class="form-text" id="taskParamsHelp">请输入脚本路径或完整命令（相对于项目根目录）</small>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="taskDescription">任务描述</label>
                        <textarea id="taskDescription" class="form-control" rows="2" placeholder="请输入任务描述（可选）"></textarea>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn-primary save-task-btn" type="button">保存</button>
            <button class="btn-secondary close-modal-btn" type="button">取消</button>
        </div>
    </dialog>

    <!-- 全局加载指示器 -->
    <div id="globalLoadingIndicator" class="global-loading-indicator">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
        <div class="progress-bar-container">
            <div class="progress-bar"></div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="messageContainer"></div>

    <script src="js/main.js?v=1.0.1"></script>
    <script src="js/loading.js?v=1.0.1"></script>
    <script src="js/scheduled_tasks.js?v=1.4.0"></script>
</body>
</html>
