<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联东AI内容营销托管智能体</title>
    <link rel="icon" href="images/1684755993.png" type="image/png">
    <link rel="shortcut icon" href="images/1684755993.png" type="image/png">
    <link rel="preload" href="css/styles.css?v=20250127" as="style">
    <link rel="stylesheet" href="css/styles.css?v=20250127">
    <link rel="stylesheet" href="css/loading.css?v=20250127">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- 添加拆分后的CSS文件 -->
    <link rel="stylesheet" href="css/styles_published.css?v=20250127">
    <!-- 添加推送状态样式 -->
    <link rel="stylesheet" href="css/push-status.css?v=20250127">

    <style>
        /* 图标彩色化样式 */
        .icon-blue { color: #1890ff !important; }
        .icon-green { color: #52c41a !important; }
        .icon-orange { color: #fa8c16 !important; }
        .icon-purple { color: #722ed1 !important; }
        .icon-cyan { color: #13c2c2 !important; }
        .icon-red { color: #f5222d !important; }
        .icon-gold { color: #faad14 !important; }
        .icon-magenta { color: #eb2f96 !important; }
    </style>
</head>
<body>
    <!-- 添加顶部进度条 -->
    <div id="topProgressBar" class="progress-bar"></div>

    <div class="container">
        <header>            <div class="header-content">
                <h1><div class="rotating-logo" aria-label="联东Logo"></div> 联东AI内容营销托管智能体</h1>
                <div class="header-subtitle">智能化房源管理与内容营销平台</div>
            </div>
                        <nav>
                <ul>
                    <li><a href="index.html" class="nav-home"><i class="fas fa-home icon-blue"></i> 首页</a></li>
                    <li><a href="cities.html" class="nav-cities"><i class="fas fa-city icon-cyan"></i> 城市管理</a></li>
                    <li><a href="ports.html" class="nav-ports"><i class="fas fa-plug icon-green"></i> 端口管理</a></li>
                    <li><a href="status.html" class="nav-status"><i class="fas fa-chart-line icon-magenta"></i> 状态查询</a></li>
                    <li><a href="factory_images_upload.html" class="nav-images"><i class="fas fa-images icon-purple"></i> 园区图库</a></li>
                    <li><a href="factory_upload.html" class="nav-upload"><i class="fas fa-upload icon-orange"></i> 房源入库</a></li>
                    <li><a href="published_data.html" class="active nav-published"><i class="fas fa-paper-plane icon-blue"></i> 房源推送</a></li>
                    <li><a href="statistics.html" class="nav-stats"><i class="fas fa-chart-bar icon-gold"></i> 发布统计</a></li>
                    <li><a href="scheduled_tasks.html" class="nav-tasks"><i class="fas fa-clock icon-green"></i> 定时任务</a></li>
                    <li><a href="competitor_analysis.html" class="nav-analysis"><i class="fas fa-search icon-cyan"></i> 竞帖分析</a></li>
                </ul>
            </nav>
        </header>

        <!-- 添加全局加载指示器 -->
        <div id="globalLoadingIndicator" class="global-loading-indicator">
            <div class="loading-spinner"></div>
            <span>加载中...</span>
        </div>

        <main>
            <div class="content-section">
                <header class="page-header">
                    <div class="page-title-wrapper">
                        <h2 class="page-title">
                            <i class="fas fa-paper-plane page-title-icon"></i>
                            已发布数据查询
                        </h2>
                        <p class="page-description">查看已发布到优推平台的厂房数据，支持多条件筛选和批量操作</p>
                    </div>
                </header>

                <!-- 搜索筛选区域 -->
                <section class="search-section" aria-label="搜索筛选">
                    <div class="search-header">
                        <h3 class="search-title">
                            <i class="fas fa-filter"></i>
                            筛选条件
                        </h3>
                    </div>
                    <form class="search-form" role="search">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="citySelect" class="form-label">
                                    <i class="fas fa-city form-label-icon"></i>
                                    城市
                                </label>
                                <div class="select-wrapper">
                                    <select id="citySelect" name="city" class="form-select" aria-describedby="citySelect-help">
                                        <option value="">全部城市</option>
                                        <!-- 城市列表将通过JavaScript动态填充 -->
                                    </select>
                                </div>
                                <small id="citySelect-help" class="form-help">选择要查询的城市</small>
                            </div>

                            <div class="form-group">
                                <label for="keyword" class="form-label">
                                    <i class="fas fa-search form-label-icon"></i>
                                    园区名称
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" id="keyword" name="keyword" class="form-input"
                                           placeholder="输入园区名称进行搜索..."
                                           aria-describedby="keyword-help">
                                    <i class="fas fa-search input-icon"></i>
                                </div>
                                <small id="keyword-help" class="form-help">支持模糊搜索</small>
                            </div>

                            <div class="form-actions">
                                <button type="button" id="searchButton" class="btn btn-primary btn-search">
                                    <i class="fas fa-search btn-icon"></i>
                                    <span class="btn-text">搜索</span>
                                    <span class="btn-loading"></span>
                                </button>
                                <button type="button" id="resetButton" class="btn btn-secondary btn-reset">
                                    <i class="fas fa-undo btn-icon"></i>
                                    <span class="btn-text">重置</span>
                                    <span class="btn-loading"></span>
                                </button>
                            </div>
                        </div>
                    </form>
                </section>

                <!-- 数据展示区域 -->
                <section class="data-section" aria-label="数据列表">
                    <div class="data-header">
                        <div class="data-stats">
                            <div class="stats-item">
                                <i class="fas fa-database stats-icon"></i>
                                <span class="stats-label">总计</span>
                                <span class="stats-value" id="totalRecords">0</span>
                                <span class="stats-unit">条数据</span>
                            </div>
                            <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                                <div class="loading-spinner-mini"></div>
                                <span class="loading-text">加载中...</span>
                            </div>
                        </div>
                        <div class="data-actions">
                            <button id="refreshDataBtn" class="btn btn-refresh" title="刷新数据" aria-label="刷新数据">
                                <i class="fas fa-sync-alt refresh-icon"></i>
                                <span class="btn-text">刷新</span>
                                <span class="btn-loading"></span>
                            </button>
                        </div>
                    </div>

                    <!-- 卡片网格容器 -->
                    <div class="cards-container">
                        <div id="publishedDataCards" class="cards-grid" role="grid" aria-label="已发布数据卡片列表">
                            <!-- 简化的骨架屏加载效果 -->
                            <div class="skeleton-card" aria-hidden="true">
                                <div class="card-bg-animation">
                                    <div class="bg-shape shape-1"></div>
                                    <div class="bg-gradient"></div>
                                </div>
                                <div class="card-content">
                                    <div class="skeleton-card-header">
                                        <div class="skeleton-title"></div>
                                        <div class="skeleton-badge"></div>
                                    </div>
                                    <div class="skeleton-card-body">
                                        <div class="skeleton-info-item"></div>
                                        <div class="skeleton-info-item"></div>
                                        <div class="skeleton-info-item"></div>
                                    </div>
                                    <div class="skeleton-card-footer">
                                        <div class="skeleton-button"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="skeleton-card" aria-hidden="true">
                                <div class="card-bg-animation">
                                    <div class="bg-shape shape-1"></div>
                                    <div class="bg-gradient"></div>
                                </div>
                                <div class="card-content">
                                    <div class="skeleton-card-header">
                                        <div class="skeleton-title"></div>
                                        <div class="skeleton-badge"></div>
                                    </div>
                                    <div class="skeleton-card-body">
                                        <div class="skeleton-info-item"></div>
                                        <div class="skeleton-info-item"></div>
                                        <div class="skeleton-info-item"></div>
                                    </div>
                                    <div class="skeleton-card-footer">
                                        <div class="skeleton-button"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="skeleton-card" aria-hidden="true">
                                <div class="card-bg-animation">
                                    <div class="bg-shape shape-1"></div>
                                    <div class="bg-gradient"></div>
                                </div>
                                <div class="card-content">
                                    <div class="skeleton-card-header">
                                        <div class="skeleton-title"></div>
                                        <div class="skeleton-badge"></div>
                                    </div>
                                    <div class="skeleton-card-body">
                                        <div class="skeleton-info-item"></div>
                                        <div class="skeleton-info-item"></div>
                                        <div class="skeleton-info-item"></div>
                                    </div>
                                    <div class="skeleton-card-footer">
                                        <div class="skeleton-button"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- 数据卡片将通过JavaScript动态填充 -->
                        </div>

                        <!-- 空状态显示 -->
                        <div id="emptyState" class="empty-state" style="display: none;">
                            <div class="empty-icon">
                                <i class="fas fa-inbox"></i>
                            </div>
                            <h3 class="empty-title">暂无数据</h3>
                            <p class="empty-description">没有找到符合条件的园区数据，请尝试调整筛选条件</p>
                        </div>
                    </div>

                    <!-- 分页控件 -->
                    <nav class="pagination-wrapper" aria-label="分页导航">
                        <div class="pagination">
                            <button id="prevPage" class="btn btn-pagination" disabled aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                                <span>上一页</span>
                            </button>
                            <div class="page-info">
                                <span class="page-text">第</span>
                                <span class="page-current" id="currentPage">1</span>
                                <span class="page-separator">/</span>
                                <span class="page-total" id="totalPages">1</span>
                                <span class="page-text">页</span>
                            </div>
                            <button id="nextPage" class="btn btn-pagination" aria-label="下一页">
                                <span>下一页</span>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </nav>
                </section>
            </div>
        </main>

        <!-- 添加推送弹窗 -->
        <div id="websiteModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">选择推送网站</h5>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <!-- 添加网站列表加载中状态 -->
                    <div id="websiteListLoading" class="website-list-loading">
                        <div class="loading-spinner-small"></div>
                        <span>加载网站列表中...</span>
                    </div>
                    <div id="websiteList" class="website-list">
                        <!-- 网站列表将通过JavaScript动态填充 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancelPushBtn">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmPushBtn">确认推送</button>
                </div>
            </div>
        </div>

        <footer>
            <div class="footer-content">
                <p>&copy; 2025 联东AI内容营销托管智能体</p>
                <div class="footer-links">
                    <a href="help.html"><i class="fas fa-book"></i> 帮助文档</a>
                    <a href="https://www.liando.cn/" target="_blank"><i class="fas fa-envelope"></i> 联系我们</a>
                    <a href="#" onclick="updateSystem(); return false;"><i class="fas fa-sync-alt"></i> 系统更新</a>
                </div>
            </div>
        </footer>

    <!-- 添加消息提示容器 -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- 添加警告弹窗 -->
    <div id="warningModal" class="warning-modal">
        <div class="warning-content">
            <div class="warning-title">重要提醒</div>
            <div class="warning-list">
                <div class="warning-item">
                    免费端口请勿尝试重复推送，否则可能导致账号风险！
                </div>
                <div class="warning-item">
                    请勿将房源推送至非58账号注册地！
                </div>
            </div>

            <!-- 添加不再提醒选项 -->
            <div class="warning-checkbox-container">
                <label class="warning-checkbox-label">
                    <input type="checkbox" id="dontShowAgainCheckbox" class="warning-checkbox">
                    <span class="warning-checkbox-text">不再显示此提醒</span>
                </label>
            </div>

            <div class="warning-buttons">
                <button class="warning-confirm-btn" onclick="confirmWarning()">我已了解，继续操作</button>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script src="js/published_data.js"></script>
</body>
</html>