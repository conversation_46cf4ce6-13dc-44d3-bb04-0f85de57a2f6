<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帮助文档 - 联东AI内容营销托管智能体</title>
    <link rel="icon" href="images/1684755993.png" type="image/png">
    <link rel="shortcut icon" href="images/1684755993.png" type="image/png">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* 图标彩色化样式 */
        .icon-blue { color: #1890ff !important; }
        .icon-green { color: #52c41a !important; }
        .icon-orange { color: #fa8c16 !important; }
        .icon-purple { color: #722ed1 !important; }
        .icon-cyan { color: #13c2c2 !important; }
        .icon-red { color: #f5222d !important; }
        .icon-gold { color: #faad14 !important; }
        .icon-magenta { color: #eb2f96 !important; }
        
        .help-content {
            background-color: #fff;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
            position: relative;
            overflow: hidden;
        }
        
        .help-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, #1890ff, #40a9ff);
        }
        
        .help-section {
            margin-bottom: 30px;
        }
        
        .help-section h2 {
            color: #1890ff;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            font-size: 1.5rem;
        }
        
        .help-section h3 {
            color: #333;
            margin: 20px 0 10px;
            font-size: 1.2rem;
            position: relative;
            padding-left: 15px;
        }
        
        .help-section h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #1890ff, #40a9ff);
            border-radius: 3px;
        }
        
        .help-section p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .help-section ul, .help-section ol {
            padding-left: 20px;
            margin-bottom: 15px;
        }
        
        .help-section li {
            margin-bottom: 8px;
            color: #666;
        }
        
        .help-section .note {
            background-color: #f0f7ff;
            border-left: 4px solid #1890ff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        
        .help-section .warning {
            background-color: #fff7e6;
            border-left: 4px solid #faad14;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        
        .process-flow {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin: 20px 0;
            position: relative;
        }
        
        .process-step {
            background-color: #f9fafc;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            width: calc(25% - 20px);
            text-align: center;
            position: relative;
            border: 1px solid #eee;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .process-step:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-color: #1890ff;
        }
        
        .process-step .step-number {
            background-color: #1890ff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-weight: bold;
        }
        
        .process-step h4 {
            color: #1890ff;
            margin-bottom: 10px;
        }
        
        .process-step p {
            font-size: 0.9rem;
            color: #666;
        }
        
        .process-arrow {
            position: absolute;
            top: 50%;
            left: calc(25% - 10px);
            transform: translateY(-50%);
            color: #1890ff;
            font-size: 1.5rem;
        }
        
        .process-arrow:nth-of-type(2) {
            left: calc(50% - 10px);
        }
        
        .process-arrow:nth-of-type(3) {
            left: calc(75% - 10px);
        }
        
        @media (max-width: 768px) {
            .process-step {
                width: 100%;
                margin-bottom: 40px;
            }
            
            .process-arrow {
                top: auto;
                left: 50%;
                transform: translateX(-50%) rotate(90deg);
                bottom: -30px;
            }
            
            .process-arrow:nth-of-type(2) {
                left: 50%;
                bottom: -30px;
                top: auto;
            }
            
            .process-arrow:nth-of-type(3) {
                left: 50%;
                bottom: -30px;
                top: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <h1><div class="rotating-logo" aria-label="联东Logo"></div> 联东AI内容营销托管智能体</h1>
                <div class="header-subtitle">智能化房源管理与内容营销平台</div>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" class="nav-home"><i class="fas fa-home icon-blue"></i> 首页</a></li>
                    <li><a href="cities.html" class="nav-cities"><i class="fas fa-city icon-cyan"></i> 城市管理</a></li>
                    <li><a href="ports.html" class="nav-ports"><i class="fas fa-plug icon-green"></i> 端口管理</a></li>
                    <li><a href="status.html" class="nav-status"><i class="fas fa-chart-line icon-magenta"></i> 状态查询</a></li>
                    <li><a href="factory_images_upload.html" class="nav-images"><i class="fas fa-images icon-purple"></i> 园区图库</a></li>
                    <li><a href="factory_upload.html" class="nav-upload"><i class="fas fa-upload icon-orange"></i> 房源入库</a></li>
                    <li><a href="published_data.html" class="nav-published"><i class="fas fa-paper-plane icon-blue"></i> 房源推送</a></li>
                    <li><a href="statistics.html" class="nav-stats"><i class="fas fa-chart-bar icon-gold"></i> 发布统计</a></li>
                    <li><a href="scheduled_tasks.html" class="nav-tasks"><i class="fas fa-clock icon-green"></i> 定时任务</a></li>
                    <li><a href="competitor_analysis.html" class="nav-analysis"><i class="fas fa-search icon-cyan"></i> 竞帖分析</a></li>
                    <li><a href="help.html" class="active nav-help"><i class="fas fa-question-circle icon-red"></i> 帮助文档</a></li>
                </ul>
            </nav>
        </header>

        <main>
            <div class="welcome-section">
                <div class="welcome-content">
                    <h2>联东AI内容营销托管智能体使用指南</h2>
                    <p>本文档将帮助您了解系统的主要功能和使用流程，让您能够快速上手并充分利用平台的各项功能。</p>
                </div>
            </div>

            <div class="help-content">
                <div class="help-section">
                    <h2><i class="fas fa-info-circle"></i> 系统概述</h2>
                    <p>联东AI内容营销托管智能体是一个专为房地产行业设计的智能化房源管理与内容营销平台。系统通过AI技术，帮助用户自动化管理房源信息，并将房源信息智能发布到多个主流平台，提高房源曝光率和转化率。</p>
                    
                    <h3>系统主要功能</h3>
                    <ul>
                        <li><strong>房源管理</strong>：集中管理所有房源信息，支持批量导入和编辑</li>
                        <li><strong>多平台发布</strong>：一键将房源信息发布到58同城、安居客等平台</li>
                        <li><strong>智能内容生成</strong>：AI自动生成符合各平台特点的房源描述</li>
                        <li><strong>图片处理</strong>：自动优化房源图片，提高展示效果</li>
                        <li><strong>数据分析</strong>：提供房源发布效果分析，帮助优化营销策略</li>
                        <li><strong>定时任务</strong>：支持设置定时发布和更新任务</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h2><i class="fas fa-sitemap"></i> 平台发布流程</h2>
                    <p>以下是使用本系统发布房源到各平台的基本流程：</p>
                    
                    <div class="process-flow">
                        <div class="process-step">
                            <div class="step-number">1</div>
                            <h4>房源入库</h4>
                            <p>通过Excel文件批量导入房源信息，或手动录入单个房源</p>
                        </div>
                        <i class="fas fa-arrow-right process-arrow"></i>
                        <div class="process-step">
                            <div class="step-number">2</div>
                            <h4>图片上传</h4>
                            <p>上传房源图片，系统会自动优化处理</p>
                        </div>
                        <i class="fas fa-arrow-right process-arrow"></i>
                        <div class="process-step">
                            <div class="step-number">3</div>
                            <h4>内容生成</h4>
                            <p>系统自动生成针对不同平台优化的房源描述</p>
                        </div>
                        <i class="fas fa-arrow-right process-arrow"></i>
                        <div class="process-step">
                            <div class="step-number">4</div>
                            <h4>发布管理</h4>
                            <p>选择目标平台发布，并查看发布状态</p>
                        </div>
                    </div>
                    
                    <h3>详细步骤说明</h3>
                    <ol>
                        <li>
                            <strong>房源入库</strong>
                            <p>在"房源入库"页面，您可以通过上传Excel文件批量导入房源信息。系统支持的Excel模板可在页面上下载。导入后，系统会自动验证数据格式，确保信息完整。</p>
                        </li>
                        <li>
                            <strong>图片上传</strong>
                            <p>在"园区图库"页面，您可以上传房源的图片。系统会自动处理图片，包括调整尺寸、优化质量等，以符合各平台的要求。</p>
                            <div class="note">
                                <p><i class="fas fa-lightbulb"></i> <strong>提示：</strong>上传高质量的原始图片，系统会自动进行优化处理，无需手动调整。</p>
                            </div>
                        </li>
                        <li>
                            <strong>内容生成与编辑</strong>
                            <p>系统会基于您提供的房源信息，自动生成针对不同平台优化的房源描述。您可以在发布前预览和编辑这些内容。</p>
                        </li>
                        <li>
                            <strong>选择发布平台</strong>
                            <p>在"房源推送"页面，选择要发布的房源和目标平台。目前系统支持58同城和安居客，未来将支持小红书和抖音。</p>
                        </li>
                        <li>
                            <strong>发布管理</strong>
                            <p>发布后，您可以在"状态查询"页面查看所有房源的发布状态，包括发布时间、平台、状态等信息。</p>
                        </li>
                        <li>
                            <strong>数据分析</strong>
                            <p>在"发布统计"页面，您可以查看房源发布的效果数据，包括浏览量、咨询量等，帮助您优化营销策略。</p>
                        </li>
                    </ol>
                </div>

                <div class="help-section">
                    <h2><i class="fas fa-cogs"></i> 系统配置</h2>
                    
                    <h3>城市管理</h3>
                    <p>在"城市管理"页面，您可以配置系统支持的城市。每个城市需要设置以下信息：</p>
                    <ul>
                        <li><strong>城市名称</strong>：城市的中文名称</li>
                        <li><strong>城市代码</strong>：各平台使用的城市代码</li>
                        <li><strong>状态</strong>：启用或禁用</li>
                    </ul>
                    
                    <h3>端口管理</h3>
                    <p>在"端口管理"页面，您可以配置各平台的账号信息。每个端口需要设置以下信息：</p>
                    <ul>
                        <li><strong>平台</strong>：选择平台（58同城、安居客等）</li>
                        <li><strong>账号</strong>：平台账号</li>
                        <li><strong>密码</strong>：平台账号密码</li>
                        <li><strong>城市</strong>：关联的城市</li>
                        <li><strong>状态</strong>：启用或禁用</li>
                    </ul>
                    <div class="warning">
                        <p><i class="fas fa-exclamation-triangle"></i> <strong>注意：</strong>请确保账号密码正确，否则将无法正常发布房源。</p>
                    </div>
                </div>

                <div class="help-section">
                    <h2><i class="fas fa-tasks"></i> 定时任务</h2>
                    <p>系统支持设置定时任务，自动执行房源发布、更新等操作。</p>
                    
                    <h3>创建定时任务</h3>
                    <p>在"定时任务"页面，您可以创建新的定时任务：</p>
                    <ol>
                        <li>点击"创建任务"按钮</li>
                        <li>选择任务类型（数据同步、内容推送、数据备份）</li>
                        <li>设置执行时间（可选择一次性或周期性）</li>
                        <li>配置任务参数</li>
                        <li>保存任务</li>
                    </ol>
                    
                    <h3>管理定时任务</h3>
                    <p>您可以查看所有定时任务的执行状态，并进行以下操作：</p>
                    <ul>
                        <li><strong>编辑</strong>：修改任务配置</li>
                        <li><strong>暂停/恢复</strong>：临时停止或重新启动任务</li>
                        <li><strong>删除</strong>：永久删除任务</li>
                        <li><strong>查看日志</strong>：查看任务执行记录</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h2><i class="fas fa-question-circle"></i> 常见问题</h2>
                    
                    <h3>如何批量导入房源？</h3>
                    <p>在"房源入库"页面，点击"下载模板"按钮获取Excel模板，填写房源信息后上传即可。系统会自动验证数据格式，确保信息完整。</p>
                    
                    <h3>为什么房源发布失败？</h3>
                    <p>房源发布失败可能有以下原因：</p>
                    <ul>
                        <li>账号密码错误</li>
                        <li>房源信息不完整</li>
                        <li>图片数量不足或质量不符合要求</li>
                        <li>平台限制（如发布频率限制）</li>
                    </ul>
                    <p>您可以在"状态查询"页面查看详细的失败原因，并根据提示进行修正。</p>
                    
                    <h3>如何优化房源展示效果？</h3>
                    <p>提高房源展示效果的建议：</p>
                    <ul>
                        <li>上传高质量、多角度的房源图片</li>
                        <li>提供详细、准确的房源信息</li>
                        <li>使用系统生成的优化描述，或根据目标平台特点进行编辑</li>
                        <li>定期更新房源信息，保持活跃度</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h2><i class="fas fa-headset"></i> 联系支持</h2>
                    <p>如果您在使用过程中遇到任何问题，或有任何建议和反馈，请通过以下方式联系我们：</p>
                    <ul>
                        <li><strong>官方网站：</strong><a href="https://www.liando.cn/" target="_blank">https://www.liando.cn/</a></li>
                        <li><strong>技术电话：</strong>13225521581（工作日 9:00-18:00）</li>
                        <li><strong>邮箱：</strong><EMAIL></li>
                    </ul>
                    <p>我们的技术支持团队将在24小时内回复您的问题。</p>
                </div>
            </div>
        </main>

        <footer>
            <div class="footer-content">
                <p>&copy; 2025 联东AI内容营销托管智能体</p>
                <div class="footer-links">
                    <a href="help.html"><i class="fas fa-book"></i> 帮助文档</a>
                    <a href="https://www.liando.cn/" target="_blank"><i class="fas fa-envelope"></i> 联系我们</a>
                    <a href="#" onclick="updateSystem(); return false;"><i class="fas fa-sync-alt"></i> 系统更新</a>
                </div>
            </div>
        </footer>
    </div>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 设置活动导航项
            setActiveNavItem();
            
            // 添加页面动画效果
            const helpContent = document.querySelector('.help-content');
            if (helpContent) {
                helpContent.style.opacity = '0';
                helpContent.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    helpContent.style.transition = 'all 0.6s ease';
                    helpContent.style.opacity = '1';
                    helpContent.style.transform = 'translateY(0)';
                }, 300);
            }
        });
    </script>
</body>
</html>
