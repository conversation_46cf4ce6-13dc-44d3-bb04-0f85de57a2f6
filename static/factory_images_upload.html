<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联东AI内容营销托管智能体</title>
    <link rel="icon" href="images/1684755993.png" type="image/png">
    <link rel="shortcut icon" href="images/1684755993.png" type="image/png">
    <link rel="preload" href="css/styles.css" as="style">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="css/process-flow-animation.css">
    <!-- 使用Font Awesome图标库的最新版本，与factory_upload.html保持一致 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- 添加Material Icons作为补充图标库 -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <!-- 添加Animate.css进行简单动画效果 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <!-- 引入拆分出的CSS文件 -->
    <link rel="stylesheet" href="css/factory_images_upload.css">
    <!-- 添加自定义图标颜色样式 -->
    <style>
        /* 图标彩色化样式 */
        .icon-blue { color: #1890ff; }
        .icon-green { color: #52c41a; }
        .icon-orange { color: #fa8c16; }
        .icon-purple { color: #722ed1; }
        .icon-cyan { color: #13c2c2; }
        .icon-red { color: #f5222d; }
        .icon-gold { color: #faad14; }
        .icon-magenta { color: #eb2f96; }

        /* 园区名称输入框样式 */
        .input-group {
            display: flex;
            align-items: center;
        }

        .input-group input {
            flex: 1;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>            <div class="header-content">
                <h1><div class="rotating-logo" aria-label="联东Logo"></div> 联东AI内容营销托管智能体</h1>
                <div class="header-subtitle">智能化房源管理与内容营销平台</div>
            </div>
                        <nav>
                <ul>
                    <li><a href="index.html" class="nav-home"><i class="fas fa-home icon-blue"></i> 首页</a></li>
                    <li><a href="cities.html" class="nav-cities"><i class="fas fa-city icon-cyan"></i> 城市管理</a></li>
                    <li><a href="ports.html" class="nav-ports"><i class="fas fa-plug icon-green"></i> 端口管理</a></li>
                    <li><a href="status.html" class="nav-status"><i class="fas fa-chart-line icon-magenta"></i> 状态查询</a></li>
                    <li><a href="factory_images_upload.html" class="active nav-images"><i class="fas fa-images icon-purple"></i> 园区图库</a></li>
                    <li><a href="factory_upload.html" class="nav-upload"><i class="fas fa-upload icon-orange"></i> 房源入库</a></li>
                    <li><a href="published_data.html" class="nav-published"><i class="fas fa-paper-plane icon-blue"></i> 房源推送</a></li>
                    <li><a href="statistics.html" class="nav-stats"><i class="fas fa-chart-bar icon-gold"></i> 发布统计</a></li>
                    <li><a href="scheduled_tasks.html" class="nav-tasks"><i class="fas fa-clock icon-green"></i> 定时任务</a></li>
                    <li><a href="competitor_analysis.html" class="nav-analysis"><i class="fas fa-search icon-cyan"></i> 竞帖分析</a></li>
                </ul>
            </nav>
        </header>

        <main>
            <div class="content-section">
                <div class="section-header animate__animated animate__fadeIn">
                    <h2><i class="fas fa-images icon-purple"></i> 园区图库管理</h2>
                    <p style="font-size: 12px;"><i class="fas fa-info-circle icon-blue"></i> 为园区建立图片库，管理园区图片资源。</p>
                </div>

                <!-- 修改为Material Design风格的条形流程图 -->
                <div class="process-bar-flow animate__animated animate__fadeIn">
                    <div class="process-bar-item animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                        <div class="pattern-line"></div>
                        <div class="pattern-dots"></div>
                        <div class="step-badge">1</div>
                        <div class="process-bar-title">
                            <span class="step-number">1</span>
                            <i class="fas fa-database"></i> 建立园区图库
                        </div>
                        <div class="process-bar-desc"><i class="fas fa-photo-film"></i> 上传园区图片建立基础图库</div>
                        <i class="fas fa-images feature-icon"></i>
                    </div>

                    <div class="process-bar-item animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                        <div class="pattern-line"></div>
                        <div class="pattern-dots"></div>
                        <div class="step-badge">2</div>
                        <div class="process-bar-title">
                            <span class="step-number">2</span>
                            <i class="fas fa-file-import"></i> 上传房源数据
                        </div>
                        <div class="process-bar-desc"><i class="fas fa-angle-double-right"></i> 前往房源数据上传页面</div>
                        <i class="fas fa-upload feature-icon"></i>
                    </div>


                </div>

                <!-- 添加园区图库查询功能 -->
                <div class="form-group">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <h3 style="margin: 0;"><i class="fas fa-search icon-blue"></i> 园区图库查询</h3>
                    </div>
                    <p style="margin-bottom: 15px;"><i class="fas fa-info-circle icon-cyan"></i> 查看已建立图库的园区，管理园区图片</p>

                    <!-- 添加重要提示 -->

                    <div class="form-row">
                        <div class="form-group" style="width: 100%;">
                            <label for="campusSearchInput"><i class="fas fa-building icon-gold"></i> 园区名称</label>
                            <div class="input-group">
                                <input type="text" id="campusSearchInput" placeholder="输入园区名称" autocomplete="off">
                                <button type="button" onclick="queryCampusGallery()" style="margin-left: 10px;">
                                    <i class="fas fa-search icon-blue"></i> 查询
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="campusGalleryResults" style="display: block;">
                        <h4><i class="fas fa-list-ul icon-orange"></i> 园区图库查询结果</h4>
                        <div id="galleryResultsContent">
                            <div class="loading-container" id="initialLoadingState">
                                <div class="loading-spinner">
                                    <i class="fas fa-spinner fa-spin icon-blue"></i>
                                </div>
                                <p class="loading-text">
                                    <i class="fas fa-cloud-download-alt icon-cyan"></i> 正在加载园区数据，请稍候...
                                </p>
                                <div class="loading-progress">
                                    <div class="loading-progress-bar"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="uploadNoticeSection">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <h3 style="margin: 0;"><i class="fas fa-info-circle icon-blue"></i> 园区图库建立说明</h3>
                    </div>

                    <div class="message warning" style="margin-bottom: 20px;">
                        <i class="fas fa-exclamation-triangle icon-orange"></i>
                        <strong>重要提示：</strong>手动创建园区图库功能已停用
                    </div>

                    <div class="message info" style="margin-bottom: 20px;">
                        <i class="fas fa-shield-alt icon-green"></i>
                        <strong>为什么停用？</strong><br>
                        为了确保园区图库的质量和数据一致性，现在统一由平台管理员负责园区图库的建立和维护。
                    </div>

                    <div class="message info" style="margin-bottom: 20px;">
                        <i class="fas fa-phone icon-blue"></i>
                        <strong>如何建立园区图库？</strong><br>
                        如需为新园区建立图库，请联系您的平台管理员或销售代表。他们将协助您完成园区图库的专业建立。
                    </div>

                    <div class="message success">
                        <i class="fas fa-lightbulb icon-gold"></i>
                        <strong>您仍然可以：</strong>
                        <ul style="margin-top: 8px; margin-bottom: 0;">
                            <li><i class="fas fa-search icon-blue"></i> 查询现有园区的图库信息</li>

                            <li><i class="fas fa-file-upload icon-orange"></i> 前往<a href="factory_upload.html" style="color: #1890ff;">房源数据上传</a>页面上传房源数据</li>
                        </ul>
                    </div>
                </div>


            </div>
        </main>

        <footer>
            <div class="footer-content">
                <p><i class="far fa-copyright icon-blue"></i> 2025 联东AI内容营销托管智能体</p>
                <div class="footer-links">
                    <a href="help.html"><i class="fas fa-book icon-purple"></i> 帮助文档</a>
                    <a href="https://www.liando.cn/" target="_blank"><i class="fas fa-envelope icon-cyan"></i> 联系我们</a>
                    <a href="#" onclick="updateSystem(); return false;"><i class="fas fa-sync-alt icon-green"></i> 系统更新</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="js/main.js"></script>
    <script src="js/factory_images_upload.js"></script>
</body>
</html>