// 注意：筛选功能已移至 HTML 内联脚本中实现

// 页面加载完成后执行初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('页面开始初始化...');

    // 添加样式
    const fileListStyle = document.createElement('style');
    fileListStyle.textContent = `
        .file-info {
            font-size: 12px;
            color: #666;
        }
        .file-name {
            word-break: break-all;
        }
    `;
    document.head.appendChild(fileListStyle);

    addLoadingComponents();
    showGlobalLoading();
    startProgress();

    try {
        setActiveNavItem();

        await loadTaskHistory();

        await loadFrequencyOptions();

        await loadTasks();

        initializeEventListeners();
        initializeFilterEventListeners(); // 初始化筛选表单事件监听器
        initTaskFilters(); // 初始化任务筛选功能
        initTaskTypeSelector(); // 初始化任务类型选择器
        initSpiderDataFilters(); // 初始化爬虫数据筛选功能
    } catch (error) {
        console.error('页面初始化失败:', error);
        showMessage('加载数据失败，请稍后重试', 'error');
    } finally {
        hideGlobalLoading();
        completeProgress();
    }
});

// 全局变量，存储频率选项
let frequencyOptions = {
    frequency_types: [],
    weekdays: [],
    hour_options: [],
    minute_options: [],
    day_options: []
};

// 分页全局变量
let cityPagination = {
    currentPage: 1,
    itemsPerPage: 12,
    totalPages: 1,
    allCities: []
};

// 初始化所有事件监听器
function initializeEventListeners() {
    // 新建任务按钮点击事件
    const createTaskBtn = document.querySelector('.create-task-btn');
    if (createTaskBtn) {
        createTaskBtn.addEventListener('click', showCreateTaskModal);
    }

    // 频率类型变化事件
    const frequencyType = document.getElementById('frequencyType');
    if (frequencyType) {
        frequencyType.addEventListener('change', updateFrequencyFormFields);
    }

    // 间隔值变化事件
    const intervalValue = document.getElementById('intervalValue');
    if (intervalValue) {
        intervalValue.addEventListener('change', updateCronExpression);
    }

    // 时间选择变化事件
    const timeFields = ['atHour', 'atMinute', 'atWeekday', 'atDay'];
    timeFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('change', updateCronExpression);
        }
    });

    // 编辑Cron表达式按钮点击事件
    const editCronBtn = document.getElementById('editCronBtn');
    if (editCronBtn) {
        editCronBtn.addEventListener('click', function(event) {
            event.preventDefault();
            toggleCronExpressionEdit(event);
        });
    }

    // 保存任务按钮点击事件
    const saveTaskBtn = document.querySelector('.save-task-btn');
    if (saveTaskBtn) {
        saveTaskBtn.addEventListener('click', saveTask);
    }

    // 关闭模态框按钮点击事件（延迟绑定确保元素存在）
    setTimeout(() => {
        const closeButtons = document.querySelectorAll('.close-modal-btn, .modal-close-btn');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                console.log('关闭按钮被点击');
                closeTaskModal();
            });
        });

        // 取消按钮单独处理
        const cancelBtn = document.querySelector('.btn-secondary');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                console.log('取消按钮被点击');
                closeTaskModal();
            });
        }

        console.log('模态框关闭按钮事件监听器已绑定');
    }, 100);

    // 对话框点击事件（用于捕获对话框外部点击）
    const taskModal = document.getElementById('taskModal');
    if (taskModal) {
        // 防止对话框内部点击导致关闭
        taskModal.addEventListener('click', function(event) {
            // 阻止事件冒泡，确保点击对话框内部不会关闭
            event.stopPropagation();
        });

        // ESC键关闭对话框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && taskModal.hasAttribute('open')) {
                closeTaskModal();
            }
        });
    }


}

// 加载频率选项
async function loadFrequencyOptions() {
    try {
        const response = await apiRequest('/api/tasks/frequency/options', 'GET');
        frequencyOptions = response;

        // 填充频率类型选项
        const frequencyTypeSelect = document.getElementById('frequencyType');
        frequencyTypeSelect.innerHTML = '';

        // 添加"请选择"选项
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = '请选择频率类型';
        frequencyTypeSelect.appendChild(defaultOption);

        // 添加频率类型选项
        frequencyOptions.frequency_types.forEach(type => {
            const option = document.createElement('option');
            option.value = type.value;
            option.textContent = type.label;
            frequencyTypeSelect.appendChild(option);
        });

        // 填充时间选项
        fillTimeOptions();

        console.log('频率选项加载完成');
    } catch (error) {
        console.error('加载频率选项失败:', error);
        showMessage('加载频率选项失败', 'error');
    }
}

// 填充时间选项
function fillTimeOptions() {
    // 填充小时选项
    const hourSelect = document.getElementById('atHour');
    hourSelect.innerHTML = '';
    frequencyOptions.hour_options.forEach(hour => {
        const option = document.createElement('option');
        option.value = hour.value;
        option.textContent = hour.label;
        hourSelect.appendChild(option);
    });

    // 填充分钟选项
    const minuteSelect = document.getElementById('atMinute');
    minuteSelect.innerHTML = '';
    frequencyOptions.minute_options.forEach(minute => {
        const option = document.createElement('option');
        option.value = minute.value;
        option.textContent = minute.label;
        minuteSelect.appendChild(option);
    });

    // 填充星期几选项
    const weekdaySelect = document.getElementById('atWeekday');
    weekdaySelect.innerHTML = '';
    frequencyOptions.weekdays.forEach(weekday => {
        const option = document.createElement('option');
        option.value = weekday.value;
        option.textContent = weekday.label;
        weekdaySelect.appendChild(option);
    });

    // 填充日期选项
    const daySelect = document.getElementById('atDay');
    daySelect.innerHTML = '';
    frequencyOptions.day_options.forEach(day => {
        const option = document.createElement('option');
        option.value = day.value;
        option.textContent = day.label;
        daySelect.appendChild(option);
    });
}

// 根据频率类型更新表单字段显示
function updateFrequencyFormFields() {
    const frequencyType = document.getElementById('frequencyType').value;
    const intervalGroup = document.querySelector('.interval-group');
    const timeGroups = document.querySelectorAll('.time-group');
    const weekdayGroup = document.querySelector('.weekday-group');
    const dayGroup = document.querySelector('.day-group');
    const intervalUnit = document.getElementById('intervalUnit');
    const cronInput = document.getElementById('cronExpression');

    // 隐藏所有字段
    intervalGroup.style.display = 'none';
    timeGroups.forEach(group => group.style.display = 'none');
    weekdayGroup.style.display = 'none';
    dayGroup.style.display = 'none';

    // 根据频率类型显示相应字段
    switch (frequencyType) {
        case 'minutes':
            intervalGroup.style.display = 'block';
            intervalUnit.textContent = '分钟';
            document.getElementById('intervalValue').value = '5'; // 默认5分钟
            cronInput.readOnly = true;
            break;

        case 'hourly':
            intervalGroup.style.display = 'block';
            timeGroups[1].style.display = 'block'; // 显示分钟选择
            intervalUnit.textContent = '小时';
            document.getElementById('intervalValue').value = '1'; // 默认1小时
            cronInput.readOnly = true;
            break;

        case 'daily':
            timeGroups.forEach(group => group.style.display = 'block'); // 显示小时和分钟
            cronInput.readOnly = true;
            break;

        case 'weekly':
            timeGroups.forEach(group => group.style.display = 'block'); // 显示小时和分钟
            weekdayGroup.style.display = 'block'; // 显示星期几
            cronInput.readOnly = true;
            break;

        case 'monthly':
            timeGroups.forEach(group => group.style.display = 'block'); // 显示小时和分钟
            dayGroup.style.display = 'block'; // 显示日期
            cronInput.readOnly = true;
            break;

        case 'custom':
            // 自定义表达式：隐藏所有标准设置，启用cron表达式编辑
            cronInput.readOnly = false;
            cronInput.focus();
            cronInput.style.backgroundColor = '#fff';
            cronInput.style.borderColor = '#1890ff';
            // 如果当前是空的，设置一个示例
            if (!cronInput.value.trim()) {
                cronInput.value = '0 6,12,18 * * *';
            }
            return; // 不调用updateCronExpression，保持用户输入
    }

    // 更新Cron表达式（仅对标准频率类型）
    updateCronExpression();
}

// 根据表单设置更新Cron表达式
function updateCronExpression() {
    const frequencyType = document.getElementById('frequencyType').value;
    if (!frequencyType) return;

    const intervalValue = document.getElementById('intervalValue').value || '1';
    const atHour = document.getElementById('atHour').value || '0';
    const atMinute = document.getElementById('atMinute').value || '0';
    const atWeekday = document.getElementById('atWeekday').value || '1';
    const atDay = document.getElementById('atDay').value || '1';

    let cronExpression = '';

    switch (frequencyType) {
        case 'minutes':
            cronExpression = `*/${intervalValue} * * * *`;
            break;

        case 'hourly':
            cronExpression = `${atMinute} */${intervalValue} * * *`;
            break;

        case 'daily':
            cronExpression = `${atMinute} ${atHour} * * *`;
            break;

        case 'weekly':
            cronExpression = `${atMinute} ${atHour} * * ${atWeekday}`;
            break;

        case 'monthly':
            cronExpression = `${atMinute} ${atHour} ${atDay} * *`;
            break;
    }

    document.getElementById('cronExpression').value = cronExpression;
}

// 从Cron表达式中提取频率设置
function extractFrequencySettingsFromCron(cronExpression) {
    if (!cronExpression) return null;

    const parts = cronExpression.trim().split(/\s+/);
    if (parts.length !== 5) return null;

    const [minute, hour, day, month, weekday] = parts;

    // 每x分钟
    if (minute.startsWith('*/') && hour === '*' && day === '*' && month === '*' && weekday === '*') {
        return {
            frequency_type: 'minutes',
            interval: parseInt(minute.substring(2)),
            at_hour: null,
            at_minute: null,
            at_day: null,
            at_weekday: null
        };
    }

    // 每x小时
    if (hour.startsWith('*/') && day === '*' && month === '*' && weekday === '*') {
        return {
            frequency_type: 'hourly',
            interval: parseInt(hour.substring(2)),
            at_hour: null,
            at_minute: parseInt(minute),
            at_day: null,
            at_weekday: null
        };
    }

    // 检查是否包含多个时间点（逗号分隔）或其他复杂表达式
    if (hour.includes(',') || minute.includes(',') || day.includes(',') || weekday.includes(',')) {
        // 对于多时间点或复杂的cron表达式，返回自定义类型
        return {
            frequency_type: 'custom',
            interval: null,
            at_hour: null,
            at_minute: null,
            at_day: null,
            at_weekday: null
        };
    }

    // 每天
    if (day === '*' && month === '*' && weekday === '*' && !hour.includes('*')) {
        return {
            frequency_type: 'daily',
            interval: null,
            at_hour: parseInt(hour),
            at_minute: parseInt(minute),
            at_day: null,
            at_weekday: null
        };
    }

    // 每周
    if (day === '*' && month === '*' && !weekday.includes('*')) {
        return {
            frequency_type: 'weekly',
            interval: null,
            at_hour: parseInt(hour),
            at_minute: parseInt(minute),
            at_day: null,
            at_weekday: parseInt(weekday)
        };
    }

    // 每月
    if (month === '*' && weekday === '*' && !day.includes('*')) {
        return {
            frequency_type: 'monthly',
            interval: null,
            at_hour: parseInt(hour),
            at_minute: parseInt(minute),
            at_day: parseInt(day),
            at_weekday: null
        };
    }

    return null;
}

// 切换Cron表达式的编辑状态
function toggleCronExpressionEdit(event) {
    // 阻止默认行为，防止表单提交
    if (event) {
        event.preventDefault();
    }

    const cronInput = document.getElementById('cronExpression');
    const editBtn = document.getElementById('editCronBtn');

    if (cronInput.readOnly) {
        // 开启编辑
        cronInput.readOnly = false;
        cronInput.focus();
        editBtn.textContent = '应用';
        // 添加临时样式，突出显示正在编辑
        cronInput.style.backgroundColor = '#fff';
        cronInput.style.borderColor = '#1890ff';
    } else {
        // 应用编辑
        cronInput.readOnly = true;
        editBtn.textContent = '编辑';
        // 恢复默认样式
        cronInput.style.backgroundColor = '';
        cronInput.style.borderColor = '';

        // 尝试从Cron表达式提取频率设置
        const settings = extractFrequencySettingsFromCron(cronInput.value);
        if (settings) {
            // 更新频率设置表单
            applyFrequencySettings(settings);
        } else {
            // 如果无法解析cron表达式（如多时间点表达式），保持当前输入不变
            // 自定义cron表达式，保持用户输入
        }
    }
}

// 应用频率设置到表单
function applyFrequencySettings(settings) {
    // 设置频率类型
    document.getElementById('frequencyType').value = settings.frequency_type;
    updateFrequencyFormFields();

    // 设置间隔值
    if (settings.interval !== null) {
        document.getElementById('intervalValue').value = settings.interval;
    }

    // 设置时间
    if (settings.at_hour !== null) {
        document.getElementById('atHour').value = settings.at_hour;
    }

    if (settings.at_minute !== null) {
        document.getElementById('atMinute').value = settings.at_minute;
    }

    // 设置星期几
    if (settings.at_weekday !== null) {
        document.getElementById('atWeekday').value = settings.at_weekday;
    }

    // 设置日期
    if (settings.at_day !== null) {
        document.getElementById('atDay').value = settings.at_day;
    }
}

// 加载任务列表
async function loadTasks(taskType = null) {
    try {
        let url = '/api/tasks';
        if (taskType) {
            url += `?task_type=${taskType}`;
        }

        const response = await apiRequest(url, 'GET');
        if (!response || !Array.isArray(response)) {
            throw new Error('无效的任务数据');
        }
        renderTasksTable(response);
    } catch (error) {
        console.error('加载任务列表失败:', error);
        showMessage('加载任务列表失败', 'error');
        throw error;
    }
}

// 加载任务执行历史
async function loadTaskHistory(filters = {}) {
    console.log('开始加载爬虫数据城市列表...');
    try {
        showGlobalLoading();

        // 构建查询参数
        const queryParams = new URLSearchParams();

        // 添加城市筛选
        if (filters.area) {
            queryParams.append('city', filters.area);
        }

        // 添加时间范围筛选
        if (filters.startTime) {
            // 将日期时间转换为 YYYYMMDD_HHMMSS 格式
            const startDate = new Date(filters.startTime);
            const startTimeStr = formatDateToTimestamp(startDate);
            queryParams.append('start_time', startTimeStr);
        }

        if (filters.endTime) {
            // 将日期时间转换为 YYYYMMDD_HHMMSS 格式
            const endDate = new Date(filters.endTime);
            const endTimeStr = formatDateToTimestamp(endDate);
            queryParams.append('end_time', endTimeStr);
        }

        // 构建完整URL
        let url = '/api/tasks/factory/spider-data-files';
        if (queryParams.toString()) {
            url += '?' + queryParams.toString();
        }
        console.log('请求URL:', url);

        try {
            const response = await apiRequest(url, 'GET');
            console.log('爬虫数据城市列表响应:', response);

            if (response && Array.isArray(response.files)) {
                console.log('开始渲染爬虫数据城市列表...');
                await renderTaskHistory(response.files);
                console.log('爬虫数据城市列表渲染完成');
                return;
            }
        } catch (apiError) {
            console.warn('从 API 加载爬虫数据失败，将使用模拟数据:', apiError);
        }

        // 如果 API 请求失败，使用模拟数据
        console.log('使用模拟城市数据');
        const mockCities = [
            { city: '北京', count: 15, last_update: new Date().toISOString() },
            { city: '上海', count: 12, last_update: new Date().toISOString() },
            { city: '广州', count: 8, last_update: new Date().toISOString() },
            { city: '深圳', count: 10, last_update: new Date().toISOString() },
            { city: '成都', count: 6, last_update: new Date().toISOString() },
            { city: '杭州', count: 9, last_update: new Date().toISOString() }
        ];

        await renderTaskHistory(mockCities);
        console.log('模拟城市数据渲染完成');
    } catch (error) {
        console.error('加载爬虫数据城市列表失败:', error);
        const citiesContainer = document.getElementById('citiesContainer');
        if (citiesContainer) {
            citiesContainer.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 30px; background-color: #f9f9f9; border-radius: 8px; color: #999; font-size: 15px;">
                    <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px; color: #d9d9d9;"></i>
                    <div>暂无城市数据</div>
                </div>
            `;
        }
        showMessage('加载爬虫数据城市列表失败: ' + error.message, 'error');
    } finally {
        hideGlobalLoading();
        console.log('爬虫数据城市列表加载流程结束');
    }
}

// 将日期格式化为 YYYYMMDD_HHMMSS 格式
function formatDateToTimestamp(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}${month}${day}_${hours}${minutes}${seconds}`;
}

// 初始化筛选表单事件监听器
function initializeFilterEventListeners() {
    console.log('筛选功能已移至 HTML 内联脚本中实现');
    // 不需要在这里绑定事件，因为已在 HTML 内联脚本中实现
}

// 解析文件名
function parseFileName(fileName) {
    // 文件名格式：区域_类型_租售_日期_时间.json
    const parts = fileName.split('_');
    if (parts.length >= 5) {
        const area = parts[0];
        const type = "定时爬取58帖子";  // 固定显示为"定时爬取58帖子"
        const date = parts[3];
        const time = parts[4].replace('.json', '');

        // 格式化日期时间
        const year = date.substring(0, 4);
        const month = date.substring(4, 6);
        const day = date.substring(6, 8);
        const hour = time.substring(0, 2);
        const minute = time.substring(2, 4);

        const formattedDate = `${year}-${month}-${day} ${hour}:${minute}`;

        return {
            area,
            type,
            formattedDate,
            originalFileName: fileName
        };
    }
    return null;
}

// 将JSON对象转换为帖子卡片HTML
function renderDataCard(data) {
    // 提取需要展示的字段
    const {
        title = '',
        price = '',
        building_area = '', // 建筑面积
        type = '',  // 类型字段（"出租"或"出售"）
        agent_name = '',  // 经纪人字段
        agent_company = '',  // 公司字段
        description = '',
        publish_time = '',
        image_url = '', // 图片URL
        url = '', // 链接
        city = '', // 城市
        rank = '', // 排名字段
        is_managed = false // 是否为平台托管的帖子
    } = data;

    // 判断是出租还是出售
    const isRent = type === '出租';

    // 格式化面积显示
    const formattedArea = building_area ? `${building_area}㎡` : '';

    return `
        <div class="post-card ${is_managed ? 'managed-post' : ''}">
            <div class="post-header">
                <div class="post-info-block">
                    <div class="post-title-and-type">
                        <span class="post-type ${isRent ? 'rent' : 'sale'}">${escapeHtml(type)}</span>
                        ${is_managed ? '<span class="post-managed">平台托管</span>' : ''}
                        <h3 class="post-title">${escapeHtml(title)}</h3>
                        ${rank ? `<div class="post-rank-container">排名: <span class="post-rank-number">${escapeHtml(rank)}</span></div>` : ''}
                    </div>
                    <div class="post-price">${escapeHtml(price)}</div>
                </div>
            </div>

            <div class="post-info">
                ${city ? `
                <div class="info-item">
                    <span class="info-icon">📍</span>
                    ${escapeHtml(city)}
                </div>` : ''}

                ${formattedArea ? `
                <div class="info-item">
                    <span class="info-icon">📐</span>
                    ${escapeHtml(formattedArea)}
                </div>` : ''}

                <div class="info-item ${is_managed ? 'managed-agent' : ''}">
                    <span class="info-icon">${is_managed ? '🌟' : '👤'}</span>
                    ${escapeHtml(agent_name || '')} ${agent_company ? `- ${escapeHtml(agent_company)}` : ''}
                </div>
            </div>

            <div class="post-footer">
                <span class="publish-time">发布时间: ${escapeHtml(publish_time || '未知')}</span>
                ${url ? `<a href="${escapeHtml(url)}" target="_blank" class="view-original">查看原始页面</a>` : ''}
            </div>
        </div>
    `;
}

// 查看爬虫数据内容
async function viewSpiderContent(city) {
    try {
        showGlobalLoading();

        // 首先获取可用的日期列表
        const datesResponse = await apiRequest(`/api/tasks/factory/spider-data-dates/${city}`, 'GET');
        console.log('获取到时间列表:', datesResponse);

        // 确保即使API未返回时间数据也能显示当前日期
        let availableTimes = [];
        if (datesResponse && Array.isArray(datesResponse.times) && datesResponse.times.length > 0) {
            availableTimes = datesResponse.times;
        } else {
            // 如果没有返回日期，添加当前日期作为默认选项
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');

            availableTimes = [{
                timestamp: Math.floor(today.getTime() / 1000),
                task_id: String(Math.floor(today.getTime() / 1000)),
                date: `${year}${month}${day}`,
                datetime: today.toISOString().substring(0, 19).replace('T', ' ')
            }];
        }

        // 默认选择最新的时间点
        const defaultTime = availableTimes.length > 0 ? availableTimes[0] : null;

        // 使用选定的时间获取数据
        const response = await apiRequest(
            `/api/tasks/factory/spider-data-content/${city}?type=sale${defaultTime ? `&timestamp=${defaultTime.task_id}` : ''}`,
            'GET'
        );
        console.log('使用时间戳获取数据:', defaultTime, response);

        // 创建筛选条件表单
        const filters = `
            <div class="filter-container">
                <div style="display: flex; flex-wrap: wrap; gap: 15px; width: 100%; padding: 10px 0;">
                    <div style="display: flex; align-items: center;">
                        <span class="filter-label">当前城市:</span>
                        <strong style="font-size: 16px; margin-left: 5px;">${escapeHtml(city)}</strong>
                    </div>

                    <div style="display: flex; align-items: center; margin-left: 20px;">
                        <span class="filter-label">物业类型:</span>
                        <select id="propertyTypeSelect" class="type-select">
                            <option value="sale" selected>出售</option>
                            <option value="rent">出租</option>
                        </select>
                    </div>

                    <div style="display: flex; align-items: center; margin-left: 20px;">
                        <span class="filter-label">帖子类型:</span>
                        <select id="managedStatusSelect" class="type-select">
                            <option value="">全部</option>
                            <option value="true">平台托管</option>
                            <option value="false">非托管</option>
                        </select>
                    </div>

                    <div style="display: flex; align-items: center; margin-left: 20px;">
                        <span class="filter-label">时间点:</span>
                        <select id="dateSelect" class="type-select">
                            ${availableTimes.map(time =>
                                `<option value='${JSON.stringify(time)}' ${defaultTime && time.timestamp === defaultTime.timestamp ? 'selected' : ''}>${time.datetime}</option>`
                            ).join('')}
                        </select>
                    </div>

                    <button id="closeModalBtn" style="margin-left: auto; background-color: #fff; border: 1px solid #d9d9d9; padding: 6px 12px; border-radius: 4px; cursor: pointer;">关闭</button>
                </div>
            </div>
        `;

        // 创建样式
        const style = document.createElement('style');
        style.textContent = `
            .filter-container {
                padding: 12px 16px;
                background: white;
                border-bottom: 1px solid #e8e8e8;
                flex-shrink: 0;
            }
            .filter-label {
                font-size: 13px;
                color: #666;
            }
            .type-select {
                padding: 4px 10px;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                font-size: 13px;
                color: #333;
                background-color: white;
                cursor: pointer;
                outline: none;
                min-width: 100px;
            }
            .type-select:hover {
                border-color: #40a9ff;
            }
            .data-cards-container {
                flex: 1;
                overflow-y: auto;
                padding: 0;
                background: #f9f9f9;
            }
            .post-cards-wrapper {
                width: 100%;
            }
            .post-card {
                background: white;
                border-bottom: 1px solid #eee;
                padding: 12px 16px;
                transition: background-color 0.2s;
                position: relative;
            }
            .post-card:hover {
                background-color: #f5f5f5;
            }
            .post-card.managed-post {
                background-color: #f0f7ff;
            }
            .post-header {
                padding-bottom: 6px;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                justify-content: space-between;
            }
            .post-title-and-type {
                display: flex;
                flex: 1;
                align-items: center;
                gap: 8px;
                margin-bottom: 6px;
            }
            .post-info-block {
                flex: 1;
                min-width: 60%;
            }
            .post-rank-container {
                color: #666;
                font-size: 12px;
                white-space: nowrap;
                margin-left: 12px;
            }
            .post-rank-number {
                color: #1890ff;
                font-weight: bold;
            }
            .post-title {
                margin: 0;
                font-size: 15px;
                font-weight: bold;
                color: #333;
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 500px;
            }
            .post-type {
                display: inline-block;
                padding: 1px 8px;
                border-radius: 2px;
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #1890ff;
                white-space: nowrap;
            }
            .post-type.rent {
                background-color: #52c41a;
            }
            .post-type.sale {
                background-color: #1890ff;
            }
            .post-managed {
                display: inline-block;
                padding: 1px 6px;
                border-radius: 2px;
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #722ed1;
                margin-right: 6px;
                white-space: nowrap;
            }
            .post-price {
                font-size: 16px;
                font-weight: bold;
                color: #f5222d;
                margin-bottom: 6px;
                white-space: nowrap;
            }
            .post-info {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-bottom: 6px;
                color: #666;
                font-size: 13px;
            }
            .info-item {
                display: flex;
                align-items: center;
                gap: 4px;
                white-space: nowrap;
            }
            .info-icon {
                font-size: 14px;
                color: #999;
            }
            .post-footer {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 8px;
                color: #999;
                font-size: 12px;
            }
            .publish-time {
                color: #999;
            }
            .view-original {
                color: #1890ff;
                text-decoration: none;
                padding: 2px 8px;
                border: 1px solid #1890ff;
                border-radius: 4px;
                font-size: 12px;
            }
            .view-original:hover {
                background-color: #e6f7ff;
            }
            .post-count {
                padding: 8px 16px;
                border-bottom: 1px solid #eee;
                font-size: 13px;
                color: #666;
                background-color: white;
            }
            .highlight {
                color: #1890ff;
                font-weight: bold;
            }

            /* 移动端适配 */
            @media (max-width: 768px) {
                .post-title {
                    font-size: 14px;
                    max-width: 300px;
                }
                .post-price {
                    font-size: 15px;
                }
            }
        `;
        document.head.appendChild(style);

        // 创建模态框
        const modal = document.createElement('dialog');
        modal.id = 'spiderContentModal';
        modal.style.width = '95%';
        modal.style.maxWidth = '1200px';
        modal.style.height = '95vh';
        modal.style.maxHeight = '95vh';
        modal.style.border = 'none';
        modal.style.borderRadius = '4px';
        modal.style.padding = '0';
        modal.style.boxShadow = '0 0 20px rgba(0, 0, 0, 0.2)';
        modal.style.position = 'fixed';
        modal.style.top = '50%';
        modal.style.left = '50%';
        modal.style.transform = 'translate(-50%, -50%)';
        modal.style.margin = '0';
        modal.style.display = 'flex';
        modal.style.flexDirection = 'column';
        modal.style.backgroundColor = '#f9f9f9';

        // 渲染数据卡片
        let cardsHtml = '';
        if (Array.isArray(response) && response.length > 0) {
            // 添加数据总数计数
            cardsHtml += `
                <div class="post-count">
                    找到 <span class="highlight">${response.length}</span> 条记录
                </div>
                ${response.map(item => renderDataCard(item)).join('')}
            `;
        } else if (Array.isArray(response) && response.length === 0) {
            cardsHtml = '<div style="padding: 20px; text-align: center; color: #999;">没有找到匹配的数据</div>';
        } else {
            // 如果不是数组，尝试渲染单个对象
            cardsHtml = renderDataCard(response);
        }

        // 组合模态框内容
        modal.innerHTML = `
            ${filters}
            <div class="data-cards-container">
                ${cardsHtml}
            </div>
        `;

        document.body.appendChild(modal);
        modal.showModal();

        // 存储原始数据
        modal.dataset.originalData = JSON.stringify(Array.isArray(response) ? response : [response]);
        modal.dataset.city = city;

        // 点击模态框外部关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.close();
            }
        });

        // 关闭按钮事件
        const closeBtn = document.getElementById('closeModalBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.close();
            });
        }

        // 模态框关闭后清理
        modal.addEventListener('close', () => {
            document.body.removeChild(modal);
            document.head.removeChild(style);
        });

        // 添加筛选事件监听器
        const propertyTypeSelect = document.getElementById('propertyTypeSelect');
        const managedStatusSelect = document.getElementById('managedStatusSelect');
        const dateSelect = document.getElementById('dateSelect');

        if (dateSelect) {
            dateSelect.addEventListener('change', () => {
                try {
                    console.log('时间选择改变，新值:', dateSelect.value);
                    const propertyType = propertyTypeSelect ? propertyTypeSelect.value : 'sale';
                    const managedStatus = managedStatusSelect ? managedStatusSelect.value : '';

                    // 解析选择的时间点对象
                    const selectedTime = JSON.parse(dateSelect.value);
                    console.log('解析后的时间对象:', selectedTime);

                    applyFilters(city, propertyType, managedStatus, selectedTime);
                } catch (error) {
                    console.error('解析时间点数据失败:', error);
                    showMessage('解析时间点数据失败', 'error');
                }
            });
        }

        if (propertyTypeSelect) {
            propertyTypeSelect.addEventListener('change', () => {
                const propertyType = propertyTypeSelect.value;
                const managedStatus = managedStatusSelect ? managedStatusSelect.value : '';

                // 取得当前选择的时间点
                let selectedTime = null;
                try {
                    if (dateSelect && dateSelect.value) {
                        selectedTime = JSON.parse(dateSelect.value);
                    }
                } catch (error) {
                    console.error('解析时间点数据失败:', error);
                }

                applyFilters(city, propertyType, managedStatus, selectedTime);
            });
        }

        if (managedStatusSelect) {
            managedStatusSelect.addEventListener('change', () => {
                const propertyType = propertyTypeSelect ? propertyTypeSelect.value : 'sale';
                const managedStatus = managedStatusSelect.value;

                // 取得当前选择的时间点
                let selectedTime = null;
                try {
                    if (dateSelect && dateSelect.value) {
                        selectedTime = JSON.parse(dateSelect.value);
                    }
                } catch (error) {
                    console.error('解析时间点数据失败:', error);
                }

                applyFilters(city, propertyType, managedStatus, selectedTime);
            });
        }

    } catch (error) {
        console.error('获取城市数据失败:', error);
        showMessage('获取城市数据失败: ' + error.message, 'error');
    } finally {
        hideGlobalLoading();
    }
}

// 格式化日期显示
function formatDateDisplay(dateStr) {
    // 处理多种可能的日期格式
    if (!dateStr) return '';

    // 如果是YYYYMMDD格式（如20230501）
    if (/^\d{8}$/.test(dateStr)) {
        const year = dateStr.substring(0, 4);
        const month = dateStr.substring(4, 6);
        const day = dateStr.substring(6, 8);
        return `${year}-${month}-${day}`;
    }

    // 如果是ISO格式或其他格式，尝试使用Date对象解析
    try {
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        }
    } catch (e) {
        console.warn('日期格式化失败:', e);
    }

    // 返回原始字符串作为后备
    return dateStr;
}

// 应用筛选功能
async function applyFilters(city, propertyType, managedStatus, date) {
    try {
        showGlobalLoading();

        // 构建查询参数
        let url = `/api/tasks/factory/spider-data-content/${city}?type=${propertyType}`;
        if (managedStatus !== '') {
            url += `&is_managed=${managedStatus}`;
        }

        // 判断date是否是时间戳对象，如果是提取timestamp，否则使用日期字符串
        if (date) {
            if (typeof date === 'object' && date.timestamp) {
                url += `&timestamp=${date.timestamp}`;
                console.log(`应用时间戳筛选: ${date.timestamp}, 完整URL: ${url}`);
            } else {
                url += `&date=${date}`;
                console.log(`应用日期筛选: ${date}, 完整URL: ${url}`);
            }
        } else {
            console.log(`未指定日期，使用默认日期, URL: ${url}`);
        }

        // 向后端发送请求，应用筛选
        const response = await apiRequest(url, 'GET');
        console.log('筛选响应数据:', response);

        // 更新显示数据
        const container = document.querySelector('.data-cards-container');
        if (container) {
            // 获取展示的日期字符串
            let dateDisplay = '';
            if (date) {
                if (typeof date === 'object') {
                    dateDisplay = date.datetime || date.date || '';
                } else {
                    dateDisplay = formatDateDisplay(date);
                }
            }

            // 重新渲染内容
            let cardsHtml = '';
            if (Array.isArray(response) && response.length > 0) {
                cardsHtml = `
                    <div class="post-count">
                        找到 <span class="highlight">${response.length}</span> 条记录${dateDisplay ? `（${dateDisplay}）` : ''}
                    </div>
                    ${response.map(item => renderDataCard(item)).join('')}
                `;
            } else if (Array.isArray(response) && response.length === 0) {
                cardsHtml = `<div style="padding: 20px; text-align: center; color: #999;">没有找到匹配的数据${dateDisplay ? `（${dateDisplay}）` : ''}</div>`;
            } else {
                cardsHtml = renderDataCard(response);
            }
            container.innerHTML = cardsHtml;
        }

        // 更新modal的原始数据
        const modal = document.getElementById('spiderContentModal');
        if (modal) {
            modal.dataset.originalData = JSON.stringify(Array.isArray(response) ? response : [response]);
        }
    } catch (error) {
        console.error('应用筛选失败:', error);
        showMessage('应用筛选失败: ' + error.message, 'error');
    } finally {
        hideGlobalLoading();
    }
}

// 加载指定类型的房源数据 (保留旧函数兼容性，使用新的applyFilters函数)
async function loadPropertyType(type, city) {
    applyFilters(city, type, '', '');
}

// 渲染任务历史表格
async function renderTaskHistory(cities) {
    const citiesContainer = document.getElementById('citiesContainer');
    if (!citiesContainer) {
        console.error('城市容器元素不存在');
        showMessage('渲染城市列表失败：找不到容器元素', 'error');
        return;
    }

    if (!cities || cities.length === 0) {
        citiesContainer.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 30px; background-color: #f9f9f9; border-radius: 8px; color: #999; font-size: 15px;">
                <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px; color: #d9d9d9;"></i>
                <div>暂无城市数据</div>
            </div>
        `;
        // 隐藏分页控件
        const paginationControls = document.getElementById('paginationControls');
        if (paginationControls) {
            paginationControls.style.display = 'none';
        }
        return;
    }

    // 保存所有城市数据，更新分页信息
    cityPagination.allCities = [...cities];
    cityPagination.totalPages = Math.ceil(cities.length / cityPagination.itemsPerPage);
    cityPagination.currentPage = 1;

    // 更新页面指示器
    updatePaginationInfo();

    // 渲染当前页数据
    renderCurrentPageCities();

    // 为分页按钮添加事件监听
    setupPaginationEvents();

    // 为查看按钮添加悬停效果
    const style = document.createElement('style');
    style.textContent = `
        .view-city-btn:hover {
            background-color: #40a9ff !important;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.35);
        }
    `;
    if (!document.getElementById('city-btn-style')) {
        style.id = 'city-btn-style';
        document.head.appendChild(style);
    }
}

// 渲染当前页城市数据
function renderCurrentPageCities() {
    const citiesContainer = document.getElementById('citiesContainer');
    if (!citiesContainer) return;

    // 计算当前页的城市
    const startIndex = (cityPagination.currentPage - 1) * cityPagination.itemsPerPage;
    const endIndex = Math.min(startIndex + cityPagination.itemsPerPage, cityPagination.allCities.length);
    const currentPageCities = cityPagination.allCities.slice(startIndex, endIndex);

    // 渲染城市卡片
    citiesContainer.innerHTML = currentPageCities.map(city => {
        // 根据城市名称生成随机浅色背景色
        const seed = city.charCodeAt(0) + (city.charCodeAt(1) || 0);
        const hue = seed % 360;
        const bgColor = `hsl(${hue}, 70%, 96%)`;
        const textColor = `hsl(${hue}, 80%, 30%)`;

        return `
            <div class="city-card" style="background-color: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); overflow: hidden; display: flex; flex-direction: column;">
                <div style="padding: 20px; flex-grow: 1; display: flex; flex-direction: column;">
                    <div style="margin-bottom: 5px; font-size: 13px; color: #888;">城市</div>
                    <div style="background-color: ${bgColor}; color: ${textColor}; display: inline-block; padding: 5px 15px; border-radius: 20px; font-size: 18px; font-weight: bold; margin-bottom: 20px; align-self: flex-start;">
                        ${escapeHtml(city)}
                    </div>
                    <div style="margin-top: auto;">
                        <button onclick="viewSpiderContent('${city}')" class="view-city-btn" style="width: 100%; height: 40px; background-color: #1890ff; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.3s; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-eye" style="margin-right: 8px;"></i> 查看内容
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    // 显示分页控件（如果有多页）
    const paginationControls = document.getElementById('paginationControls');
    if (paginationControls) {
        paginationControls.style.display = cityPagination.totalPages > 1 ? 'flex' : 'none';
    }
}

// 更新分页信息和按钮状态
function updatePaginationInfo() {
    // 更新页码信息
    const currentPageElement = document.getElementById('currentPage');
    const totalPagesElement = document.getElementById('totalPages');

    if (currentPageElement) {
        currentPageElement.textContent = cityPagination.currentPage;
    }

    if (totalPagesElement) {
        totalPagesElement.textContent = cityPagination.totalPages;
    }

    // 更新按钮状态
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');

    if (prevBtn) {
        prevBtn.disabled = cityPagination.currentPage <= 1;
    }

    if (nextBtn) {
        nextBtn.disabled = cityPagination.currentPage >= cityPagination.totalPages;
    }
}

// 设置分页按钮事件
function setupPaginationEvents() {
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');

    if (prevBtn) {
        prevBtn.onclick = function() {
            if (cityPagination.currentPage > 1) {
                cityPagination.currentPage--;
                updatePaginationInfo();
                renderCurrentPageCities();
                // 滚动到容器顶部
                document.getElementById('citiesContainer').scrollIntoView({ behavior: 'smooth' });
            }
        };
    }

    if (nextBtn) {
        nextBtn.onclick = function() {
            if (cityPagination.currentPage < cityPagination.totalPages) {
                cityPagination.currentPage++;
                updatePaginationInfo();
                renderCurrentPageCities();
                // 滚动到容器顶部
                document.getElementById('citiesContainer').scrollIntoView({ behavior: 'smooth' });
            }
        };
    }
}

// 显示新建任务模态框
function showCreateTaskModal() {
    document.getElementById('modalTitle').textContent = '新建定时任务';
    resetTaskForm();
    const modal = document.getElementById('taskModal');
    if (modal) {
        if (typeof modal.showModal === 'function') {
            modal.showModal();
        } else {
            modal.setAttribute('open', '');
        }
    }
}

// 重置任务表单
function resetTaskForm() {
    document.getElementById('taskName').value = '';
    // 重置为默认任务类型（Shell脚本）
    document.querySelector('input[name="taskType"][value="shell_script"]').checked = true;
    updateTaskTypeUI('shell_script');
    document.getElementById('cronExpression').value = '';
    document.getElementById('taskParams').value = '';
    document.getElementById('taskDescription').value = '';

    // 重置频率设置
    document.getElementById('frequencyType').value = '';
    document.getElementById('intervalValue').value = '';

    // 隐藏所有高级设置
    document.querySelector('.interval-group').style.display = 'none';
    document.querySelectorAll('.time-group').forEach(group => group.style.display = 'none');
    document.querySelector('.weekday-group').style.display = 'none';
    document.querySelector('.day-group').style.display = 'none';

    // 清除所有错误状态
    clearFormErrors();
}

// 显示编辑任务模态框
async function editTask(taskCode) {
    try {
        showGlobalLoading();

        // 获取任务详情
        const task = await apiRequest(`/api/tasks/${taskCode}`, 'GET');

        // 确保模态框存在
        const taskModal = document.getElementById('taskModal');
        if (!taskModal) {
            showMessage('系统错误：无法打开编辑窗口', 'error');
            return;
        }

        // 设置模态框标题和数据
        document.getElementById('modalTitle').textContent = '编辑定时任务';
        document.getElementById('taskName').value = task.task_name;

        // 设置任务类型（单选框）- 根据实际任务类型设置
        const taskType = task.task_type || 'shell_script'; // 默认为shell_script
        const taskTypeRadio = document.querySelector(`input[name="taskType"][value="${taskType}"]`);
        if (taskTypeRadio) {
            taskTypeRadio.checked = true;
            // 更新任务类型相关的UI
            updateTaskTypeUI(taskType);
        } else {
            console.warn(`找不到任务类型 ${taskType} 的单选框元素`);
            // 降级处理：选择默认的shell_script
            const defaultRadio = document.querySelector('input[name="taskType"][value="shell_script"]');
            if (defaultRadio) {
                defaultRadio.checked = true;
                updateTaskTypeUI('shell_script');
            }
        }

        // 设置其他字段
        document.getElementById('cronExpression').value = task.cron_expression;
        document.getElementById('taskDescription').value = task.description || '';

        // 如果有任务参数，填充到表单
        if (task.command) {
            document.getElementById('taskParams').value = task.command;
        }

        // 尝试从Cron表达式提取频率设置
        const settings = extractFrequencySettingsFromCron(task.cron_expression);
        if (settings) {
            // 更新频率设置表单
            applyFrequencySettings(settings);
        } else {
            // 如果无法提取，至少选择一个默认的频率类型
            document.getElementById('frequencyType').value = 'minutes';
            updateFrequencyFormFields();
        }

        // 存储任务代码到模态框，用于后续保存
        taskModal.dataset.taskCode = taskCode;

        // 显示对话框
        console.log('显示编辑任务对话框');
        if (typeof taskModal.showModal === 'function') {
            taskModal.showModal();
            console.log('编辑对话框已显示(modal)');
        } else {
            // 降级处理
            taskModal.setAttribute('open', '');
            console.log('编辑对话框已显示(attribute)');
        }

    } catch (error) {
        console.error('加载任务详情失败:', error);
        showMessage('加载任务详情失败: ' + error.message, 'error');
    } finally {
        hideGlobalLoading();
    }
}

// 关闭任务模态框
function closeTaskModal() {
    const modal = document.getElementById('taskModal');
    if (modal) {
        // 添加关闭动画
        modal.style.animation = 'modalFadeOut 0.3s ease-out forwards';

        setTimeout(() => {
            if (typeof modal.close === 'function') {
                modal.close();
            } else {
                modal.removeAttribute('open');
            }
            // 重置动画
            modal.style.animation = '';
        }, 300);
    }
    resetTaskForm();
}

// 保存任务
async function saveTask() {
    try {
        // 收集表单数据
        const selectedTaskType = document.querySelector('input[name="taskType"]:checked').value;
        const taskData = {
            task_name: document.getElementById('taskName').value.trim(),
            task_type: selectedTaskType,
            cron_expression: document.getElementById('cronExpression').value.trim(),
            command: document.getElementById('taskParams').value.trim(),
            description: document.getElementById('taskDescription').value.trim()
        };

        // 添加频率设置（仅对非自定义类型）
        const frequencyType = document.getElementById('frequencyType').value;
        if (frequencyType && frequencyType !== 'custom') {
            const frequencySettings = {
                frequency_type: frequencyType,
                interval: 1,  // 默认值
                at_hour: null,
                at_minute: null,
                at_day: null,
                at_weekday: null
            };

            // 根据不同频率类型收集相应设置
            switch (frequencyType) {
                case 'minutes':
                    frequencySettings.interval = parseInt(document.getElementById('intervalValue').value || '5');
                    break;

                case 'hourly':
                    frequencySettings.interval = parseInt(document.getElementById('intervalValue').value || '1');
                    frequencySettings.at_minute = parseInt(document.getElementById('atMinute').value || '0');
                    break;

                case 'daily':
                    frequencySettings.interval = 1; // 每天的间隔为1
                    frequencySettings.at_hour = parseInt(document.getElementById('atHour').value || '0');
                    frequencySettings.at_minute = parseInt(document.getElementById('atMinute').value || '0');
                    break;

                case 'weekly':
                    frequencySettings.interval = 1; // 每周的间隔为1
                    frequencySettings.at_hour = parseInt(document.getElementById('atHour').value || '0');
                    frequencySettings.at_minute = parseInt(document.getElementById('atMinute').value || '0');
                    frequencySettings.at_weekday = parseInt(document.getElementById('atWeekday').value || '1');
                    break;

                case 'monthly':
                    frequencySettings.interval = 1; // 每月的间隔为1
                    frequencySettings.at_hour = parseInt(document.getElementById('atHour').value || '0');
                    frequencySettings.at_minute = parseInt(document.getElementById('atMinute').value || '0');
                    frequencySettings.at_day = parseInt(document.getElementById('atDay').value || '1');
                    break;
            }

            taskData.frequency_settings = frequencySettings;
        }
        // 对于自定义类型，只发送cron_expression，不发送frequency_settings

        // 验证表单数据
        const validationErrors = validateTaskForm(taskData);
        if (validationErrors.length > 0) {
            validationErrors.forEach(error => {
                showFormError(error.field, error.message);
            });
            return;
        }

        // 显示加载指示器
        showGlobalLoading();

        let response;
        const taskModal = document.getElementById('taskModal');
        const taskCode = taskModal.dataset.taskCode;

        if (taskCode) {
            // 更新现有任务
            response = await apiRequest(`/api/tasks/${taskCode}`, 'PUT', taskData);
            showMessage('任务已成功更新', 'success');
        } else {
            // 创建新任务
            // 生成一个唯一的任务代码
            taskData.task_code = 'TASK_' + new Date().getTime();
            response = await apiRequest('/api/tasks', 'POST', taskData);
            showMessage('任务已成功创建', 'success');
        }

        // 关闭模态框
        closeTaskModal();

        // 重新加载任务列表
        const currentFilter = document.getElementById('taskTypeFilter').value;
        await loadTasks(currentFilter || null);
    } catch (error) {
        console.error('保存任务失败:', error);
        showMessage('保存任务失败: ' + error.message, 'error');
    } finally {
        hideGlobalLoading();
    }
}

// 验证任务表单
function validateTaskForm(data) {
    clearFormErrors();
    const errors = [];

    if (!data.task_name) {
        errors.push({ field: 'taskName', message: '请输入任务名称' });
    }

    // 任务类型不再需要验证，因为它总是有值

    // 验证频率设置
    const frequencyType = document.getElementById('frequencyType').value;
    if (!frequencyType) {
        errors.push({ field: 'frequencyType', message: '请选择执行频率类型' });
    } else if (frequencyType !== 'custom') {
        // 对于非自定义类型，根据频率类型验证相应字段
        switch (frequencyType) {
            case 'minutes':
            case 'hourly':
                const intervalValue = document.getElementById('intervalValue').value;
                if (!intervalValue || isNaN(intervalValue) || parseInt(intervalValue) <= 0) {
                    errors.push({ field: 'intervalValue', message: '请输入有效的间隔值（大于0）' });
                }
                break;
        }
    }
    // 对于自定义类型，只验证cron表达式本身

    if (!data.cron_expression) {
        errors.push({ field: 'cronExpression', message: '请输入执行周期' });
    } else if (!isValidCronExpression(data.cron_expression)) {
        errors.push({ field: 'cronExpression', message: '无效的Cron表达式格式' });
    }

    if (!data.command) {
        errors.push({ field: 'taskParams', message: '请输入命令' });
    }

    return errors;
}

// 显示表单错误
function showFormError(fieldId, message) {
    // 获取字段元素
    const field = document.getElementById(fieldId);
    if (!field) return;

    // 添加错误样式
    field.classList.add('error');

    // 查找或创建错误消息元素
    let errorElement = field.nextElementSibling;
    if (!errorElement || !errorElement.classList.contains('form-error')) {
        errorElement = document.createElement('div');
        errorElement.className = 'form-error';
        field.parentNode.insertBefore(errorElement, field.nextSibling);
    }

    // 设置错误消息
    errorElement.textContent = message;
}

// 清除表单错误
function clearFormErrors() {
    // 移除所有错误样式
    const errorFields = document.querySelectorAll('.error');
    errorFields.forEach(field => {
        field.classList.remove('error');
    });

    // 清除所有错误消息
    const errorMessages = document.querySelectorAll('.form-error');
    errorMessages.forEach(message => {
        message.textContent = '';
        message.remove();
    });
}

// 验证Cron表达式
function isValidCronExpression(cron) {
    // 更宽松的Cron表达式验证，支持分钟字段中的*/n格式
    const parts = cron.trim().split(/\s+/);
    if (parts.length !== 5) return false;

    // 简化验证，只要格式是5部分的都认为有效
    // 因为这些表达式是由系统生成的，基本可以保证有效性
    return true;
}

// 切换任务状态
async function toggleTaskStatus(taskCode, currentStatus) {
    try {
        if (!confirm(currentStatus === 1 ? '确定要暂停此任务吗？' : '确定要启动此任务吗？')) {
            return;
        }

        showGlobalLoading();
        let endpoint;

        if (currentStatus === 1) {
            endpoint = `/api/tasks/${taskCode}/pause`;
        } else {
            endpoint = `/api/tasks/${taskCode}/resume`;
        }

        const response = await apiRequest(endpoint, 'POST');
        showMessage(currentStatus === 1 ? '任务已暂停' : '任务已启动', 'success');

        // 刷新任务列表以显示最新状态
        const currentFilter = document.getElementById('taskTypeFilter').value;
        await loadTasks(currentFilter || null);
    } catch (error) {
        console.error('切换任务状态失败:', error);
        showMessage('切换任务状态失败: ' + error.message, 'error');
    } finally {
        hideGlobalLoading();
    }
}

// 删除任务
async function deleteTask(taskCode) {
    try {
        if (!confirm('确定要删除此任务吗？此操作不可恢复！')) {
            return;
        }

        showGlobalLoading();
        await apiRequest(`/api/tasks/${taskCode}`, 'DELETE');
        showMessage('任务已成功删除', 'success');

        // 刷新任务列表
        const currentFilter = document.getElementById('taskTypeFilter').value;
        await loadTasks(currentFilter || null);
    } catch (error) {
        console.error('删除任务失败:', error);
        showMessage('删除任务失败: ' + error.message, 'error');
    } finally {
        hideGlobalLoading();
    }
}

// 显示任务执行详情
async function showTaskDetails(recordId) {
    try {
        showGlobalLoading();
        const details = await apiRequest(`/tasks/history/${recordId}`, 'GET');
        alert(JSON.stringify(details, null, 2));  // 临时使用alert，后续可以改为模态框展示
    } catch (error) {
        console.error('加载任务详情失败:', error);
        showMessage('加载任务详情失败: ' + error.message, 'error');
    } finally {
        hideGlobalLoading();
    }
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
}

// HTML转义
function escapeHtml(str) {
    if (!str) return '';
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
}

// 立即执行任务
async function runTaskNow(taskCode) {
    try {
        if (!confirm('确定要立即执行此任务吗？')) {
            return;
        }

        showGlobalLoading();
        const response = await apiRequest(`/api/tasks/${taskCode}/run_now`, 'POST');
        showMessage('任务已开始执行', 'success');

        // 刷新任务列表以显示最新状态
        await loadTasks();
    } catch (error) {
        console.error('执行任务失败:', error);
        showMessage('执行任务失败: ' + error.message, 'error');
    } finally {
        hideGlobalLoading();
    }
}

// 添加任务按钮事件监听器（使用事件委托避免重复绑定）
function addTaskButtonEventListeners() {
    // 移除旧的事件监听器（如果存在）
    const tbody = document.getElementById('tasksTableBody');
    if (tbody && !tbody.hasAttribute('data-listeners-added')) {
        tbody.addEventListener('click', function(event) {
            const target = event.target;

            // 编辑按钮
            if (target.classList.contains('edit-task-btn')) {
                const taskCode = target.dataset.taskCode;
                editTask(taskCode);
                return;
            }

            // 切换状态按钮
            if (target.classList.contains('toggle-status-btn')) {
                const taskCode = target.dataset.taskCode;
                const isActive = target.dataset.isActive === '1';
                toggleTaskStatus(taskCode, isActive ? 1 : 0);
                return;
            }

            // 立即执行按钮
            if (target.classList.contains('run-now-btn')) {
                const taskCode = target.dataset.taskCode;
                runTaskNow(taskCode);
                return;
            }

            // 删除按钮
            if (target.classList.contains('delete-task-btn')) {
                const taskCode = target.dataset.taskCode;
                deleteTask(taskCode);
                return;
            }
        });

        // 标记已添加监听器
        tbody.setAttribute('data-listeners-added', 'true');
    }
}

// 加载爬虫数据文件
async function loadSpiderDataFiles() {
    try {
        const response = await apiRequest('/factory/spider-data-files', 'GET');
        console.log('爬虫数据文件响应:', response);
        return response.files || [];
    } catch (error) {
        console.error('加载爬虫数据文件失败:', error);
        showMessage('加载爬虫数据文件失败: ' + error.message, 'error');
        return [];
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 渲染任务列表表格
function renderTasksTable(tasks) {
    const tbody = document.getElementById('tasksTableBody');
    if (!tasks || tasks.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">暂无定时任务</td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = tasks.map(task => `
        <tr>
            <td>${escapeHtml(task.task_name)}</td>
            <td>
                <span class="task-type-badge ${task.task_type === 'python_script' ? 'python' : 'shell'}">
                    ${task.task_type === 'python_script' ? 'Python脚本' : 'Shell脚本'}
                </span>
            </td>
            <td>${escapeHtml(task.cron_expression)}</td>
            <td>${formatDateTime(task.next_run_time)}</td>
            <td>
                <span class="status-badge ${task.is_active ? 'success' : 'warning'}">
                    ${task.is_active ? '运行中' : '已暂停'}
                </span>
            </td>
            <td>${formatDateTime(task.last_run_time)}</td>
            <td>
                <button class="btn-small primary edit-task-btn" data-task-code="${task.task_code}">编辑</button>
                <button class="btn-small ${task.is_active ? 'warning' : 'success'} toggle-status-btn"
                    data-task-code="${task.task_code}"
                    data-is-active="${task.is_active ? 1 : 0}">
                    ${task.is_active ? '暂停' : '启动'}
                </button>
                <button class="btn-small primary run-now-btn" data-task-code="${task.task_code}">立即执行</button>
                <button class="btn-small danger delete-task-btn" data-task-code="${task.task_code}">删除</button>
            </td>
        </tr>
    `).join('');

    // 添加按钮事件监听器
    addTaskButtonEventListeners();
}

// 图片轮播相关函数
function initializeImageSliders() {
    const sliders = document.querySelectorAll('.image-slider');

    sliders.forEach(slider => {
        const items = slider.querySelectorAll('.image-item');
        if (items.length === 0) return;

        // 添加导航按钮
        if (items.length > 1) {
            const nav = document.createElement('div');
            nav.className = 'image-navigation';
            nav.innerHTML = `
                <button class="prev" onclick="prevImage(this)">&lt;</button>
                <button class="next" onclick="nextImage(this)">&gt;</button>
            `;
            slider.appendChild(nav);
        }

        // 激活第一张图片
        items[0].classList.add('active');
    });
}

function prevImage(button) {
    const slider = button.closest('.image-slider');
    const items = slider.querySelectorAll('.image-item');
    let activeIndex = Array.from(items).findIndex(item => item.classList.contains('active'));

    items[activeIndex].classList.remove('active');
    activeIndex = (activeIndex - 1 + items.length) % items.length;
    items[activeIndex].classList.add('active');
}

function nextImage(button) {
    const slider = button.closest('.image-slider');
    const items = slider.querySelectorAll('.image-item');
    let activeIndex = Array.from(items).findIndex(item => item.classList.contains('active'));

    items[activeIndex].classList.remove('active');
    activeIndex = (activeIndex + 1) % items.length;
    items[activeIndex].classList.add('active');
}

// 任务统计数据
let taskStats = {
    totalTasks: 0,
    activeTasks: 0,
    pausedTasks: 0,
    todayExecutions: 0
};

// 数字增长动画
function animateNumbers(elementId) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const finalValue = parseInt(element.textContent);
    if (isNaN(finalValue)) return;

    let startValue = 0;
    const duration = 1000; // 动画时间（毫秒）
    const frameDuration = 16; // 每帧时间（毫秒）
    const totalFrames = Math.round(duration / frameDuration);
    let currentFrame = 0;

    function easeOutQuad(t) {
        return t * (2 - t);
    }

    function updateNumber() {
        currentFrame++;
        const progress = easeOutQuad(currentFrame / totalFrames);
        const currentValue = Math.round(startValue + progress * (finalValue - startValue));
        element.textContent = currentValue;

        if (currentFrame < totalFrames) {
            requestAnimationFrame(updateNumber);
        } else {
            element.textContent = finalValue;
        }
    }

    requestAnimationFrame(updateNumber);
}

// 更新状态卡片
function updateStatsCards() {
    document.getElementById('totalTasksCount').textContent = taskStats.totalTasks;
    document.getElementById('activeTasksCount').textContent = taskStats.activeTasks;
    document.getElementById('pausedTasksCount').textContent = taskStats.pausedTasks;
    document.getElementById('todayExecutionsCount').textContent = taskStats.todayExecutions;

    // 添加数字增长动画
    animateNumbers('totalTasksCount');
    animateNumbers('activeTasksCount');
    animateNumbers('pausedTasksCount');
    animateNumbers('todayExecutionsCount');
}

// 加载任务统计数据
async function loadTaskStats() {
    try {
        console.log('开始加载任务统计数据...');

        try {
            // 尝试从 API 获取数据
            const response = await apiRequest('/api/tasks', 'GET');
            if (response && Array.isArray(response)) {
                // 计算统计数据
                taskStats.totalTasks = response.length;
                taskStats.activeTasks = response.filter(task => task.is_active).length;
                taskStats.pausedTasks = response.filter(task => !task.is_active).length;

                // 模拟今日执行数
                taskStats.todayExecutions = Math.floor(Math.random() * 20) + 5;

                // 更新状态卡片
                updateStatsCards();
                console.log('任务统计数据加载完成');
                return;
            }
        } catch (apiError) {
            console.warn('从 API 加载任务统计数据失败，将使用模拟数据:', apiError);
        }

        // 如果 API 请求失败，使用模拟数据
        console.log('使用模拟统计数据');

        // 设置模拟数据
        taskStats.totalTasks = 3;
        taskStats.activeTasks = 2;
        taskStats.pausedTasks = 1;
        taskStats.todayExecutions = 8;

        // 更新状态卡片
        updateStatsCards();
        console.log('模拟统计数据加载完成');

    } catch (error) {
        console.error('加载任务统计数据失败:', error);
        showMessage('加载任务统计数据失败', 'error');

        // 即使出错也设置一些默认值
        taskStats.totalTasks = 0;
        taskStats.activeTasks = 0;
        taskStats.pausedTasks = 0;
        taskStats.todayExecutions = 0;
        updateStatsCards();
    }
}

// 更新任务类型UI
function updateTaskTypeUI(taskType) {
    const taskParamsHelp = document.getElementById('taskParamsHelp');
    const taskParams = document.getElementById('taskParams');

    if (taskType === 'python_script') {
        taskParamsHelp.textContent = '请输入Python脚本路径或完整命令（如：scripts/example.py 或 python run_ports_manage.py full）';
        taskParams.placeholder = 'scripts/example.py 或 python run_ports_manage.py full';
    } else {
        taskParamsHelp.textContent = '请输入Shell脚本路径（相对于项目根目录，如：scripts/example.sh）';
        taskParams.placeholder = 'scripts/example.sh';
    }

    // 更新隐藏字段
    document.getElementById('taskType').value = taskType;
}

// 初始化任务筛选功能
function initTaskFilters() {
    const applyFilterBtn = document.getElementById('applyTaskFilterBtn');
    const resetFilterBtn = document.getElementById('resetTaskFilterBtn');

    if (applyFilterBtn) {
        applyFilterBtn.addEventListener('click', async function() {
            const taskType = document.getElementById('taskTypeFilter').value;
            showGlobalLoading();
            try {
                await loadTasks(taskType || null);
                showMessage('筛选已应用', 'success');
            } catch (error) {
                showMessage('应用筛选失败: ' + error.message, 'error');
            } finally {
                hideGlobalLoading();
            }
        });
    }

    if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', async function() {
            document.getElementById('taskTypeFilter').value = '';
            showGlobalLoading();
            try {
                await loadTasks(null);
                showMessage('筛选已重置', 'success');
            } catch (error) {
                showMessage('重置筛选失败: ' + error.message, 'error');
            } finally {
                hideGlobalLoading();
            }
        });
    }
}

// 初始化任务类型选择器
function initTaskTypeSelector() {
    const taskTypeRadios = document.querySelectorAll('input[name="taskType"]');
    taskTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                updateTaskTypeUI(this.value);
            }
        });
    });
}

// 爬虫数据筛选功能（从内联脚本移入）
function initSpiderDataFilters() {
    const applyFilterBtn = document.getElementById('applyFilterBtn');
    const resetFilterBtn = document.getElementById('resetFilterBtn');

    if (applyFilterBtn) {
        applyFilterBtn.addEventListener('click', function() {
            const areaFilter = document.getElementById('areaFilter').value.trim();
            const queryParams = new URLSearchParams();

            if (areaFilter) {
                queryParams.append('city', areaFilter);
            }

            let url = '/api/tasks/factory/spider-data-files';
            if (queryParams.toString()) {
                url += '?' + queryParams.toString();
            }

            showMessage('正在应用筛选...', 'info');

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data && Array.isArray(data.files)) {
                        renderTaskHistory(data.files);
                        showMessage('筛选已应用', 'success');
                    } else {
                        throw new Error('获取筛选结果失败');
                    }
                })
                .catch(error => {
                    showMessage('应用筛选时出错: ' + error.message, 'error');
                });
        });
    }

    if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', function() {
            document.getElementById('areaFilter').value = '';
            showMessage('正在重置筛选...', 'info');

            fetch('/api/tasks/factory/spider-data-files')
                .then(response => response.json())
                .then(data => {
                    if (data && Array.isArray(data.files)) {
                        renderTaskHistory(data.files);
                        showMessage('筛选已重置', 'success');
                    } else {
                        throw new Error('获取重置后的数据失败');
                    }
                })
                .catch(error => {
                    showMessage('重置筛选时出错: ' + error.message, 'error');
                });
        });
    }
}