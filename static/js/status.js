// 城市配置数据
let cityConfig = {};
// 记录分页数据
let recordsData = {
    page: 1,
    limit: 10,
    total: 0,
    records: []
};
// 过滤条件
let recordsFilter = {
    erpHouseId: '',
    websiteId: ''
};
// 统计数据
let statsData = {
    todayCount: 0,
    successCount: 0,
    failedCount: 0,
    needAuthCount: 0
};

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面动画
    initPageAnimations();
    
    // 获取城市配置
    fetchCityConfig();
    
    // 绑定高级筛选区域显示/隐藏功能
    document.getElementById('toggleFilterBtn').addEventListener('click', function() {
        const filterContent = document.getElementById('advancedFilterContent');
        const isHidden = filterContent.style.display === 'none';
        
        if (isHidden) {
            filterContent.style.display = 'block';
            this.classList.add('active');
            this.querySelector('i').classList.remove('fa-chevron-down');
            this.querySelector('i').classList.add('fa-chevron-up');
        } else {
            filterContent.style.display = 'none';
            this.classList.remove('active');
            this.querySelector('i').classList.remove('fa-chevron-up');
            this.querySelector('i').classList.add('fa-chevron-down');
        }
    });
    
    // 初始时隐藏高级筛选区域
    document.getElementById('advancedFilterContent').style.display = 'none';
    
    // 绑定城市输入框变化事件
    document.getElementById('cityInput').addEventListener('change', function() {
        const userInput = this.value.trim();
        let cityCode = '';

        // 尝试从 datalist 中查找匹配的选项来获取 cityCode
        const dataListOptions = document.getElementById('cityList').options;
        for (let i = 0; i < dataListOptions.length; i++) {
            // 检查是否与 "城市名称 (代码)" 格式匹配
            if (dataListOptions[i].value === userInput) {
                 // 从 "城市名称 (代码)" 中提取代码
                 const match = userInput.match(/\(([^)]+)\)$/);
                 if (match && match[1]) {
                     cityCode = match[1];
                     break;
                 }
            }
            // 检查是否直接输入了代码
            if (dataListOptions[i].dataset.value === userInput) {
                cityCode = userInput;
                break;
            }
        }

        // 如果未找到有效 cityCode 或 cityCode 不在配置中，进行提示
        if (!cityCode || !cityConfig.domains[cityCode]) {
             // 尝试检查用户输入是否直接是 cityConfig 中的 key
            if (cityConfig.domains[userInput]) {
                cityCode = userInput;
                // 更新输入框的值为规范格式
                const cityName = cityConfig.names[cityCode] || cityCode;
                this.value = `${cityName} (${cityCode})`;
            } else {
                showToast('warning', '请输入或选择一个有效的城市');
                document.getElementById('userIdInput').value = '';
                clearRecordsTable();
                return; // 阻止后续操作
            }
        }


        if (cityCode) {
            // 获取默认用户ID
            const userId = cityConfig.accounts[cityCode]?.user_id || '';
            document.getElementById('userIdInput').value = userId;
            
            // 获取该城市的推送记录
            fetchPushRecords(cityCode);
        } else {
            document.getElementById('userIdInput').value = '';
            clearRecordsTable();
        }
    });
    
    // 用户ID输入框变化时重新加载记录
    document.getElementById('userIdInput').addEventListener('change', function() {
        const cityInput = document.getElementById('cityInput').value.trim();
        const cityCode = getCityCodeFromInput(cityInput); // 使用辅助函数获取代码
        if (cityCode) {
            fetchPushRecords(cityCode);
        }
    });
    
    // 绑定筛选按钮事件
    document.getElementById('filterBtn').addEventListener('click', function() {
        // 添加按钮加载效果
        setButtonLoading(this, true);
        
        recordsFilter.erpHouseId = document.getElementById('filterErpId').value.trim();
        recordsFilter.websiteId = document.getElementById('filterWebsite').value;
        recordsData.page = 1;
        
        const cityInput = document.getElementById('cityInput').value.trim();
        const cityCode = getCityCodeFromInput(cityInput);
        if (cityCode) {
            fetchPushRecords(cityCode).finally(() => {
                setButtonLoading(this, false);
            });
        } else {
            showToast('warning', '请先选择一个有效的城市');
            setButtonLoading(this, false);
        }
    });
    
    // 绑定重置筛选按钮事件
    document.getElementById('resetFilterBtn').addEventListener('click', function() {
        // 添加按钮加载效果
        setButtonLoading(this, true);
        
        document.getElementById('filterErpId').value = '';
        document.getElementById('filterWebsite').value = '';
        recordsFilter.erpHouseId = '';
        recordsFilter.websiteId = '';
        recordsData.page = 1;
        
        const cityInput = document.getElementById('cityInput').value.trim();
        const cityCode = getCityCodeFromInput(cityInput);
        if (cityCode) {
            fetchPushRecords(cityCode).finally(() => {
                setButtonLoading(this, false);
            });
        } else {
            clearRecordsTable(); // 如果城市无效，清空表格
            setButtonLoading(this, false);
        }
    });
    
    // 绑定分页按钮事件
    document.getElementById('prevPageBtn').addEventListener('click', function() {
        if (recordsData.page > 1) {
            // 添加按钮加载效果
            setButtonLoading(this, true);
            
            recordsData.page--;
            const cityInput = document.getElementById('cityInput').value.trim();
            const cityCode = getCityCodeFromInput(cityInput);
            if (cityCode) {
                fetchPushRecords(cityCode).finally(() => {
                    setButtonLoading(this, false);
                });
            } else {
                setButtonLoading(this, false);
            }
        }
    });
    
    document.getElementById('nextPageBtn').addEventListener('click', function() {
        const totalPages = Math.ceil(recordsData.total / recordsData.limit);
        if (recordsData.page < totalPages) {
            // 添加按钮加载效果
            setButtonLoading(this, true);
            
            recordsData.page++;
            const cityInput = document.getElementById('cityInput').value.trim();
            const cityCode = getCityCodeFromInput(cityInput);
            if (cityCode) {
                fetchPushRecords(cityCode).finally(() => {
                    setButtonLoading(this, false);
                });
            } else {
                setButtonLoading(this, false);
            }
        }
    });
    
    // 绑定返回列表按钮事件
    document.getElementById('backToListBtn').addEventListener('click', function() {
        document.getElementById('resultSection').style.display = 'none';
        document.getElementById('recordsSection').style.display = 'block';
    });

    addCssStyles();
});

// 初始化页面动画效果
function initPageAnimations() {
    // 为所有状态卡片添加入场动画
    const statusCards = document.querySelectorAll('.status-card');
    statusCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 + (index * 100));
    });
    
    // 表格容器动画
    const tableContainer = document.querySelector('.table-container');
    if (tableContainer) {
        tableContainer.style.opacity = '0';
        tableContainer.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            tableContainer.style.transition = 'all 0.5s ease';
            tableContainer.style.opacity = '1';
            tableContainer.style.transform = 'translateY(0)';
        }, 500);
    }
}

// 获取城市配置
async function fetchCityConfig() {
    try {
        startProgress();
        console.log('开始获取城市配置...');
        // 调用API获取城市配置
        const domainsResponse = await apiRequest('/config/domains', 'GET');
        const accountsResponse = await apiRequest('/config/accounts', 'GET');
        
        console.log('城市域名响应:', domainsResponse);
        console.log('城市账号响应:', accountsResponse);
        
        // 构建城市配置对象
        cityConfig = {
            domains: domainsResponse.domains || {},
            names: domainsResponse.names || {},
            accounts: accountsResponse.accounts || {}
        };
        
        populateCitySelect();
        populateWebsiteFilter();
        completeProgress();
    } catch (error) {
        console.error('获取城市配置失败:', error);
        showToast('error', '获取城市配置失败: ' + error.message);
        completeProgress();
    }
}

// 填充城市选择datalist
function populateCitySelect() {
    const cityList = document.getElementById('cityList');
    const cityCodes = Object.keys(cityConfig.domains);
    
    console.log('填充城市选择datalist，城市代码:', cityCodes);
    
    // 清空现有选项
    cityList.innerHTML = ''; // 清空 datalist
    
    // 添加城市选项
    cityCodes.forEach(code => {
        const name = cityConfig.names[code] || code;
        const option = document.createElement('option');
        // 设置 value 为 "城市名称 (代码)" 格式，方便用户识别和输入匹配
        option.value = `${name} (${code})`;
        // 使用 dataset 存储实际的 code 值
        option.dataset.value = code;
        cityList.appendChild(option);
    });
    
    // 清空输入框的初始值，避免遗留上次的值
    document.getElementById('cityInput').value = '';
}

// 填充网站筛选下拉框
function populateWebsiteFilter() {
    const websiteSelect = document.getElementById('filterWebsite');
    
    // 添加常用网站选项
    const websites = {
        '111': '58免费三网合一版本',
        '119': '搜房帮',
        '220': '百度乐居',
        '236': '搜狐焦点新端口',
        '238': '酷房网',
        '382': '网易房产',
        '402': '安居客三网合一版本',
        '403': '58付费三网合一版本',
        '404': '赶集付费三网合一版本',
        '414': '网易租房',
        '422': '今日头条房产',
        '514': '优房网付费版本',
        '521': '安居客VIP端口'
    };
    
    // 清空现有选项（保留第一个选项）
    while (websiteSelect.options.length > 1) {
        websiteSelect.remove(1);
    }
    
    // 添加网站选项
    Object.entries(websites).forEach(([id, name]) => {
        const option = document.createElement('option');
        option.value = id;
        option.textContent = name;
        websiteSelect.appendChild(option);
    });
}

// 获取推送记录
async function fetchPushRecords(cityCode) {
    try {
        const userId = document.getElementById('userIdInput').value;
        
        if (!userId) {
            showToast('warning', '请输入用户ID');
            return;
        }
        
        console.log('开始获取推送记录...');
        console.log('城市:', cityCode);
        console.log('用户ID:', userId);
        console.log('过滤条件:', recordsFilter);
        console.log('分页数据:', recordsData);
        
        // 显示加载状态
        startProgress();
        
        const tableBody = document.getElementById('recordsTableBody');
        tableBody.innerHTML = '<tr><td colspan="7" class="loading-text">正在加载推送记录...</td></tr>';
        
        // 显示骨架屏
        document.querySelectorAll('.skeleton-row').forEach(row => {
            row.style.display = 'table-row';
        });
        
        // 构建请求参数
        const requestData = {
            city: cityCode,
            user_id: userId,
            limit: recordsData.limit,
            offset: (recordsData.page - 1) * recordsData.limit
        };
        
        // 添加可选过滤参数
        if (recordsFilter.erpHouseId) {
            requestData.erp_house_id = recordsFilter.erpHouseId;
        }
        
        if (recordsFilter.websiteId) {
            requestData.website_id = recordsFilter.websiteId;
        }
        
        console.log('发送API请求, 参数:', requestData);
        
        // 调用推送记录API
        const response = await apiRequest('/youtui/push/records', 'POST', requestData);
        
        console.log('API响应:', response);
        
        if (response.success) {
            recordsData.records = response.records || [];
            recordsData.total = response.total || 0;
            
            console.log('获取到记录数:', recordsData.records.length);
            console.log('总记录数:', recordsData.total);
            
            // 计算统计数据
            calculateStats(recordsData.records);
            
            // 更新状态卡片
            updateStatsCards();
            
            // 更新分页信息
            updatePagination();
            
            // 渲染记录
            renderRecordsTable();
        } else {
            tableBody.innerHTML = `<tr><td colspan="7" class="error">获取推送记录失败: ${response.message || '未知错误'}</td></tr>`;
            showToast('error', '获取推送记录失败: ' + response.message);
        }
    } catch (error) {
        console.error('获取推送记录失败:', error);
        const tableBody = document.getElementById('recordsTableBody');
        tableBody.innerHTML = `<tr><td colspan="7" class="error">获取推送记录失败: ${error.message}</td></tr>`;
        showToast('error', '获取推送记录失败: ' + error.message);
    } finally {
        // 隐藏骨架屏
        document.querySelectorAll('.skeleton-row').forEach(row => {
            row.style.display = 'none';
        });
        
        completeProgress();
    }
}

// 计算统计数据
function calculateStats(records) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // 重置统计数据
    statsData = {
        todayCount: 0,
        successCount: 0,
        failedCount: 0,
        needAuthCount: 0
    };
    
    // 遍历记录
    records.forEach(record => {
        // 检查是否为今日记录
        if (record.timestamp) {
            const recordDate = new Date(record.timestamp);
            if (recordDate >= today) {
                statsData.todayCount++;
            }
        }
        
        // 成功/失败统计
        if (record.success !== undefined) {
            if (record.success) {
                statsData.successCount++;
            } else {
                statsData.failedCount++;
            }
        }
        
        // 需要认证
        const pushMsg = record.push_info?.push_msg || '';
        const message = record.push_info?.message || '';
        if ((pushMsg.includes('房本认证') || message.includes('房本认证')) &&
            record.push_info?.renzheng && 
            record.push_info.renzheng !== 'null' && 
            record.push_info.renzheng !== 'undefined') {
            statsData.needAuthCount++;
        }
    });
}

// 更新状态卡片
function updateStatsCards() {
    document.getElementById('todayPushCount').textContent = statsData.todayCount;
    document.getElementById('successPushCount').textContent = statsData.successCount;
    document.getElementById('failedPushCount').textContent = statsData.failedCount;
    document.getElementById('needAuthCount').textContent = statsData.needAuthCount;
    
    // 添加数字增长动画
    animateNumbers('todayPushCount');
    animateNumbers('successPushCount');
    animateNumbers('failedPushCount');
    animateNumbers('needAuthCount');
}

// 数字增长动画
function animateNumbers(elementId) {
    const element = document.getElementById(elementId);
    const finalValue = parseInt(element.textContent);
    
    if (isNaN(finalValue)) return;
    
    let startValue = 0;
    const duration = 1000; // 持续时间1秒
    const frameDuration = 16; // 每帧持续时间（约60帧/秒）
    const totalFrames = Math.ceil(duration / frameDuration);
    let frame = 0;
    
    element.textContent = '0';
    
    const animate = () => {
        frame++;
        const progress = frame / totalFrames;
        const currentValue = Math.round(progress * finalValue);
        
        element.textContent = currentValue;
        
        if (frame < totalFrames) {
            requestAnimationFrame(animate);
        }
    };
    
    requestAnimationFrame(animate);
}

// 渲染记录表格
function renderRecordsTable() {
    const tableBody = document.getElementById('recordsTableBody');
    tableBody.innerHTML = '';
    
    if (recordsData.records.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7" class="empty-text">暂无推送记录</td></tr>';
        return;
    }
    
    recordsData.records.forEach((record, index) => {
        const row = document.createElement('tr');
        // 添加行的淡入动画延迟
        row.style.opacity = '0';
        row.style.transform = 'translateY(10px)';
        
        // 处理时间
        const timestamp = record.timestamp || '';
        
        // 处理成功状态
        const success = record.success !== undefined ? record.success : false;
        const statusClass = success ? 'status-success' : 'status-error';
        const statusText = success ? '成功' : '失败';
        
        // 处理推送消息
        let pushMessage = record.push_info?.push_msg || 
                        record.push_info?.message || 
                        (success ? '推送成功' : '推送失败');
        
        // 处理房本认证二维码
        if (record.push_info?.renzheng && record.push_info.renzheng.trim() !== '' && 
            record.push_info.renzheng !== 'null' && record.push_info.renzheng !== 'undefined') {
            // 确认推送消息中包含房本认证相关关键词
            const pushMsg = record.push_info.push_msg || '';
            const message = record.push_info.message || '';
            const needCertification = 
                (pushMsg.includes('房本认证') || pushMsg.includes('扫描二维码') || pushMsg.includes('认证')) || 
                (message.includes('房本认证') || message.includes('扫描二维码') || message.includes('认证'));
            
            if (needCertification) {
                pushMessage += `<div class="auth-card">
                    <div class="auth-card-header">
                        <i class="fas fa-qrcode"></i> 房本认证
                    </div>
                    <p class="auth-card-desc">请扫描下方二维码完成房本认证：</p>
                    <div class="auth-card-qrcode">
                        <img src="${record.push_info.renzheng}" alt="房本认证二维码">
                    </div>
                    <div class="auth-card-actions">
                        <button class="send-sms-btn" data-process-id="${record.process_id}">
                            <i class="fas fa-sms"></i> 发送短信通知
                        </button>
                        <button class="send-dingtalk-btn" data-process-id="${record.process_id}" data-account="${record.accountInfo?.account || ''}" data-name="${record.accountInfo?.name || ''}" data-url="${record.push_info.renzheng}">
                            <i class="fas fa-comment-dots"></i> 发送钉钉通知
                        </button>
                    </div>
                </div>`;
            }
        }
        
        row.innerHTML = `
            <td><i class="far fa-clock text-muted"></i> ${timestamp}</td>
            <td><i class="fas fa-building text-muted"></i> ${record.community || '未知园区'}</td>
            <td><i class="fas fa-globe text-muted"></i> ${record.website_name || getWebsiteName(record.website_id) || record.website_id || ''}</td>
            <td><i class="fas fa-fingerprint text-muted"></i> ${record.process_id || '无'}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
            <td>${pushMessage}</td>
            <td>
                <button type="button" class="view-details-btn" data-process-id="${record.process_id}">
                    <i class="fas fa-search-plus"></i> 查看详情
                </button>
            </td>
        `;
        
        tableBody.appendChild(row);
        
        // 添加行的淡入动画延迟
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, 50 * index);
    });
    
    // 绑定查看详情按钮事件
    document.querySelectorAll('.view-details-btn').forEach(button => {
        button.addEventListener('click', function() {
            const processId = this.getAttribute('data-process-id');
            if (processId) {
                queryByProcessId(processId);
            }
        });
    });

    // 绑定发送短信按钮事件
    document.querySelectorAll('.send-sms-btn').forEach(button => {
        button.addEventListener('click', function() {
            const processId = this.getAttribute('data-process-id');
            if (processId) {
                sendAuthSms(processId);
            }
        });
    });

    // 绑定发送钉钉通知按钮事件
    document.querySelectorAll('.send-dingtalk-btn').forEach(button => {
        button.addEventListener('click', function() {
            const processId = this.getAttribute('data-process-id');
            const mobile = this.getAttribute('data-account');
            const name = this.getAttribute('data-name');
            const url = this.getAttribute('data-url');
            
            if (processId && url) {
                sendDingtalkNotification(mobile, name, url);
            }
        });
    });
}

// 按进程ID查询
async function queryByProcessId(processId) {
    const cityInput = document.getElementById('cityInput').value.trim();
    const cityCode = getCityCodeFromInput(cityInput);
    const userId = document.getElementById('userIdInput').value;

    if (!cityCode) {
        showToast('warning', '请先选择一个有效的城市');
        return;
    }
    if (!userId) {
         showToast('warning', '请输入用户ID');
        return;
    }
    
    console.log('按进程ID查询:', processId);
    
    // 显示Loading
    startProgress();
    showToast('info', '正在查询进程状态...');
    
    try {
        // 调用API查询进程状态
        const response = await apiRequest('/youtui/push/get-push-info', 'POST', {
            process_id: processId,
            city: cityCode,
            user_id: userId
        });
        
        console.log('查询响应:', response);
        
        let resultHtml = '<div class="result-container">';
        
        if (response.success) {
            const pushInfo = response.push_info;
            
            if (pushInfo) {
                // 处理单个结果和多个结果
                const pushInfoArray = Array.isArray(pushInfo) ? pushInfo : [pushInfo];
                
                resultHtml += '<div class="result-header">';
                resultHtml += `<h3><i class="fas fa-info-circle"></i> 进程 ${processId} 的详细信息</h3>`;
                resultHtml += '</div>';
                
                resultHtml += '<div class="result-table-container">';
                resultHtml += '<table class="result-table"><thead><tr>';
                resultHtml += '<th><i class="fas fa-globe"></i> 网站</th>';
                resultHtml += '<th><i class="fas fa-info-circle"></i> 状态</th>';
                resultHtml += '<th><i class="fas fa-comment-alt"></i> 详情</th>';
                resultHtml += '<th><i class="fas fa-external-link-alt"></i> 操作</th>';
                resultHtml += '</tr></thead><tbody>';
                
                pushInfoArray.forEach(item => {
                    const status = item.status || '未知';
                    const statusClass = status === '成功' ? 'status-success' : 
                                      (status === '失败' ? 'status-error' : 'status-processing');
                    
                    let additionalContent = '';
                    // 处理房本认证二维码和账号信息
                    if (item.push_msg && item.push_msg.includes('房本认证')) {
                        let accountInfo = '';
                        if (item.accountInfo) {
                            accountInfo = `
                                <div class="info-section">
                                    <div class="info-header"><i class="fas fa-user-circle"></i> 需要认证的账号信息</div>
                                    <div class="info-content">
                                        <p><strong>账号：</strong>${item.accountInfo.account || '未知'}</p>
                                        ${item.accountInfo.name ? `<p><strong>姓名：</strong>${item.accountInfo.name}</p>` : ''}
                                    </div>
                                </div>
                            `;
                        }
                        
                        // 显示短信通知状态
                        let smsNotificationInfo = '';
                        if (item.sms_notification) {
                            const smsSuccess = item.sms_notification.success;
                            smsNotificationInfo = `
                                <div class="notification-status ${smsSuccess ? 'success' : 'error'}">
                                    <i class="fas ${smsSuccess ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                    <span>短信通知${smsSuccess ? '已发送' : '发送失败'}</span>
                                    ${item.sms_notification.message ? `<p class="notification-message">${item.sms_notification.message}</p>` : ''}
                                </div>
                            `;
                        }
                        
                        if (item.renzheng && item.renzheng.trim() !== '' && 
                            item.renzheng !== 'null' && item.renzheng !== 'undefined') {
                            additionalContent = `
                                <div class="auth-card">
                                    <div class="auth-card-header">
                                        <i class="fas fa-qrcode"></i> 房本认证
                                    </div>
                                    ${accountInfo}
                                    ${smsNotificationInfo}
                                    <p class="auth-card-desc">请扫描下方二维码完成房本认证：</p>
                                    <div class="auth-card-qrcode">
                                        <img src="${item.renzheng}" alt="房本认证二维码">
                                    </div>
                                    <div class="auth-card-actions">
                                        <button class="send-sms-btn" data-process-id="${item.process_id}">
                                            <i class="fas fa-sms"></i> 发送短信通知
                                        </button>
                                        <button class="send-dingtalk-btn" data-process-id="${item.process_id}" data-account="${item.accountInfo?.account || ''}" data-name="${item.accountInfo?.name || ''}" data-url="${item.renzheng}">
                                            <i class="fas fa-comment-dots"></i> 发送钉钉通知
                                        </button>
                                    </div>
                                </div>
                            `;
                        }
                    }
                    
                    resultHtml += `<tr>
                        <td>${item.web_name || getWebsiteName(item.web_id) || '未知'}</td>
                        <td><span class="${statusClass}">${status}</span></td>
                        <td>
                            ${item.push_msg || item.message || '无详情'}
                            ${additionalContent}
                        </td>
                        <td>${item.push_url ? `<a href="${item.push_url}" target="_blank" class="view-link">查看</a>` : '<span class="no-link">无法查看</span>'}</td>
                    </tr>`;
                });
                
                resultHtml += '</tbody></table>';
                resultHtml += '</div>';
            } else {
                resultHtml += '<div class="empty-result"><i class="fas fa-info-circle"></i> 未找到相关记录</div>';
            }
        } else {
            resultHtml += `<div class="error-result"><i class="fas fa-exclamation-triangle"></i> 查询失败: ${response.message || '未知错误'}</div>`;
        }
        
        resultHtml += '</div>';
        
        // 显示结果
        displayResult(resultHtml);
        
        // 处理完成通知
        showToast(response.success ? 'success' : 'error', response.success ? '查询完成' : '查询失败：' + response.message);
        
        // 绑定发送短信按钮事件
        document.querySelectorAll('.send-sms-btn').forEach(button => {
            button.addEventListener('click', function() {
                const processId = this.getAttribute('data-process-id');
                if (processId) {
                    sendAuthSms(processId);
                }
            });
        });
        
        // 绑定发送钉钉通知按钮事件
        document.querySelectorAll('.send-dingtalk-btn').forEach(button => {
            button.addEventListener('click', function() {
                const processId = this.getAttribute('data-process-id');
                const mobile = this.getAttribute('data-account');
                const name = this.getAttribute('data-name');
                const url = this.getAttribute('data-url');
                
                if (processId && url) {
                    sendDingtalkNotification(mobile, name, url);
                }
            });
        });
        
        // 隐藏记录列表，显示查询结果
        document.getElementById('recordsSection').style.display = 'none';
        document.getElementById('resultSection').style.display = 'block';
        
        // 添加结果区域的动画
        const resultSection = document.getElementById('resultSection');
        resultSection.classList.add('animate__animated', 'animate__fadeIn');
        
        // 滚动到查询结果
        document.getElementById('resultSection').scrollIntoView({ behavior: 'smooth' });
    } catch (error) {
        console.error('查询状态失败:', error);
        const resultHtml = `<div class="error-result"><i class="fas fa-exclamation-triangle"></i> 查询失败: ${error.message}</div>`;
        
        displayResult(resultHtml);
        showToast('error', '查询失败');
    } finally {
        completeProgress();
    }
}

// 更新分页信息
function updatePagination() {
    const totalPages = Math.ceil(recordsData.total / recordsData.limit) || 1;
    document.getElementById('pageInfo').textContent = `第 ${recordsData.page} 页 / 共 ${totalPages} 页 (总记录数: ${recordsData.total})`;
    
    // 设置按钮状态
    document.getElementById('prevPageBtn').disabled = recordsData.page <= 1;
    document.getElementById('nextPageBtn').disabled = recordsData.page >= totalPages;
}

// 清空记录表格
function clearRecordsTable() {
    const tableBody = document.getElementById('recordsTableBody');
    tableBody.innerHTML = '<tr><td colspan="7" class="empty-text">请先选择城市</td></tr>';
    
    // 重置分页数据
    recordsData = {
        page: 1,
        limit: 10,
        total: 0,
        records: []
    };
    
    // 清空状态卡片数据
    statsData = {
        todayCount: 0,
        successCount: 0,
        failedCount: 0,
        needAuthCount: 0
    };
    
    // 更新UI
    updateStatsCards();
    updatePagination();
}

// 根据网站ID获取网站名称
function getWebsiteName(webID) {
    const websiteNames = {
        '111': '58免费三网合一版本',
        '119': '搜房帮',
        '220': '百度乐居',
        '236': '搜狐焦点新端口',
        '238': '酷房网',
        '382': '网易房产',
        '402': '安居客三网合一版本',
        '403': '58付费三网合一版本',
        '404': '赶集付费三网合一版本',
        '414': '网易租房',
        '422': '今日头条房产',
        '514': '优房网付费版本',
        '521': '安居客VIP端口'
    };
    
    return websiteNames[webID] || `网站${webID}`;
}

// 显示查询结果
function displayResult(result) {
    const resultSection = document.getElementById('resultSection');
    const resultContent = document.getElementById('resultContent');
    
    resultContent.innerHTML = result;
    
    resultSection.style.display = 'block';
}

// 隐藏查询结果
function hideResult() {
    document.getElementById('resultSection').style.display = 'none';
    document.getElementById('recordsSection').style.display = 'block';
}

// 全局加载指示器控制
function showGlobalLoading() {
    document.getElementById('globalLoadingIndicator').classList.add('active');
}

function hideGlobalLoading() {
    document.getElementById('globalLoadingIndicator').classList.remove('active');
}

// 顶部进度条控制
function startProgress() {
    const progressBar = document.getElementById('topProgressBar');
    progressBar.style.width = '0%';
    progressBar.style.opacity = '1';
    
    setTimeout(() => {
        progressBar.style.width = '70%';
    }, 100);
}

function completeProgress() {
    const progressBar = document.getElementById('topProgressBar');
    progressBar.style.width = '100%';
    
    setTimeout(() => {
        progressBar.style.opacity = '0';
        setTimeout(() => {
            progressBar.style.width = '0%';
        }, 300);
    }, 500);
}

// 按钮加载状态控制
function setButtonLoading(button, isLoading) {
    if (isLoading) {
        button.classList.add('loading');
        button.disabled = true;
    } else {
        button.classList.remove('loading');
        button.disabled = false;
    }
}

// Toast 提示函数
function showToast(type, message, duration = 3000) {
    const container = document.getElementById('toastContainer');
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    
    container.appendChild(toast);
    
    // 强制重排
    void toast.offsetHeight;
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (container.contains(toast)) {
                container.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// 显示消息的辅助函数
function showMessage(message, type = 'info') {
    showToast(type, message);
}

// API请求工具函数
async function apiRequest(url, method = 'GET', data = null) {
    try {
        console.log(`API请求: ${method} ${url}`, data);
        
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        // 添加请求体
        if (data) {
            if (method === 'GET') {
                // 将数据转换为查询字符串
                const params = new URLSearchParams();
                for (const key in data) {
                    params.append(key, data[key]);
                }
                url += '?' + params.toString();
                console.log('GET请求URL:', url);
            } else {
                options.body = JSON.stringify(data);
            }
        }
        
        const response = await fetch(url, options);
        console.log('API响应状态:', response.status);
        
        const result = await response.json();
        console.log('API响应数据:', result);
        
        if (!response.ok) {
            throw new Error(result.detail || result.message || '请求失败');
        }
        
        return result;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

// 发送房本认证短信通知
async function sendAuthSms(processId) {
    if (!processId) {
        showToast('error', '缺少进程ID');
        return;
    }
    
    console.log('发送房本认证短信通知:', processId);
    
    // 获取当前选择的城市
    const cityInput = document.getElementById('cityInput').value.trim();
    const cityCode = getCityCodeFromInput(cityInput);
    
    if (!cityCode) {
        showToast('warning', '请先选择一个有效的城市');
        return;
    }
    
    try {
        // 显示Loading
        startProgress();
        showToast('info', '正在发送短信通知...');
        
        // 为按钮添加加载状态
        const buttons = document.querySelectorAll(`.send-sms-btn[data-process-id="${processId}"]`);
        buttons.forEach(button => setButtonLoading(button, true));
        
        // 调用API发送短信
        const response = await apiRequest('/youtui/push/send-auth-sms', 'POST', {
            process_id: processId,
            city: cityCode
        });
        
        console.log('发送短信响应:', response);
        
        if (response.success) {
            showToast('success', '短信发送成功！');
            // 添加成功发送的动画效果
            buttons.forEach(button => {
                button.classList.add('success-action');
                setTimeout(() => {
                    button.classList.remove('success-action');
                }, 1500);
            });
            
            // 刷新当前状态
            queryByProcessId(processId);
        } else {
            showToast('error', `短信发送失败: ${response.message}`);
            // 添加失败发送的动画效果
            buttons.forEach(button => {
                button.classList.add('error-action');
                setTimeout(() => {
                    button.classList.remove('error-action');
                }, 1500);
            });
        }
    } catch (error) {
        console.error('发送短信失败:', error);
        showToast('error', `短信发送失败: ${error.message}`);
    } finally {
        // 移除按钮加载状态
        const buttons = document.querySelectorAll(`.send-sms-btn[data-process-id="${processId}"]`);
        buttons.forEach(button => setButtonLoading(button, false));
        
        completeProgress();
    }
}

// 发送钉钉房本认证通知
async function sendDingtalkNotification(mobile, name, url) {
    if (!url) {
        showToast('error', '缺少二维码链接');
        return;
    }
    
    console.log('发送钉钉房本认证通知:', { mobile, name, url });
    
    try {
        // 显示Loading
        startProgress();
        showToast('info', '正在发送钉钉通知...');
        
        // 为所有相关按钮添加加载状态
        const buttons = document.querySelectorAll(`.send-dingtalk-btn[data-url="${url}"]`);
        buttons.forEach(button => setButtonLoading(button, true));
        
        // 构建请求参数
        const requestData = {
            url: url
        };
        
        // 如果有手机号或姓名，则添加到请求参数中
        if (mobile) {
            requestData.mobile = mobile;
        }
        
        if (name) {
            requestData.name = name;
        }
        
        // 调用API发送钉钉通知
        const response = await apiRequest('/dingtalk/send-certification', 'POST', requestData);
        
        console.log('发送钉钉通知响应:', response);
        
        if (response.success) {
            showToast('success', '钉钉通知发送成功！');
            
            // 添加成功发送的动画效果
            buttons.forEach(button => {
                button.classList.add('success-action');
                setTimeout(() => {
                    button.classList.remove('success-action');
                }, 1500);
            });
        } else {
            showToast('error', `钉钉通知发送失败: ${response.message}`);
            
            // 添加失败发送的动画效果
            buttons.forEach(button => {
                button.classList.add('error-action');
                setTimeout(() => {
                    button.classList.remove('error-action');
                }, 1500);
            });
        }
    } catch (error) {
        console.error('发送钉钉通知失败:', error);
        showToast('error', `钉钉通知发送失败: ${error.message}`);
    } finally {
        // 移除按钮加载状态
        const buttons = document.querySelectorAll(`.send-dingtalk-btn[data-url="${url}"]`);
        buttons.forEach(button => setButtonLoading(button, false));
        
        completeProgress();
    }
}

// 新增辅助函数：从输入值（可能是 "名称 (代码)" 或直接是代码）获取城市代码
function getCityCodeFromInput(inputValue) {
    if (!inputValue) return null;

    // 检查是否是 "名称 (代码)" 格式
    const match = inputValue.match(/\(([^)]+)\)$/);
    if (match && match[1] && cityConfig.domains[match[1]]) {
        return match[1]; // 提取代码并验证
    }

     // 检查是否直接是代码
    if (cityConfig.domains[inputValue]) {
        return inputValue; // 直接是代码
    }

     // 如果都匹配不上，在 datalist 中再次查找
    const dataListOptions = document.getElementById('cityList').options;
     for (let i = 0; i < dataListOptions.length; i++) {
         if (dataListOptions[i].value === inputValue && dataListOptions[i].dataset.value) {
              if (cityConfig.domains[dataListOptions[i].dataset.value]) {
                 return dataListOptions[i].dataset.value;
              }
         }
         if (dataListOptions[i].dataset.value === inputValue) {
            if (cityConfig.domains[inputValue]) {
                return inputValue;
            }
         }
     }

    return null; // 无效输入
}

// 添加CSS样式
function addCssStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .text-muted {
            color: #8c8c8c;
            margin-right: 6px;
        }
        
        .auth-card {
            margin-top: 15px;
            padding: 16px;
            border: 1px solid #f0f0f0;
            border-radius: 12px;
            background-color: #fafafa;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
        }
        
        .auth-card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
        }
        
        .auth-card-header {
            margin-bottom: 12px;
            color: #1890ff;
            font-weight: 600;
            font-size: 16px;
            display: flex;
            align-items: center;
        }
        
        .auth-card-header i {
            margin-right: 8px;
        }
        
        .auth-card-desc {
            margin-bottom: 12px;
            color: #595959;
        }
        
        .auth-card-qrcode {
            text-align: center;
            margin-bottom: 16px;
        }
        
        .auth-card-qrcode img {
            max-width: 180px;
            border: 1px solid #eee;
            padding: 5px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }
        
        .auth-card-qrcode img:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }
        
        .auth-card-actions {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 12px;
            margin-top: 16px;
        }
        
        .auth-card-actions button {
            padding: 10px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 130px;
        }
        
        .auth-card-actions button i {
            margin-right: 8px;
        }
        
        .send-sms-btn {
            background-color: #52c41a;
            color: white;
            border: none;
        }
        
        .send-sms-btn:hover {
            background-color: #6ad82b;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(82, 196, 26, 0.3);
        }
        
        .send-dingtalk-btn {
            background-color: #1890ff;
            color: white;
            border: none;
        }
        
        .send-dingtalk-btn:hover {
            background-color: #40a9ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);
        }
        
        .result-container {
            animation: fadeIn 0.5s ease;
        }
        
        .result-header {
            margin-bottom: 24px;
        }
        
        .result-header h3 {
            font-size: 18px;
            margin: 0;
            color: #1a1a1a;
            display: flex;
            align-items: center;
        }
        
        .result-header h3 i {
            margin-right: 10px;
            color: #1890ff;
        }
        
        .result-table-container {
            overflow-x: auto;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        }
        
        .result-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .result-table th,
        .result-table td {
            padding: 15px 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .result-table th {
            background-color: #f7f9fc;
            font-weight: 600;
            color: #1a1a1a;
        }
        
        .result-table th i {
            margin-right: 8px;
            color: #1890ff;
        }
        
        .result-table tr:last-child td {
            border-bottom: none;
        }
        
        .result-table tr:hover td {
            background-color: #f5f9ff;
        }
        
        .empty-result,
        .error-result {
            padding: 40px;
            text-align: center;
            background-color: #fafafa;
            border-radius: 12px;
            margin: 20px 0;
            color: #595959;
            font-size: 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .empty-result i,
        .error-result i {
            font-size: 32px;
            margin-bottom: 16px;
            color: #8c8c8c;
        }
        
        .error-result i {
            color: #ff4d4f;
        }
        
        .view-link {
            color: #1890ff;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .view-link:hover {
            color: #40a9ff;
            text-decoration: underline;
        }
        
        .no-link {
            color: #8c8c8c;
            font-style: italic;
        }
        
        .info-section {
            background-color: #f0f7ff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .info-header {
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .info-header i {
            margin-right: 8px;
        }
        
        .info-content p {
            margin: 4px 0;
            color: #595959;
        }
        
        .notification-status {
            display: flex;
            align-items: flex-start;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 16px;
        }
        
        .notification-status.success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        
        .notification-status.error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
        }
        
        .notification-status i {
            margin-right: 8px;
            font-size: 16px;
        }
        
        .notification-status.success i {
            color: #52c41a;
        }
        
        .notification-status.error i {
            color: #ff4d4f;
        }
        
        .notification-message {
            margin-top: 6px;
            margin-left: 24px;
            font-size: 13px;
            color: #8c8c8c;
        }
        
        /* 按钮动画 */
        .success-action {
            animation: successPulse 1.5s ease;
        }
        
        .error-action {
            animation: errorShake 0.6s ease;
        }
        
        @keyframes successPulse {
            0% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7); }
            30% { box-shadow: 0 0 0 15px rgba(82, 196, 26, 0); }
            100% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0); }
        }
        
        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            15%, 45%, 75% { transform: translateX(-8px); }
            30%, 60%, 90% { transform: translateX(8px); }
        }
    `;
    document.head.appendChild(style);
} 