// 用于存储上传的Excel文件
let uploadedExcelFile = null;
let allRecords = []; // 存储所有园区记录
let cityList = []; // 存储所有城市
let cityCount = {}; // 每个城市的园区数量
let currentCity = "all"; // 当前选中的城市，"all"表示所有城市

// 文件上传交互
document.getElementById('excelFile').addEventListener('change', function(e) {
    const fileName = e.target.files[0]?.name;
    const fileNameDiv = document.querySelector('.file-name');
    const uploadLabel = document.querySelector('.file-upload-label .upload-text');

    if (fileName) {
        fileNameDiv.textContent = fileName;
        uploadLabel.textContent = '重新选择文件';
        uploadLabel.parentElement.classList.add('has-file');

        // 激活步骤指示器
        document.getElementById('step1').classList.add('completed');
        document.getElementById('step2').classList.add('active');
    } else {
        fileNameDiv.textContent = '';
        uploadLabel.textContent = '点击选择Excel文件';
        uploadLabel.parentElement.classList.remove('has-file');
    }
});

// 添加流程指引交互
document.addEventListener('DOMContentLoaded', function() {
    // 初始化流程指引
    initProcessFlow();

    // 初始化流程图动画
    initFlowAnimation();

    // 添加动画开关功能
    const animationToggle = document.getElementById('animationToggle');
    if (animationToggle) {
        // 检查本地存储的默认设置
        const savedPreference = localStorage.getItem('flowAnimationEnabled');
        if (savedPreference !== null) {
            const isEnabled = savedPreference === 'true';
            animationToggle.checked = isEnabled;
            toggleFlowAnimation(isEnabled);
        }

        // 监听开关变化
        animationToggle.addEventListener('change', function() {
            const isEnabled = this.checked;
            toggleFlowAnimation(isEnabled);
            // 保存用户偏好到本地存储
            localStorage.setItem('flowAnimationEnabled', isEnabled);
        });
    }
});

// 初始化流程指引
function initProcessFlow() {
    const steps = document.querySelectorAll('.process-step');

    steps.forEach(step => {
        step.addEventListener('click', function() {
            const stepId = this.id;

            if (stepId === 'step1') {
                // 点击第一步，聚焦到文件上传区域
                document.getElementById('excelFile').click();
            } else if (stepId === 'step2') {
                // 点击第二步，触发上传按钮
                if (document.getElementById('excelFile').files.length > 0) {
                    document.getElementById('parseButton').click();
                } else {
                    showMessage('请先选择Excel文件', 'warning');
                }
            } else if (stepId === 'step3') {
                // 点击第三步，如果可以发布则触发发布按钮
                if (!document.getElementById('publishButton').disabled) {
                    document.getElementById('publishButton').click();
                } else {
                    showMessage('请先上传并预览Excel文件', 'warning');
                }
            }
        });
    });
}

// 初始化流程图动画 - 优化版本
function initFlowAnimation() {
    // 获取流程图元素
    const processFlow = document.querySelector('.upload-process-flow');
    if (!processFlow) return;

    // 默认不添加动画类，由toggleFlowAnimation决定
    // 检查本地存储的默认设置
    const savedPreference = localStorage.getItem('flowAnimationEnabled');
    const isEnabled = savedPreference !== null ? savedPreference === 'true' : true;

    if (isEnabled) {
        processFlow.classList.add('animate-flow');
    } else {
        processFlow.classList.remove('animate-flow');
    }

    // 使用更高效的方式高亮步骤，减少DOM操作频率
    const steps = ['step1', 'step2', 'step3'];
    let currentStep = 0;

    // 只有当动画启用时才创建定时器
    if (isEnabled) {
        startFlowAnimationTimer(steps, currentStep);
    }

    // 添加页面可见性检测，当页面不可见时暂停动画
    document.addEventListener('visibilitychange', function() {
        if (document.hidden && window.flowAnimationTimer) {
            clearInterval(window.flowAnimationTimer);
            window.flowAnimationTimer = null;
        } else if (!document.hidden && !window.flowAnimationTimer && processFlow.classList.contains('animate-flow')) {
            // 页面重新可见且动画已启用时恢复动画
            startFlowAnimationTimer(steps, currentStep);
        }
    });
}

// 开始流程图动画定时器
function startFlowAnimationTimer(steps, currentStep) {
    window.flowAnimationTimer = setInterval(() => {
        // 重置所有步骤的高亮状态
        document.querySelectorAll('.process-step').forEach(step => {
            step.classList.remove('highlight-step');
        });

        // 只高亮当前步骤
        const stepElement = document.getElementById(steps[currentStep]);
        if (stepElement) {
            stepElement.classList.add('highlight-step');
        }

        // 移动到下一个步骤
        currentStep = (currentStep + 1) % steps.length;
    }, 8000); // 增加间隔时间到8秒
}

// 切换流程图动画开关
function toggleFlowAnimation(isEnabled) {
    const processFlow = document.querySelector('.upload-process-flow');
    if (!processFlow) return;

    if (isEnabled) {
        // 启用动画
        processFlow.classList.add('animate-flow');
        // 如果定时器不存在，创建新的定时器
        if (!window.flowAnimationTimer && !document.hidden) {
            const steps = ['step1', 'step2', 'step3'];
            let currentStep = 0;
            startFlowAnimationTimer(steps, currentStep);
        }
    } else {
        // 禁用动画
        processFlow.classList.remove('animate-flow');
        // 清除定时器
        if (window.flowAnimationTimer) {
            clearInterval(window.flowAnimationTimer);
            window.flowAnimationTimer = null;
        }
        // 移除所有高亮效果
        document.querySelectorAll('.process-step').forEach(step => {
            step.classList.remove('highlight-step');
        });
    }
}

// 创建城市选择器
function createCitySelector(cityList, cityCount) {
    const container = document.createElement('div');
    container.className = 'city-selector';

    // 添加标题
    const title = document.createElement('h4');
    title.innerHTML = '<i class="fas fa-city"></i> 选择城市';
    container.appendChild(title);

    // 创建城市选择按钮组
    const buttonGroup = document.createElement('div');
    buttonGroup.className = 'city-button-group';

    // 添加"全部"按钮
    const allButton = document.createElement('button');
    allButton.className = 'city-button active';
    allButton.setAttribute('data-city', 'all');
    allButton.innerHTML = `全部城市 <span class="count">(${allRecords.length})</span>`;
    allButton.addEventListener('click', function() {
        setActiveCity('all');
    });
    buttonGroup.appendChild(allButton);

    // 添加各个城市按钮
    cityList.forEach(city => {
        const button = document.createElement('button');
        button.className = 'city-button';
        button.setAttribute('data-city', city);
        button.innerHTML = `${city} <span class="count">(${cityCount[city] || 0})</span>`;
        button.addEventListener('click', function() {
            setActiveCity(city);
        });
        buttonGroup.appendChild(button);
    });

    container.appendChild(buttonGroup);
    return container;
}

// 设置活动城市并刷新显示
function setActiveCity(city) {
    currentCity = city;

    // 更新按钮样式
    document.querySelectorAll('.city-button').forEach(button => {
        if (button.getAttribute('data-city') === city) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });

    // 重新加载数据表格
    populateDataTable();
}

// 填充园区数据表格
function populateDataTable() {
    console.log("填充数据表格，当前城市：", currentCity);
    console.log("所有记录数量：", allRecords.length);

    const tbody = document.getElementById('excelPreviewBody');
    tbody.innerHTML = '';

    // 添加调试信息到DOM
    const debugInfo = document.createElement('tr');
    const debugCell = document.createElement('td');
    debugCell.colSpan = 2;
    debugCell.textContent = `调试信息: 记录数量=${allRecords.length}, 城市列表=${JSON.stringify(cityList)}`;
    debugCell.style.background = '#ffffdd';
    debugCell.style.padding = '10px';
    debugCell.style.fontSize = '12px';
    debugInfo.appendChild(debugCell);
    tbody.appendChild(debugInfo);

    // 如果没有记录，显示提示
    if (!allRecords || allRecords.length === 0) {
        const emptyRow = document.createElement('tr');
        const emptyCell = document.createElement('td');
        emptyCell.colSpan = 2;
        emptyCell.textContent = '没有接收到数据或数据为空';
        emptyCell.style.textAlign = 'center';
        emptyCell.style.padding = '20px';
        emptyCell.style.color = 'red';
        emptyRow.appendChild(emptyCell);
        tbody.appendChild(emptyRow);
        return;
    }

    // 过滤当前城市的记录
    const records = currentCity === 'all'
        ? allRecords
        : allRecords.filter(record => record['城市'] === currentCity);

    console.log("过滤后记录数量：", records.length);

    // 更新表头说明
    const previewHeader = document.querySelector('#previewArea .preview-info');
    previewHeader.innerHTML = `
        <i class="fas fa-info-circle"></i>
        当前显示: <strong>${currentCity === 'all' ? '全部城市' : currentCity}</strong> 的园区数据，
        共 <strong>${records.length}</strong> 个园区
    `;

    // 显示空数据提示
    if (records.length === 0) {
        const emptyRow = document.createElement('tr');
        const emptyCell = document.createElement('td');
        emptyCell.colSpan = 2;
        emptyCell.textContent = '该城市没有园区数据';
        emptyCell.style.textAlign = 'center';
        emptyCell.style.padding = '20px';
        emptyRow.appendChild(emptyCell);
        tbody.appendChild(emptyRow);
        return;
    }

    // 为每个园区创建一个分组行
    records.forEach((record, index) => {
        // 创建园区标题行
        const headerRow = document.createElement('tr');
        headerRow.className = 'park-header';

        const headerCell = document.createElement('td');
        headerCell.colSpan = 2;
        headerCell.innerHTML = `
            <div class="park-title">
                <span class="park-number">#${index + 1}</span>
                <strong>${record['园区名称'] || record['community'] || '未命名园区'}</strong>
                <span class="park-meta">${record['城市'] || ''} ${record['区域'] || record['zone'] || ''}</span>
            </div>
        `;
        headerRow.appendChild(headerCell);
        tbody.appendChild(headerRow);

        // 创建园区数据行
        Object.entries(record).forEach(([key, value]) => {
            // 跳过空值
            if (value === null || value === undefined || value === '') {
                return;
            }

            const row = document.createElement('tr');
            row.className = 'park-data';

            const fieldCell = document.createElement('td');
            fieldCell.textContent = key;
            row.appendChild(fieldCell);

            const valueCell = document.createElement('td');
            valueCell.textContent = value;
            row.appendChild(valueCell);

            tbody.appendChild(row);
        });

        // 如果不是最后一个园区，添加分隔行
        if (index < records.length - 1) {
            const separatorRow = document.createElement('tr');
            separatorRow.className = 'separator';
            const separatorCell = document.createElement('td');
            separatorCell.colSpan = 2;
            separatorRow.appendChild(separatorCell);
            tbody.appendChild(separatorRow);
        }
    });
}

// 解析并预览Excel函数
function parseAndPreviewExcel() {
    const fileInput = document.getElementById('excelFile');

    // 使用上传的文件或从input中获取
    const file = uploadedExcelFile || (fileInput.files.length > 0 ? fileInput.files[0] : null);

    if (!file) {
        showMessage('请选择Excel文件', 'error');
        return;
    }

    // 如果是新上传的文件，保存它以便后续使用
    if (!uploadedExcelFile && fileInput.files.length > 0) {
        uploadedExcelFile = fileInput.files[0];
    }

    const formData = new FormData();
    formData.append('file', file);

    // 禁用上传按钮，显示加载状态
    const parseButton = document.getElementById('parseButton');
    parseButton.disabled = true;
    parseButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';

    // 激活步骤指示器
    document.getElementById('step2').classList.add('completed');
    document.getElementById('step3').classList.add('active');

    // 清除现有的信息框
    const existingInfoBox = document.querySelector('#previewArea .info-box');
    if (existingInfoBox) {
        existingInfoBox.remove();
    }

    // 清除现有的城市选择器
    const existingCitySelector = document.querySelector('#previewArea .city-selector');
    if (existingCitySelector) {
        existingCitySelector.remove();
    }

    // 发送上传请求到解析并保存API
    fetch('/excel/parse-excel', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // 添加调试信息
        console.log("接收到的API响应数据:", data);

        // 恢复上传按钮状态
        parseButton.disabled = false;
        parseButton.innerHTML = '<i class="fas fa-upload"></i> 上传并入库';

        // 处理响应结果
        if (data.success) {
            // 显示Excel预览区域
            document.getElementById('previewArea').style.display = 'block';

            // 保存所有数据
            allRecords = data.all_records || [];
            console.log("接收到的记录数量:", allRecords.length);

            cityList = data.city_list || [];
            console.log("接收到的城市列表:", cityList);

            cityCount = data.city_count || {};
            console.log("接收到的城市统计:", cityCount);

            currentCity = 'all'; // 默认显示所有城市

            // 滚动到预览区域
            document.getElementById('previewArea').scrollIntoView({ behavior: 'smooth' });

            // 清空Excel预览表格
            const tbody = document.getElementById('excelPreviewBody');
            tbody.innerHTML = '';

            // 显示文件信息
            const fileInfoBox = document.createElement('div');
            fileInfoBox.className = 'info-box';

            // 构建数据库保存结果信息
            let dbResultInfo = '';
            if (data.db_result) {
                const dbResult = data.db_result;
                const successClass = dbResult.success ? 'success' : 'error';
                const icon = dbResult.success ? 'check-circle' : 'times-circle';

                dbResultInfo = `
                <div class="db-result ${successClass}">
                    <h4><i class="fas fa-database"></i> 数据库保存结果</h4>
                    <p><i class="fas fa-${icon}"></i> ${dbResult.message}</p>
                    ${dbResult.rows_saved > 0 ? `<p>成功导入: <strong>${dbResult.rows_saved}</strong> 条记录</p>` : ''}
                    ${dbResult.rows_failed > 0 ? `<p>导入失败: <strong>${dbResult.rows_failed}</strong> 条记录</p>` : ''}
                </div>`;
            }

            fileInfoBox.innerHTML = `
                <h4><i class="fas fa-info-circle"></i> 文件信息</h4>
                <p>文件名称: <strong>${uploadedExcelFile.name}</strong></p>
                <p>文件大小: <strong>${(uploadedExcelFile.size / 1024).toFixed(2)} KB</strong></p>
                <p>园区总数: <strong>${allRecords.length || data.row_count || 0}个</strong></p>
                <p>涵盖城市: <strong>${cityList.length}个</strong> (${cityList.join(', ')})</p>
                ${dbResultInfo}
            `;
            document.getElementById('previewArea').insertBefore(fileInfoBox, document.getElementById('excelPreviewTable').parentNode);

            // 创建并插入城市选择器
            if (cityList.length > 0) {
                const citySelector = createCitySelector(cityList, cityCount);
                document.getElementById('previewArea').insertBefore(citySelector, document.getElementById('excelPreviewTable').parentNode);
            } else {
                console.warn("没有城市列表数据");
            }

            // 填充数据表格
            populateDataTable();

            // 保存解析后的数据ID，用于发布
            document.getElementById('publishButton').dataset.parseId = data.parse_id;

            // 根据数据库保存结果决定是否启用发布按钮
            if (data.db_result && data.db_result.success && data.db_result.rows_saved > 0) {
                // 修改发布按钮文本，启用按钮
                document.getElementById('publishButton').innerHTML = '<i class="fas fa-paper-plane"></i> 确认发布推送';
                document.getElementById('publishButton').disabled = false;

                // 添加成功提示
                showMessage(`Excel数据已成功保存到数据库，共 ${data.db_result.rows_saved} 条记录`, 'success');
            } else {
                // 禁用发布按钮，显示保存失败
                document.getElementById('publishButton').innerHTML = '<i class="fas fa-times-circle"></i> 保存失败，无法发布';
                document.getElementById('publishButton').disabled = true;

                // 添加错误提示
                showMessage('Excel数据保存到数据库失败，请检查错误并重试', 'error');
            }

            // 隐藏结果区域（如果之前显示过）
            document.getElementById('resultArea').style.display = 'none';
        } else {
            // 显示错误信息
            document.getElementById('resultArea').style.display = 'block';

            const resultMessage = document.getElementById('resultMessage');
            resultMessage.className = 'message error';
            resultMessage.textContent = data.message || '解析Excel文件失败，请重试';

            document.getElementById('resultDetails').innerHTML = `
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 15px;">
                    <h4>调试信息:</h4>
                    <pre style="overflow: auto; max-height: 200px;">${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('上传失败:', error);

        // 恢复上传按钮状态
        parseButton.disabled = false;
        parseButton.innerHTML = '<i class="fas fa-upload"></i> 上传并入库';

        // 显示错误信息
        document.getElementById('resultArea').style.display = 'block';

        const resultMessage = document.getElementById('resultMessage');
        resultMessage.className = 'message error';
        resultMessage.textContent = '系统错误，请稍后重试';

        document.getElementById('resultDetails').innerHTML = `
            <div class="error-icon"><i class="fas fa-exclamation-circle"></i></div>
            <p>发生系统错误，请稍后重新尝试。如果问题仍然存在，请联系系统管理员。</p>
            <p>错误详情: ${error.toString()}</p>
        `;
    });
}

// 显示消息提示的辅助函数
function showMessage(message, type = 'info') {
    // 使用主文件中的消息提示功能
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
    } else {
        // 创建一个消息容器
        const messageContainer = document.createElement('div');
        messageContainer.className = `message-toast message-${type}`;
        messageContainer.innerHTML = `
            <div class="message-icon">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle'}"></i>
            </div>
            <div class="message-content">${message}</div>
        `;

        // 添加到文档中
        document.body.appendChild(messageContainer);

        // 添加动画效果
        setTimeout(() => {
            messageContainer.classList.add('show');
        }, 10);

        // 设置定时器移除消息
        setTimeout(() => {
            messageContainer.classList.remove('show');
            messageContainer.classList.add('hide');

            // 动画结束后移除元素
            setTimeout(() => {
                document.body.removeChild(messageContainer);
            }, 300);
        }, 3000);
    }
}

// 添加消息提示的CSS样式
function addMessageStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .message-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            z-index: 9999;
            transform: translateX(120%);
            transition: transform 0.3s ease;
            max-width: 300px;
        }

        .message-toast.show {
            transform: translateX(0);
        }

        .message-toast.hide {
            transform: translateX(120%);
        }

        .message-icon {
            margin-right: 12px;
            font-size: 20px;
        }

        .message-success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
        }

        .message-success .message-icon {
            color: #52c41a;
        }

        .message-error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
        }

        .message-error .message-icon {
            color: #ff4d4f;
        }

        .message-info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
        }

        .message-info .message-icon {
            color: #1890ff;
        }

        .message-warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
        }

        .message-warning .message-icon {
            color: #faad14;
        }

        /* 城市选择器样式 */
        .city-selector {
            margin: 15px 0;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e9ecef;
        }

        .city-selector h4 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .city-button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .city-button {
            padding: 6px 12px;
            border-radius: 4px;
            border: 1px solid #ddd;
            background-color: #fff;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }

        .city-button:hover {
            background-color: #e6f7ff;
            border-color: #1890ff;
        }

        .city-button.active {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .city-button.active .count {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .city-button .count {
            background-color: #f5f5f5;
            border-radius: 10px;
            padding: 1px 6px;
            font-size: 12px;
            margin-left: 5px;
        }

        /* 园区数据样式 */
        .park-header {
            background-color: #f0f5ff;
        }

        .park-title {
            padding: 8px 0;
            display: flex;
            align-items: center;
        }

        .park-number {
            background-color: #1890ff;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-right: 8px;
        }

        .park-meta {
            margin-left: 10px;
            color: #666;
            font-size: 13px;
        }

        .park-data td:first-child {
            font-weight: bold;
            width: 30%;
        }

        tr.separator td {
            height: 10px;
            background-color: #f9f9f9;
            border-bottom: 1px dashed #eee;
        }

        /* 数据库结果样式 */
        .db-result {
            margin-top: 15px;
            padding: 10px 15px;
            border-radius: 6px;
        }

        .db-result.success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
        }

        .db-result.error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
        }

        .db-result h4 {
            margin: 0 0 8px 0;
            font-size: 15px;
        }

        .db-result p {
            margin: 5px 0;
        }
    `;
    document.head.appendChild(style);
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加消息提示样式
    addMessageStyles();

    // 上传并解析Excel文件
    document.getElementById('parseButton').addEventListener('click', parseAndPreviewExcel);

    // 监听文件输入变化
    document.getElementById('excelFile').addEventListener('change', function() {
        // 当文件更改时，清除之前保存的文件
        uploadedExcelFile = null;
    });

    // 取消按钮事件
    document.getElementById('cancelButton').addEventListener('click', function() {
        document.getElementById('previewArea').style.display = 'none';
        document.getElementById('step3').classList.remove('active');
    });

    // 返回上传按钮事件
    document.getElementById('backButton').addEventListener('click', function() {
        document.getElementById('resultArea').style.display = 'none';
        document.getElementById('uploadForm').scrollIntoView({ behavior: 'smooth' });
        // 重置步骤指示器
        document.getElementById('step2').classList.remove('completed');
        document.getElementById('step3').classList.remove('active');
    });

    // 发布处理
    document.getElementById('publishButton').addEventListener('click', function() {
        const parseId = this.dataset.parseId;
        if (!parseId) {
            showMessage('没有可发布的数据，请先上传Excel文件', 'error');
            return;
        }

        // 禁用发布按钮，显示加载状态
        const publishButton = document.getElementById('publishButton');
        publishButton.disabled = true;
        publishButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发布中...';

        // 发送发布请求
        fetch('/excel/publish-parsed', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                parse_id: parseId
            })
        })
        .then(response => response.json())
        .then(data => {
            // 添加调试信息
            console.log("发布结果:", data);

            // 恢复发布按钮状态
            publishButton.disabled = false;
            publishButton.innerHTML = '<i class="fas fa-paper-plane"></i> 确认发布';

            // 显示结果区域
            document.getElementById('resultArea').style.display = 'block';
            document.getElementById('previewArea').style.display = 'none';

            // 滚动到结果区域
            document.getElementById('resultArea').scrollIntoView({ behavior: 'smooth' });

            const resultMessage = document.getElementById('resultMessage');
            const resultDetails = document.getElementById('resultDetails');

            if (data.success) {
                resultMessage.className = 'message success';
                resultMessage.textContent = data.message;

                let detailsHTML = '<div class="result-summary">';
                if (data.success_count > 0) {
                    detailsHTML += `<div class="result-stat success"><i class="fas fa-check-circle"></i> 成功发布：<span>${data.success_count}</span> 条</div>`;
                }
                if (data.failed_count > 0) {
                    detailsHTML += `<div class="result-stat error"><i class="fas fa-times-circle"></i> 发布失败：<span>${data.failed_count}</span> 条</div>`;
                }
                detailsHTML += '</div>';

                if (data.success_ids && data.success_ids.length > 0) {
                    detailsHTML += '<div class="result-section"><h4><i class="fas fa-list-ul"></i> 成功发布的房源ID</h4><div class="id-list">';
                    data.success_ids.forEach(id => {
                        detailsHTML += `<div class="id-item"><i class="fas fa-check"></i> ${id}</div>`;
                    });
                    detailsHTML += '</div></div>';
                }

                if (data.failed_items && data.failed_items.length > 0) {
                    detailsHTML += '<div class="result-section"><h4><i class="fas fa-exclamation-triangle"></i> 发布失败的项目</h4>';
                    detailsHTML += '<table class="result-table"><thead><tr><th>行号</th><th>原因</th></tr></thead><tbody>';
                    data.failed_items.forEach(item => {
                        detailsHTML += `<tr><td>${item.row || '未知'}</td><td>${item.reason || '未知错误'}</td></tr>`;
                    });
                    detailsHTML += '</tbody></table></div>';
                }

                resultDetails.innerHTML = detailsHTML;
            } else {
                resultMessage.className = 'message error';
                resultMessage.textContent = data.message || '发布失败，请重试';
                resultDetails.innerHTML = `
                    <div class="error-icon"><i class="fas fa-exclamation-circle"></i></div>
                    <p>请检查数据格式是否正确，然后重新尝试。</p>
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 15px;">
                        <h4>调试信息:</h4>
                        <pre style="overflow: auto; max-height: 200px;">${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('发布失败:', error);

            // 恢复发布按钮状态
            publishButton.disabled = false;
            publishButton.innerHTML = '<i class="fas fa-paper-plane"></i> 确认发布';

            // 显示错误信息
            document.getElementById('resultArea').style.display = 'block';
            document.getElementById('previewArea').style.display = 'none';

            // 滚动到结果区域
            document.getElementById('resultArea').scrollIntoView({ behavior: 'smooth' });

            const resultMessage = document.getElementById('resultMessage');
            resultMessage.className = 'message error';
            resultMessage.textContent = '系统错误，请稍后重试';

            document.getElementById('resultDetails').innerHTML = `
                <div class="error-icon"><i class="fas fa-exclamation-circle"></i></div>
                <p>发生系统错误，请稍后重新尝试。如果问题仍然存在，请联系系统管理员。</p>
                <p>错误详情: ${error.toString()}</p>
            `;
        });
    });
});
