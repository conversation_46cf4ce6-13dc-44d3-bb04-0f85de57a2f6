/**
 * 优推科技ERP管理系统 JavaScript 主文件
 * 包含公共函数和API调用
 */

// API基本URL
const API_BASE_URL = '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 设置活动导航项
    setActiveNavItem();

    // 添加页面动画效果
    addPageAnimations();
});

/**
 * 设置当前活动的导航项
 */
function setActiveNavItem() {
    const currentPage = window.location.pathname.split('/').pop();
    const navLinks = document.querySelectorAll('nav ul li a');

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPage ||
            (currentPage === '' && link.getAttribute('href') === 'index.html')) {
            link.classList.add('active');
        }
    });
}

/**
 * 显示消息提示
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, error, info)
 * @param {number} duration - 显示时长（毫秒）
 */
function showMessage(message, type = 'info', duration = 3000) {
    // 检查是否已存在消息容器
    let messageContainer = document.querySelector('.message-container');

    if (!messageContainer) {
        messageContainer = document.createElement('div');
        messageContainer.className = 'message-container';
        document.body.appendChild(messageContainer);
    }

    // 创建消息元素
    const messageElement = document.createElement('div');
    messageElement.className = `message message-${type}`;
    messageElement.textContent = message;

    // 添加到容器
    messageContainer.appendChild(messageElement);

    // 设置自动消失
    setTimeout(() => {
        messageElement.classList.add('message-fade-out');
        setTimeout(() => {
            messageContainer.removeChild(messageElement);
        }, 300);
    }, duration);
}

/**
 * 通用API请求函数
 * @param {string} url - API接口地址
 * @param {string} method - 请求方法 (GET, POST, PUT, DELETE)
 * @param {object} data - 请求数据 (对于POST和PUT请求)
 * @returns {Promise} 包含响应数据的Promise
 */
async function apiRequest(url, method = 'GET', data = null) {
    try {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(`${API_BASE_URL}${url}`, options);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.detail || `请求失败，状态码: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API请求错误:', error);
        showMessage(`API请求错误: ${error.message}`, 'error');
        throw error;
    }
}

/**
 * 将日期格式化为字符串
 * @param {Date|string} date - 日期对象或日期字符串
 * @param {string} format - 格式化模式
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    const d = new Date(date);

    if (isNaN(d.getTime())) {
        return 'Invalid Date';
    }

    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 表单验证函数
 * @param {HTMLFormElement} form - 表单元素
 * @returns {boolean} 验证是否通过
 */
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;

            // 创建错误提示
            let errorMessage = field.nextElementSibling;
            if (!errorMessage || !errorMessage.classList.contains('error-message')) {
                errorMessage = document.createElement('div');
                errorMessage.className = 'error-message';
                field.parentNode.insertBefore(errorMessage, field.nextSibling);
            }
            errorMessage.textContent = `${field.getAttribute('placeholder') || '此字段'} 不能为空`;
        } else {
            field.classList.remove('error');

            // 移除错误提示
            const errorMessage = field.nextElementSibling;
            if (errorMessage && errorMessage.classList.contains('error-message')) {
                errorMessage.textContent = '';
            }
        }
    });

    return isValid;
}

// 添加CSS样式
const style = document.createElement('style');
style.textContent = `
    .message-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
    }

    .message {
        padding: 10px 15px;
        margin-bottom: 10px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        animation: message-fade-in 0.3s ease;
    }

    .message-success {
        background-color: #f6ffed;
        border: 1px solid #b7eb8f;
        color: #52c41a;
    }

    .message-error {
        background-color: #fff2f0;
        border: 1px solid #ffccc7;
        color: #ff4d4f;
    }

    .message-info {
        background-color: #e6f7ff;
        border: 1px solid #91d5ff;
        color: #1890ff;
    }

    .message-fade-out {
        animation: message-fade-out 0.3s ease forwards;
    }

    .error-message {
        color: #ff4d4f;
        font-size: 12px;
        margin-top: 5px;
    }

    input.error, select.error, textarea.error {
        border-color: #ff4d4f;
    }

    @keyframes message-fade-in {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes message-fade-out {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(-10px); }
    }
`;
document.head.appendChild(style);

/**
 * 添加页面动画效果
 */
function addPageAnimations() {
    // 添加欢迎区域动画
    const welcomeSection = document.querySelector('.welcome-section');
    if (welcomeSection) {
        welcomeSection.style.opacity = '0';
        welcomeSection.style.transform = 'translateY(20px)';
        setTimeout(() => {
            welcomeSection.style.transition = 'all 0.6s ease';
            welcomeSection.style.opacity = '1';
            welcomeSection.style.transform = 'translateY(0)';
        }, 100);
    }

    // 添加支持平台区域动画
    const supportedPlatforms = document.querySelector('.supported-platforms');
    if (supportedPlatforms) {
        supportedPlatforms.style.opacity = '0';
        supportedPlatforms.style.transform = 'translateY(20px)';
        setTimeout(() => {
            supportedPlatforms.style.transition = 'all 0.6s ease';
            supportedPlatforms.style.opacity = '1';
            supportedPlatforms.style.transform = 'translateY(0)';

            // 添加平台卡片的逐个渐入效果
            const platformCards = document.querySelectorAll('.platform-card');
            platformCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(15px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 200 + (index * 100));
            });
        }, 200);
    }

    // 添加卡片容器动画
    const dashboard = document.querySelector('.dashboard');
    if (dashboard) {
        dashboard.style.opacity = '0';
        dashboard.style.transform = 'translateY(20px)';
        setTimeout(() => {
            dashboard.style.transition = 'all 0.6s ease';
            dashboard.style.opacity = '1';
            dashboard.style.transform = 'translateY(0)';
        }, 400);
    }

    // 添加系统信息区域动画
    const systemInfo = document.querySelector('.system-info');
    if (systemInfo) {
        systemInfo.style.opacity = '0';
        systemInfo.style.transform = 'translateY(20px)';
        setTimeout(() => {
            systemInfo.style.transition = 'all 0.6s ease';
            systemInfo.style.opacity = '1';
            systemInfo.style.transform = 'translateY(0)';
        }, 600);
    }

    // 导航菜单不需要动画，确保其始终可见
    // 添加一个微妙的高亮效果
    const navItems = document.querySelectorAll('nav ul li a.active');
    navItems.forEach((item) => {
        setTimeout(() => {
            item.style.transition = 'all 0.3s ease';
            item.style.boxShadow = '0 2px 8px rgba(24, 144, 255, 0.3)';
        }, 500);
    });
}