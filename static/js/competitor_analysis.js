// 全局变量，存储分析数据
let analysisData = {
    overview: {
        totalPosts: 0,
        totalCompetitors: 0,
        newPosts: 0,
        ourPostsPercentage: 0,
        avgPostsPerAccount: 0,
        timeSeriesData: [],
        lastUpdateTime: null, // 添加数据最后更新时间字段
        competitorAvgPostsPerAccount: 0
    },
    competitors: [],
    districts: [],
    contentQuality: {
        our: { avgPopularity: 0, avgImages: 0, updateFrequency: 0, avgVideos: 0 },
        competitor: { avgPopularity: 0, avgImages: 0, updateFrequency: 0, avgVideos: 0 }
    }
};

// 页面加载完成后执行初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('竞帖分析页面开始初始化...');

    addLoadingComponents();
    showGlobalLoading();
    startProgress();

    try {
        console.log('设置活动导航项...');
        setActiveNavItem();

        console.log('加载城市和区域数据...');
        await loadCitiesAndDistricts();

        // 设置默认日期范围
        setDefaultDateRange();

        // 获取竞帖数据的最后更新时间
        fetchLastDataUpdateTime();

        // 先显示页面框架，延迟加载数据和图表
        setTimeout(async () => {
            try {
                // 分步加载数据，先显示基础统计数据
                await loadBasicData();

                // 更新基础数据到UI
                updateBasicPageData();

                // 初始化图表前先添加事件监听器
                initializeEventListeners();

                // 延迟加载并初始化图表
                setTimeout(() => {
                    initializePrimaryCharts();

                    // 再延迟加载次要图表
                    setTimeout(() => {
                        initializeSecondaryCharts();

                        // 加载并更新表格和列表数据
                        loadDetailedData().then(() => {
                            updateDetailedPageData();
                            console.log('所有数据和图表加载完成');
                            hideGlobalLoading();
                            completeProgress();
                        });
                    }, 300);
                }, 300);
            } catch (error) {
                console.error('加载数据和图表失败:', error);
                showMessage('加载数据失败，请稍后重试', 'error');
                hideGlobalLoading();
                completeProgress();
            }
        }, 100);
    } catch (error) {
        console.error('页面初始化失败:', error);
        showMessage('加载数据失败，请稍后重试', 'error');
        hideGlobalLoading();
        completeProgress();
    }
});

// 设置默认日期范围
function setDefaultDateRange() {
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);

    const startDateInput = document.getElementById('startDateFilter');
    const endDateInput = document.getElementById('endDateFilter');

    if (startDateInput) {
        startDateInput.value = formatDateForInput(thirtyDaysAgo);
    }

    if (endDateInput) {
        endDateInput.value = formatDateForInput(today);
    }
}

// 格式化日期为日期输入框格式 (YYYY-MM-DD)
function formatDateForInput(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 加载基础数据（概览统计）
async function loadBasicData() {
    try {
        const cityId = document.getElementById('cityFilter').value;
        const districtId = document.getElementById('districtFilter').value;
        const startDate = document.getElementById('startDateFilter').value;
        const endDate = document.getElementById('endDateFilter').value;
        const listingType = document.getElementById('listingTypeFilter')?.value;
        const yearFilter = document.getElementById('yearFilter')?.value;
        const monthFilter = document.getElementById('monthFilter')?.value;

        // 更新时间范围显示
        document.getElementById('overviewTimeRange').textContent = getDateRangeText(startDate, endDate);

        // 获取基础统计数据（轻量级数据）
        const basicData = await fetchBasicStatistics(cityId, districtId, startDate, endDate, listingType, yearFilter, monthFilter);

        // 更新全局数据中的概览部分
        analysisData.overview = basicData;

        return basicData;
    } catch (error) {
        console.error('加载基础数据失败:', error);
        throw error;
    }
}

// 加载详细数据（竞争对手、区域热力图等）
async function loadDetailedData() {
    try {
        const cityId = document.getElementById('cityFilter').value;
        const districtId = document.getElementById('districtFilter').value;
        const startDate = document.getElementById('startDateFilter').value;
        const endDate = document.getElementById('endDateFilter').value;
        const listingType = document.getElementById('listingTypeFilter')?.value;

        // 获取内容质量数据
        try {
            const contentQualityData = await fetchContentQualityData(cityId, districtId, startDate, endDate);
            analysisData.contentQuality = contentQualityData;
        } catch (error) {
            console.error('加载内容质量数据失败:', error);
            // 确保数据结构正确，即使加载失败
            analysisData.contentQuality = {
                our: { avgPopularity: 0, avgImages: 0, updateFrequency: 0, avgVideos: 0 },
                competitor: { avgPopularity: 0, avgImages: 0, updateFrequency: 0, avgVideos: 0 }
            };
        }

        // 获取竞争对手数据
        try {
            const competitorsData = await fetchCompetitorsData(cityId, districtId, startDate, endDate);
            analysisData.competitors = competitorsData;
        } catch (error) {
            console.error('加载竞争对手数据失败:', error);
            analysisData.competitors = [];
        }

        // 获取区域数据
        try {
            const districtsData = await fetchDistrictsData(cityId, startDate, endDate);
            analysisData.districts = districtsData;
        } catch (error) {
            console.error('加载区域数据失败:', error);
            analysisData.districts = [];
        }

        // 获取热门公司数据
        try {
            const topCompanies = await fetchTopCompanies(cityId, districtId, listingType, startDate, endDate);
            analysisData.topCompanies = topCompanies;

            // 更新折线图时间序列数据
            updateTimeSeriesDataFromCompanies(topCompanies);
        } catch (error) {
            console.error('加载热门公司数据失败:', error);
            analysisData.topCompanies = [];
        }
    } catch (error) {
        console.error('加载详细数据时出错:', error);
        // 确保数据结构正确，即使加载失败
        analysisData.contentQuality = {
            our: { avgPopularity: 0, avgImages: 0, updateFrequency: 0, avgVideos: 0 },
            competitor: { avgPopularity: 0, avgImages: 0, updateFrequency: 0, avgVideos: 0 }
        };
        analysisData.competitors = [];
        analysisData.districts = [];
        analysisData.topCompanies = [];
    }
}

// 获取基础统计数据
async function fetchBasicStatistics(cityId, districtId, startDate, endDate, listingType, year, month) {
    try {
        // 构建API请求URL和参数
        let url = '/listing-analysis/stats';
        const params = new URLSearchParams();

        if (cityId) {
            params.append('city', cityId);
        }

        if (districtId) {
            params.append('district', districtId);
        }

        // 添加租售类型筛选
        if (listingType !== undefined && listingType !== null && listingType !== '') {
            params.append('listing_type', listingType);
        }

        // 添加年月筛选
        if (year) {
            params.append('year', year);
        }

        if (month) {
            params.append('month', month);
        }

        // 添加日期范围参数
        if (startDate) {
            params.append('start_date', startDate);
        }

        if (endDate) {
            params.append('end_date', endDate);
        }

        // 添加参数到URL
        if (params.toString()) {
            url += '?' + params.toString();
        }

        // 调用后端API
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`获取统计数据失败: ${response.status}`);
        }

        // 解析API返回的数据
        const data = await response.json();

        // 获取时间序列数据（使用实际API替代模拟数据）
        let timeSeriesData;
        try {
            timeSeriesData = await fetchTimeSeriesData(cityId, districtId, listingType, startDate, endDate);
        } catch (error) {
            console.error('获取时间序列数据失败:', error);
            // 如果API调用失败，仍使用模拟数据作为备选
            timeSeriesData = generateSimplifiedTimeSeriesData(
                30,
                data.ourPostsPercentage || 0
            );
        }

        // 返回组合后的数据
        return {
            totalPosts: data.totalPosts || 0,
            totalCompetitors: data.totalCompetitors || 0,
            newPosts: data.newPosts || 0,
            ourPostsPercentage: data.ourPostsPercentage || 0,
            avgPostsPerAccount: data.avgPostsPerAccount || 0,  // 直接使用API返回的单账号平均帖子数
            timeSeriesData: timeSeriesData,
            competitorAvgPostsPerAccount: data.competitorAvgPostsPerAccount || 0
        };
    } catch (error) {
        console.error('获取基础统计数据失败:', error);

        // 发生错误时返回默认值
        return {
            totalPosts: 0,
            totalCompetitors: 0,
            newPosts: 0,
            ourPostsPercentage: 0,
            avgPostsPerAccount: 0,
            timeSeriesData: [],
            competitorAvgPostsPerAccount: 0
        };
    }
}

// 获取时间序列数据
async function fetchTimeSeriesData(cityId, districtId, listingType, startDate, endDate) {
    try {
        // 构建API请求URL和参数
        let url = '/listing-analysis/time-series';
        const params = new URLSearchParams();

        if (cityId) {
            params.append('city', cityId);
        }

        if (districtId) {
            params.append('district', districtId);
        }

        // 添加租售类型筛选
        if (listingType !== undefined && listingType !== null && listingType !== '') {
            params.append('listing_type', listingType);
        }

        // 添加日期范围
        if (startDate) {
            params.append('start_date', startDate);
        }

        if (endDate) {
            params.append('end_date', endDate);
        }

        // 添加参数到URL
        url += '?' + params.toString();

        // 调用后端API
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`获取时间序列数据失败: ${response.status}`);
        }

        // 解析API返回的数据
        const result = await response.json();

        // 存储公司名称列表，供图表使用
        if (result.companies && Array.isArray(result.companies)) {
            window.companyLabels = result.companies;
        }

        // 返回时间序列数据
        return result.series || [];
    } catch (error) {
        console.error('获取时间序列数据失败:', error);
        throw error;
    }
}

// 简化的时间序列数据生成
function generateSimplifiedTimeSeriesData(daysRange, ourPercentage) {
    // 减少数据点，只取每周一个点
    const pointsToGenerate = Math.min(daysRange, Math.ceil(daysRange / 7) + 1);
    const data = [];
    const now = new Date();
    const competitors = ['我方', '竞争对手A', '竞争对手B', '竞争对手C', '其他'];

    for (let i = 0; i < pointsToGenerate; i++) {
        const daysBack = i === 0 ? 0 : Math.floor(i * (daysRange / (pointsToGenerate - 1)));
        const date = new Date(now);
        date.setDate(date.getDate() - daysBack);
        const dateString = formatDate(date);

        const competitorValues = {};
        let total = 0;

        competitors.forEach(competitor => {
            let base = 0;
            if (competitor === '我方') {
                base = Math.random() * 5 + (ourPercentage / 20);
            } else if (competitor === '其他') {
                base = Math.random() * 2 + 1;
            } else {
                base = Math.random() * 4 + 2;
            }

            const trend = Math.sin(daysBack / 10) * 0.5 + 0.5;
            const value = Math.round(base * (1 + trend));
            competitorValues[competitor] = value;
            total += value;
        });

        data.push({
            date: dateString,
            ...competitorValues,
            total
        });
    }

    // 按日期从早到晚排序
    return data.sort((a, b) => new Date(a.date) - new Date(b.date));
}

// 模拟API请求 - 获取竞争对手数据
async function fetchCompetitorsData(cityId, districtId, startDate, endDate) {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 300));

    // 生成简化的竞争对手数据
    return generateCompetitors(Math.floor(analysisData.overview.totalPosts * 0.75));
}

// 模拟API请求 - 获取区域热力数据
async function fetchDistrictsData(cityId, startDate, endDate) {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 250));

    return generateDistrictData();
}

// 模拟API请求 - 获取内容质量数据
async function fetchContentQualityData(cityId, districtId, startDate, endDate) {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 200));

    return {
        our: {
            avgPopularity: 120,     // 平均收藏数
            avgImages: 8,           // 平均图片数
            updateFrequency: 2.5,   // 更新频率
            avgVideos: 2            // 平均视频数
        },
        competitor: {
            avgPopularity: 80,      // 平均收藏数
            avgImages: 5,           // 平均图片数
            updateFrequency: 5.5,   // 更新频率
            avgVideos: 1            // 平均视频数
        }
    };
}

// 获取热门公司数据
async function fetchTopCompanies(cityId, districtId, listingType, startDate, endDate) {
    try {
        // 构建API请求URL和参数
        let url = '/listing-analysis/top-companies';
        const params = new URLSearchParams();

        if (cityId) {
            params.append('city', cityId);
        }

        if (districtId) {
            params.append('district', districtId);
        }

        // 添加租售类型筛选
        if (listingType !== undefined && listingType !== null && listingType !== '') {
            params.append('listing_type', listingType);
        }

        // 添加日期范围
        if (startDate) {
            params.append('start_date', startDate);
        }

        if (endDate) {
            params.append('end_date', endDate);
        }

        // 设置返回前10家公司
        params.append('limit', '10');

        // 添加参数到URL
        if (params.toString()) {
            url += '?' + params.toString();
        }

        // 调用后端API
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`获取热门公司数据失败: ${response.status}`);
        }

        // 解析API返回的数据
        const companies = await response.json();

        // 处理并返回公司数据
        return companies.map(company => ({
            name: company.name || '未知公司',
            postCount: company.postCount || 0,
            lastPostTime: company.lastPostTime || null
        }));
    } catch (error) {
        console.error('获取热门公司数据失败:', error);
        return [];
    }
}

// 更新热门公司列表
function updateTopCompanies(companies) {
    try {
        const container = document.getElementById('topCompaniesContainer');
        if (!container) return;

        // 清空现有内容
        container.innerHTML = '';

        // 如果没有数据，显示提示信息
        if (!companies || companies.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无数据</div>';
            return;
        }

        // 创建表格
        const table = document.createElement('table');
        table.className = 'data-table';
        table.innerHTML = `
            <thead>
                <tr>
                    <th>排名</th>
                    <th>公司名称</th>
                    <th>帖子数量</th>
                    <th>最近发帖时间</th>
                </tr>
            </thead>
            <tbody id="topCompaniesTableBody"></tbody>
        `;
        container.appendChild(table);

        const tableBody = document.getElementById('topCompaniesTableBody');

        // 添加数据行
        companies.forEach((company, index) => {
            const row = document.createElement('tr');

            // 判断是否为我方公司（包含"联东"）
            const isOurCompany = company.name && company.name.includes('联东');

            row.innerHTML = `
                <td>${index + 1}</td>
                <td class="${isOurCompany ? 'highlight' : ''}">${escapeHtml(company.name)}</td>
                <td>${company.postCount}</td>
                <td>${company.lastPostTime || '未知'}</td>
            `;

            tableBody.appendChild(row);
        });

        // 更新折线图的数据
        updateTimeSeriesDataFromCompanies(companies);
    } catch (error) {
        console.error('更新热门公司列表失败:', error);
    }
}

// 从热门公司数据更新时间序列数据
function updateTimeSeriesDataFromCompanies(companies) {
    try {
        // 检查是否有时间序列数据，如有则保留现有数据
        if (analysisData.overview.timeSeriesData && analysisData.overview.timeSeriesData.length > 0) {
            return;
        }

        if (!companies || companies.length === 0) return;

        // 提取前5家公司，如果不足5家则全部使用
        const topCompanies = companies.slice(0, 5);

        // 获取日期列表（可以从已有数据中获取，或生成新的）
        // 这里假设已有时间序列数据
        const existingTimeSeriesData = analysisData.overview.timeSeriesData || [];
        const dates = existingTimeSeriesData.length > 0
            ? existingTimeSeriesData.map(item => item.date)
            : generateDateRangeForLastMonth();

        // 创建新的时间序列数据
        const newTimeSeriesData = [];

        // 分类公司：我方、前3家竞争对手、其他
        const ourCompanies = topCompanies.filter(company => company.name && company.name.includes('联东'));
        const competitorCompanies = topCompanies.filter(company => company.name && !company.name.includes('联东'));

        // 为每个日期创建数据点
        for (let i = 0; i < dates.length; i++) {
            const dataPoint = { date: dates[i] };

            // 添加我方数据
            const ourPostsTotal = ourCompanies.reduce((total, company) => total + company.postCount, 0);
            dataPoint['我方'] = Math.round(ourPostsTotal / dates.length);

            // 添加竞争对手数据（最多3家）
            for (let j = 0; j < Math.min(competitorCompanies.length, 3); j++) {
                const companyName = `竞争对手${String.fromCharCode(65 + j)}`; // A, B, C
                dataPoint[companyName] = Math.round(competitorCompanies[j].postCount / dates.length);
            }

            // 添加其他公司数据
            const otherCompaniesTotal = competitorCompanies.slice(3).reduce((total, company) => total + company.postCount, 0);
            dataPoint['其他'] = Math.round(otherCompaniesTotal / dates.length);

            newTimeSeriesData.push(dataPoint);
        }

        // 仅在没有现有数据时更新全局数据
        if (!analysisData.overview.timeSeriesData || analysisData.overview.timeSeriesData.length === 0) {
            analysisData.overview.timeSeriesData = newTimeSeriesData;

            // 如果图表已初始化，则更新图表
            if (window.postsOverTimeChart) {
                updatePostsOverTimeChart();
            }
        }
    } catch (error) {
        console.error('从公司数据更新时间序列失败:', error);
    }
}

// 生成最近一个月的日期范围
function generateDateRangeForLastMonth() {
    const dates = [];
    const now = new Date();
    const intervals = 6; // 6个数据点

    for (let i = 0; i < intervals; i++) {
        const date = new Date(now);
        date.setDate(date.getDate() - Math.floor((intervals - i - 1) * (30 / (intervals - 1))));
        dates.push(formatDate(date));
    }

    return dates;
}

// 更新基础数据到页面
function updateBasicPageData() {
    try {
        // 设置一个工具函数，安全地更新元素内容
        function safeUpdateElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                return element;
            }
            return null;
        }

        safeUpdateElement('totalPosts', analysisData.overview.totalPosts);
        safeUpdateElement('totalCompetitors', analysisData.overview.totalCompetitors);
        safeUpdateElement('newPosts', analysisData.overview.newPosts);
        safeUpdateElement('ourPostsPercentage', `${analysisData.overview.ourPostsPercentage}%`);
        safeUpdateElement('avgPostsPerAccount', analysisData.overview.avgPostsPerAccount);
        safeUpdateElement('competitorAvgPostsPerAccount', analysisData.overview.competitorAvgPostsPerAccount || 0);
    } catch (error) {
        console.error('更新基础数据时出错:', error);
    }
}

// 更新详细数据到页面
function updateDetailedPageData() {
    try {
        // 设置一个工具函数，安全地更新元素内容
        function safeUpdateElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                return element;
            }
            return null;
        }

        // 更新内容质量对比数据
        safeUpdateElement('avgPopularityOur', analysisData.contentQuality.our.avgPopularity);
        safeUpdateElement('avgPopularityCompetitor', analysisData.contentQuality.competitor.avgPopularity);
        safeUpdateElement('avgImagesOur', analysisData.contentQuality.our.avgImages);
        safeUpdateElement('avgImagesCompetitor', analysisData.contentQuality.competitor.avgImages);
        safeUpdateElement('updateFrequencyOur', analysisData.contentQuality.our.updateFrequency);
        safeUpdateElement('updateFrequencyCompetitor', analysisData.contentQuality.competitor.updateFrequency);
        safeUpdateElement('avgVideosOur', analysisData.contentQuality.our.avgVideos);
        safeUpdateElement('avgVideosCompetitor', analysisData.contentQuality.competitor.avgVideos);

        // 计算对比变化率并更新
        const popularityChange = ((analysisData.contentQuality.our.avgPopularity / analysisData.contentQuality.competitor.avgPopularity - 1) * 100).toFixed(0);
        const imgChange = ((analysisData.contentQuality.our.avgImages / analysisData.contentQuality.competitor.avgImages - 1) * 100).toFixed(0);
        const freqChange = ((analysisData.contentQuality.competitor.updateFrequency / analysisData.contentQuality.our.updateFrequency - 1) * 100).toFixed(0);
        const videosChange = ((analysisData.contentQuality.our.avgVideos / analysisData.contentQuality.competitor.avgVideos - 1) * 100).toFixed(0);

        // 安全地更新变化率元素
        let element;

        element = safeUpdateElement('avgPopularityChange', popularityChange > 0 ? `+${popularityChange}%` : `${popularityChange}%`);
        if (element) element.className = `comparison-change ${popularityChange >= 0 ? 'positive' : 'negative'}`;

        element = safeUpdateElement('avgImagesChange', imgChange > 0 ? `+${imgChange}%` : `${imgChange}%`);
        if (element) element.className = `comparison-change ${imgChange >= 0 ? 'positive' : 'negative'}`;

        element = safeUpdateElement('updateFrequencyChange', freqChange > 0 ? `+${freqChange}%` : `${freqChange}%`);
        if (element) element.className = `comparison-change ${freqChange >= 0 ? 'positive' : 'negative'}`;

        element = safeUpdateElement('avgVideosChange', videosChange > 0 ? `+${videosChange}%` : `${videosChange}%`);
        if (element) element.className = `comparison-change ${videosChange >= 0 ? 'positive' : 'negative'}`;

        // 更新竞争对手表格
        updateCompetitorTable();

        // 更新热门竞争对手列表
        updateTopCompetitorsList();

        // 更新热门公司列表
        updateTopCompanies(analysisData.topCompanies);

        // 更新图表
        updateCharts();
    } catch (error) {
        console.error('更新详细数据时出错:', error);
    }
}

// 获取日期范围文本
function getDateRangeText(startDate, endDate) {
    if (!startDate || !endDate) {
        return '近30天数据';
    }

    return `${startDate} 至 ${endDate}`;
}

// 格式化日期为yyyy-MM-dd
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 格式化日期时间为yyyy-MM-dd HH:mm
function formatDateTime(date) {
    const formattedDate = formatDate(date);
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${formattedDate} ${hours}:${minutes}`;
}

// HTML转义函数
function escapeHtml(str) {
    if (!str) return '';
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
}

// 初始化所有事件监听器
function initializeEventListeners() {
    try {
        // 刷新数据按钮
        const refreshBtn = document.querySelector('.refresh-data-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', async () => {
                showMessage('正在刷新数据...', 'info');
                showGlobalLoading();
                startProgress();

                try {
                    // 获取最新的数据更新时间
                    await fetchLastDataUpdateTime();

                    // 分步刷新数据
                    await loadBasicData();
                    updateBasicPageData();

                    // 延迟加载详细数据
                    setTimeout(async () => {
                        try {
                            await loadDetailedData();
                            updateDetailedPageData();
                            showMessage('数据刷新成功', 'success');
                        } catch (error) {
                            console.error('详细数据加载或更新失败:', error);
                            showMessage('部分数据刷新失败', 'warning');
                        } finally {
                            hideGlobalLoading();
                            completeProgress();
                        }
                    }, 300);
                } catch (error) {
                    console.error('数据刷新失败:', error);
                    showMessage('数据刷新失败，请稍后重试', 'error');
                    hideGlobalLoading();
                    completeProgress();
                }
            });
        }

        // 导出报告按钮
        const exportBtn = document.querySelector('.export-data-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', async () => {
                showMessage('正在生成报告...', 'info');
                try {
                    await exportReport();
                    showMessage('报告导出成功', 'success');
                } catch (error) {
                    // 错误已在exportReport函数中处理
                }
            });
        }

        // 城市筛选变化事件
        const cityFilter = document.getElementById('cityFilter');
        if (cityFilter) {
            cityFilter.addEventListener('change', () => {
                updateDistrictOptions();
            });
        }

        // 应用筛选按钮
        const applyFilterBtn = document.getElementById('applyFilterBtn');
        if (applyFilterBtn) {
            applyFilterBtn.addEventListener('click', async () => {
                showGlobalLoading();
                startProgress();

                try {
                    // 更新时间范围显示
                    const startDate = document.getElementById('startDateFilter').value;
                    const endDate = document.getElementById('endDateFilter').value;
                    document.getElementById('overviewTimeRange').textContent = getDateRangeText(startDate, endDate);

                    // 获取城市和区域值（使用自定义下拉框的值）
                    const cityId = document.getElementById('cityFilter').value;
                    const districtId = document.getElementById('districtFilter').value;
                    const listingType = document.getElementById('listingTypeFilter')?.value;

                    // 重新加载数据
                    await loadBasicData();
                    updateBasicPageData();

                    await loadDetailedData();
                    updateDetailedPageData();

                    // 更新图表
                    updateCharts();
                    
                    showMessage('数据已更新', 'success');
                } catch (error) {
                    console.error('更新数据失败:', error);
                    showMessage('更新数据失败', 'error');
                } finally {
                    hideGlobalLoading();
                    completeProgress();
                }
            });
        }

        // 重置筛选按钮
        const resetFilterBtn = document.getElementById('resetFilterBtn');
        if (resetFilterBtn) {
            resetFilterBtn.addEventListener('click', async () => {
                resetFilters();
                showMessage('已重置筛选条件', 'info');

                // 分步重新加载
                showGlobalLoading();
                startProgress();

                try {
                    await loadBasicData();
                    updateBasicPageData();

                    setTimeout(async () => {
                        try {
                            await loadDetailedData();
                            updateDetailedPageData();
                            showMessage('数据已重新加载', 'success');
                        } catch (error) {
                            console.error('详细数据加载或更新失败:', error);
                            showMessage('部分数据加载失败', 'warning');
                        } finally {
                            hideGlobalLoading();
                            completeProgress();
                        }
                    }, 300);
                } catch (error) {
                    console.error('数据重新加载失败:', error);
                    showMessage('数据重新加载失败，请稍后重试', 'error');
                    hideGlobalLoading();
                    completeProgress();
                }
            });
        }

        // 竞争对手详情模态框关闭按钮
        const modalCloseBtn = document.querySelector('.modal-close-btn');
        if (modalCloseBtn) {
            modalCloseBtn.addEventListener('click', () => {
                const modal = document.getElementById('competitorDetailModal');
                if (modal && typeof modal.close === 'function') {
                    modal.close();
                }
            });
        }

        // 模态框点击外部关闭
        const modal = document.getElementById('competitorDetailModal');
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.close();
                }
            });
        }

        console.log('事件监听器初始化完成');
    } catch (error) {
        console.error('初始化事件监听器时出错:', error);
    }
}

// 重置筛选条件
function resetFilters() {
    document.getElementById('cityFilter').value = '';
    document.getElementById('citySearchInput').value = '';
    document.getElementById('districtFilter').value = '';

    // 重置日期选择器
    setDefaultDateRange();

    // 重置租售类型和年月筛选
    if (document.getElementById('listingTypeFilter')) {
        document.getElementById('listingTypeFilter').value = '';
    }

    if (document.getElementById('yearFilter')) {
        document.getElementById('yearFilter').value = '';
    }

    if (document.getElementById('monthFilter')) {
        document.getElementById('monthFilter').value = '';
    }

    updateDistrictOptions();
}

// 加载城市和区域数据
async function loadCitiesAndDistricts() {
    try {
        // 从API获取城市数据
        const citiesResponse = await fetch('/listing-analysis/cities');
        if (!citiesResponse.ok) {
            throw new Error(`获取城市数据失败: ${citiesResponse.status}`);
        }
        const cities = await citiesResponse.json();

        // 填充城市下拉框（原有select元素，保留作为数据存储）
        const cityFilter = document.getElementById('cityFilter');
        cities.forEach(cityName => {
            const option = document.createElement('option');
            option.value = cityName;
            option.textContent = cityName;
            cityFilter.appendChild(option);
        });

        // 填充自定义下拉菜单
        const cityDropdown = document.getElementById('cityDropdown');
        cities.forEach(cityName => {
            const dropdownItem = document.createElement('div');
            dropdownItem.className = 'dropdown-item';
            dropdownItem.dataset.value = cityName;
            dropdownItem.textContent = cityName;
            cityDropdown.appendChild(dropdownItem);
        });

        // 初始化自定义下拉搜索功能
        initCustomCitySelect();

        // 存储区域数据的对象
        window.districtData = {};

        // 在用户选择城市时，会触发updateDistrictOptions函数加载对应区域数据

        console.log('城市数据加载完成');
    } catch (error) {
        console.error('加载城市和区域数据失败:', error);
        showMessage('加载城市和区域数据失败', 'error');
    }
}

// 初始化自定义城市选择控件
function initCustomCitySelect() {
    const citySearchInput = document.getElementById('citySearchInput');
    const cityDropdown = document.getElementById('cityDropdown');
    const cityFilter = document.getElementById('cityFilter');
    const dropdownItems = cityDropdown.querySelectorAll('.dropdown-item');
    
    // 显示下拉框
    citySearchInput.addEventListener('focus', () => {
        cityDropdown.style.display = 'block';
        filterDropdownItems('');
    });

    // 点击下拉项时选择城市
    dropdownItems.forEach(item => {
        item.addEventListener('click', () => {
            const selectedValue = item.dataset.value;
            citySearchInput.value = item.textContent;
            cityFilter.value = selectedValue;
            cityDropdown.style.display = 'none';
            
            // 触发城市变更，更新区域选项
            updateDistrictOptions();
        });
    });

    // 输入搜索时过滤城市
    citySearchInput.addEventListener('input', (e) => {
        const searchText = e.target.value.toLowerCase();
        filterDropdownItems(searchText);
    });

    // 点击其他区域关闭下拉框
    document.addEventListener('click', (e) => {
        if (!e.target.matches('#citySearchInput') && 
            !e.target.closest('#cityDropdown')) {
            cityDropdown.style.display = 'none';
        }
    });

    // 过滤下拉项
    function filterDropdownItems(searchText) {
        let hasVisibleItems = false;
        
        dropdownItems.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (text.includes(searchText) || item.dataset.value === '') {
                item.style.display = 'block';
                hasVisibleItems = true;
            } else {
                item.style.display = 'none';
            }
        });

        // 如果没有匹配项，显示"全部城市"选项
        if (!hasVisibleItems) {
            const allCitiesItem = cityDropdown.querySelector('.dropdown-item[data-value=""]');
            if (allCitiesItem) {
                allCitiesItem.style.display = 'block';
            }
        }
    }
}

// 更新区域选项
async function updateDistrictOptions() {
    const cityFilter = document.getElementById('cityFilter');
    const districtFilter = document.getElementById('districtFilter');
    const selectedCity = cityFilter.value;

    // 清空现有选项
    districtFilter.innerHTML = '<option value="">全部区域</option>';

    // 如果选择了城市，获取并填充对应的区域
    if (selectedCity) {
        try {
            // 检查是否已经加载过该城市的区域数据
            if (!window.districtData[selectedCity]) {
                // 从API获取区域数据
                const districtsResponse = await fetch(`/listing-analysis/districts/${selectedCity}`);
                if (!districtsResponse.ok) {
                    throw new Error(`获取区域数据失败: ${districtsResponse.status}`);
                }
                const districts = await districtsResponse.json();

                // 缓存区域数据
                window.districtData[selectedCity] = districts;
            }

            // 填充区域下拉框
            window.districtData[selectedCity].forEach(districtName => {
                const option = document.createElement('option');
                option.value = districtName;
                option.textContent = districtName;
                districtFilter.appendChild(option);
            });
        } catch (error) {
            console.error(`加载${selectedCity}的区域数据失败:`, error);
            showMessage(`加载${selectedCity}的区域数据失败`, 'error');
        }
    }
}

// 生成竞争对手数据（优化版）
function generateCompetitors(totalCompetitorPosts) {
    const competitors = [];
    const competitorNames = [
        '某房产中介A', '某工业地产B', '某招商平台C',
        '某厂房租赁D', '某园区运营E', '某工业地产F',
        '某招商服务G', '某厂房中介H', '某地产集团I'
    ];

    let remainingPosts = totalCompetitorPosts;
    const totalCompetitors = Math.min(competitorNames.length, Math.floor(Math.random() * 3) + 7); // 7-9个竞争对手

    for (let i = 0; i < totalCompetitors; i++) {
        const isLast = i === totalCompetitors - 1;

        // 分配帖子数，保证合理分布
        let posts;
        if (isLast) {
            posts = remainingPosts;
        } else {
            const sharePercent = Math.random() * 0.25 + (i === 0 ? 0.2 : 0.05);
            posts = Math.floor(totalCompetitorPosts * sharePercent);
            remainingPosts -= posts;
        }

        // 计算市场份额
        const marketShare = (posts / totalCompetitorPosts * 100).toFixed(1);

        // 随机生成月增长率，-20%到40%之间
        const growthRate = (Math.random() * 60 - 20).toFixed(1);

        // 随机生成最近发帖时间，1小时到10天之内
        const lastPostDate = new Date();
        lastPostDate.setHours(lastPostDate.getHours() - Math.floor(Math.random() * 240));

        competitors.push({
            id: `comp${i + 1}`,
            name: competitorNames[i],
            posts,
            marketShare,
            growthRate,
            lastPostDate: formatDateTime(lastPostDate)
        });
    }

    // 按帖子数排序
    return competitors.sort((a, b) => b.posts - a.posts);
}

// 生成区域热力数据（优化版）
function generateDistrictData() {
    const areas = ['海淀区', '朝阳区', '丰台区', '石景山区', '西城区', '东城区', '房山区', '大兴区'];
    const competitors = ['我方', '竞争对手A', '竞争对手B', '竞争对手C', '其他'];

    // 限制数据量
    const limitedAreas = areas.slice(0, 6);
    const limitedCompetitors = competitors.slice(0, 4);

    const data = [];

    limitedAreas.forEach(area => {
        const areaData = { area };

        limitedCompetitors.forEach(competitor => {
            // 简化的随机生成逻辑
            let posts;
            if (competitor === '我方') {
                posts = Math.floor(Math.random() * 15) + 10;
            } else if (competitor === '其他') {
                posts = Math.floor(Math.random() * 8) + 2;
            } else {
                posts = Math.floor(Math.random() * 15) + 5;
            }

            areaData[competitor] = posts;
        });

        data.push(areaData);
    });

    return data;
}

// 更新竞争对手表格
function updateCompetitorTable() {
    const tableBody = document.getElementById('competitorTableBody');
    tableBody.innerHTML = '';

    // 只显示前10个竞争对手
    const displayedCompetitors = analysisData.competitors.slice(0, 10);

    displayedCompetitors.forEach((competitor, index) => {
        const row = document.createElement('tr');

        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${escapeHtml(competitor.name)}</td>
            <td>${competitor.posts}</td>
            <td>${competitor.marketShare}%</td>
            <td class="${competitor.growthRate >= 0 ? 'positive' : 'negative'}">${competitor.growthRate >= 0 ? '+' : ''}${competitor.growthRate}%</td>
            <td>${competitor.lastPostDate}</td>
            <td>
                <button class="btn-small info" onclick="showCompetitorDetail('${competitor.id}')">查看详情</button>
            </td>
        `;

        tableBody.appendChild(row);
    });
}

// 更新热门竞争对手列表
function updateTopCompetitorsList() {
    const list = document.getElementById('topCompetitorsList');
    list.innerHTML = '';

    // 获取前5名竞争对手
    const topCompetitors = analysisData.competitors.slice(0, 5);

    topCompetitors.forEach(competitor => {
        const item = document.createElement('div');
        item.className = 'competitor-item';

        // 计算竞争强度，根据帖子数、市场份额和增长率
        const share = parseFloat(competitor.marketShare);
        const growth = parseFloat(competitor.growthRate);
        const intensity = (share * 0.5 + (growth > 0 ? growth * 0.3 : 0) + 10) / 50;

        // 竞争强度等级
        let intensityLevel, intensityText;
        if (intensity < 0.3) {
            intensityLevel = 'low';
            intensityText = '较弱';
        } else if (intensity < 0.6) {
            intensityLevel = 'medium';
            intensityText = '中等';
        } else {
            intensityLevel = 'high';
            intensityText = '较强';
        }

        item.innerHTML = `
            <div class="competitor-header">
                <h4 class="competitor-name">${escapeHtml(competitor.name)}</h4>
            </div>
            <div class="competitor-content">
                <div class="competitor-stats">
                    <div class="competitor-stat">帖子数: <span>${competitor.posts}</span></div>
                    <div class="competitor-stat">份额: <span>${competitor.marketShare}%</span></div>
                    <div class="competitor-stat">增长率: <span class="${competitor.growthRate >= 0 ? 'positive' : 'negative'}">${competitor.growthRate >= 0 ? '+' : ''}${competitor.growthRate}%</span></div>
                </div>
                <div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>竞争强度:</span>
                        <span>${intensityText}</span>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar ${intensityLevel}" style="width: ${intensity * 100}%"></div>
                    </div>
                </div>
                <div class="competitor-actions">
                    <button class="btn-small info" onclick="showCompetitorDetail('${competitor.id}')">查看详情</button>
                </div>
            </div>
        `;

        list.appendChild(item);
    });
}

// 导出报告
async function exportReport() {
    try {
        const cityFilter = document.getElementById('cityFilter');
        const districtFilter = document.getElementById('districtFilter');

        const cityName = cityFilter.options[cityFilter.selectedIndex].text;
        const districtName = districtFilter.selectedIndex > 0 ? districtFilter.options[districtFilter.selectedIndex].text : '全部区域';
        const timeRangeText = document.getElementById('overviewTimeRange').textContent;

        const title = `${cityName}${districtName !== '全部区域' ? '-' + districtName : ''}竞争对手分析报告-${timeRangeText}`;

        const report = {
            title: title,
            date: new Date().toLocaleDateString(),
            data: analysisData
        };

        // 调用后端API生成PDF报告
        console.log('正在调用后端API生成报告...');

        // 发送POST请求到后端API
        // 创建一个安全的报告对象，使用英文标题
        const safeReport = {
            title: "Competitor_Analysis_Report",
            date: report.date,
            data: report.data
        };

        const response = await fetch('/reports/competitor-analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(safeReport)
        });

        if (!response.ok) {
            throw new Error(`生成报告失败: ${response.status}`);
        }

        // 获取PDF文件的blob对象
        const blob = await response.blob();

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${title}.pdf`;
        document.body.appendChild(a);
        a.click();

        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        console.log('报告导出成功');
    } catch (error) {
        console.error('导出报告失败:', error);
        showMessage(`导出报告失败: ${error.message}`, 'error');
    }
}

// 生成随机数据数组
function generateRandomData(size, min, max) {
    const data = [];
    for (let i = 0; i < size; i++) {
        data.push(Math.floor(Math.random() * (max - min + 1)) + min);
    }
    return data;
}

// 生成模拟帖子
function generateRandomPosts(competitorName) {
    const postTitles = [
        '【出售】一手工业用地50亩，可定制厂房',
        '【出租】标准厂房3000平，带行车，大电',
        '【出售】独栋双证厂房，形象好，超低单价',
        '【出租】产业园仓库1500平，交通便利',
        '【出售】开发区独栋生产研发楼，位置优越'
    ];

    let html = '';

    for (let i = 0; i < 5; i++) {
        const title = postTitles[Math.floor(Math.random() * postTitles.length)];
        const daysAgo = Math.floor(Math.random() * 10);
        const views = Math.floor(Math.random() * 900) + 100;

        html += `
            <div style="padding: 10px; border-bottom: 1px solid #eee; margin-bottom: 10px;">
                <div style="font-weight: bold; margin-bottom: 5px;">${title}</div>
                <div style="display: flex; justify-content: space-between; color: #888; font-size: 12px;">
                    <span>发布人: ${competitorName}</span>
                    <span>${daysAgo === 0 ? '今天' : `${daysAgo}天前`}</span>
                    <span>浏览: ${views}</span>
                </div>
            </div>
        `;
    }

    return html;
}

// 初始化主要图表
function initializePrimaryCharts() {
    console.log('初始化主要图表...');
    initializePostsOverTimeChart();
    initializeCompetitorShareChart();
}

// 初始化次要图表
function initializeSecondaryCharts() {
    console.log('初始化次要图表...');
    try {
        initializeContentQualityChart();
        initializeDistrictHeatmap();
    } catch (error) {
        console.error('初始化次要图表时出错:', error);
    }
}

// 更新所有图表
function updateCharts() {
    try {
        updatePostsOverTimeChart();
        updateCompetitorShareChart();
        updateContentQualityChart();
        updateDistrictHeatmap();
    } catch (error) {
        console.error('更新图表时出错:', error);
    }
}

// 从本地加载echarts库
function loadLocalEcharts() {
    if (window.echarts) return;

    const script = document.createElement('script');
    script.src = 'vendor/echarts.min.js';
    script.async = true;
    script.onload = function() {
        console.log('本地echarts库加载成功');
    };
    script.onerror = function() {
        console.error('本地echarts库加载失败');
    };
    document.head.appendChild(script);
}

// 初始化和更新帖子随时间变化的折线图
function initializePostsOverTimeChart() {
    try {
        const chartDom = document.getElementById('postsOverTimeChart');
        if (!chartDom) {
            console.error('未找到帖子随时间变化图表的DOM元素');
            return;
        }

        let chart;
        try {
            chart = echarts.init(chartDom);
        } catch (error) {
            console.error('初始化echarts图表失败:', error);
            // 尝试从本地加载echarts
            loadLocalEcharts();
            setTimeout(() => {
                try {
                    chart = echarts.init(chartDom);
                } catch (retryError) {
                    console.error('重试初始化echarts图表失败:', retryError);
                    return;
                }
            }, 500);
            return;
        }

        window.postsOverTimeChart = chart;

        // 获取公司标签（实际公司名称）
        const companyLabels = window.companyLabels || ['我方', '竞争对手A', '竞争对手B', '竞争对手C', '其他'];

        // 为图表系列生成颜色映射
        const colorMap = {
            '我方': '#1890ff',
            '其他': '#722ed1'
        };

        // 为竞争对手分配颜色
        const competitorColors = ['#13c2c2', '#52c41a', '#faad14', '#eb2f96', '#fa8c16', '#a0d911'];
        for (let i = 1; i < companyLabels.length - 1; i++) {
            colorMap[companyLabels[i]] = competitorColors[(i - 1) % competitorColors.length];
        }

        const option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: companyLabels,
                top: 'top'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: []
            },
            yAxis: {
                type: 'value',
                name: '帖子数'
            },
            series: companyLabels.map(company => ({
                name: company,
                type: 'line',
                stack: 'Total',
                areaStyle: {},
                emphasis: {
                    focus: 'series'
                },
                data: [],
                itemStyle: { color: colorMap[company] || '#1890ff' }
            }))
        };

        chart.setOption(option);

        // 窗口大小变化时自动调整图表大小
        window.addEventListener('resize', function() {
            chart.resize();
        });

        // 立即更新一次图表数据
        updatePostsOverTimeChart();
    } catch (error) {
        console.error('初始化帖子随时间变化图表失败:', error);
    }
}

// 更新帖子随时间变化的折线图
function updatePostsOverTimeChart() {
    const chart = window.postsOverTimeChart;
    if (!chart) return;

    const timeSeriesData = analysisData.overview.timeSeriesData;
    if (!timeSeriesData || !timeSeriesData.length) return;

    // 获取公司标签（实际公司名称）
    const companyLabels = window.companyLabels || ['我方', '竞争对手A', '竞争对手B', '竞争对手C', '其他'];

    // 提取日期
    const dates = timeSeriesData.map(item => item.date);

    // 为每个公司提取数据
    const seriesData = companyLabels.map(company => {
        return {
            name: company,
            data: timeSeriesData.map(item => item[company] || 0)
        };
    });

    chart.setOption({
        legend: {
            data: companyLabels
        },
        xAxis: {
            data: dates
        },
        series: seriesData
    });
}

// 初始化和更新竞争对手市场份额饼图
function initializeCompetitorShareChart() {
    const chartDom = document.getElementById('competitorShareChart');
    const chart = echarts.init(chartDom);
    window.competitorShareChart = chart;

    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'horizontal',
            bottom: 'bottom'
        },
        series: [
            {
                name: '市场份额',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '16',
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: []
            }
        ]
    };

    chart.setOption(option);

    // 窗口大小变化时自动调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
}

function updateCompetitorShareChart() {
    const chart = window.competitorShareChart;
    if (!chart) return;

    // 准备竞争对手份额数据
    const pieData = [];

    // 添加我方数据
    pieData.push({
        value: analysisData.overview.ourPostsPercentage,
        name: '我方',
        itemStyle: { color: '#1890ff' }
    });

    // 添加竞争对手数据（限制数量，避免过多影响性能）
    const maxCompetitorsToShow = 4;
    analysisData.competitors.slice(0, maxCompetitorsToShow).forEach((comp, index) => {
        const colors = ['#13c2c2', '#52c41a', '#faad14', '#722ed1'];
        pieData.push({
            value: parseFloat(comp.marketShare),
            name: comp.name,
            itemStyle: { color: colors[index % colors.length] }
        });
    });

    // 如果竞争对手超过限制数量，将剩余的合并为"其他"
    if (analysisData.competitors.length > maxCompetitorsToShow) {
        const otherShare = analysisData.competitors
            .slice(maxCompetitorsToShow)
            .reduce((sum, comp) => sum + parseFloat(comp.marketShare), 0);

        pieData.push({
            value: otherShare,
            name: '其他竞争对手',
            itemStyle: { color: '#bfbfbf' }
        });
    }

    chart.setOption({
        series: [
            {
                data: pieData
            }
        ]
    });
}

// 初始化和更新内容质量雷达图
function initializeContentQualityChart() {
    try {
        const chartDom = document.getElementById('contentQualityChart');
        if (!chartDom) {
            console.warn('无法找到contentQualityChart元素');
            return;
        }

        const chart = echarts.init(chartDom);
        window.contentQualityChart = chart;

        const option = {
            tooltip: {
                trigger: 'item'
            },
            legend: {
                data: ['我方', '竞争对手'],
                bottom: 'bottom'
            },
            radar: {
                indicator: [
                    { name: '收藏数', max: 10 },
                    { name: '图片数', max: 10 },
                    { name: '更新频率', max: 10 },
                    { name: '视频数', max: 10 }
                ]
            },
            series: [
                {
                    name: '内容质量对比',
                    type: 'radar',
                    data: [
                        {
                            value: [0, 0, 0, 0],
                            name: '我方',
                            symbolSize: 8,
                            lineStyle: {
                                width: 2
                            },
                            areaStyle: {
                                opacity: 0.3
                            },
                            itemStyle: {
                                color: '#1890ff'
                            }
                        },
                        {
                            value: [0, 0, 0, 0],
                            name: '竞争对手',
                            symbolSize: 8,
                            lineStyle: {
                                width: 2
                            },
                            areaStyle: {
                                opacity: 0.3
                            },
                            itemStyle: {
                                color: '#faad14'
                            }
                        }
                    ]
                }
            ]
        };

        chart.setOption(option);

        // 窗口大小变化时自动调整图表大小
        window.addEventListener('resize', function() {
            chart.resize();
        });
    } catch (error) {
        console.error('初始化内容质量图表时出错:', error);
    }
}

function updateContentQualityChart() {
    try {
        const chart = window.contentQualityChart;
        if (!chart) return;

        // 计算我方指标值
        const ourPopularity = Math.min(analysisData.contentQuality.our.avgPopularity / 15, 10);
        const ourImages = Math.min(analysisData.contentQuality.our.avgImages, 10);
        const ourUpdateFreq = Math.min(10 / analysisData.contentQuality.our.updateFrequency, 10);
        const ourVideos = Math.min(analysisData.contentQuality.our.avgVideos * 2, 10);

        // 计算竞争对手指标值
        const compPopularity = Math.min(analysisData.contentQuality.competitor.avgPopularity / 15, 10);
        const compImages = Math.min(analysisData.contentQuality.competitor.avgImages, 10);
        const compUpdateFreq = Math.min(10 / analysisData.contentQuality.competitor.updateFrequency, 10);
        const compVideos = Math.min(analysisData.contentQuality.competitor.avgVideos * 2, 10);

        chart.setOption({
            series: [
                {
                    data: [
                        {
                            value: [ourPopularity, ourImages, ourUpdateFreq, ourVideos],
                            name: '我方'
                        },
                        {
                            value: [compPopularity, compImages, compUpdateFreq, compVideos],
                            name: '竞争对手'
                        }
                    ]
                }
            ]
        });
    } catch (error) {
        console.error('更新内容质量图表时出错:', error);
    }
}

// 初始化和更新区域热力图
function initializeDistrictHeatmap() {
    // 使用表格显示区域热力图
    const heatmapTable = document.getElementById('districtHeatmap');
    if (!heatmapTable) return;

    // 初始化表格结构
    heatmapTable.innerHTML = '<thead><tr><th>区域</th><th>我方</th><th>竞争对手A</th><th>竞争对手B</th><th>竞争对手C</th><th>总计</th></tr></thead>';

    const tbody = document.createElement('tbody');
    heatmapTable.appendChild(tbody);
}

function updateDistrictHeatmap() {
    try {
        const heatmapTable = document.getElementById('districtHeatmap');
        if (!heatmapTable) return;

        const tbody = heatmapTable.querySelector('tbody');
        tbody.innerHTML = '';

        // 找出最大值，用于计算热力值
        let maxPosts = 0;
        analysisData.districts.forEach(district => {
            ['我方', '竞争对手A', '竞争对手B', '竞争对手C'].forEach(competitor => {
                if (district[competitor] > maxPosts) {
                    maxPosts = district[competitor];
                }
            });
        });

        // 生成表格行
        analysisData.districts.forEach(district => {
            const row = document.createElement('tr');

            // 区域名称
            const areaCell = document.createElement('td');
            areaCell.textContent = district.area;
            row.appendChild(areaCell);

            // 竞争对手数据
            let total = 0;
            ['我方', '竞争对手A', '竞争对手B', '竞争对手C'].forEach(competitor => {
                const posts = district[competitor] || 0;
                total += posts;

                const cell = document.createElement('td');

                // 热力颜色，根据帖子数量的相对大小
                const intensity = maxPosts > 0 ? posts / maxPosts : 0;
                let backgroundColor = '';

                if (competitor === '我方') {
                    const blue = Math.floor(255 - intensity * 200);
                    backgroundColor = `rgba(24, 144, ${blue}, ${0.2 + intensity * 0.8})`;
                } else {
                    const red = Math.floor(255 - intensity * 150);
                    const green = Math.floor(255 - intensity * 200);
                    backgroundColor = `rgba(${red}, ${green}, 220, ${0.2 + intensity * 0.7})`;
                }

                cell.className = 'heatmap-cell';
                cell.style.backgroundColor = backgroundColor;
                cell.textContent = posts;
                row.appendChild(cell);
            });

            // 总计
            const totalCell = document.createElement('td');
            totalCell.textContent = total;
            totalCell.style.fontWeight = 'bold';
            row.appendChild(totalCell);

            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('更新区域热力图时出错:', error);
    }
}

// 显示竞争对手详情
function showCompetitorDetail(competitorId) {
    try {
        // 查找竞争对手
        const competitor = analysisData.competitors.find(c => c.id === competitorId);
        if (!competitor) return;

        // 更新模态框标题
        const titleElement = document.getElementById('competitorModalTitle');
        if (titleElement) {
            titleElement.textContent = `竞争对手详情: ${competitor.name}`;
        }

        // 创建详情内容
        const content = document.getElementById('competitorModalContent');
        if (!content) return;

        // 生成模拟的详细数据
        const postsByMonth = generateRandomData(6, 10, 30);
        const postsByType = [
            { name: '厂房出售', value: Math.floor(Math.random() * 40) + 20 },
            { name: '厂房出租', value: Math.floor(Math.random() * 30) + 15 },
            { name: '土地出售', value: Math.floor(Math.random() * 20) + 5 },
            { name: '写字楼出租', value: Math.floor(Math.random() * 15) + 5 }
        ];

        // 创建HTML内容
        content.innerHTML = `
            <div style="margin-bottom: 20px;">
                <h4 style="margin-top: 0;">基本信息</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="stat-item">
                        <div class="stat-label">帖子总数</div>
                        <div class="stat-value">${competitor.posts}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">市场份额</div>
                        <div class="stat-value">${competitor.marketShare}%</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">月增长率</div>
                        <div class="stat-value ${competitor.growthRate >= 0 ? 'positive' : 'negative'}">${competitor.growthRate >= 0 ? '+' : ''}${competitor.growthRate}%</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">最近发帖</div>
                        <div class="stat-value" style="font-size: 16px;">${competitor.lastPostDate}</div>
                    </div>
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <h4>月度发帖趋势</h4>
                <div id="competitorMonthlyChart" style="height: 300px;"></div>
            </div>

            <div style="margin-bottom: 20px;">
                <h4>帖子类型分布</h4>
                <div id="competitorTypeChart" style="height: 300px;"></div>
            </div>

            <div>
                <h4>最新帖子</h4>
                <div class="competitor-posts" style="max-height: 300px; overflow-y: auto;">
                    ${generateRandomPosts(competitor.name)}
                </div>
            </div>
        `;

        // 显示模态框
        const modal = document.getElementById('competitorDetailModal');
        if (modal && typeof modal.showModal === 'function') {
            modal.showModal();

            // 渲染图表
            setTimeout(() => {
                try {
                    // 月度趋势图表
                    const monthlyChartElement = document.getElementById('competitorMonthlyChart');
                    if (!monthlyChartElement) return;

                    const monthlyChart = echarts.init(monthlyChartElement);
                    monthlyChart.setOption({
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: ['1月前', '2月前', '3月前', '4月前', '5月前', '本月']
                        },
                        yAxis: {
                            type: 'value',
                            name: '帖子数'
                        },
                        series: [
                            {
                                name: '帖子数',
                                type: 'bar',
                                data: postsByMonth,
                                itemStyle: {
                                    color: '#1890ff'
                                }
                            }
                        ]
                    });

                    // 帖子类型饼图
                    const typeChartElement = document.getElementById('competitorTypeChart');
                    if (!typeChartElement) return;

                    const typeChart = echarts.init(typeChartElement);
                    typeChart.setOption({
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c} ({d}%)'
                        },
                        legend: {
                            orient: 'horizontal',
                            bottom: 'bottom'
                        },
                        series: [
                            {
                                name: '帖子类型',
                                type: 'pie',
                                radius: '50%',
                                data: postsByType.map(item => {
                                    return {
                                        name: item.name,
                                        value: item.value
                                    };
                                }),
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    });

                    // 监听窗口大小变化
                    function resizeCharts() {
                        monthlyChart.resize();
                        typeChart.resize();
                    }

                    window.addEventListener('resize', resizeCharts);

                    // 监听模态框关闭事件，移除事件监听
                    modal.addEventListener('close', () => {
                        window.removeEventListener('resize', resizeCharts);
                        monthlyChart.dispose();
                        typeChart.dispose();
                    }, { once: true });
                } catch (error) {
                    console.error('渲染竞争对手详情图表失败:', error);
                }
            }, 10);
        }
    } catch (error) {
        console.error('显示竞争对手详情失败:', error);
    }
}

// 将函数暴露给全局作用域，以便HTML中的内联事件可以调用
window.showCompetitorDetail = showCompetitorDetail;

// 获取任务最后执行时间
async function fetchLastDataUpdateTime() {
    try {
        const response = await fetch('http://8.141.5.67:8000/api/tasks/');
        if (!response.ok) {
            throw new Error(`获取任务信息失败: ${response.status}`);
        }

        const tasksData = await response.json();

        // 查找爬取竞帖数据的任务
        const scrapingTask = tasksData.find(task =>
            task.task_name && task.task_name.includes('爬取竞帖数据')
        );

        if (scrapingTask && scrapingTask.last_run_time) {
            // 保存最后更新时间
            analysisData.overview.lastUpdateTime = scrapingTask.last_run_time;

            // 更新页面上的最后更新时间显示
            updateLastUpdateTimeDisplay();
        } else {
            console.warn('未找到爬取竞帖数据的任务信息');
        }
    } catch (error) {
        console.error('获取任务执行时间失败:', error);
    }
}

// 更新最后更新时间显示
function updateLastUpdateTimeDisplay() {
    try {
        const lastUpdateTimeElement = document.getElementById('dataLastUpdateTime');
        if (lastUpdateTimeElement && analysisData.overview.lastUpdateTime) {
            const updateTime = new Date(analysisData.overview.lastUpdateTime);
            lastUpdateTimeElement.textContent = `数据更新时间: ${formatDateTime(updateTime)}`;
            lastUpdateTimeElement.style.display = 'block';
        }
    } catch (error) {
        console.error('更新最后更新时间显示失败:', error);
    }
}