/**
 * 厂房数据管理JS
 * 处理工厂房源数据的加载、展示和操作
 */

// 存储已加载的所有数据
let allFactoryData = null;
// 存储园区统计数据
let communitiesData = null;
// 存储分页信息
let paginationInfo = {
    page: 1,
    page_size: 10,
    total: 0,
    total_pages: 0
};
// SSE事件源
let eventSource = null;

// 存储筛选条件
let filterConditions = {
    is_published: null,
    has_content: null,
    has_gallery: null,
    has_knowledge_base: null,
    has_poi_data: null,
    city: null
};

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载城市列表
    loadCityList();

    // 加载已导入的房源数据
    loadImportedFactoryData(1);

    // 添加刷新按钮事件
    document.getElementById('refreshDataBtn').addEventListener('click', function() {
        loadImportedFactoryData(1);
    });

    // 生成所有园区营销内容按钮事件
    const generateAllBtn = document.getElementById('generateAllBtn');
    if (generateAllBtn) {
        generateAllBtn.addEventListener('click', function() {
            generateAllCommunityContent();
        });
    }

    // 添加筛选按钮事件
    const applyFilterBtn = document.getElementById('applyFilterBtn');
    if (applyFilterBtn) {
        applyFilterBtn.addEventListener('click', function() {
            // 获取筛选条件
            const publishFilter = document.getElementById('publishFilter').value;
            const contentFilter = document.getElementById('contentFilter').value;
            const galleryFilter = document.getElementById('galleryFilter').value;
            const knowledgeBaseFilter = document.getElementById('knowledgeBaseFilter').value;
            const poiDataFilter = document.getElementById('poiDataFilter').value;
            const cityFilter = document.getElementById('cityFilter').value;

            // 更新筛选条件
            filterConditions.is_published = publishFilter ? parseInt(publishFilter) : null;
            filterConditions.has_content = contentFilter ? parseInt(contentFilter) : null;
            filterConditions.has_gallery = galleryFilter ? parseInt(galleryFilter) : null;
            filterConditions.has_knowledge_base = knowledgeBaseFilter ? parseInt(knowledgeBaseFilter) : null;
            filterConditions.has_poi_data = poiDataFilter ? parseInt(poiDataFilter) : null;
            filterConditions.city = cityFilter || null;

            // 重新加载数据
            loadImportedFactoryData(1);
        });
    }

    // 添加重置按钮事件
    const resetFilterBtn = document.getElementById('resetFilterBtn');
    if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', function() {
            // 重置筛选条件
            document.getElementById('publishFilter').value = '';
            document.getElementById('contentFilter').value = '';
            document.getElementById('galleryFilter').value = '';
            document.getElementById('knowledgeBaseFilter').value = '';
            document.getElementById('poiDataFilter').value = '';
            document.getElementById('cityFilter').value = '';

            // 清空筛选条件
            filterConditions.is_published = null;
            filterConditions.has_content = null;
            filterConditions.has_gallery = null;
            filterConditions.has_knowledge_base = null;
            filterConditions.has_poi_data = null;
            filterConditions.city = null;

            // 重新加载数据
            loadImportedFactoryData(1);
        });
    }

    // 连接到SSE事件流
    connectToEventSource();

    // 页面关闭前断开SSE连接
    window.addEventListener('beforeunload', function() {
        disconnectEventSource();
    });
});

/**
 * 加载城市列表
 */
async function loadCityList() {
    try {
        const response = await fetch('/excel/cities');
        const result = await response.json();

        if (result.success && result.data) {
            const cityFilter = document.getElementById('cityFilter');
            if (cityFilter) {
                // 清空现有选项（保留"全部"选项）
                cityFilter.innerHTML = '<option value="">-- 全部 --</option>';

                // 添加城市选项
                result.data.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city;
                    option.textContent = city;
                    cityFilter.appendChild(option);
                });

            }
        }
    } catch (error) {
        // 静默处理错误，不影响主要功能
    }
}

/**
 * 加载已导入的房源数据
 * @param {number} page - 页码
 */
async function loadImportedFactoryData(page = 1) {
    try {
        // 显示加载状态
        const loadingContainer = document.getElementById('loadingContainer');
        const dataSummary = document.getElementById('dataSummary');
        const communitiesTableContainer = document.getElementById('communitiesTableContainer');
        const emptyDataContainer = document.getElementById('emptyDataContainer');

        if (loadingContainer) loadingContainer.style.display = 'flex';
        if (dataSummary) dataSummary.style.display = 'none';
        if (communitiesTableContainer) communitiesTableContainer.style.display = 'none';
        if (emptyDataContainer) emptyDataContainer.style.display = 'none';

        // 更新当前页码
        paginationInfo.page = page;

        // 构建查询参数
        let queryParams = `page=${page}&page_size=10`;

        // 添加筛选条件
        if (filterConditions.is_published !== null) {
            queryParams += `&is_published=${filterConditions.is_published}`;
        }

        if (filterConditions.has_content !== null) {
            queryParams += `&has_content=${filterConditions.has_content}`;
        }

        if (filterConditions.has_gallery !== null) {
            queryParams += `&has_gallery=${filterConditions.has_gallery}`;
        }

        if (filterConditions.has_knowledge_base !== null) {
            queryParams += `&has_knowledge_base=${filterConditions.has_knowledge_base}`;
        }

        if (filterConditions.has_poi_data !== null) {
            queryParams += `&has_poi_data=${filterConditions.has_poi_data}`;
        }

        if (filterConditions.city !== null) {
            queryParams += `&city=${encodeURIComponent(filterConditions.city)}`;
        }

        // 同时获取房源数据和任务状态
        const [factoryResponse, tasksResponse] = await Promise.all([
            fetch(`/excel/imported-factory-data?${queryParams}`),
            fetch('/tasks/content-status')  // 使用新的SSE任务状态接口
        ]);

        const factoryData = await factoryResponse.json();
        let tasksData = {};

        // 处理任务状态数据 - 适配新的响应格式
        if (tasksResponse.ok) {
            const tasksResult = await tasksResponse.json();
            if (tasksResult.success && tasksResult.data) {
                // 新格式：tasksResult.data 是一个对象，键是园区名称，值是任务状态信息
                tasksData = tasksResult.data;
            }
        }



        // 隐藏加载状态
        if (loadingContainer) loadingContainer.style.display = 'none';

        // 处理响应结果
        if (factoryData.success && factoryData.data && factoryData.data.length > 0) {
            // 保存数据
            allFactoryData = factoryData.data;
            // 注意：后端不再返回communities数据
            communitiesData = {};

            // 更新分页信息
            if (factoryData.pagination) {
                paginationInfo = {
                    page: factoryData.pagination.page || 1,
                    page_size: factoryData.pagination.page_size || 10,
                    total: factoryData.pagination.total || 0,
                    total_pages: factoryData.pagination.total_pages || 1
                };
            }

            // 显示数据概览
            if (dataSummary) {
                dataSummary.style.display = 'flex';
                const totalCountElement = document.getElementById('totalCount');
                if (totalCountElement) totalCountElement.textContent = factoryData.statistics.total || 0;

                // 显示有内容概率
                const contentPercentageElement = document.getElementById('contentPercentage');
                if (contentPercentageElement) {
                    const percentage = factoryData.statistics.with_content_percent || 0;
                    contentPercentageElement.textContent = percentage + '%';
                }

                // 显示有图库概率
                const galleryPercentageElement = document.getElementById('galleryPercentage');
                if (galleryPercentageElement) {
                    const percentage = factoryData.statistics.with_gallery_percent || 0;
                    galleryPercentageElement.textContent = percentage + '%';
                }

                // 显示有知识库概率
                const knowledgeBasePercentageElement = document.getElementById('knowledgeBasePercentage');
                if (knowledgeBasePercentageElement) {
                    const percentage = factoryData.statistics.with_knowledge_base_percent || 0;
                    knowledgeBasePercentageElement.textContent = percentage + '%';
                }

                // 显示有周边配套概率
                const poiDataPercentageElement = document.getElementById('poiDataPercentage');
                if (poiDataPercentageElement) {
                    const percentage = factoryData.statistics.with_poi_data_percent || 0;
                    poiDataPercentageElement.textContent = percentage + '%';
                }

                // 设置待生成和已生成内容的园区数量
                const pendingCountElement = document.getElementById('pendingCount');
                const completedCountElement = document.getElementById('completedCount');
                const withGalleryCountElement = document.getElementById('withGalleryCount');
                const withKnowledgeBaseCountElement = document.getElementById('withKnowledgeBaseCount');
                const withPoiDataCountElement = document.getElementById('withPoiDataCount');

                if (pendingCountElement) pendingCountElement.textContent = factoryData.statistics.without_content || 0;
                if (completedCountElement) completedCountElement.textContent = factoryData.statistics.with_content || 0;
                if (withGalleryCountElement) withGalleryCountElement.textContent = factoryData.statistics.with_gallery || 0;
                if (withKnowledgeBaseCountElement) withKnowledgeBaseCountElement.textContent = factoryData.statistics.with_knowledge_base || 0;
                if (withPoiDataCountElement) withPoiDataCountElement.textContent = factoryData.statistics.with_poi_data || 0;
            }

            // 填充园区表格，同时应用任务状态
            populateCommunityTable(tasksData);

            // 创建分页控件
            createPagination();

            if (communitiesTableContainer) communitiesTableContainer.style.display = 'block';

            // 显示按钮状态
            const pendingCount = factoryData.statistics.without_content || 0;
            const generateAllBtn = document.getElementById('generateAllBtn');
            if (generateAllBtn) {
                generateAllBtn.style.display = pendingCount > 0 ? 'flex' : 'none';

                // 检查是否有任务正在进行中，如果有则禁用"生成所有"按钮
                const hasRunningTasks = Object.values(tasksData).some(task => task.status === 0 || task.status === 1);
                generateAllBtn.disabled = hasRunningTasks;
            }
        } else {
            // 显示空数据状态
            if (emptyDataContainer) emptyDataContainer.style.display = 'block';
        }
    } catch (error) {
        // 隐藏加载状态
        const loadingContainer = document.getElementById('loadingContainer');
        if (loadingContainer) loadingContainer.style.display = 'none';

        // 显示错误消息
        const errorContainer = document.createElement('div');
        errorContainer.className = 'empty-data';
        errorContainer.innerHTML = `
            <i class="fas fa-exclamation-triangle" style="color: #ff4d4f;"></i>
            <h4>加载数据失败</h4>
            <p>发生错误: ${error.message || '未知错误'}</p>
            <button id="retryBtn" class="action-btn view-btn" style="margin-top: 15px;">
                <i class="fas fa-sync-alt"></i> 重试
            </button>
        `;

        const importedDataArea = document.getElementById('importedDataArea');
        if (importedDataArea) importedDataArea.appendChild(errorContainer);

        // 添加重试按钮事件
        const retryBtn = document.getElementById('retryBtn');
        if (retryBtn) {
            retryBtn.addEventListener('click', function() {
                errorContainer.remove();
                loadImportedFactoryData(1);
            });
        }
    }
}

/**
 * 创建分页控件
 */
function createPagination() {
    const paginationContainer = document.getElementById('pagination');
    if (!paginationContainer) return;

    paginationContainer.innerHTML = '';

    // 安全地获取分页信息
    const page = paginationInfo.page || 1;
    const total_pages = paginationInfo.total_pages || 1;

    // 如果只有一页，不显示分页控件
    if (total_pages <= 1) {
        return;
    }

    // 添加"上一页"按钮
    const prevBtn = document.createElement('button');
    prevBtn.className = `pagination-btn ${page <= 1 ? 'disabled' : ''}`;
    prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
    if (page > 1) {
        prevBtn.addEventListener('click', () => loadImportedFactoryData(page - 1));
    }
    paginationContainer.appendChild(prevBtn);

    // 计算显示的页码范围
    let startPage = Math.max(1, page - 2);
    let endPage = Math.min(total_pages, startPage + 4);

    // 调整startPage确保显示5个页码（如果有足够的页面）
    if (endPage - startPage < 4 && startPage > 1) {
        startPage = Math.max(1, endPage - 4);
    }

    // 添加第一页按钮（如果需要）
    if (startPage > 1) {
        const firstPageBtn = document.createElement('button');
        firstPageBtn.className = 'pagination-btn';
        firstPageBtn.textContent = '1';
        firstPageBtn.addEventListener('click', () => loadImportedFactoryData(1));
        paginationContainer.appendChild(firstPageBtn);

        // 添加省略号（如果需要）
        if (startPage > 2) {
            const ellipsisBtn = document.createElement('button');
            ellipsisBtn.className = 'pagination-btn disabled';
            ellipsisBtn.textContent = '...';
            paginationContainer.appendChild(ellipsisBtn);
        }
    }

    // 添加页码按钮
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `pagination-btn ${i === page ? 'active' : ''}`;
        pageBtn.textContent = i;
        if (i !== page) {
            pageBtn.addEventListener('click', () => loadImportedFactoryData(i));
        }
        paginationContainer.appendChild(pageBtn);
    }

    // 添加省略号和最后一页（如果需要）
    if (endPage < total_pages) {
        if (endPage < total_pages - 1) {
            const ellipsisBtn = document.createElement('button');
            ellipsisBtn.className = 'pagination-btn disabled';
            ellipsisBtn.textContent = '...';
            paginationContainer.appendChild(ellipsisBtn);
        }

        const lastPageBtn = document.createElement('button');
        lastPageBtn.className = 'pagination-btn';
        lastPageBtn.textContent = total_pages;
        lastPageBtn.addEventListener('click', () => loadImportedFactoryData(total_pages));
        paginationContainer.appendChild(lastPageBtn);
    }

    // 添加"下一页"按钮
    const nextBtn = document.createElement('button');
    nextBtn.className = `pagination-btn ${page >= total_pages ? 'disabled' : ''}`;
    nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
    if (page < total_pages) {
        nextBtn.addEventListener('click', () => loadImportedFactoryData(page + 1));
    }
    paginationContainer.appendChild(nextBtn);

    // 添加页码信息
    const pageInfoElement = document.createElement('div');
    pageInfoElement.className = 'pagination-info';
    pageInfoElement.textContent = `第 ${page} 页 / 共 ${total_pages} 页`;
    paginationContainer.appendChild(pageInfoElement);
}



/**
 * 填充园区数据卡片
 * @param {Object} tasksData - 任务状态数据
 */
function populateCommunityTable(tasksData = {}) {
    const cardsContainer = document.getElementById('communityCards');
    if (!cardsContainer || !allFactoryData) return;

    cardsContainer.innerHTML = '';
    allFactoryData.forEach((item, index) => {
        const communityName = item.community;
        if (!communityName) return;

        // 直接使用原始园区名称匹配任务状态（后端已处理名称映射）
        const taskInfo = tasksData[communityName];

        let buttonHtml = '';
        let statusHtml = '';
        let publishStatusHtml = '';
        let publishButtonHtml = '';

        // 计算营销内容状态
        const hasContent = item.has_marketing_content;

        // 根据任务状态和内容状态决定显示
        if (taskInfo && (taskInfo.status === 0 || taskInfo.status === 1)) {
            // 有任务正在进行中，显示进行中状态
            const progress = taskInfo.progress || 0;
            const statusClass = 'status-badge status-progress';
            const statusText = taskInfo.status === 0 ? '等待中' : `进行中 ${progress}%`;
            const statusIcon = taskInfo.status === 0 ? 'fa-hourglass-half' : 'fa-spinner fa-spin';

            statusHtml = `
                <span class="${statusClass}">
                    <i class="fas ${statusIcon}"></i> ${statusText}
                </span>
            `;

            buttonHtml = `
                <button class="action-btn generate-btn" disabled data-erp-id="${item.erp_id}" data-community="${communityName}">
                    <i class="fas ${statusIcon}"></i> ${statusText}
                </button>
            `;
        } else if (taskInfo && taskInfo.status === 3) {
            // 任务失败
            const statusClass = 'status-badge status-error';
            const statusText = '生成失败';

            statusHtml = `
                <span class="${statusClass}">
                    <i class="fas fa-exclamation-circle"></i> ${statusText}
                </span>
            `;

            buttonHtml = `
                <button class="action-btn generate-btn" title="${taskInfo.error_message || '生成失败'}" data-erp-id="${item.erp_id}" data-community="${communityName}">
                    <i class="fas fa-exclamation-circle"></i> 重试
                </button>
            `;
        } else if (hasContent) {
            // 已有内容
            const statusClass = 'status-badge status-complete';
            const statusText = '已生成';

            statusHtml = `
                <span class="${statusClass}">
                    <i class="fas fa-check-circle"></i> ${statusText}
                </span>
            `;

            buttonHtml = `
                <button class="action-btn view-btn" data-erp-id="${item.erp_id}" data-community="${communityName}">
                    <i class="fas fa-eye"></i> 查看
                </button>
            `;
        } else {
            // 未生成内容，且无任务
            const statusClass = 'status-badge status-pending';
            const statusText = '未生成';

            statusHtml = `
                <span class="${statusClass}">
                    <i class="fas fa-exclamation-circle"></i> ${statusText}
                </span>
            `;

            buttonHtml = `
                <button class="action-btn generate-btn" data-erp-id="${item.erp_id}" data-community="${communityName}">
                    <i class="fas fa-magic"></i> 生成内容
                </button>
            `;
        }

        // 添加发布状态和发布按钮
        // 检查是否已发布到优推
        if (item.is_published === 1) {
            publishStatusHtml = `
                <span class="status-badge status-complete">
                    <i class="fas fa-check-circle"></i> 已发布
                </span>
            `;

            // 如果有优推房源ID，显示查看按钮
            if (item.youtui_house_id) {
                publishButtonHtml = `
                    <button class="action-btn view-btn" data-youtui-id="${item.youtui_house_id}" title="查看优推房源">
                        <i class="fas fa-external-link-alt"></i> 查看优推
                    </button>
                `;
            }
        } else {
            publishStatusHtml = `
                <span class="status-badge status-pending">
                    <i class="fas fa-times-circle"></i> 未发布
                </span>
            `;

            publishButtonHtml = `
                <button class="action-btn publish-btn" data-erp-id="${item.erp_id}">
                    <i class="fas fa-paper-plane"></i> 发布
                </button>
            `;
        }

        // 创建卡片元素
        const card = document.createElement('div');
        card.className = 'community-card';

        // 准备图库状态显示
        let galleryStatusHtml = '';
        if (item.has_gallery === true) {
            galleryStatusHtml = `
                <span class="status-badge status-complete">
                    <i class="fas fa-images"></i> 有图库
                </span>
            `;
        } else if (item.has_gallery === false) {
            galleryStatusHtml = `
                <span class="status-badge status-error">
                    <i class="fas fa-exclamation-circle"></i> 无图库
                </span>
            `;
        }

        // 准备知识库状态显示
        let knowledgeBaseStatusHtml = '';
        if (item.has_knowledge_base === true) {
            knowledgeBaseStatusHtml = `
                <span class="status-badge status-complete">
                    <i class="fas fa-brain"></i> 有知识库
                </span>
            `;
        } else if (item.has_knowledge_base === false) {
            knowledgeBaseStatusHtml = `
                <span class="status-badge status-error">
                    <i class="fas fa-exclamation-circle"></i> 无知识库
                </span>
            `;
        }

        // 准备周边配套状态显示
        let poiDataStatusHtml = '';
        if (item.has_poi_data === true) {
            poiDataStatusHtml = `
                <span class="status-badge status-complete">
                    <i class="fas fa-map-marked-alt"></i> 有周边配套
                </span>
            `;
        } else if (item.has_poi_data === false) {
            poiDataStatusHtml = `
                <span class="status-badge status-error">
                    <i class="fas fa-exclamation-circle"></i> 无周边配套
                </span>
            `;
        }

        // 填充卡片内容
        card.innerHTML = `
            <div class="community-card-header">
                <h3 class="community-card-title">${communityName || '未命名园区'}</h3>
                <div class="community-card-location">
                    <i class="fas fa-map-marker-alt"></i> ${item.city || '-'} ${item.zone || ''}
                </div>
            </div>

            <div class="community-card-content">
                <div class="community-card-status">
                    ${statusHtml}
                    ${publishStatusHtml}
                    ${galleryStatusHtml}
                    ${knowledgeBaseStatusHtml}
                    ${poiDataStatusHtml}
                </div>

                <div class="community-card-actions">
                    ${buttonHtml}
                    ${publishButtonHtml}
                </div>
            </div>
        `;

        // 添加按钮事件
        const actionBtns = card.querySelectorAll('.action-btn');
        actionBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.disabled) return;

                const communityName = this.getAttribute('data-community');
                const erpId = this.getAttribute('data-erp-id');
                const youtuiId = this.getAttribute('data-youtui-id');

                if (this.classList.contains('generate-btn')) {
                    generateCommunityContent(communityName);
                } else if (this.classList.contains('publish-btn')) {
                    publishToYoutui(erpId);
                } else if (youtuiId) {
                    // 查看优推房源
                    window.open(`https://www.youtui360.com/house/${youtuiId}`, '_blank');
                } else {
                    viewCommunityContent(communityName, erpId);
                }
            });
        });

        cardsContainer.appendChild(card);
    });
}

/**
 * 为指定园区生成营销内容
 * @param {string} communityName - 园区名称
 */
async function generateCommunityContent(communityName) {
    try {
        // 禁用按钮并显示加载状态
        const buttons = document.querySelectorAll(`.action-btn[data-community="${communityName}"]`);
        buttons.forEach(btn => {
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
        });

        // 发送请求生成内容
        const response = await fetch('/excel/generate-content', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                community_name: communityName
            })
        });

        const data = await response.json();

        // 处理数据检查警告
        if (data.warning_type === 'no_data') {
            // 没有任何数据，显示错误信息并阻止生成
            showMessage(data.message, 'error');
            buttons.forEach(btn => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-magic"></i> 生成内容';
            });
            return;
        } else if (data.warning_type === 'partial_data') {
            // 部分数据缺失，显示警告并询问用户是否继续
            const confirmed = confirm(`⚠️ 数据不完整警告\n\n${data.warning_message}\n\n是否仍要继续生成内容？`);
            if (!confirmed) {
                // 用户取消，恢复按钮状态
                buttons.forEach(btn => {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-magic"></i> 生成内容';
                });
                return;
            }
            // 用户确认继续，调用强制生成接口
            await forceGenerateCommunityContent(communityName);
            return;
        }

        if (data.success) {
            showMessage(`成功提交园区 "${communityName}" 的内容生成请求`, 'success');
        } else {
            showMessage(`提交园区 "${communityName}" 内容生成请求失败: ${data.message}`, 'error');

            // 恢复按钮原始状态
            buttons.forEach(btn => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-magic"></i> 生成内容';
            });
        }
    } catch (error) {
        // 恢复按钮状态
        const buttons = document.querySelectorAll(`.action-btn[data-community="${communityName}"]`);
        buttons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-magic"></i> 生成内容';
        });

        // 显示错误消息
        showMessage(`系统错误: ${error.message || '未知错误'}`, 'error');
    }
}

/**
 * 强制为指定园区生成营销内容（跳过数据检查）
 * @param {string} communityName - 园区名称
 */
async function forceGenerateCommunityContent(communityName) {
    try {
        // 发送强制生成请求
        const response = await fetch('/excel/generate-content-force', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                community_name: communityName
            })
        });

        const data = await response.json();
        if (data.success) {
            showMessage(`成功提交园区 "${communityName}" 的强制内容生成请求`, 'success');
        } else {
            showMessage(`提交园区 "${communityName}" 强制内容生成请求失败: ${data.message}`, 'error');

            // 恢复按钮原始状态
            const buttons = document.querySelectorAll(`.action-btn[data-community="${communityName}"]`);
            buttons.forEach(btn => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-magic"></i> 生成内容';
            });
        }
    } catch (error) {
        // 恢复按钮状态
        const buttons = document.querySelectorAll(`.action-btn[data-community="${communityName}"]`);
        buttons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-magic"></i> 生成内容';
        });

        // 显示错误消息
        showMessage(`系统错误: ${error.message || '未知错误'}`, 'error');
    }
}

/**
 * 查看园区营销内容
 * @param {string} communityName - 园区名称
 * @param {string} erpId - ERP ID
 */
function viewCommunityContent(communityName, erpId) {
    // 创建模态框显示内容
    createContentModal(communityName);
}

/**
 * 创建内容查看模态框
 * @param {string} communityName - 园区名称
 */
function createContentModal(communityName) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-container">
            <div class="modal-header">
                <h3>${communityName} - 营销内容</h3>
                <button class="modal-close-btn"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="loading-spinner" id="contentLoading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>加载内容中...</span>
                </div>
                <div id="contentContainer" style="display: none;"></div>
            </div>
        </div>
    `;

    // 添加到文档
    document.body.appendChild(modal);

    // 添加关闭按钮事件
    const closeBtn = modal.querySelector('.modal-close-btn');
    closeBtn.addEventListener('click', function() {
        modal.remove();
    });

    // 点击背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });

    // 直接调用API获取园区数据
    fetch(`/excel/factory-detail?community_name=${encodeURIComponent(communityName)}`)
        .then(response => response.json())
        .then(result => {
            // 隐藏加载状态
            document.getElementById('contentLoading').style.display = 'none';

            // 显示内容容器
            const contentContainer = document.getElementById('contentContainer');
            contentContainer.style.display = 'block';

            // 填充内容
            if (result.success && result.data) {
                const data = result.data;

                contentContainer.innerHTML = `
                    <div class="content-section">
                        <h4><i class="fas fa-tag icon-blue"></i> 标题</h4>
                        <div class="content-box">${data.topic || '无'}</div>
                    </div>

                    <div class="content-section">
                        <h4><i class="fas fa-file-alt icon-green"></i> 详细描述</h4>
                        <div class="content-box">${formatContent(data.content) || '无'}</div>
                    </div>

                    <div class="content-section">
                        <h4><i class="fas fa-info-circle icon-orange"></i> 基本信息</h4>
                        <div class="content-box">
                            <p><strong>园区名称:</strong> ${data.community || '-'}</p>
                            <p><strong>城市:</strong> ${data.city || '-'}</p>
                            <p><strong>区域:</strong> ${data.zone || '-'}</p>
                            <p><strong>地址:</strong> ${data.address || '-'}</p>
                            <p><strong>面积:</strong> ${data.square ? data.square + '㎡' : '-'}</p>
                            <p><strong>租金:</strong> ${data.total || '-'} ${data.rentunitdaily || ''}</p>
                        </div>
                    </div>
                `;
            } else {
                contentContainer.innerHTML = `
                    <div class="empty-data" style="padding: 20px;">
                        <i class="fas fa-exclamation-circle" style="color: #ff4d4f; font-size: 30px;"></i>
                        <h4>无法加载内容</h4>
                        <p>${result ? result.message : '未找到数据'}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            // 隐藏加载状态
            document.getElementById('contentLoading').style.display = 'none';

            // 显示错误信息
            const contentContainer = document.getElementById('contentContainer');
            contentContainer.style.display = 'block';
            contentContainer.innerHTML = `
                <div class="empty-data" style="padding: 20px;">
                    <i class="fas fa-exclamation-circle" style="color: #ff4d4f; font-size: 30px;"></i>
                    <h4>加载失败</h4>
                    <p>发生错误: ${error.message || '未知错误'}</p>
                </div>
            `;
        });
}

/**
 * 加载完整的房源数据
 * @param {string} erpId - ERP ID
 * @returns {Promise} - 房源数据
 */
async function loadFullPropertyData(erpId) {
    try {
        // 查找该ID的房源
        const property = allFactoryData.find(item => item.erp_id === erpId);

        if (!property || !property.community) {
            return {
                success: false,
                message: "未找到该房源数据或园区名称"
            };
        }

        // 使用园区名称调用新的API接口
        const response = await fetch(`/excel/factory-detail?community_name=${encodeURIComponent(property.community)}`);
        const result = await response.json();

        if (result.success && result.data) {
            return {
                success: true,
                message: "获取园区数据成功",
                data: result.data
            };
        } else {
            return {
                success: false,
                message: result.message || "未找到园区数据"
            };
        }
    } catch (error) {
        return {
            success: false,
            message: `获取园区数据失败: ${error.message || '未知错误'}`
        };
    }
}

/**
 * 格式化内容文本（保留换行）
 * @param {string} text - 原始文本
 * @returns {string} - 格式化后的HTML
 */
function formatContent(text) {
    if (!text) return '';
    return text.replace(/\n/g, '<br>');
}

/**
 * 发布厂房数据到优推平台
 * @param {string} erpId - 要发布的厂房的ERP ID
 */
async function publishToYoutui(erpId) {
    try {
        // 显示确认对话框
        if (!confirm('确定要发布该厂房数据到优推平台吗？')) {
            return;
        }

        // 找到并禁用发布按钮
        const publishBtns = document.querySelectorAll(`.action-btn.publish-btn[data-erp-id="${erpId}"]`);
        publishBtns.forEach(btn => {
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发布中...';
        });

        // 发送发布请求
        const response = await fetch('/excel/publish-unpublished', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                erp_ids: [erpId]
            })
        });

        const data = await response.json();
        if (data.success && data.success_count > 0) {
            showMessage(`成功发布到优推平台，优推房源ID: ${data.success_ids[0]}`, 'success');

            // 刷新数据
            const currentPage = paginationInfo && paginationInfo.page ? paginationInfo.page : 1;
            loadImportedFactoryData(currentPage);
        } else {
            // 恢复按钮状态
            publishBtns.forEach(btn => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-paper-plane"></i> 发布到优推';
            });

            // 显示错误消息
            let errorMessage = data.message || '未知错误';

            // 检查是否有关于图库的错误
            if (data.failed_items && data.failed_items.length > 0) {
                const noImageError = data.failed_items.find(item =>
                    item.reason && item.reason.includes('该园区还未录入图库'));

                if (noImageError) {
                    errorMessage = noImageError.reason;
                }
            }

            showMessage(`发布失败: ${errorMessage}`, 'error');
        }
    } catch (error) {
        // 恢复按钮状态
        const publishBtns = document.querySelectorAll(`.action-btn.publish-btn[data-erp-id="${erpId}"]`);
        publishBtns.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-paper-plane"></i> 发布到优推';
        });

        // 显示错误消息
        showMessage(`系统错误: ${error.message || '未知错误'}`, 'error');
    }
}

/**
 * 为所有未生成内容的园区生成营销内容
 */
async function generateAllCommunityContent() {
    if (!communitiesData) {
        showMessage('没有可用的园区数据', 'info');
        return;
    }

    // 获取所有需要生成内容的园区
    const pendingCommunities = [];
    for (const community in communitiesData) {
        if (communitiesData[community].without_content > 0) {
            pendingCommunities.push(community);
        }
    }

    if (pendingCommunities.length === 0) {
        showMessage('没有需要生成营销内容的园区', 'info');
        return;
    }

    // 显示确认对话框
    if (!confirm(`确定要为 ${pendingCommunities.length} 个园区生成营销内容吗？这可能需要一些时间。`)) {
        return;
    }

    // 禁用生成按钮
    const generateAllBtn = document.getElementById('generateAllBtn');
    if (generateAllBtn) generateAllBtn.disabled = true;

    // 依次提交每个园区的生成请求
    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < pendingCommunities.length; i++) {
        const community = pendingCommunities[i];

        try {
            // 发送请求生成内容
            const response = await fetch('/excel/generate-content', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    community_name: community
                })
            });

            const data = await response.json();

            // 处理数据检查警告
            if (data.warning_type === 'no_data') {
                // 没有任何数据，跳过该园区
                failCount++;
            } else if (data.warning_type === 'partial_data') {
                // 部分数据缺失，自动使用强制生成
                try {
                    const forceResponse = await fetch('/excel/generate-content-force', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            community_name: community
                        })
                    });
                    const forceData = await forceResponse.json();
                    if (forceData.success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (forceError) {
                    failCount++;
                }
            } else if (data.success) {
                successCount++;
            } else {
                failCount++;
            }
        } catch (error) {
            failCount++;
        }

        // 短暂延迟，避免API限制
        await new Promise(resolve => setTimeout(resolve, 200));
    }

    // 显示结果消息
    showMessage(`已提交 ${pendingCommunities.length} 个园区的内容生成请求，成功: ${successCount}, 失败: ${failCount}`,
        successCount > 0 ? 'success' : 'warning');
}

/**
 * 显示消息提示
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型：success, error, info, warning
 */
function showMessage(message, type = 'info') {
    // 使用factory_upload.js中的消息提示功能
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
    } else {
        // 使用工厂上传页面中的showMessage函数
        if (typeof showMessage === 'function' && this !== window) {
            showMessage(message, type);
        } else {
            // 创建一个消息容器
            const messageContainer = document.createElement('div');
            messageContainer.className = `message-toast message-${type}`;
            messageContainer.innerHTML = `
                <div class="message-icon">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-times-circle' : type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'}"></i>
                </div>
                <div class="message-content">${message}</div>
            `;

            // 添加到文档中
            document.body.appendChild(messageContainer);

            // 添加动画效果
            setTimeout(() => {
                messageContainer.classList.add('show');
            }, 10);

            // 设置定时器移除消息
            setTimeout(() => {
                messageContainer.classList.remove('show');
                messageContainer.classList.add('hide');

                // 动画结束后移除元素
                setTimeout(() => {
                    document.body.removeChild(messageContainer);
                }, 300);
            }, 3000);
        }
    }
}

/**
 * 连接到SSE事件流
 */
function connectToEventSource() {
    // 如果已经有SSE连接，先断开
    disconnectEventSource();

    // 创建一个新的SSE连接
    eventSource = new EventSource('/tasks/events');

    // 监听连接建立事件
    eventSource.addEventListener('connected', function(event) {
        // 连接已建立，无需特殊处理
    });

    // 监听消息事件
    eventSource.addEventListener('message', function(event) {
        try {
            const eventData = JSON.parse(event.data);

            if (eventData.type === 'task_update') {
                // 更新任务状态UI - 适配新的数据格式
                updateTaskStatusUI(eventData.data);

                // 检查是否有任务刚完成，需要刷新数据
                const hasJustCompleted = Object.values(eventData.data).some(task =>
                    (task.status === 2 || task.status === 3) &&
                    task.completed_at && (Date.now() / 1000 - task.completed_at) < 5  // 5秒内完成的任务
                );

                if (hasJustCompleted) {
                    // 任务刚完成，刷新数据
                    const currentPage = paginationInfo && paginationInfo.page ? paginationInfo.page : 1;
                    loadImportedFactoryData(currentPage);
                }
            }
        } catch (error) {
            // 忽略SSE消息处理错误
        }
    });

    // 监听ping事件（保持连接）
    eventSource.addEventListener('ping', function(event) {
        // 心跳事件，保持连接
    });

    // 添加错误事件监听器
    eventSource.onerror = function(event) {
        // 如果连接断开，尝试重连
        setTimeout(connectToEventSource, 5000);
    };
}

/**
 * 断开SSE连接
 */
function disconnectEventSource() {
    if (eventSource) {
        eventSource.close();
        eventSource = null;
    }
}

/**
 * 更新任务状态UI - 适配新的任务数据格式
 * @param {Object} tasks - 任务状态数据，格式为 {园区名称: {status, progress, ...}}
 */
function updateTaskStatusUI(tasks) {
    // 只有当已加载房源数据时才更新UI
    if (!allFactoryData || allFactoryData.length === 0) {
        return;
    }

    // 查找所有生成按钮并更新状态
    for (const item of allFactoryData) {
        const communityName = item.community;
        if (!communityName) continue;

        // 直接使用原始园区名称获取任务信息（后端已处理名称映射）
        const taskInfo = tasks[communityName];
        const buttons = document.querySelectorAll(`.action-btn[data-community="${communityName}"]`);

        // 如果该园区有任务状态且有按钮，更新UI
        if (taskInfo && buttons.length > 0) {
            const status = taskInfo.status;
            const progress = taskInfo.progress || 0;

            buttons.forEach(btn => {
                // 更新按钮状态和文本
                if (status === 1) { // 进行中
                    btn.disabled = true;
                    btn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 生成中 ${progress}%`;
                } else if (status === 0) { // 等待中
                    btn.disabled = true;
                    btn.innerHTML = `<i class="fas fa-hourglass-half"></i> 等待中...`;
                } else if (status === 2) { // 已完成
                    // 任务完成，将按钮改为查看按钮
                    btn.disabled = false;
                    btn.classList.remove('generate-btn');
                    btn.classList.add('view-btn');
                    btn.innerHTML = `<i class="fas fa-eye"></i> 查看`;
                } else if (status === 3) { // 失败
                    // 任务失败，允许重试
                    btn.disabled = false;
                    btn.innerHTML = `<i class="fas fa-exclamation-circle"></i> 重试`;
                    if (taskInfo.error_message) {
                        btn.setAttribute('title', taskInfo.error_message);
                    }
                }
            });

            // 更新状态标签
            const statusBadges = document.querySelectorAll(`.community-card[data-community="${communityName}"] .status-badge`);
            statusBadges.forEach(badge => {
                if (status === 1) { // 进行中
                    badge.className = 'status-badge status-progress';
                    badge.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 进行中 ${progress}%`;
                } else if (status === 0) { // 等待中
                    badge.className = 'status-badge status-pending';
                    badge.innerHTML = `<i class="fas fa-hourglass-half"></i> 等待中`;
                } else if (status === 2) { // 已完成
                    badge.className = 'status-badge status-complete';
                    badge.innerHTML = `<i class="fas fa-check-circle"></i> 已完成`;
                } else if (status === 3) { // 失败
                    badge.className = 'status-badge status-error';
                    badge.innerHTML = `<i class="fas fa-exclamation-circle"></i> 失败`;
                }
            });
        }
    }

    // 检查是否有任务正在进行中，如果有则禁用"生成所有"按钮
    const hasRunningTasks = Object.values(tasks).some(task => task.status === 0 || task.status === 1);
    const generateAllBtn = document.getElementById('generateAllBtn');
    if (generateAllBtn) {
        generateAllBtn.disabled = hasRunningTasks;
    }
}