/**
 * 加载效果工具函数
 * 包含全局加载、进度条、按钮加载状态等功能
 */

// 全局加载指示器控制
function showGlobalLoading(message) {
    if (window.loadingManager) {
        window.loadingManager.showGlobalLoading(message);
    }
}

function hideGlobalLoading() {
    if (window.loadingManager) {
        window.loadingManager.hideGlobalLoading();
    }
}

// 顶部进度条控制
function startProgress() {
    const progressBar = document.getElementById('topProgressBar');
    progressBar.style.width = '0%';
    progressBar.style.opacity = '1';
    
    setTimeout(() => {
        progressBar.style.width = '70%';
    }, 100);
}

function completeProgress() {
    const progressBar = document.getElementById('topProgressBar');
    progressBar.style.width = '100%';
    
    setTimeout(() => {
        progressBar.style.opacity = '0';
        setTimeout(() => {
            progressBar.style.width = '0%';
        }, 300);
    }, 500);
}

// 按钮加载状态控制
function setButtonLoading(button, isLoading) {
    if (!button) return;
    
    if (isLoading) {
        button.setAttribute('disabled', 'disabled');
        button.classList.add('loading');
        button._originalText = button.textContent;
        button.textContent = '';
    } else {
        button.removeAttribute('disabled');
        button.classList.remove('loading');
        if (button._originalText) {
            button.textContent = button._originalText;
            delete button._originalText;
        }
    }
}

// 显示骨架屏
function showSkeletonRows() {
    document.querySelectorAll('.skeleton-row').forEach(row => {
        row.style.display = 'table-row';
    });
}

// 隐藏骨架屏
function hideSkeletonRows() {
    document.querySelectorAll('.skeleton-row').forEach(row => {
        row.style.display = 'none';
    });
}

// 显示内容加载中状态
function showContentLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'content-loading';
        loadingDiv.id = containerId + 'Loading';
        loadingDiv.innerHTML = `
            <div class="loading-spinner-small"></div>
            <span>加载中...</span>
        `;
        container.innerHTML = '';
        container.appendChild(loadingDiv);
    }
}

// 隐藏内容加载状态
function hideContentLoading(containerId) {
    const loadingDiv = document.getElementById(containerId + 'Loading');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

// 添加消息提示函数
function showToast(type, message, duration = 3000) {
    // 确保容器存在
    let container = document.getElementById('toastContainer');
    if (!container) {
        container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container';
        document.body.appendChild(container);
    }
    
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    
    // 添加到容器
    container.appendChild(toast);
    
    // 触发重排以启动动画
    void toast.offsetHeight;
    
    // 显示提示
    toast.classList.add('show');
    
    // 设置定时器移除提示
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            container.removeChild(toast);
        }, 300);
    }, duration);
}

// 显示消息的辅助函数
function showMessage(message, type = 'info') {
    showToast(type, message);
}

// 显示模态框
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        
        // 触发重排后添加显示类以启动动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }
}

// 关闭模态框函数
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 动态添加淡入行效果
function addFadeInEffect(element, index = 0) {
    if (element) {
        element.classList.add('fade-in-row');
        element.style.animationDelay = `${index * 50}ms`;
    }
    return element;
}

// 创建表格骨架屏行
function createSkeletonRow(colCount = 4) {
    const tr = document.createElement('tr');
    tr.className = 'skeleton-row';
    
    // 创建普通单元格
    for (let i = 0; i < colCount - 1; i++) {
        const td = document.createElement('td');
        td.innerHTML = '<div class="skeleton-cell"></div>';
        tr.appendChild(td);
    }
    
    // 创建操作按钮单元格
    const tdAction = document.createElement('td');
    tdAction.innerHTML = '<div class="skeleton-button"></div>';
    tr.appendChild(tdAction);
    
    return tr;
}

// 为页面添加全局加载组件
function addLoadingComponents() {
    // 检查是否已存在
    if (!document.getElementById('topProgressBar')) {
        // 添加顶部进度条
        const progressBar = document.createElement('div');
        progressBar.id = 'topProgressBar';
        progressBar.className = 'progress-bar';
        document.body.prepend(progressBar);
    }
    
    if (!document.getElementById('globalLoadingIndicator')) {
        // 添加全局加载指示器
        const loadingIndicator = document.createElement('div');
        loadingIndicator.id = 'globalLoadingIndicator';
        loadingIndicator.className = 'global-loading-indicator';
        loadingIndicator.innerHTML = `
            <div class="loading-spinner"></div>
            <span>加载中...</span>
        `;
        
        // 添加到container后
        const container = document.querySelector('.container');
        if (container) {
            container.appendChild(loadingIndicator);
        } else {
            document.body.appendChild(loadingIndicator);
        }
    }
    
    if (!document.getElementById('toastContainer')) {
        // 添加消息提示容器
        const toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }
}

// 加载状态管理类
class LoadingManager {
    constructor() {
        this.globalLoadingElement = null;
        this.createGlobalLoadingElement();
    }

    // 创建全局加载元素
    createGlobalLoadingElement() {
        this.globalLoadingElement = document.createElement('div');
        this.globalLoadingElement.className = 'global-loading';
        this.globalLoadingElement.style.display = 'none';
        this.globalLoadingElement.innerHTML = `
            <div class="global-loading-content">
                <div class="global-loading-spinner"></div>
                <div class="global-loading-text">加载中...</div>
            </div>
        `;
        document.body.appendChild(this.globalLoadingElement);
    }

    // 显示全局加载
    showGlobalLoading(message = '加载中...') {
        this.globalLoadingElement.querySelector('.global-loading-text').textContent = message;
        this.globalLoadingElement.style.display = 'flex';
    }

    // 隐藏全局加载
    hideGlobalLoading() {
        this.globalLoadingElement.style.display = 'none';
    }

    // 设置按钮加载状态
    setButtonLoading(button, isLoading) {
        if (isLoading) {
            button.classList.add('button-loading');
            button.disabled = true;
        } else {
            button.classList.remove('button-loading');
            button.disabled = false;
        }
    }

    // 显示骨架屏
    showTableSkeleton(tableBody, rowCount = 5) {
        const skeletonHTML = Array(rowCount).fill(`
            <tr class="skeleton-row">
                <td><div class="skeleton-text"></div></td>
                <td><div class="skeleton-text"></div></td>
                <td><div class="skeleton-text"></div></td>
                <td><div class="skeleton-text"></div></td>
                <td><div class="skeleton-text"></div></td>
            </tr>
        `).join('');
        tableBody.innerHTML = skeletonHTML;
    }

    // 异步操作包装器
    async wrapAsyncOperation(operation, options = {}) {
        const {
            button = null,
            loadingMessage = '加载中...',
            showGlobal = true
        } = options;

        try {
            if (button) this.setButtonLoading(button, true);
            if (showGlobal) this.showGlobalLoading(loadingMessage);

            const result = await operation();
            return result;
        } finally {
            if (button) this.setButtonLoading(button, false);
            if (showGlobal) this.hideGlobalLoading();
        }
    }
}

// 创建全局实例
window.loadingManager = new LoadingManager(); 