// 添加消息提示函数
function showToast(type, message, duration = 3000) {
    // 确保容器存在
    let container = document.getElementById('toastContainer');
    if (!container) {
        container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container';
        document.body.appendChild(container);
    }

    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;

    // 添加到容器
    container.appendChild(toast);

    // 触发重排以启动动画
    void toast.offsetHeight;

    // 显示提示
    toast.classList.add('show');

    // 设置定时器移除提示
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            container.removeChild(toast);
        }, 300);
    }, duration);
}

// 显示消息的辅助函数
function showMessage(message, type = 'info') {
    showToast(type, message);
}

// 全局加载指示器控制
function showGlobalLoading() {
    document.getElementById('globalLoadingIndicator').classList.add('active');
}

function hideGlobalLoading() {
    document.getElementById('globalLoadingIndicator').classList.remove('active');
}

// 顶部进度条控制
function startProgress() {
    const progressBar = document.getElementById('topProgressBar');
    progressBar.style.width = '0%';
    progressBar.style.opacity = '1';

    setTimeout(() => {
        progressBar.style.width = '70%';
    }, 100);
}

function completeProgress() {
    const progressBar = document.getElementById('topProgressBar');
    progressBar.style.width = '100%';

    setTimeout(() => {
        progressBar.style.opacity = '0';
        setTimeout(() => {
            progressBar.style.width = '0%';
        }, 300);
    }, 500);
}

function setButtonLoading(button, isLoading) {
    if (isLoading) {
        button.classList.add('loading');
        button.disabled = true;
    } else {
        button.classList.remove('loading');
        button.disabled = false;
    }
}

function setPushButtonLoading(button, isLoading) {
    if (isLoading) {
        button.classList.add('push-btn-loading');
        button.disabled = true;
    } else {
        button.classList.remove('push-btn-loading');
        button.disabled = false;
    }
}

function setConfirmButtonLoading(button, isLoading) {
    if (isLoading) {
        button.classList.add('confirm-btn-loading');
        button.disabled = true;
    } else {
        button.classList.remove('confirm-btn-loading');
        button.disabled = false;
    }
}

function setRemoveButtonLoading(button, isLoading) {
    if (isLoading) {
        button.classList.add('remove-btn-loading');
        button.disabled = true;
    } else {
        button.classList.remove('remove-btn-loading');
        button.disabled = false;
    }
}

let isLoading = false;
let currentPage = 1;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查用户是否选择不再显示提醒
    if (!shouldShowWarning()) {
        // 如果用户选择不再显示，直接启用所有交互并初始化页面
        enableAllInteractions();
        initializePage();
    } else {
        // 显示警告弹窗
        showWarningModal();
    }
});

// 检查是否应该显示警告弹窗
function shouldShowWarning() {
    try {
        const dontShowAgain = localStorage.getItem('publishedDataWarningDismissed');
        return dontShowAgain !== 'true';
    } catch (error) {
        console.warn('无法访问localStorage，将显示警告弹窗:', error);
        return true;
    }
}

// 保存用户选择不再显示警告
function saveDontShowAgainPreference() {
    try {
        localStorage.setItem('publishedDataWarningDismissed', 'true');
        console.log('已保存用户选择：不再显示警告弹窗');
    } catch (error) {
        console.warn('无法保存用户选择到localStorage:', error);
    }
}

// 重置警告显示状态（用于开发测试）
function resetWarningPreference() {
    try {
        localStorage.removeItem('publishedDataWarningDismissed');
        console.log('已重置警告显示状态，下次访问将重新显示警告');
    } catch (error) {
        console.warn('无法重置警告状态:', error);
    }
}

// 显示警告弹窗
function showWarningModal() {
    const modal = document.getElementById('warningModal');
    modal.style.display = 'block';
    // 触发重排以启动动画
    void modal.offsetHeight;
    modal.classList.add('show');

    // 禁用页面上的所有按钮和输入框
    disableAllInteractions();
}

// 确认警告
function confirmWarning() {
    // 检查用户是否选择不再显示
    const dontShowAgainCheckbox = document.getElementById('dontShowAgainCheckbox');
    if (dontShowAgainCheckbox && dontShowAgainCheckbox.checked) {
        saveDontShowAgainPreference();
        console.log('用户选择不再显示警告弹窗');
    }

    const modal = document.getElementById('warningModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
        // 启用页面上的所有按钮和输入框
        enableAllInteractions();
        // 初始化页面
        initializePage();
    }, 300);
}

// 禁用所有交互元素
function disableAllInteractions() {
    document.querySelectorAll('button, input, select').forEach(element => {
        element.disabled = true;
        element.style.opacity = '0.5';
        element.style.cursor = 'not-allowed';
    });
    // 确保警告确认按钮和复选框可用
    const warningBtn = document.querySelector('.warning-confirm-btn');
    const warningCheckbox = document.getElementById('dontShowAgainCheckbox');

    if (warningBtn) {
        warningBtn.disabled = false;
        warningBtn.style.opacity = '1';
        warningBtn.style.cursor = 'pointer';
    }

    if (warningCheckbox) {
        warningCheckbox.disabled = false;
        warningCheckbox.style.opacity = '1';
        warningCheckbox.style.cursor = 'pointer';
    }
}

// 启用所有交互元素
function enableAllInteractions() {
    document.querySelectorAll('button, input, select').forEach(element => {
        element.disabled = false;
        element.style.opacity = '1';
        element.style.cursor = 'pointer';
    });
}

async function initializePage() {
    try {
        // 显示全局加载
        showGlobalLoading();
        startProgress();

        // 初始化城市下拉列表
        await initializeCitySelect();

        // 加载初始数据
        await loadPublishedData();

        // 隐藏全局加载
        hideGlobalLoading();
        completeProgress();

        // 绑定搜索按钮事件
        document.getElementById('searchButton').addEventListener('click', async function() {
            const button = this;
            setButtonLoading(button, true);
            await handleSearch();
            setButtonLoading(button, false);
        });

        // 绑定重置按钮事件
        document.getElementById('resetButton').addEventListener('click', async function() {
            const button = this;
            setButtonLoading(button, true);
            document.getElementById('citySelect').value = '';
            document.getElementById('keyword').value = '';
            currentPage = 1;
            await loadPublishedData();
            setButtonLoading(button, false);
        });

        // 绑定刷新按钮事件
        document.getElementById('refreshDataBtn').addEventListener('click', async function() {
            const button = this;
            setButtonLoading(button, true);
            await loadPublishedData(currentPage);
            setButtonLoading(button, false);

            // 添加按钮图标旋转效果
            const icon = button.querySelector('.refresh-icon');
            icon.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                icon.style.transform = '';
            }, 500);

            showMessage('数据已刷新', 'success');
        });

        // 绑定输入框、下拉框的事件
        document.getElementById('keyword').addEventListener('input', debounce(handleSearch, 500));
        document.getElementById('citySelect').addEventListener('change', handleCityChange);

        // 绑定分页按钮事件
        document.getElementById('prevPage').addEventListener('click', async function() {
            if (currentPage > 1) {
                const button = this;
                setButtonLoading(button, true);
                currentPage--;
                await loadPublishedData(currentPage);
                setButtonLoading(button, false);
            }
        });

        document.getElementById('nextPage').addEventListener('click', async function() {
            const totalPages = parseInt(document.getElementById('totalPages').textContent);
            if (currentPage < totalPages) {
                const button = this;
                setButtonLoading(button, true);
                currentPage++;
                await loadPublishedData(currentPage);
                setButtonLoading(button, false);
            }
        });
    } catch (error) {
        console.error('页面初始化失败：', error);
        showMessage('页面初始化失败，请刷新重试', 'error');
        hideGlobalLoading();
    }
}

// 初始化城市下拉列表
async function initializeCitySelect() {
    try {
        const response = await apiRequest('/files/cities', 'GET');
        if (response.success && response.cities) {
            const citySelect = document.getElementById('citySelect');
            citySelect.innerHTML = '<option value="">全部</option>';

            response.cities.forEach(city => {
                const option = document.createElement('option');
                option.value = city.code;
                option.textContent = city.name;
                citySelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('初始化城市列表失败：', error);
        showMessage('初始化城市列表失败，请刷新重试', 'error');
    }
}

// 加载已发布数据
async function loadPublishedData(page = currentPage) {
    if (isLoading) return;

    try {
        isLoading = true;
        startProgress();
        document.getElementById('loadingIndicator').style.display = 'block';

        // 显示骨架屏
        document.querySelectorAll('.skeleton-card').forEach(card => {
            card.style.display = 'block';
        });

        const params = {
            city: document.getElementById('citySelect').value,
            community: document.getElementById('keyword').value,
            page: page,
            page_size: 10
        };

        const response = await apiRequest('/factory/list', 'POST', params);

        if (response && response.data) {
            document.querySelectorAll('.skeleton-card').forEach(card => {
                card.style.display = 'none';
            });

            updateCardsData(response.data);
            updatePagination({
                current_page: response.current_page || page,
                total_pages: response.total_pages || 1,
                total: response.total || 0
            });
            currentPage = response.current_page || page;
        } else {
            showMessage('获取数据失败，响应格式不正确', 'error');
        }
    } catch (error) {
        showMessage('加载数据失败，请重试', 'error');
    } finally {
        isLoading = false;
        document.getElementById('loadingIndicator').style.display = 'none';
        completeProgress();

        // 隐藏骨架屏
        document.querySelectorAll('.skeleton-card').forEach(card => {
            card.style.display = 'none';
        });
    }
}

function updateCardsData(data) {
    const cardsContainer = document.getElementById('publishedDataCards');
    const emptyState = document.getElementById('emptyState');

    const skeletonCards = Array.from(cardsContainer.querySelectorAll('.skeleton-card'));
    cardsContainer.innerHTML = '';
    skeletonCards.forEach(card => {
        card.style.display = 'none';
        cardsContainer.appendChild(card);
    });

    if (!Array.isArray(data)) {
        console.error('数据格式不正确，期望数组，实际收到：', data);
        showMessage('数据格式不正确', 'error');
        return;
    }

    // 如果没有数据，显示空状态
    if (data.length === 0) {
        emptyState.style.display = 'block';
        console.log('没有数据，显示空状态');
        return;
    } else {
        emptyState.style.display = 'none';
    }

    // 创建并延迟显示每个卡片，以产生逐个淡入的效果
    data.forEach((item, index) => {
        const card = document.createElement('div');
        card.classList.add('data-card', 'fade-in-row');
        card.classList.add(item.type === '3' || item.type === 3 ? 'rent-type' : 'sale-type');
        // 设置动画延迟，每个卡片增加100ms延迟
        card.style.animationDelay = `${index * 100}ms`;

        const typeText = getTypeDisplayName(item.type);
        const badgeClass = (item.type === '3' || item.type === 3) ? 'badge-rent' : 'badge-sale';
        const cityName = getCityDisplayName(item.city);

        card.innerHTML = `
            <!-- 简化的卡片背景 -->
            <div class="card-bg-animation">
                <div class="bg-shape shape-1"></div>
                <div class="bg-gradient"></div>
            </div>

            <!-- 卡片内容 -->
            <div class="card-content">
                <div class="card-header">
                    <h3 class="card-title">${item.community || '未知园区'}</h3>
                    <span class="card-badge ${badgeClass}">${typeText}</span>
                </div>

                <div class="card-body">
                    <div class="card-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span class="location-text">${cityName}</span>
                    </div>

                    <div class="card-info-grid">
                        <div class="info-item">
                            <span class="info-label">
                                <i class="fas fa-barcode"></i>
                                ERP ID
                            </span>
                            <span class="info-value">${item.erp_id || '-'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">
                                <i class="fas fa-external-link-alt"></i>
                                优推 ID
                            </span>
                            <span class="info-value">${item.youtui_id || '-'}</span>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="card-meta">
                        <i class="fas fa-clock"></i>
                        <span>刚刚更新</span>
                    </div>
                    <div class="card-actions">
                        <button class="card-action-btn btn-push" data-id="${item.id}">
                            <i class="fas fa-paper-plane"></i>
                            推送
                        </button>
                    </div>
                </div>
            </div>
        `;
        cardsContainer.appendChild(card);
    });

    document.querySelectorAll('.btn-push').forEach(button => {
        const factoryId = button.dataset.id;
        button.onclick = function() {
            handlePushClick(factoryId);
        };
    });

    restorePushTasks();
}

// 更新分页信息
function updatePagination(data) {
    document.getElementById('currentPage').textContent = data.current_page;
    document.getElementById('totalPages').textContent = data.total_pages;
    document.getElementById('totalRecords').textContent = data.total;

    document.getElementById('prevPage').disabled = data.current_page <= 1;
    document.getElementById('nextPage').disabled = data.current_page >= data.total_pages;
}

// 获取租售类型显示名称
function getTypeDisplayName(type) {
    if (type === '3' || type === 3) {
        return '出租';
    } else if (type === '6' || type === 6) {
        return '出售';
    }
    return type || '-';
}

// 获取城市显示名称
function getCityDisplayName(cityCode) {
    const citySelect = document.getElementById('citySelect');
    const option = Array.from(citySelect.options).find(opt => opt.value === cityCode);
    return option ? option.textContent : cityCode;
}

// 存储当前选中的厂房ID和网站列表数据
let currentFactoryId = null;
let availableWebsites = null;

// 处理推送按钮点击
async function handlePushClick(factoryId) {
    try {
        startProgress();

        currentFactoryId = factoryId;

        // 查找对应的推送按钮元素
        const pushButton = document.querySelector(`button[data-id="${factoryId}"]`);
        if (!pushButton) {
            showToast('error', '找不到对应的推送按钮');
            completeProgress();
            return;
        }

        // 获取当前卡片的租售类型
        const card = pushButton.closest('.data-card');
        const typeBadge = card.querySelector('.card-badge');
        const type = typeBadge.textContent;

        const confirmButton = document.getElementById('confirmPushBtn');
        setConfirmButtonLoading(confirmButton, false);

        const modal = document.getElementById('websiteModal');
        modal.style.display = 'block';

        // 触发重排后添加显示类以启动动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);

        // 显示网站列表加载状态
        document.getElementById('websiteListLoading').style.display = 'flex';
        document.getElementById('websiteList').style.display = 'none';

        // 获取可用网站列表
        const response = await apiRequest(`/factory/get-available-websites/${factoryId}`, 'POST');

        // 恢复按钮状态
        if (pushButton) {
            setPushButtonLoading(pushButton, false);
        }
        completeProgress();

        // 隐藏网站列表加载状态
        document.getElementById('websiteListLoading').style.display = 'none';
        document.getElementById('websiteList').style.display = 'block';

        if (response.success) {
            // 保存网站列表数据
            availableWebsites = response.websites;
            const websiteNames = response.website_names;
            const accountNames = response.account_names || {}; // 添加账号姓名映射

            // 清空并重新填充网站列表
            const websiteList = document.getElementById('websiteList');
            websiteList.innerHTML = '';

            // 为每个网站创建分组
            availableWebsites.forEach((website, index) => {
                const webId = website.webID;
                const webName = websiteNames[webId] || `网站${webId}`;
                const userNameArr = website.userNameArr || {};
                const actArr = website.actArr || {};

                // 创建网站分组容器
                const websiteGroup = document.createElement('div');
                websiteGroup.className = 'website-group';
                websiteGroup.style.animationDelay = `${index * 50}ms`;

                // 创建网站标题
                const websiteTitle = document.createElement('div');
                websiteTitle.className = 'website-title';
                websiteTitle.innerHTML = `<h3>${webName}</h3>`;
                websiteGroup.appendChild(websiteTitle);

                // 创建账号列表容器
                const accountList = document.createElement('div');
                accountList.className = 'account-list';

                // 添加每个账号
                Object.entries(userNameArr).forEach(([accountId, accountInfo]) => {
                    const username = accountInfo[0];
                    const status = accountInfo[2];
                    const isAvailable = status === "可用";
                    const accountName = accountNames[accountId] || '未知'; // 获取账号对应的姓名

                    const accountDiv = document.createElement('div');
                    accountDiv.className = `account-item ${isAvailable ? '' : 'disabled'}`;

                    // 构建账号选择器，添加姓名显示
                    accountDiv.innerHTML = `
                        <input type="checkbox" name="website"
                                id="account${accountId}"
                                value="${webId}"
                                data-account-id="${accountId}"
                                ${isAvailable ? '' : 'disabled'}>
                        <label for="account${accountId}">
                            <div class="account-info">
                                <div class="account-user-info">
                                    <span class="account-username">${username}</span>
                                    <span class="account-realname">${accountName}</span>
                                </div>
                                <span class="account-status ${isAvailable ? 'status-active' : 'status-inactive'}">${status}</span>
                            </div>
                            <div class="account-actions">
                                ${Object.entries(actArr).map(([code, name]) =>
                                    `<span class="action-tag ${name.includes('未推广') ? 'disabled' : ''}">${name}</span>`
                                ).join('')}
                            </div>
                        </label>
                    `;

                    // 修改点击事件处理
                    if (isAvailable) {
                        accountDiv.addEventListener('click', function(e) {
                            // 如果点击的是已禁用的action-tag，不触发选择
                            if (e.target.classList.contains('action-tag') && e.target.classList.contains('disabled')) {
                                e.preventDefault();
                                e.stopPropagation();
                                return;
                            }

                            // 如果点击的是正常的action-tag，处理点击事件
                            if (e.target.classList.contains('action-tag') && !e.target.classList.contains('disabled')) {
                                e.preventDefault();
                                e.stopPropagation();
                                // 处理推广按钮点击事件
                                const checkbox = this.querySelector('input[type="checkbox"]');
                                const accountId = checkbox.dataset.accountId;
                                const webId = checkbox.value;

                                // 只有点击的是推广相关的按钮才调用API
                                if (e.target.textContent.includes('推广')) {
                                    handlePromotionClick(webId, accountId);
                                    return;
                                }

                                if (!checkbox.disabled) {
                                    checkbox.checked = !checkbox.checked;
                                    this.classList.toggle('selected');
                                }
                                return;
                            }

                            // 如果点击的是label区域内的其他元素，但不是action-tag
                            if (e.target.closest('.account-actions') && !e.target.classList.contains('action-tag')) {
                                e.preventDefault();
                                e.stopPropagation();
                                return;
                            }

                            // 处理其他区域的点击
                            const checkbox = this.querySelector('input[type="checkbox"]');
                            if (!checkbox.disabled) {
                                checkbox.checked = !checkbox.checked;
                                this.classList.toggle('selected');
                            }
                        });
                    }

                    accountList.appendChild(accountDiv);
                });

                websiteGroup.appendChild(accountList);
                websiteList.appendChild(websiteGroup);
            });

            // 确保确认按钮的状态正确
            const confirmButton = document.getElementById('confirmPushBtn');
            confirmButton.disabled = false;
            confirmButton.style.opacity = '1';
            confirmButton.style.cursor = 'pointer';
        } else {
            showToast('error', `获取网站列表失败: ${response.message}`);
            closeModal();
        }
    } catch (error) {
        console.error('获取网站列表失败:', error);
        showToast('error', '获取网站列表失败，请稍后重试');
        completeProgress();
        closeModal();
    }
}

// 关闭模态框函数
function closeModal() {
    const modal = document.getElementById('websiteModal');
    modal.classList.remove('show');

    const confirmButton = document.getElementById('confirmPushBtn');
    setConfirmButtonLoading(confirmButton, false);

    setTimeout(() => {
        modal.style.display = 'none';
        // 清空网站列表
        document.getElementById('websiteList').innerHTML = '';
        // 重置当前选中的厂房ID
        currentFactoryId = null;
        // 重置可用网站列表
        availableWebsites = null;

        // 确保任何可能打开的帖子列表模态框也被关闭
        closePostsListModal();
    }, 300);
}

// 添加关闭按钮事件监听器
document.addEventListener('DOMContentLoaded', function() {
    document.querySelector('.close').addEventListener('click', closeModal);

    // 添加取消按钮事件监听器
    document.getElementById('cancelPushBtn').addEventListener('click', closeModal);

    // 添加点击模态框外部关闭功能
    document.getElementById('websiteModal').addEventListener('click', function(event) {
        if (event.target === this) {
            closeModal();
        }
    });

    // 修改确认推送按钮的处理逻辑
    document.getElementById('confirmPushBtn').addEventListener('click', async function() {
        const button = this;
        try {

            // 获取所有选中的账号
            const selectedAccounts = Array.from(document.querySelectorAll('.account-item.selected input[type="checkbox"]'))
                .filter(checkbox => !checkbox.disabled);

            if (selectedAccounts.length === 0) {
                showToast('warning', '请至少选择一个要推送的账号');
                return;
            }

            setConfirmButtonLoading(button, true);
            startProgress();

            // 构建每个账号的推送信息
            const pushWebsiteInfos = selectedAccounts.map(selectedAccount => {
                const websiteId = selectedAccount.value;
                const accountId = selectedAccount.dataset.accountId;

                // 从保存的网站列表中找到对应的网站信息
                const websiteInfo = availableWebsites.find(w => w.webID.toString() === websiteId.toString());
                if (!websiteInfo) {
                    throw new Error(`未找到网站ID为 ${websiteId} 的信息`);
                }

                // 获取该账号对应的推广类型的第一个key
                const actKey = Object.keys(websiteInfo.actArr)[0] || "00";

                // 构建新的推送格式
                return {
                    webID: websiteId,
                    actArr: {
                        [accountId]: actKey
                    }
                };
            });

            createSSEConnection();

            await new Promise(resolve => {
                if (window.currentEventSource && window.currentEventSource.readyState === EventSource.OPEN) {
                    resolve();
                } else {
                    window.currentEventSource.addEventListener('open', () => {
                        resolve();
                    }, { once: true });

                    setTimeout(() => {
                        resolve();
                    }, 3000);
                }
            });

            // 依次推送到每个选中的账号
            for (const pushWebsiteInfo of pushWebsiteInfos) {
                const tempTaskId = 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                const progressContainer = createPushStatusContainer(tempTaskId, currentFactoryId);
                console.log(`创建临时进度容器: ${tempTaskId}`);

                // 不再预设步骤1状态，完全依赖后端SSE事件
                console.log('等待后端SSE事件更新进度...');

                const response = await apiRequest(
                    `/factory/push/${currentFactoryId}?website_id=${pushWebsiteInfo.webID}`,
                    'POST',
                    pushWebsiteInfo
                );

                // 模拟进度已移除，无需清除定时器

                // 获取最终的任务ID（优先使用后端返回的真实任务ID）
                const finalTaskId = response.task_id || tempTaskId;

                // 查找正确的容器（可能已被SSE更新ID）
                let finalContainer = document.getElementById(`push-status-${finalTaskId}`);
                if (!finalContainer) {
                    // 如果找不到，可能还是临时ID
                    finalContainer = progressContainer;
                }

                if (!response.success) {
                    const accountId = Object.keys(pushWebsiteInfo.actArr)[0];
                    showToast('error', `推送到账号 ${accountId} 失败: ${response.message}`);
                    updateStepStatus(finalContainer, 4, 'error', response.message);
                } else {
                    const accountId = Object.keys(pushWebsiteInfo.actArr)[0];
                    showToast('success', `推送到账号 ${accountId} 成功`);
                    // 如果SSE没有更新到第4步，手动更新
                    updateStepStatus(finalContainer, 4, 'completed', '推送完成');
                }

                // 延迟关闭进度容器
                setTimeout(() => {
                    // 尝试关闭所有可能的容器ID
                    closePushStatus(finalTaskId);
                    if (finalTaskId !== tempTaskId) {
                        closePushStatus(tempTaskId);
                    }
                }, 3000);
            }

            if (window.currentEventSource) {
                window.currentEventSource.close();
            }

            // 关闭对话框
            closeModal();
            showToast('success', '推送任务已完成');
            // 刷新数据列表
            await loadPublishedData(currentPage);

            setConfirmButtonLoading(button, false);
            completeProgress();
        } catch (error) {
            console.error('推送失败:', error);
            showToast('error', '推送失败，请稍后重试');
            setConfirmButtonLoading(button, false);
            completeProgress();
        }
    });
});

const progressTimeouts = new Map();
const activePushTasks = new Map();
let sseReconnectAttempts = 0;
const maxSSEReconnectAttempts = 3;
function createSSEConnection() {
    if (window.currentEventSource && window.currentEventSource.readyState === EventSource.OPEN) {
        return window.currentEventSource;
    }

    if (window.currentEventSource) {
        window.currentEventSource.close();
    }

    window.currentEventSource = new EventSource('/tasks/events');

    window.currentEventSource.addEventListener('message', function(event) {
        try {
            const eventData = JSON.parse(event.data);
            if (eventData.type === 'push_status') {
                updatePushStatusUI(eventData.data);
            } else if (eventData.type === 'push_background') {
                handleBackgroundPushEvent(eventData.data);
            } else if (eventData.type === 'push_completed') {
                handlePushCompletedEvent(eventData.data);
            } else if (eventData.type === 'push_sensitive_word_error') {
                handleSensitiveWordErrorEvent(eventData.data);
            }
        } catch (error) {
            console.error('解析SSE消息失败:', error);
        }
    });

    // 监听连接错误
    window.currentEventSource.addEventListener('error', function(event) {
        if (sseReconnectAttempts < maxSSEReconnectAttempts) {
            sseReconnectAttempts++;
            setTimeout(() => {
                window.currentEventSource.close();
                createSSEConnection();
            }, 2000 * sseReconnectAttempts);
        } else {
            showToast('warning', '进度更新连接不稳定，请刷新页面');
        }
    });

    window.currentEventSource.addEventListener('open', function() {
        sseReconnectAttempts = 0;
    });

    return window.currentEventSource;
}

function disableBackgroundPushButton(factoryId, text = '已完成') {
    const statusContainer = document.querySelector(`.push-status-container[data-factory-id="${factoryId}"]`);
    if (statusContainer) {
        const backgroundBtn = statusContainer.querySelector('.background-push-btn');
        if (backgroundBtn) {
            backgroundBtn.disabled = true;
            backgroundBtn.textContent = text;
            backgroundBtn.style.opacity = '0.5';
            backgroundBtn.style.cursor = 'not-allowed';
        }
    }
}

async function backgroundPush(taskId, factoryId) {
    const statusContainer = document.querySelector(`.push-status-container[data-factory-id="${factoryId}"]`);
    if (statusContainer) {
        const backgroundBtn = statusContainer.querySelector('.background-push-btn');
        if (backgroundBtn && backgroundBtn.disabled) {
            showToast('info', '推送任务已完成，无需转为后台执行');
            return;
        }
    }

    if (activePushTasks.has(factoryId)) {
        const task = activePushTasks.get(factoryId);
        task.is_background = true;
        activePushTasks.set(factoryId, task);
    }

    try {
        const response = await fetch('/tasks/push/set-background', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                factory_id: factoryId,
                task_id: taskId
            })
        });

        if (response.ok) {
            updatePushButtonStatus(factoryId, 'pushing');
            closePushStatus(taskId);
            closeWebsiteModal();
            showToast('info', '推送任务已转为后台执行，您可以继续其他操作');
        } else {
            showToast('error', '设置后台推送失败');
        }
    } catch (error) {
        showToast('error', '设置后台推送失败');
    }
}

function updatePushButtonStatus(factoryId, status) {
    const pushButton = document.querySelector(`button[data-id="${factoryId}"]`);
    if (!pushButton) return;

    switch (status) {
        case 'pushing':
            pushButton.textContent = '查看进度';
            pushButton.disabled = false;
            pushButton.classList.add('pushing');
            pushButton.classList.remove('push-btn-loading', 'retry');
            pushButton.onclick = function() {
                viewPushProgress(factoryId);
            };
            break;
        case 'retry':
            pushButton.textContent = '重新推送';
            pushButton.disabled = false;
            pushButton.classList.add('retry');
            pushButton.classList.remove('pushing', 'push-btn-loading');
            pushButton.onclick = function() {
                retryPush(factoryId);
            };
            break;
        case 'normal':
            pushButton.textContent = '推送';
            pushButton.disabled = false;
            pushButton.classList.remove('pushing', 'push-btn-loading', 'retry');
            pushButton.onclick = function() {
                handlePushClick(factoryId);
            };
            break;
        case 'loading':
            setPushButtonLoading(pushButton, true);
            break;
    }
}

async function restorePushTasks() {
    try {
        const response = await fetch('/tasks/push/active-tasks');
        if (response.ok) {
            const activeTasks = await response.json();

            // 清理前端状态中已经不在Redis中的任务
            const activeFactoryIds = new Set(Object.keys(activeTasks));
            for (const [factoryId] of activePushTasks) {
                if (!activeFactoryIds.has(factoryId)) {
                    console.log(`清理已过期的推送状态: ${factoryId}`);
                    activePushTasks.delete(factoryId);
                    updatePushButtonStatus(factoryId, 'normal');
                }
            }

            Object.entries(activeTasks).forEach(([factoryId, taskData]) => {
                activePushTasks.set(factoryId, {
                    task_id: taskData.task_id,
                    step: taskData.step,
                    status: taskData.status,
                    message: taskData.message,
                    is_background: taskData.is_background
                });

                if (taskData.status === 'sensitive_word_error') {
                    updatePushButtonStatus(factoryId, 'retry');
                } else if (taskData.is_background) {
                    updatePushButtonStatus(factoryId, 'pushing');
                }
            });
        }
    } catch (error) {
        // 静默失败，不影响页面正常使用
        console.log('恢复推送任务状态失败:', error);
    }
}

async function viewPushProgress(factoryId) {
    const task = activePushTasks.get(factoryId);
    if (!task) {
        console.log(`查看进度失败：未找到推送任务状态 - ${factoryId}`);
        showToast('warning', '未找到正在进行的推送任务');
        return;
    }

    try {
        const response = await fetch(`/tasks/push/progress/${factoryId}`);
        if (response.ok) {
            const progressData = await response.json();
            console.log(`成功获取推送进度 - ${factoryId}:`, progressData);
            const progressContainer = createPushStatusContainer(task.task_id, factoryId);

            if (progressData.step) {
                for (let i = 1; i < progressData.step; i++) {
                    updateStepStatus(progressContainer, i, 'completed', getStepCompletedMessage(i));
                }
                updateStepStatus(progressContainer, progressData.step, progressData.status, progressData.message);
            }

            task.is_background = false;
            activePushTasks.set(factoryId, task);
            showToast('info', '已重新打开进度查看窗口');
        } else if (response.status === 404) {
            // Redis中没有进度数据，清理前端状态
            console.log(`Redis中没有进度数据，清理前端状态 - ${factoryId}`);
            activePushTasks.delete(factoryId);
            updatePushButtonStatus(factoryId, 'normal');
            showToast('warning', '推送任务已完成或已过期');
        } else {
            console.log(`获取推送进度失败 - ${factoryId}, 状态码: ${response.status}`);
            showToast('error', '获取推送进度失败');
        }
    } catch (error) {
        console.log(`获取推送进度异常 - ${factoryId}:`, error);
        showToast('error', '获取推送进度失败');
    }
}

function getStepCompletedMessage(step) {
    const messages = {
        1: '园区优质帖子选择完成',
        2: '内容优化处理完成',
        3: '园区图片分配完成',
        4: '园区视频生成完成',
        5: '推送至58完成'
    };
    return messages[step] || '步骤完成';
}

function handleBackgroundPushEvent(data) {
    const { factory_id } = data;
    if (activePushTasks.has(factory_id)) {
        const task = activePushTasks.get(factory_id);
        task.is_background = true;
        activePushTasks.set(factory_id, task);
    }
    updatePushButtonStatus(factory_id, 'pushing');
}

function handlePushCompletedEvent(data) {
    const { factory_id, success, message, is_background } = data;

    // 清理前端状态
    activePushTasks.delete(factory_id);
    updatePushButtonStatus(factory_id, 'normal');

    disableBackgroundPushButton(factory_id, '已完成');

    if (is_background) {
        if (success) {
            showToast('success', `后台推送完成: ${message || '推送任务已完成'}`);
        } else {
            showToast('error', `后台推送失败: ${message || '推送任务失败'}`);
        }
    } else {
        // 前台推送完成的原有逻辑
        if (success) {
            showToast('success', message || '推送任务已完成');
        } else {
            showToast('error', message || '推送任务失败');
        }
    }
}

function handleSensitiveWordErrorEvent(data) {
    const { factory_id, message } = data;
    activePushTasks.delete(factory_id);
    updatePushButtonStatus(factory_id, 'retry');
    showToast('error', message || '内容包含敏感词，无法推送');
}

async function retryPush(factoryId) {
    try {
        // 清理Redis中的推送状态
        const clearResponse = await fetch(`/tasks/push/clear/${factoryId}`, {
            method: 'DELETE'
        });

        if (clearResponse.ok) {
            // 重新触发推送流程
            handlePushClick(factoryId);
        } else {
            showToast('error', '清理推送状态失败');
        }
    } catch (error) {
        showToast('error', '重新推送失败');
    }
}



function updatePushStatusUI(statusData) {
    const { task_id, factory_id, step, status, message } = statusData;

    if (step === 1 && status === 'processing') {
        activePushTasks.set(factory_id, {
            task_id,
            step,
            status,
            message,
            is_background: false
        });
        updatePushButtonStatus(factory_id, 'loading');
    } else if (activePushTasks.has(factory_id)) {
        const task = activePushTasks.get(factory_id);
        task.step = step;
        task.status = status;
        task.message = message;
        activePushTasks.set(factory_id, task);

        if (task.is_background) {
            return;
        }
    }

    let statusContainer = document.getElementById(`push-status-${task_id}`);

    if (!statusContainer) {
        const tempContainers = document.querySelectorAll('.push-status-container[id^="push-status-temp_"]');

        if (tempContainers.length > 0) {
            statusContainer = tempContainers[tempContainers.length - 1];
            statusContainer.id = `push-status-${task_id}`;

            const closeBtn = statusContainer.querySelector('.close-status-btn');
            if (closeBtn) {
                closeBtn.setAttribute('onclick', `closePushStatus('${task_id}')`);
            }

            const backgroundBtn = statusContainer.querySelector('.background-push-btn');
            if (backgroundBtn) {
                backgroundBtn.setAttribute('onclick', `backgroundPush('${task_id}', '${factory_id}')`);
            }
        } else {
            statusContainer = createPushStatusContainer(task_id, factory_id);
        }
    }

    if (statusContainer) {
        updateStepStatus(statusContainer, step, status, message);

        if (status === 'completed' || status === 'error') {
            disableBackgroundPushButton(factory_id, status === 'completed' ? '已完成' : '已结束');
        }

        // 管理进度超时检测
        manageProgressTimeout(task_id, step, status);

        // 如果推送完成，自动关闭进度容器
        if (step === 5 && (status === 'completed' || status === 'error')) {
            setTimeout(() => {
                closePushStatus(task_id);
            }, 3000);
        }
    } else {
        console.error(`无法找到或创建状态容器 push-status-${task_id}`);
    }
}

function manageProgressTimeout(taskId, step, status) {
    const timeoutKey = `${taskId}_${step}`;

    // 清除之前的超时检测
    if (progressTimeouts.has(timeoutKey)) {
        clearTimeout(progressTimeouts.get(timeoutKey));
        progressTimeouts.delete(timeoutKey);
    }

    // 如果是processing状态，设置超时检测
    if (status === 'processing') {
        const timeout = setTimeout(() => {
            console.warn(`步骤${step}处理超时，任务ID: ${taskId}`);
            const container = document.getElementById(`push-status-${taskId}`);
            if (container) {
                updateStepStatus(container, step, 'warning', '处理时间较长，请耐心等待...');
            }
            progressTimeouts.delete(timeoutKey);
        }, 60000); // 60秒超时

        progressTimeouts.set(timeoutKey, timeout);
    }
}

function createPushStatusContainer(taskId, factoryId) {
    const container = document.createElement('div');
    container.id = `push-status-${taskId}`;
    container.className = 'push-status-container';
    container.setAttribute('data-factory-id', factoryId);

    container.innerHTML = `
        <div class="push-status-header">
            <h4>推送进度</h4>
            <div class="header-buttons">
                <button class="background-push-btn" onclick="backgroundPush('${taskId}', '${factoryId}')">后台推送</button>
                <button class="close-status-btn" onclick="closePushStatus('${taskId}')">&times;</button>
            </div>
        </div>
        <div class="push-steps">
            <div class="step" data-step="1">
                <div class="step-icon"><i class="fas fa-file-alt"></i></div>
                <div class="step-content">
                    <div class="step-title">选择优质帖子</div>
                    <div class="step-message">等待开始...</div>
                </div>
                <div class="step-status"></div>
            </div>
            <div class="step" data-step="2">
                <div class="step-icon"><i class="fas fa-shield-alt"></i></div>
                <div class="step-content">
                    <div class="step-title">内容优化处理</div>
                    <div class="step-message">等待开始...</div>
                </div>
                <div class="step-status"></div>
            </div>
            <div class="step" data-step="3">
                <div class="step-icon"><i class="fas fa-images"></i></div>
                <div class="step-content">
                    <div class="step-title">分配园区图片</div>
                    <div class="step-message">等待开始...</div>
                </div>
                <div class="step-status"></div>
            </div>
            <div class="step" data-step="4">
                <div class="step-icon"><i class="fas fa-video"></i></div>
                <div class="step-content">
                    <div class="step-title">生成园区视频</div>
                    <div class="step-message">等待开始...</div>
                </div>
                <div class="step-status"></div>
            </div>
            <div class="step" data-step="5">
                <div class="step-icon"><i class="fas fa-upload"></i></div>
                <div class="step-content">
                    <div class="step-title">推送至58</div>
                    <div class="step-message">等待开始...</div>
                </div>
                <div class="step-status"></div>
            </div>
        </div>
    `;

    document.body.appendChild(container);

    setTimeout(() => {
        container.classList.add('show');
    }, 100);

    return container;
}

function updateStepStatus(container, step, status, message) {
    if (!container) return;

    const stepElement = container.querySelector(`[data-step="${step}"]`);
    if (!stepElement) return;

    const messageElement = stepElement.querySelector('.step-message');
    const statusElement = stepElement.querySelector('.step-status');

    messageElement.textContent = message;

    stepElement.classList.remove('processing', 'completed', 'error', 'warning');
    statusElement.innerHTML = '';

    switch (status) {
        case 'processing':
            stepElement.classList.add('processing');
            statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            break;
        case 'completed':
            stepElement.classList.add('completed');
            statusElement.innerHTML = '<i class="fas fa-check"></i>';
            break;
        case 'error':
            stepElement.classList.add('error');
            statusElement.innerHTML = '<i class="fas fa-times"></i>';
            break;
        case 'warning':
            stepElement.classList.add('warning');
            statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            break;
    }
}

function closePushStatus(taskId) {
    const container = document.getElementById(`push-status-${taskId}`);
    if (container) {
        container.classList.remove('show');
        setTimeout(() => container.remove(), 300);
    }
}

function closeWebsiteModal() {
    const modal = document.getElementById('websiteModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.classList.remove('modal-open');
    }
}



function handleSearch() {
    currentPage = 1;
    loadPublishedData();
}

function handleCityChange() {
    currentPage = 1;
    loadPublishedData();
}



// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 检查main.js中的apiRequest函数是否正确实现
async function apiRequest(endpoint, method = 'GET', data = null) {
    try {
        console.log(`发送${method}请求到${endpoint}，数据：`, data);

        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(endpoint, options);
        console.log(`收到响应状态：${response.status}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('解析响应数据：', result);
        return result;
    } catch (error) {
        console.error(`API请求失败 (${endpoint}):`, error);
        throw error;
    }
}

// 添加推广按钮点击处理函数以及获取城市代码
async function handlePromotionClick(webId, accountId) {
    try {
        // 检查账号是否已经被选中
        const accountCheckbox = document.querySelector(`input[data-account-id="${accountId}"][value="${webId}"]`);
        if (accountCheckbox && accountCheckbox.checked) {
            // 如果已选中，则取消选中
            accountCheckbox.checked = false;
            const accountItem = accountCheckbox.closest('.account-item');
            if (accountItem) {
                accountItem.classList.remove('selected');
            }
            showToast('info', '已取消选择该账号');
            return;
        }

        showToast('info', '正在获取账号帖子信息...');
        startProgress();

        // 创建/显示帖子列表模态框，但显示加载状态
        showPostsListModal([], webId, accountId, true);

        // 获取当前厂房信息的所有信息
        const factoryCard = document.querySelector(`button[data-id="${currentFactoryId}"]`).closest('.data-card');
        const cityElement = factoryCard.querySelector('.location-text');
        const cityName = cityElement.textContent;
        const cityCode = getCityCodeFromName(cityName);

        if (!cityCode) {
            showToast('error', `无法确定城市代码: ${cityName}`);
            completeProgress();
            showPostsListModal([], webId, accountId, false, '无法确定城市代码');
            return;
        }

        // 准备webcontent数据
        const webcontentData = {
            webarr: [
                {
                    webID: webId,
                    actArr: {
                        [accountId]: "1"
                    }
                }
            ]
        };

        // 将webcontent数据转为JSON字符串并进行base64编码
        const webcontentJson = JSON.stringify(webcontentData);
        const webcontent = btoa(webcontentJson);

        // 调用API获取该账号的帖子列表
        const response = await apiRequest(`/youtui/agent-post-stats?city=${cityCode}`, 'POST', {
            city: cityCode,
            webcontent: webcontent
        });

        if (response.success && response.data) {
            // 获取套餐限制，如果没有则使用默认值20
            const packageLimit = response.package_limit || 20;

            // 检查是否达到套餐限制
            const needForceRemove = response.data.length >= packageLimit;

            // 显示帖子列表弹窗
            showPostsListModal(
                response.data,
                webId,
                accountId,
                false,
                null,
                needForceRemove,
                cityCode,
                packageLimit  // 传递套餐限制
            );

            if (needForceRemove) {
                showToast('warning', `帖子数量已达上限(${packageLimit}个)，必须下架一个帖子才能继续推送`, 5000);
            } else {
                showToast('info', `当前账号有${response.data.length}个帖子，套餐限制${packageLimit}个，未达上限`, 3000);
            }
        } else {
            showToast('error', response.message || '获取帖子信息失败');
            showPostsListModal([], webId, accountId, false, response.message || '获取帖子信息失败', false, cityCode, 20);
        }
    } catch (error) {
        console.error('获取账号帖子信息失败:', error);
        showToast('error', '获取帖子信息失败，请稍后重试');

        // 尝试获取cityCode，如果失败则使用null
        let cityCode = null;
        try {
            const factoryCard = document.querySelector(`button[data-id="${currentFactoryId}"]`).closest('.data-card');
            const cityElement = factoryCard.querySelector('.location-text');
            const cityName = cityElement.textContent;
            cityCode = getCityCodeFromName(cityName);
        } catch (cityError) {
            console.warn('无法获取城市代码:', cityError);
        }

        showPostsListModal([], webId, accountId, false, '获取数据过程中发生错误', false, cityCode, 20);
    } finally {
        completeProgress();
    }
}

// 根据城市名称获取城市代码
function getCityCodeFromName(cityName) {
    const citySelect = document.getElementById('citySelect');
    const options = Array.from(citySelect.options);

    for (const option of options) {
        if (option.textContent === cityName) {
            return option.value;
        }
    }

    return null;
}

// 添加关闭帖子列表模态框的函数
function closePostsListModal() {
    const modal = document.getElementById('postsListModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

// 允许账号选择的函数
function enableAccountSelection(accountId) {
    // 查找该账号的复选框
    const accountCheckbox = document.querySelector(`input[data-account-id="${accountId}"]`);
    if (accountCheckbox && !accountCheckbox.disabled) {
        // 选中账号并触发选中效果
        accountCheckbox.checked = true;
        const accountItem = accountCheckbox.closest('.account-item');
        if (accountItem) {
            accountItem.classList.add('selected');
        }
    }
}

// 修改帖子列表模态框跳过按钮处理逻辑
function bindSkipButtonEvent(skipBtn, accountId) {
    skipBtn.onclick = () => {
        // 关闭帖子列表模态框
        closePostsListModal();

        // 自动选中对应账号
        setTimeout(() => {
            enableAccountSelection(accountId);
        }, 100);
    };
}

// 添加显示帖子列表弹窗函数
function showPostsListModal(
    posts,
    webId,
    accountId,
    isLoading = false,
    errorMessage = null,
    needForceRemove = false,
    cityCode = null,
    packageLimit = 20  // 添加套餐限制参数，默认20
) {
    // 创建模态框
    const modalId = 'postsListModal';
    let modal = document.getElementById(modalId);

    // 如果模态框不存在，则创建
    if (!modal) {
        modal = document.createElement('div');
        modal.id = modalId;
        modal.className = 'modal';

        // 添加模态框内容 - 更新footer部分的结构，确保按钮在底部正确显示
        modal.innerHTML = `
            <div class="modal-content post-modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">账号帖子列表</h5>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="postsCountInfo" class="posts-count-info" style="display: none;">
                        <div class="count-badge">
                            <span>总数：</span>
                            <span id="postsCount">0</span><span>/</span><span id="packageLimitDisplay">20</span>
                        </div>
                        <div id="limitWarning" class="limit-warning" style="display: none;">
                            <i class="warning-icon">!</i>
                            <span>帖子数量已达上限，需下架后才能继续推送</span>
                        </div>
                    </div>
                    <div id="postsListLoading" class="posts-loading" style="display: none;">
                        <div class="loading-spinner-small"></div>
                        <p>正在加载帖子数据，请稍候...</p>
                    </div>
                    <div id="postsListError" class="posts-error" style="display: none;">
                        <div class="error-icon">!</div>
                        <p class="error-message">加载失败</p>
                    </div>
                    <div id="postsListContainer" class="posts-list">
                        <!-- 帖子列表将通过JavaScript动态填充 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="footer-info" id="footerInfo">
                        <div class="footer-hint" id="skipHint">
                            <i class="fas fa-info-circle"></i>
                            <span id="skipHintText">帖子数量未达上限，可以跳过直接推送</span>
                        </div>
                    </div>
                    <div class="footer-buttons">
                        <button type="button" class="secondary-button" id="closePostsBtn">关闭</button>
                        <button type="button" class="primary-button" id="skipBtn">
                            <i class="fas fa-forward"></i> 跳过并推送
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    // 更新套餐限制显示（无论是新创建还是已存在的模态框）
    const packageLimitDisplay = modal.querySelector('#packageLimitDisplay');
    if (packageLimitDisplay) {
        packageLimitDisplay.textContent = packageLimit;
    }

    // 存储当前账号ID，以便关闭时使用
    modal.dataset.accountId = accountId;

    // 确保模态框显示在页面顶部
    modal.style.display = 'block';
    window.scrollTo(0, 0); // 滚动到页面顶部
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);

    // 获取内容容器和按钮元素
    const postsListContainer = modal.querySelector('#postsListContainer');
    const postsListLoading = modal.querySelector('#postsListLoading');
    const postsListError = modal.querySelector('#postsListError');
    const postsCountInfo = modal.querySelector('#postsCountInfo');
    const skipBtn = modal.querySelector('#skipBtn');
    const closeBtn = modal.querySelector('#closePostsBtn');
    const closeIcon = modal.querySelector('.close');
    const skipHint = modal.querySelector('#skipHint');
    const skipHintText = modal.querySelector('#skipHintText');

    // 根据状态显示不同的内容
    if (isLoading) {
        postsListContainer.style.display = 'none';
        postsListLoading.style.display = 'flex';
        postsListError.style.display = 'none';
        postsCountInfo.style.display = 'none';
        skipBtn.style.display = 'none';
        return;
    } else if (errorMessage) {
        postsListContainer.style.display = 'none';
        postsListLoading.style.display = 'none';
        postsListError.style.display = 'flex';
        postsCountInfo.style.display = 'none';
        skipBtn.style.display = 'none';
        postsListError.querySelector('.error-message').textContent = errorMessage;

        // 允许关闭模态框但不自动选择账号（API调用失败场景）
        enableModalCloseWithoutSelection(modal, closeBtn, closeIcon);
        return;
    } else {
        postsListContainer.style.display = 'flex';
        postsListContainer.style.flexDirection = 'column';
        postsListLoading.style.display = 'none';
        postsListError.style.display = 'none';

        // 显示帖子数量信息
        if (posts && posts.length > 0) {
            postsCountInfo.style.display = 'flex';
            const countBadge = postsCountInfo.querySelector('.count-badge');
            const limitWarning = postsCountInfo.querySelector('#limitWarning');
            const postsCount = postsCountInfo.querySelector('#postsCount');

            // 获取已下架帖子数量
            const removedCount = window.removedPostIds ?
                posts.filter(post => window.removedPostIds.includes(post.RID || post.InfoID || post.id)).length : 0;

            // 计算有效帖子数
            const activePostsCount = posts.length - removedCount;

            postsCount.textContent = activePostsCount;

            // 设置跳过按钮状态 - 始终显示跳过按钮，但根据帖子数量设置其状态
            skipBtn.style.display = 'inline-flex'; // 确保跳过按钮可见

            if (activePostsCount >= packageLimit) {
                // 帖子数量达到或超过上限
                countBadge.classList.add('full');
                limitWarning.style.display = 'flex';

                // 禁用跳过按钮
                skipBtn.disabled = true;
                skipHint.classList.add('warning');
                skipHintText.textContent = '帖子数量已达上限，必须下架后才能继续！';

                // 禁用关闭按钮和外部点击关闭
                disableModalClose(modal, closeBtn, closeIcon);

                // 更新关闭按钮文本
                closeBtn.textContent = '请先下架帖子';
                closeBtn.classList.add('warning-btn');
            } else {
                // 帖子数量未达上限
                countBadge.classList.remove('full');
                limitWarning.style.display = 'none';

                // 启用关闭按钮和外部点击关闭，并传递账号ID
                enableModalClose(modal, closeBtn, closeIcon, accountId);

                // 更新关闭按钮文本
                closeBtn.textContent = '关闭';
                closeBtn.classList.remove('warning-btn');

                // 启用跳过按钮
                skipBtn.disabled = false;
                skipHint.classList.remove('warning');
                skipHintText.textContent = '帖子数量未达上限，可以跳过直接推送';

                // 使用新函数绑定跳过按钮事件
                bindSkipButtonEvent(skipBtn, accountId);
            }
        } else {
            postsCountInfo.style.display = 'none';

            // 允许关闭模态框并传递账号ID
            enableModalClose(modal, closeBtn, closeIcon, accountId);

            // 没有帖子，启用跳过按钮
            skipBtn.style.display = 'inline-flex'; // 确保跳过按钮可见
            skipBtn.disabled = false;
            skipHint.classList.remove('warning');
            skipHintText.textContent = '账号无帖子，可以直接推送';

            // 使用新函数绑定跳过按钮事件
            bindSkipButtonEvent(skipBtn, accountId);
        }
    }

    // 清空并填充帖子列表
    postsListContainer.innerHTML = '';

    if (posts && posts.length > 0) {
        // 存储已下架的帖子ID
        if (!window.removedPostIds) {
            window.removedPostIds = [];
        }

        posts.forEach((post, index) => {
            // 创建帖子元素
            const postElement = document.createElement('div');
            postElement.className = 'post-item';
            postElement.style.animationDelay = `${index * 30}ms`; // 减少动画延迟

            // 解析帖子数据，兼容不同的字段命名格式
            const title = post.Title || post.title || '无标题';
            const id = post.RID || post.InfoID || post.id || '未知ID';
            const sid = post.SID || '';
            const content = post.Com || post.content || '';
            const tags = post.Tags || '';
            const viewsDaily = post.clickT || post.view || 0;
            const callsMonthly = post.clickM || post.call || 0;
            const addTime = post.AddTime || post.addTime || '未知';
            const url = post.House_URL || '';
            // 获取租售类型，6表示出售，3表示出租
            const houseType = post.Type || '6';

            // 检查该帖子是否已被下架
            const isRemoved = window.removedPostIds.includes(id);
            if (isRemoved) {
                postElement.classList.add('removed');
            }

            // 优化帖子项布局，移除缩略图
            let postHTML = `
                <div class="post-header">
                    <div class="post-title">${title}</div>
                    <div class="post-actions">
                        <button class="remove-post-btn" data-post-id="${id}" data-type="${houseType}" ${isRemoved ? 'disabled' : ''}>
                            ${isRemoved ? '已下架' : '下架'}
                        </button>
                        ${url ? `<a href="${url}" target="_blank" class="view-post-btn"><i class="fas fa-external-link-alt"></i> 查看</a>` : ''}
                    </div>
                </div>
                <div class="post-meta">
                    <span class="post-id">ID: ${id}</span>
                    ${sid ? `<span class="post-sid">SID: ${sid}</span>` : ''}
                    <span class="post-time">发布时间: ${addTime}</span>
                </div>
            `;

            // 添加内容和标签（简化）
            postHTML += `<div class="post-info">`;
            if (content) {
                postHTML += `<div class="post-content">${content}</div>`;
            }

            // 添加标签（如果有）- 优化标签显示
            if (tags) {
                const tagList = tags.replace(/【/g, '').replace(/】/g, ',').split(',').filter(tag => tag.trim());
                if (tagList.length > 0) {
                    postHTML += `<div class="post-tags">`;
                    tagList.forEach(tag => {
                        if (tag.trim()) {
                            postHTML += `<span class="post-tag">${tag.trim()}</span>`;
                        }
                    });
                    postHTML += `</div>`;
                }
            }

            postHTML += `</div>`;

            // 添加统计信息（优化布局）
            postHTML += `
                <div class="post-stats">
                    <div class="stat-item">
                        <i class="fas fa-eye"></i>
                        <span>当天浏览量: </span>
                        <span class="stat-value">${viewsDaily}</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-chart-line"></i>
                        <span>累计90天浏览量: </span>
                        <span class="stat-value">${callsMonthly}</span>
                    </div>
                </div>
            `;

            postElement.innerHTML = postHTML;
            postsListContainer.appendChild(postElement);

            // 绑定下架按钮事件
            if (!isRemoved) {
                const removeButton = postElement.querySelector('.remove-post-btn');
                if (removeButton) {
                    removeButton.addEventListener('click', async function() {
                        await handleRemovePost(this, webId, accountId, cityCode);
                    });
                }
            }
        });
    } else {
        postsListContainer.innerHTML = '<div class="no-posts">该账号暂无帖子</div>';
    }
}

// 启用模态框关闭功能
function enableModalClose(modal, closeBtn, closeIcon, accountId) {
    const currentAccountId = accountId || modal.dataset.accountId;

    // 启用关闭按钮
    closeBtn.disabled = false;
    closeBtn.onclick = () => {
        closePostsListModal();

        // 如果有账号ID，自动选中对应账号
        if (currentAccountId) {
            setTimeout(() => {
                enableAccountSelection(currentAccountId);
            }, 100);
        }
    };

    // 启用右上角X关闭
    closeIcon.style.cursor = 'pointer';
    closeIcon.onclick = () => {
        closePostsListModal();

        // 如果有账号ID，自动选中对应账号
        if (currentAccountId) {
            setTimeout(() => {
                enableAccountSelection(currentAccountId);
            }, 100);
        }
    };

    // 启用点击外部关闭
    modal.removeEventListener('click', modal.handleOutsideClick);

    // 更新点击外部关闭的处理函数，添加自动选择账号的逻辑
    modal.handleOutsideClick = function(event) {
        if (event.target === this) {
            closePostsListModal();

            // 如果有账号ID，自动选中对应账号
            if (currentAccountId) {
                setTimeout(() => {
                    enableAccountSelection(currentAccountId);
                }, 100);
            }
        }
    };

    modal.addEventListener('click', modal.handleOutsideClick);
}

// 启用模态框关闭功能（不自动选择账号）- 用于API调用失败的场景
function enableModalCloseWithoutSelection(modal, closeBtn, closeIcon) {
    // 启用关闭按钮
    closeBtn.disabled = false;
    closeBtn.onclick = () => {
        closePostsListModal();
        // 注意：这里不执行自动选择账号的逻辑
    };

    // 启用右上角X关闭
    closeIcon.style.cursor = 'pointer';
    closeIcon.onclick = () => {
        closePostsListModal();
        // 注意：这里不执行自动选择账号的逻辑
    };

    // 启用点击外部关闭
    modal.removeEventListener('click', modal.handleOutsideClick);

    // 更新点击外部关闭的处理函数，不包含自动选择账号的逻辑
    modal.handleOutsideClick = function(event) {
        if (event.target === this) {
            closePostsListModal();
            // 注意：这里不执行自动选择账号的逻辑
        }
    };

    modal.addEventListener('click', modal.handleOutsideClick);
}

// 禁用模态框关闭功能
function disableModalClose(modal, closeBtn, closeIcon) {
    // 禁用关闭按钮
    closeBtn.disabled = true;
    closeBtn.onclick = null;

    // 禁用右上角X关闭
    closeIcon.style.cursor = 'not-allowed';
    closeIcon.onclick = null;

    // 禁用点击外部关闭
    modal.removeEventListener('click', modal.handleOutsideClick);
}

// 处理下架帖子
async function handleRemovePost(button, webId, accountId, cityCode) {
    const postId = button.dataset.postId;
    const postItem = button.closest('.post-item');
    // 获取房源类型（6表示出售，3表示出租）
    const houseType = button.dataset.type || '6';

    try {
        const originalText = button.textContent;
        button.textContent = '下架中';
        setRemoveButtonLoading(button, true);
        startProgress();

        // 构建webcontent参数 - Base64编码的JSON
        const webcontentObj = {
            "webarr": [
                {
                    "webID": webId,
                    "actArr": {
                        [accountId]: "1"
                    }
                }
            ]
        };
        const webcontent = btoa(JSON.stringify(webcontentObj));

        // 调用新的房源下架API
        const response = await apiRequest(`/youtui/house/offline`, 'POST', {
            city: cityCode,
            remote_id: postId,
            webcontent: webcontent,
            house_type: houseType // 添加房源类型参数
        });

        if (response.success) {
            postItem.classList.add('removed');
            button.disabled = true;
            button.textContent = '已下架';
            setRemoveButtonLoading(button, false);

            // 保存已下架的帖子ID
            if (!window.removedPostIds) {
                window.removedPostIds = [];
            }
            window.removedPostIds.push(postId);

            // 更新帖子计数
            updatePostsCount();

            showToast('success', '帖子下架成功');

            // 检查是否可以继续推送（如果之前达到了上限）
            const postsCount = document.querySelectorAll('#postsListContainer .post-item:not(.removed)').length;
            const skipBtn = document.getElementById('skipBtn');
            const closeBtn = document.getElementById('closePostsBtn');
            const closeIcon = document.querySelector('#postsListModal .close');
            const modal = document.getElementById('postsListModal');

            // 获取当前套餐限制（从模态框数据中获取，如果没有则使用默认值20）
            const packageLimitElement = document.getElementById('packageLimitDisplay');
            const currentPackageLimit = packageLimitElement ? parseInt(packageLimitElement.textContent) : 20;

            console.log(`下架后检查: 当前帖子数=${postsCount}, 套餐限制=${currentPackageLimit}`);

            if (postsCount < currentPackageLimit) {
                // 帖子数量降到套餐限制以下，启用所有关闭功能

                // 启用跳过按钮
                if (skipBtn) {
                    skipBtn.disabled = false;
                }

                // 更新警告提示
                const limitWarning = document.getElementById('limitWarning');
                if (limitWarning) {
                    limitWarning.style.display = 'none';
                }

                // 更新计数标记样式
                const countBadge = document.querySelector('.count-badge');
                if (countBadge) {
                    countBadge.classList.remove('full');
                }

                // 更新提示文字
                const skipHint = document.getElementById('skipHint');
                const skipHintText = document.getElementById('skipHintText');
                if (skipHint && skipHintText) {
                    skipHint.classList.remove('warning');
                    skipHintText.textContent = '帖子数量未达上限，可以跳过直接推送';
                }

                // 启用所有关闭功能
                enableModalClose(modal, closeBtn, closeIcon, accountId);

                // 更新关闭按钮文本
                if (closeBtn) {
                    closeBtn.textContent = '关闭';
                    closeBtn.classList.remove('warning-btn');
                }

                // 绑定继续按钮事件
                if (skipBtn) {
                    bindSkipButtonEvent(skipBtn, accountId);
                }

                console.log('已启用推送功能：跳过按钮和关闭按钮都已启用');
            } else {
                console.log('帖子数量仍达到或超过限制，保持禁用状态');
            }
        } else {
            showToast('error', response.message || '帖子下架失败');
            setRemoveButtonLoading(button, false);
            button.textContent = originalText;
        }
    } catch (error) {
        console.error('下架帖子失败:', error);
        showToast('error', '下架帖子失败，请稍后重试');
        setRemoveButtonLoading(button, false);
        button.textContent = '下架';
    } finally {
        completeProgress();
    }
}

// 更新帖子计数
function updatePostsCount() {
    const totalPosts = document.querySelectorAll('#postsListContainer .post-item').length;
    const removedPosts = document.querySelectorAll('#postsListContainer .post-item.removed').length;
    const activePosts = totalPosts - removedPosts;

    const postsCount = document.getElementById('postsCount');
    if (postsCount) {
        postsCount.textContent = activePosts;
    }
}