// 城市配置数据
let cityConfig = {};
let websitesData = {};
let currentViewMode = 'grid'; // 默认网格视图
let currentTaocan = null; // 存储当前套餐类型

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化动画效果
    initPageAnimations();
    
    // 获取城市配置
    fetchCityConfig();
    
    // 绑定获取端口按钮事件
    document.getElementById('fetchBtn').addEventListener('click', function() {
        const form = document.getElementById('portForm');
        const result = validateForm(form);
        if (result) {
            fetchWebsites(result.cityCode);
        }
    });
    
    // 绑定城市选择变化事件
    document.getElementById('cityInput').addEventListener('change', function() {
        const cityInput = this.value.trim();
        const cityCode = getCityCodeFromInput(cityInput);
        
        if (cityCode) {
            // 获取默认用户ID
            const userId = cityConfig.accounts[cityCode]?.user_id || '';
            document.getElementById('userIdInput').value = userId;
            
            // 添加输入动画效果
            const userIdInput = document.getElementById('userIdInput');
            userIdInput.classList.add('highlight-input');
            setTimeout(() => {
                userIdInput.classList.remove('highlight-input');
            }, 1000);
        } else {
            if (cityInput) {
                showToast('warning', '请输入或选择一个有效的城市');
            }
            document.getElementById('userIdInput').value = '';
            resetWebsites();
        }
    });
    
    // 绑定清空按钮事件
    document.getElementById('clearBtn').addEventListener('click', function() {
        document.getElementById('cityInput').value = '';
        document.getElementById('userIdInput').value = '';
        resetWebsites();
        
        // 添加清空动画效果
        showToast('info', '表单已清空');
    });
    
    // 绑定刷新钉钉账号按钮事件
    document.getElementById('refreshDingTalkBtn').addEventListener('click', function() {
        refreshDingTalkAccounts();
    });
    
    // 绑定视图切换按钮事件
    document.getElementById('gridViewBtn').addEventListener('click', function() {
        switchView('grid');
    });
    
    document.getElementById('listViewBtn').addEventListener('click', function() {
        switchView('list');
    });
});

// 初始化页面动画效果
function initPageAnimations() {
    // 添加顶部进度条动画
    const progressBar = document.getElementById('topProgressBar');
    progressBar.style.width = '100%';
    
    // 为页面元素添加入场动画
    const contentSections = document.querySelectorAll('.content-section');
    contentSections.forEach((section, index) => {
        setTimeout(() => {
            section.classList.add('animate__fadeInUp');
        }, 300 * (index + 1));
    });
    
    // 统计卡片动画
    const statsCards = document.querySelectorAll('.ports-card');
    statsCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 200 * (index + 1));
    });
}

// 切换视图模式（网格/列表）
function switchView(mode) {
    currentViewMode = mode;
    
    const gridBtn = document.getElementById('gridViewBtn');
    const listBtn = document.getElementById('listViewBtn');
    const portsContent = document.getElementById('portsContent');
    
    // 更新按钮状态
    if (mode === 'grid') {
        gridBtn.classList.add('active');
        listBtn.classList.remove('active');
    } else {
        gridBtn.classList.remove('active');
        listBtn.classList.add('active');
    }
    
    // 应用过渡动画
    portsContent.classList.add('animate__fadeOut');
    
    setTimeout(() => {
        // 重新渲染视图
        if (websitesData && websitesData.length > 0) {
            renderWebsites();
        }
        
        // 移除过渡动画并添加入场动画
        portsContent.classList.remove('animate__fadeOut');
        portsContent.classList.add('animate__fadeIn');
        
        setTimeout(() => {
            portsContent.classList.remove('animate__fadeIn');
        }, 1000);
    }, 300);
}

// 获取城市配置
async function fetchCityConfig() {
    try {
        // 显示加载状态
        showGlobalLoading();
        startProgress();

        // 调用API获取城市配置
        const domainsResponse = await apiRequest('/config/domains', 'GET');
        const accountsResponse = await apiRequest('/config/accounts', 'GET');
        
        // 构建城市配置对象
        cityConfig = {
            domains: domainsResponse.domains || {},
            names: domainsResponse.names || {},
            accounts: accountsResponse.accounts || {}
        };
        
        populateCitySelect();

        // 隐藏加载状态
        hideGlobalLoading();
        completeProgress();
    } catch (error) {
        console.error('获取城市配置失败:', error);
        showToast('error', '获取城市配置失败: ' + error.message);
        hideGlobalLoading();
        completeProgress();
    }
}

// 填充城市选择下拉框
function populateCitySelect() {
    const cityList = document.getElementById('cityList');
    const cityCodes = Object.keys(cityConfig.domains);
    
    // 清空现有选项
    cityList.innerHTML = ''; // 清空 datalist
    
    // 添加城市选项
    cityCodes.forEach(code => {
        const name = cityConfig.names[code] || code;
        const option = document.createElement('option');
        // 设置 value 为 "城市名称 (代码)" 格式，方便用户识别和输入匹配
        option.value = `${name} (${code})`;
        // 使用 dataset 存储实际的 code 值
        option.dataset.value = code;
        cityList.appendChild(option);
    });
    
    // 清空输入框的初始值，避免遗留上次的值
    document.getElementById('cityInput').value = '';
}

// 获取城市代码辅助函数
function getCityCodeFromInput(inputValue) {
    if (!inputValue) return null;

    // 检查是否是 "名称 (代码)" 格式
    const match = inputValue.match(/\(([^)]+)\)$/);
    if (match && match[1] && cityConfig.domains[match[1]]) {
        return match[1]; // 提取代码并验证
    }

    // 检查是否直接是代码
    if (cityConfig.domains[inputValue]) {
        return inputValue; // 直接是代码
    }

    // 如果都匹配不上，在 datalist 中再次查找
    const dataListOptions = document.getElementById('cityList').options;
    for (let i = 0; i < dataListOptions.length; i++) {
        if (dataListOptions[i].value === inputValue && dataListOptions[i].dataset.value) {
            if (cityConfig.domains[dataListOptions[i].dataset.value]) {
                return dataListOptions[i].dataset.value;
            }
        }
        if (dataListOptions[i].dataset.value === inputValue) {
            if (cityConfig.domains[inputValue]) {
                return inputValue;
            }
        }
    }

    return null; // 无效输入
}

// 验证表单
function validateForm(form) {
    const cityInput = document.getElementById('cityInput').value.trim();
    const cityCode = getCityCodeFromInput(cityInput);
    const userId = document.getElementById('userIdInput').value;
    
    if (!cityCode) {
        showToast('warning', '请选择或输入有效的城市');
        shakeElement(document.getElementById('cityInput'));
        return false;
    }
    
    if (!userId) {
        showToast('warning', '请输入用户ID');
        shakeElement(document.getElementById('userIdInput'));
        return false;
    }
    
    return { cityCode, userId };
}

// 获取可用网站列表
async function fetchWebsites(cityCode) {
    try {
        // 显示加载状态
        showGlobalLoading('正在获取端口列表...');
        startProgress();
        
        // 显示骨架屏加载效果
        document.getElementById('skeleton-loading').style.display = 'flex';
        document.getElementById('portsContent').style.display = 'none';

        // 调用API获取网站数据
        const userId = document.getElementById('userIdInput').value;
        const response = await apiRequest(`/youtui/push/websites?city=${cityCode}&user_id=${userId}`, 'GET');
        
        // 隐藏骨架屏
        document.getElementById('skeleton-loading').style.display = 'none';
        document.getElementById('portsContent').style.display = 'block';
        
        // 检查响应是否包含网站数据
        if (response && response.websites && Array.isArray(response.websites) && response.websites.length > 0) {
            websitesData = response.websites;
            showToast('success', response.message || '成功获取端口列表');
            
            // 更新统计卡片数据
            updateStatisticsCards();
        } else {
            websitesData = [];
            const portsContent = document.getElementById('portsContent');
            portsContent.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <p>当前城市没有可用的端口: ${response.message || '未知错误'}</p>
                </div>
            `;
            resetStatisticsCards();
            return;
        }
        
        renderWebsites();
    } catch (error) {
        console.error('获取端口列表失败:', error);
        showToast('error', '获取端口列表失败: ' + error.message);
        resetWebsites();
        resetStatisticsCards();
    } finally {
        // 隐藏加载状态
        hideGlobalLoading();
        completeProgress();
    }
}

// 更新统计卡片数据
function updateStatisticsCards() {
    let availablePortsCount = 0;
    let activePortsCount = 0;
    let verifiedAccountsCount = 0;
    let issuesCount = 0;
    
    // 计算各项统计数据
    if (websitesData && websitesData.length > 0) {
        websitesData.forEach(site => {
            if (site.userNameArr) {
                const accountsCount = Object.keys(site.userNameArr).length;
                availablePortsCount += accountsCount;
                
                // 检查活跃状态
                Object.values(site.userNameArr).forEach(accountData => {
                    const status = accountData[2] || '';
                    if (status === '可用') {
                        activePortsCount++;
                    } else {
                        issuesCount++;
                    }
                    
                    // 检查认证状态
                    const displayName = accountData[3] || '';
                    if (displayName.trim() !== '') {
                        verifiedAccountsCount++;
                    }
                });
            }
        });
    }
    
    // 使用动画效果更新统计数据
    animateCounterUpdate('availablePortsCount', availablePortsCount);
    animateCounterUpdate('activePortsCount', activePortsCount);
    animateCounterUpdate('verifiedAccountsCount', verifiedAccountsCount);
    animateCounterUpdate('issuesCount', issuesCount);
}

// 重置统计卡片数据
function resetStatisticsCards() {
    document.getElementById('availablePortsCount').textContent = '--';
    document.getElementById('activePortsCount').textContent = '--';
    document.getElementById('verifiedAccountsCount').textContent = '--';
    document.getElementById('issuesCount').textContent = '--';
}

// 数字动画更新函数
function animateCounterUpdate(elementId, targetValue) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    // 获取当前值（如果不是数字则设为0）
    const startValue = parseInt(element.textContent) || 0;
    const duration = 1500; // 动画持续时间（毫秒）
    const step = Math.ceil((targetValue - startValue) / (duration / 16)); // 每16ms的变化量（大约60fps）
    
    let currentValue = startValue;
    const updateCounter = () => {
        if (currentValue < targetValue) {
            currentValue = Math.min(currentValue + step, targetValue);
            element.textContent = currentValue;
            requestAnimationFrame(updateCounter);
        } else {
            element.textContent = targetValue;
        }
    };
    
    // 启动计数动画
    requestAnimationFrame(updateCounter);
}

// 渲染网站选择界面
function renderWebsites() {
    const portsContent = document.getElementById('portsContent');
    
    if (!websitesData || websitesData.length === 0) {
        portsContent.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-plug"></i>
                </div>
                <p>当前城市没有可用的端口</p>
            </div>
        `;
        return;
    }
    
    // 根据当前视图模式渲染
    if (currentViewMode === 'grid') {
        let html = '<div class="port-list">';
        // 创建网站卡片
        websitesData.forEach(site => {
            const webID = site.webID;
            const websiteName = getWebsiteName(webID);
            
            // 处理所有账号
            if (site.userNameArr) {
                Object.entries(site.userNameArr).forEach(([accountId, accountData], index) => {
                    const username = accountData[0] || '未知';
                    const status = accountData[2] || '未知';
                    const displayName = accountData[3] || ''; // 添加显示姓名字段
                    
                    // 确定推广类型
                    let actTypesHtml = '';
                    if (site.actArr) {
                        if (Array.isArray(site.actArr)) {
                            actTypesHtml = `<p><i class="fas fa-tag"></i> 推广类型: ${site.actArr.join(', ')}</p>`;
                        } else {
                            const actTypesArr = Object.entries(site.actArr).map(([code, name]) => name);
                            actTypesHtml = `<p><i class="fas fa-tag"></i> 推广类型: ${actTypesArr.join(', ')}</p>`;
                        }
                    }
                    
                    html += `
                        <div class="port-card" data-web-id="${webID}" data-account-id="${accountId}" style="animation-delay: ${index * 0.1}s">
                            <div class="port-header">
                                <h3><i class="fas fa-globe"></i> ${websiteName}</h3>
                                <span class="port-id"><i class="fas fa-hashtag"></i> ${webID}</span>
                            </div>
                            <div class="port-info">
                                <p><i class="fas fa-user"></i> 账号: ${username}</p>
                                <p><i class="fas fa-id-card"></i> 账号ID: ${accountId}</p>
                                <p><i class="fas fa-signal"></i> 状态: <span class="${status === '可用' ? 'status-active' : 'status-inactive'}">${status}</span></p>
                                ${actTypesHtml}
                                <div class="name-edit-container">
                                    <span class="name-label"><i class="fas fa-user-edit"></i> 姓名:</span>
                                    <input type="text" class="name-edit-input" value="${displayName}" placeholder="请输入姓名">
                                    <button class="name-edit-btn" onclick="saveAccountName(this)"><i class="fas fa-save"></i></button>
                                </div>
                                <button class="view-stats-btn" onclick="viewPostStats('${webID}', '${accountId}')">
                                    <i class="fas fa-chart-line"></i> 查看点击量
                                </button>
                            </div>
                        </div>
                    `;
                });
            }
        });
        html += '</div>';
        portsContent.innerHTML = html;
        
        // 添加动画效果
        setTimeout(() => {
            const cards = document.querySelectorAll('.port-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('animate__animated', 'animate__fadeInUp');
                }, index * 50);
            });
        }, 100);
    } else {
        // 列表视图
        let html = '<table class="ports-table">';
        html += `
            <thead>
                <tr>
                    <th><i class="fas fa-globe"></i> 网站名称</th>
                    <th><i class="fas fa-hashtag"></i> 网站ID</th>
                    <th><i class="fas fa-user"></i> 账号</th>
                    <th><i class="fas fa-id-card"></i> 账号ID</th>
                    <th><i class="fas fa-signal"></i> 状态</th>
                    <th><i class="fas fa-user-edit"></i> 姓名</th>
                    <th><i class="fas fa-cog"></i> 操作</th>
                </tr>
            </thead>
            <tbody>
        `;
        
        // 创建表格行
        websitesData.forEach(site => {
            const webID = site.webID;
            const websiteName = getWebsiteName(webID);
            
            // 处理所有账号
            if (site.userNameArr) {
                Object.entries(site.userNameArr).forEach(([accountId, accountData], index) => {
                    const username = accountData[0] || '未知';
                    const status = accountData[2] || '未知';
                    const displayName = accountData[3] || ''; // 添加显示姓名字段
                    
                    html += `
                        <tr class="port-row" data-web-id="${webID}" data-account-id="${accountId}" style="animation-delay: ${index * 0.05}s">
                            <td>${websiteName}</td>
                            <td>${webID}</td>
                            <td>${username}</td>
                            <td>${accountId}</td>
                            <td><span class="${status === '可用' ? 'status-active' : 'status-inactive'}">${status}</span></td>
                            <td>
                                <div class="name-edit-container-table">
                                    <input type="text" class="name-edit-input" value="${displayName}" placeholder="请输入姓名">
                                    <button class="name-edit-btn table-btn" onclick="saveAccountName(this)"><i class="fas fa-save"></i></button>
                                </div>
                            </td>
                            <td>
                                <button class="view-stats-btn table-btn" onclick="viewPostStats('${webID}', '${accountId}')">
                                    <i class="fas fa-chart-line"></i> 查看
                                </button>
                            </td>
                        </tr>
                    `;
                });
            }
        });
        
        html += '</tbody></table>';
        portsContent.innerHTML = html;
        
        // 添加动画效果
        setTimeout(() => {
            const rows = document.querySelectorAll('.port-row');
            rows.forEach((row, index) => {
                setTimeout(() => {
                    row.classList.add('animate__animated', 'animate__fadeInUp');
                }, index * 30);
            });
        }, 100);
    }
}

// 表单验证时的抖动动画
function shakeElement(element) {
    if (!element) return;
    
    element.classList.add('animate__animated', 'animate__shakeX');
    
    // 移除动画类，以便下次可以再次添加
    setTimeout(() => {
        element.classList.remove('animate__animated', 'animate__shakeX');
    }, 1000);
}

// 新增：查看经纪人帖子点击量
async function viewPostStats(webId, accountId) {
    try {
        // 获取城市代码
        const cityInput = document.getElementById('cityInput').value.trim();
        const cityCode = getCityCodeFromInput(cityInput);
        
        if (!cityCode) {
            showToast('error', '无法确定当前城市');
            return;
        }
        
        // 获取页面上输入的用户ID，而不是使用账号ID
        const userId = document.getElementById('userIdInput').value.trim();
        
        if (!userId) {
            showToast('error', '请先输入用户ID');
            return;
        }
        
        // 显示加载状态
        showGlobalLoading('正在获取帖子点击量...');
        startProgress();
        
        // 准备webcontent数据
        const webcontentData = {
            webarr: [
                {
                    webID: webId,
                    actArr: {
                        [accountId]: "1"
                    }
                }
            ]
        };
        
        // 将webcontent数据转为JSON字符串并进行base64编码
        const webcontentJson = JSON.stringify(webcontentData);
        const webcontent = btoa(webcontentJson);
        
        // 同时在URL查询参数和请求体中提供city参数，使用页面上的用户ID，并添加webcontent字段
        const response = await apiRequest(`/youtui/agent-post-stats?city=${cityCode}`, 'POST', {
            city: cityCode,
            user_id: userId,
            webcontent: webcontent
        });
        
        // 处理返回结果
        if (response.success && response.data) {
            console.log('帖子点击量数据:', response.data); // 添加调试输出
            console.log('套餐类型:', response.taocan); // 添加套餐类型调试输出
            // 创建并显示点击量数据弹窗，传递完整的响应对象
            showPostStatsModal(response, userId, accountId); // 传递完整响应对象
            showToast('success', response.message || '获取点击量成功');
        } else {
            showToast('error', response.message || '获取点击量失败');
        }
    } catch (error) {
        console.error('获取帖子点击量失败:', error);
        showToast('error', '获取帖子点击量失败: ' + error.message);
    } finally {
        // 隐藏加载状态
        hideGlobalLoading();
        completeProgress();
    }
}

// 显示点击量数据弹窗
function showPostStatsModal(response, userId, originalAccountId) {
    console.log('显示点击量弹窗, 原始账号ID:', originalAccountId);

    // 从响应对象中提取数据和套餐类型
    const data = response.data || [];
    const taocan = response.taocan || '未知套餐';

    // 存储当前套餐类型到全局变量
    currentTaocan = taocan;

    // 检查是否已存在弹窗，如果存在则移除
    let existingModal = document.getElementById('statsModal');
    if (existingModal) {
        document.body.removeChild(existingModal);
    }

    // 创建弹窗
    const modal = document.createElement('div');
    modal.id = 'statsModal';
    modal.className = 'stats-modal';

    // 检查已下架的帖子ID
    if (!window.removedPostIds) {
        window.removedPostIds = [];
    }

    // 计算活跃帖子数量
    let activePosts = 0;
    if (Array.isArray(data)) {
        activePosts = data.filter(item => !window.removedPostIds.includes(item.RID)).length;
    }
    
    // 准备弹窗内容
    let modalContent = `
        <div class="stats-modal-content">
            <div class="stats-modal-header">
                <h3><i class="fas fa-file-alt"></i> 帖子管理</h3>
                <button class="stats-modal-close" onclick="closePostStatsModal()">&times;</button>
            </div>
            <div class="stats-modal-body">
    `;
    
    // 添加帖子数量统计和套餐类型信息
    if (Array.isArray(data) && data.length > 0) {
        modalContent += `
            <div class="stats-count">
                <div>
                    <i class="fas fa-clipboard-list"></i> 当前帖子：
                    <span class="count-badge"><i class="fas fa-chart-line"></i><span id="activePostsCount">${activePosts}</span> / ${data.length}</span>
                </div>
                <div>
                    <button onclick="toggleTableStyle()" class="secondary-button" style="padding:4px 8px; font-size:12px;">
                        <i class="fas fa-table"></i> 切换视图
                    </button>
                </div>
            </div>
            <div class="package-info">
                <div class="package-type">
                    <i class="fas fa-box"></i> 套餐类型：
                    <span class="package-badge">${taocan}</span>
                </div>
            </div>
        `;
    }
    
    // 处理数据并生成表格
    if (Array.isArray(data) && data.length > 0) {
        modalContent += `<div class="table-scroll-container">
            <table class="stats-table-compact" id="postsTable">
                <thead>
                    <tr>
                        <th><i class="fas fa-hashtag"></i> 房源ID</th>
                        <th><i class="fas fa-heading"></i> 标题</th>
                        <th><i class="fas fa-calendar-day"></i> 今日点击</th>
                        <th><i class="fas fa-calendar-alt"></i> 近90天点击</th>
                        <th><i class="fas fa-bolt"></i> 激活时间</th>
                        <th><i class="fas fa-clock"></i> 发布时间</th>
                        <th><i class="fas fa-globe"></i> 网站</th>
                        <th><i class="fas fa-retweet"></i> 租售类型</th>
                        <th><i class="fas fa-cogs"></i> 操作</th>
                    </tr>
                </thead>
                <tbody id="postsTableBody">
        `;
        
        data.forEach(item => {
            let houseId = item.RID || '未知';
            let title = item.Title || '未知标题';
            let todayClicks = item.clickT || '0';
            let monthClicks = item.clickM || '0';
            let siteId = item.SID || '';
            let url = item.House_URL || '#';
            let tags = item.Tags || '';
            let type = item.Type || ''; // 获取租售类型
            let retime = item.Retime || ''; // 激活时间
            let potime = item.Potime || ''; // 发布时间

            // 根据类型值确定租售类型显示文本
            let typeText = '未知';
            let typeClass = 'type-unknown';
            if (type === '6') {
                typeText = '出售';
                typeClass = 'type-sale';
            } else if (type === '3') {
                typeText = '出租';
                typeClass = 'type-rent';
            }

            // 根据SID获取网站名称
            let siteName = getWebsiteName(siteId);

            // 检查是否已下架
            const isRemoved = window.removedPostIds.includes(houseId);

            // 为点击量添加颜色类别
            const todayClicksClass = getTodayClicksClass(todayClicks);
            const monthClicksClass = getMonthClicksClass(monthClicks);

            // 格式化时间显示
            const formattedRetime = formatDateTime(retime);
            const formattedPotime = formatDateTime(potime);

            modalContent += `
                <tr class="${isRemoved ? 'removed' : ''}">
                    <td class="id-column" title="${houseId}">${formatHouseId(houseId)}</td>
                    <td class="post-title-cell" title="${title}">${title}</td>
                    <td><span class="clicks-count ${todayClicksClass}" title="今日点击量">${todayClicks}</span></td>
                    <td><span class="clicks-count ${monthClicksClass}" title="近90天点击量">${monthClicks}</span></td>
                    <td class="time-column">${formattedRetime}</td>
                    <td class="time-column">${formattedPotime}</td>
                    <td><i class="fas fa-globe-asia" style="color:#1890ff;margin-right:4px;font-size:11px;"></i>${siteName}</td>
                    <td><span class="type-label ${typeClass}">${typeText}</span></td>
                    <td>
                        <button class="view-post-btn" onclick="openPostInBrowser('${url}')"><i class="fas fa-eye"></i> 查看</button>
                        ${isRemoved ?
                        `<button class="remove-post-btn" disabled><i class="fas fa-ban"></i> 已下架</button>` :
                        `<button class="remove-post-btn" data-post-id="${houseId}" data-web-id="${siteId}" data-account-id="${originalAccountId}" data-type="${type}" onclick="handleRemovePost(this)"><i class="fas fa-trash-alt"></i> 下架</button>
                         <button class="activate-post-btn" data-post-id="${houseId}" data-web-id="${siteId}" data-account-id="${originalAccountId}" data-type="${type}" onclick="handleActivatePost(this)"><i class="fas fa-bolt"></i> 激活</button>`}
                    </td>
                </tr>
            `;
        });
        
        modalContent += `
                </tbody>
            </table>
        </div>
        `;
    } else {
        modalContent += `
            <div class="stats-empty">
                <i class="fas fa-inbox"></i>
                <p>暂无帖子数据</p>
            </div>
        `;
    }
    
    modalContent += `
            </div>
        </div>
    `;
    
    modal.innerHTML = modalContent;
    document.body.appendChild(modal);
    
    // 显示弹窗
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

// 获取今日点击量的类别
function getTodayClicksClass(clicks) {
    clicks = parseInt(clicks) || 0;
    if (clicks >= 5) return 'high';
    if (clicks <= 1) return 'low';
    return '';
}

// 获取近90天点击量的类别
function getMonthClicksClass(clicks) {
    clicks = parseInt(clicks) || 0;
    if (clicks >= 20) return 'high';
    if (clicks <= 5) return 'low';
    return '';
}

// 格式化房源ID，使其更短更易读
function formatHouseId(id) {
    if (!id || id === '未知') return id;
    if (id.length > 12) {
        return id.substring(0, 4) + '...' + id.substring(id.length - 8);
    }
    return id;
}

// 格式化日期时间显示
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr || dateTimeStr === '未知' || dateTimeStr === '') {
        return '<span class="time-unknown">--</span>';
    }

    try {
        // 解析日期时间字符串 "YYYY-MM-DD HH:MM:SS"
        const date = new Date(dateTimeStr);
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        const postDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

        // 格式化时间部分 HH:MM
        const timeStr = date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });

        // 判断日期类型并返回相应格式
        if (postDate.getTime() === today.getTime()) {
            // 今天：显示"今天 HH:MM"
            return `<span class="time-today" title="${dateTimeStr}">今天 ${timeStr}</span>`;
        } else if (postDate.getTime() === yesterday.getTime()) {
            // 昨天：显示"昨天 HH:MM"
            return `<span class="time-yesterday" title="${dateTimeStr}">昨天 ${timeStr}</span>`;
        } else {
            // 其他日期：显示"MM-DD HH:MM"
            const monthDay = date.toLocaleDateString('zh-CN', {
                month: '2-digit',
                day: '2-digit'
            }).replace(/\//g, '-');
            return `<span class="time-other" title="${dateTimeStr}">${monthDay} ${timeStr}</span>`;
        }
    } catch (error) {
        console.warn('日期格式化失败:', dateTimeStr, error);
        return `<span class="time-error" title="${dateTimeStr}">格式错误</span>`;
    }
}

// 切换表格样式
function toggleTableStyle() {
    const table = document.getElementById('postsTable');
    if (table) {
        if (table.classList.contains('stats-table-compact')) {
            table.classList.remove('stats-table-compact');
            table.classList.add('stats-table');
        } else {
            table.classList.remove('stats-table');
            table.classList.add('stats-table-compact');
        }
    }
}

// 处理下架帖子
async function handleRemovePost(button) {
    const postId = button.dataset.postId;
    const webId = button.dataset.webId;
    const accountId = button.dataset.accountId; // 从button的data属性获取账号ID
    const type = button.dataset.type || '6'; // 获取租售类型，默认为6(出售)
    const tr = button.closest('tr');
    

    try {
        // 获取当前城市
        const cityInput = document.getElementById('cityInput').value.trim();
        const cityCode = getCityCodeFromInput(cityInput);
        
        if (!cityCode) {
            showToast('error', '无法确定当前城市');
            return;
        }
        
        // 获取当前用户ID
        const userId = document.getElementById('userIdInput').value.trim();
        
        if (!userId) {
            showToast('error', '无法确定当前用户ID');
            return;
        }
        
        // 设置按钮为加载状态
        button.disabled = true;
        button.classList.add('loading');
        const originalText = button.textContent;
        button.textContent = '下架中';
        startProgress();
        
        // 构建webcontent参数 - Base64编码的JSON
        const webcontentObj = {
            "webarr": [
                {
                    "webID": webId,
                    "actArr": {
                        [accountId]: "1" // 使用原始账号ID
                    }
                }
            ]
        };
                
        const webcontent = btoa(JSON.stringify(webcontentObj));
        
        // 调用房源下架API
        const response = await apiRequest(`/youtui/house/offline`, 'POST', {
            city: cityCode,
            remote_id: postId,
            webcontent: webcontent,
            user_id: userId,
            house_type: type // 添加租售类型参数
        });
        
        if (response.success) {
            // 标记帖子为已下架
            tr.classList.add('removed');
            button.disabled = true;
            button.textContent = '已下架';
            button.classList.remove('loading');

            // 保存已下架的帖子ID
            if (!window.removedPostIds) {
                window.removedPostIds = [];
            }
            window.removedPostIds.push(postId);

            // 更新帖子计数
            updatePostsCount();

            // 更新套餐类型显示
            if (currentTaocan) {
                const newTaocan = updateTaocanAfterRemove(currentTaocan);
                currentTaocan = newTaocan; // 更新全局变量
                updateTaocanDisplay(newTaocan);
            }

            showToast('success', '帖子下架成功');
        } else {
            showToast('error', response.message || '帖子下架失败');
            button.disabled = false;
            button.classList.remove('loading');
            button.textContent = originalText;
        }
    } catch (error) {
        console.error('下架帖子失败:', error);
        showToast('error', '下架帖子失败，请稍后重试');
        button.disabled = false;
        button.classList.remove('loading');
        button.textContent = '下架';
    } finally {
        completeProgress();
    }
}

// 更新帖子计数
function updatePostsCount() {
    const tableBody = document.getElementById('postsTableBody');
    if (tableBody) {
        const totalRows = tableBody.querySelectorAll('tr').length;
        const removedRows = tableBody.querySelectorAll('tr.removed').length;
        const activeRows = totalRows - removedRows;

        const activePostsCount = document.getElementById('activePostsCount');
        if (activePostsCount) {
            activePostsCount.textContent = activeRows;
        }
    }
}

// 下架后更新套餐类型
function updateTaocanAfterRemove(taocan) {
    if (!taocan || !taocan.includes('-')) return taocan;

    const parts = taocan.split('-');
    const used = parseInt(parts[0]);
    const remaining = parseInt(parts[1]);

    // 下架：已用减1，剩余加1
    const newUsed = Math.max(0, used - 1);
    const newRemaining = remaining + 1;

    return `${newUsed}-${newRemaining}`;
}

// 更新套餐类型显示
function updateTaocanDisplay(newTaocan) {
    const packageBadge = document.querySelector('.package-badge');
    if (packageBadge && newTaocan) {
        packageBadge.textContent = newTaocan;
        // 添加更新动画效果
        packageBadge.style.color = '#52c41a';
        packageBadge.style.fontWeight = 'bold';
        setTimeout(() => {
            packageBadge.style.color = '';
            packageBadge.style.fontWeight = '';
        }, 2000);
    }
}

// 关闭点击量数据弹窗
function closePostStatsModal() {
    const modal = document.getElementById('statsModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 300);
    }
}

// 保存账号姓名
async function saveAccountName(button) {
    // 从按钮获取父元素（卡片或表格行）
    const container = button.closest('.port-card') || button.closest('tr');
    if (!container) return;
    
    // 获取对应的webId和accountId
    const webId = container.dataset.webId;
    const accountId = container.dataset.accountId;
    if (!webId || !accountId) {
        showToast('error', '无法获取账号信息');
        return;
    }
    
    // 获取名称输入框
    const nameInput = button.closest('.name-edit-container') 
        || button.closest('.name-edit-container-table');
    if (!nameInput) {
        showToast('error', '无法获取名称输入框');
        return;
    }
    
    const inputElement = nameInput.querySelector('.name-edit-input');
    if (!inputElement) {
        showToast('error', '无法获取输入元素');
        return;
    }
    
    const name = inputElement.value.trim();

    try {
        setButtonLoading(button, true);
        
        // 调用保存姓名的API
        const response = await apiRequest('/youtui/push/account/name', 'POST', {
            web_id: webId,
            account_id: accountId,
            name: name
        });

        showToast('success', '姓名保存成功');
        
        // 更新本地数据
        websitesData.forEach(site => {
            if (site.webID === webId && site.userNameArr && site.userNameArr[accountId]) {
                site.userNameArr[accountId][3] = name;
            }
        });
    } catch (error) {
        console.error('保存姓名失败:', error);
        showToast('error', '保存姓名失败: ' + error.message);
    } finally {
        setButtonLoading(button, false);
    }
}

// 根据网站ID获取网站名称
function getWebsiteName(webID) {
    // 网站ID与名称映射
    const websiteNames = {
        '111': '免费端口',
        '402': '安居客三网合一版本',
        '403': '58付费三网合一版本',
        '404': '链家网',
        '405': '贝壳找房',
        '406': '我爱我家',
        '407': '房天下',
        '408': '58同城',
        '409': '赶集网',
        '410': '安居客'
    };
    
    return websiteNames[webID] || `网站${webID}`;
}

// 重置网站选择界面
function resetWebsites() {
    const portsContent = document.getElementById('portsContent');
    portsContent.innerHTML = `
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-plug"></i>
            </div>
            <p>请先选择城市和用户ID，点击"获取端口列表"按钮查看可用端口</p>
        </div>
    `;
    websitesData = {};
}

// 显示全局加载指示器
function showGlobalLoading(message = '加载中...') {
    const indicator = document.getElementById('globalLoadingIndicator');
    const messageElement = indicator.querySelector('span');
    messageElement.textContent = message;
    indicator.classList.add('active');
}

// 隐藏全局加载指示器
function hideGlobalLoading() {
    const indicator = document.getElementById('globalLoadingIndicator');
    indicator.classList.remove('active');
}

// 启动进度条动画
function startProgress() {
    const bar = document.getElementById('topProgressBar');
    bar.style.width = '0';
    bar.style.opacity = '1';
    
    // 模拟加载进度
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15; // 随机增加进度，最大不超过100
        if (progress > 70) {
            clearInterval(interval);
        }
        bar.style.width = `${Math.min(progress, 70)}%`;
    }, 200);
}

// 完成进度条动画
function completeProgress() {
    const bar = document.getElementById('topProgressBar');
    
    // 快速完成进度
    bar.style.width = '100%';
    
    // 延迟后隐藏进度条
    setTimeout(() => {
        bar.style.opacity = '0';
        
        // 重置进度条，以便下次使用
        setTimeout(() => {
            bar.style.width = '0';
        }, 500);
    }, 500);
}

// 显示提示消息
function showToast(type, message) {
    // 创建Toast元素
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = message;
    
    // 添加到容器
    const container = document.getElementById('toastContainer');
    container.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // 自动移除
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            container.removeChild(toast);
        }, 300);
    }, 3000);
}

// 显示消息（兼容旧版本）
function showMessage(message, type) {
    showToast(type, message);
}

// 刷新钉钉账号信息
async function refreshDingTalkAccounts() {
    try {
        // 检查表单是否有效
        const form = document.getElementById('portForm');
        const result = validateForm(form);
        if (!result) return;
        
        // 显示加载状态
        const button = document.getElementById('refreshDingTalkBtn');
        setButtonLoading(button, true);
        showGlobalLoading('正在刷新钉钉账号...');
        startProgress();
        
        // 调用API刷新钉钉账号
        const response = await apiRequest(`/youtui/push/dingtalk/refresh?city=${result.cityCode}`, 'GET');
        
        if (response && response.success) {
            showToast('success', response.message || '钉钉账号刷新成功');
            
            // 重新获取网站列表数据
            fetchWebsites(result.cityCode);
        } else {
            showToast('error', response.message || '钉钉账号刷新失败');
        }
    } catch (error) {
        console.error('刷新钉钉账号失败:', error);
        showToast('error', '刷新钉钉账号失败: ' + error.message);
    } finally {
        // 恢复按钮状态
        setButtonLoading(document.getElementById('refreshDingTalkBtn'), false);
        hideGlobalLoading();
        completeProgress();
    }
}

// 设置按钮加载状态
function setButtonLoading(button, isLoading) {
    if (!button) return;
    
    if (isLoading) {
        button.classList.add('loading');
    } else {
        button.classList.remove('loading');
    }
}

// API请求封装
async function apiRequest(url, method = 'GET', data = null) {
    try {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.message || `请求失败: ${response.status}`);
        }
        
        return result;
    } catch (error) {
        console.error('API请求错误:', error);
        throw error;
    }
}

// 在新窗口打开帖子
function openPostInBrowser(url) {
    window.open(url, '_blank');
}

// 处理激活帖子
async function handleActivatePost(button) {
    const postId = button.dataset.postId;
    const webId = button.dataset.webId;
    const accountId = button.dataset.accountId;
    const type = button.dataset.type || '6'; // 获取租售类型，默认为6(出售)
    const tr = button.closest('tr');

    try {
        // 获取当前城市
        const cityInput = document.getElementById('cityInput').value.trim();
        const cityCode = getCityCodeFromInput(cityInput);
        
        if (!cityCode) {
            showToast('error', '无法确定当前城市');
            return;
        }
        
        // 获取当前用户ID
        const userId = document.getElementById('userIdInput').value.trim();
        
        if (!userId) {
            showToast('error', '无法确定当前用户ID');
            return;
        }
        
        // 设置按钮为加载状态
        button.disabled = true;
        button.classList.add('loading');
        const originalText = button.textContent;
        button.textContent = '激活中';
        startProgress();
        
        // 构建webcontent参数 - Base64编码的JSON
        const webcontentObj = {
            "webarr": [
                {
                    "webID": webId,
                    "actArr": {
                        [accountId]: "1"
                    }
                }
            ]
        };
                
        const webcontent = btoa(JSON.stringify(webcontentObj));
        
        // 调用房源激活API - 使用新的激活接口
        const response = await apiRequest(`/youtui/house/activate`, 'POST', {
            city: cityCode,
            remote_id: postId,
            webcontent: webcontent,
            user_id: userId,
            house_type: type
        });
        
        if (response.success) {
            // 激活成功
            showToast('success', '帖子激活成功');

            // 立即更新页面上的激活时间和状态
            const retimeCell = tr.querySelector('td:nth-child(5)'); // 激活时间列
            if (retimeCell) {
                const now = new Date();
                const timeString = now.getFullYear() + '-' +
                    String(now.getMonth() + 1).padStart(2, '0') + '-' +
                    String(now.getDate()).padStart(2, '0') + ' ' +
                    String(now.getHours()).padStart(2, '0') + ':' +
                    String(now.getMinutes()).padStart(2, '0') + ':' +
                    String(now.getSeconds()).padStart(2, '0');
                retimeCell.textContent = timeString;
                retimeCell.style.color = '#52c41a'; // 绿色表示刚更新
                retimeCell.style.fontWeight = 'bold';

                // 3秒后恢复原样式
                setTimeout(() => {
                    retimeCell.style.color = '';
                    retimeCell.style.fontWeight = '';
                }, 3000);
            }

            // 恢复按钮状态
            button.disabled = false;
            button.classList.remove('loading');
            button.textContent = originalText;

            // 可以添加一些视觉反馈，比如短暂改变行的背景色
            tr.classList.add('highlight-row');
            setTimeout(() => {
                tr.classList.remove('highlight-row');
            }, 2000);
        } else {
            showToast('error', response.message || '帖子激活失败');
            button.disabled = false;
            button.classList.remove('loading');
            button.textContent = originalText;
        }
    } catch (error) {
        console.error('激活帖子失败:', error);
        showToast('error', '激活帖子失败，请稍后重试');
        button.disabled = false;
        button.classList.remove('loading');
        button.textContent = '激活';
    } finally {
        completeProgress();
    }
} 