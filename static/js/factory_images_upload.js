document.addEventListener('DOMContentLoaded', function() {
    const campusGalleryResults = document.getElementById('campusGalleryResults');
    const galleryResultsContent = document.getElementById('galleryResultsContent');
    const campusSearchInput = document.getElementById('campusSearchInput');

    const pageSize = 10;
    campusSearchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            queryCampusGallery();
        }
    });



    window.queryCampusGallery = queryCampusGallery;
    window.selectCampusFromResults = selectCampusFromResults;
    window.viewCampusDetail = viewCampusDetail;
    window.changePage = changePage;
    function selectCampusFromResults(campusName) {
        campusSearchInput.value = campusName;
        queryCampusGallery();
    }

    function viewCampusDetail(campusName) {
        if (campusSearchInput.value === campusName) {
            return;
        }

        campusSearchInput.value = campusName;
        queryCampusGallery();
    }

    function returnToCampusList() {
        campusSearchInput.value = '';
        campusSearchInput.placeholder = '输入园区名称';
        currentPage = 1;
        loadCampusGalleryPage(1);
    }

    function loadCampusGalleryPage(page) {
        galleryResultsContent.innerHTML = `
            <div class="loading-container">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin icon-blue"></i>
                </div>
                <p class="loading-text">
                    <i class="fas fa-search icon-cyan"></i> 正在加载第${page}页园区数据...
                </p>
                <div class="loading-progress">
                    <div class="loading-progress-bar"></div>
                </div>
            </div>
        `;
        campusGalleryResults.style.display = 'block';
        fetch(`/files/campus-gallery?page=${page}&page_size=${pageSize}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data && data.data.communities) {
                    paginationInfo = data.pagination;
                    renderCampusGalleryWithPagination(data.data, data.pagination);
                } else {
                    galleryResultsContent.innerHTML = `<div class="message error">
                        <i class="fas fa-exclamation-triangle icon-red"></i>
                        加载园区数据失败: ${data.message || '未知错误'}
                    </div>`;
                }
            })
            .catch(error => {
                galleryResultsContent.innerHTML = `<div class="message error">
                    <i class="fas fa-exclamation-triangle icon-red"></i>
                    加载园区数据失败: ${error.message}
                </div>`;
            });
    }

    function changePage(pageNum) {
        currentPage = pageNum;
        loadCampusGalleryPage(pageNum);

        window.scrollTo({
            top: campusGalleryResults.offsetTop - 20,
            behavior: 'smooth'
        });
    }



    function initializePage() {
        const initialLoadingState = document.getElementById('initialLoadingState');
        if (initialLoadingState) {
            initialLoadingState.style.display = 'block';
        }
        fetch(`/files/campus-gallery?page=1&page_size=${pageSize}`)
            .then(response => response.json())
            .then(data => {
                if (initialLoadingState) {
                    initialLoadingState.style.display = 'none';
                }

                if (data.success && data.data && data.data.communities) {
                    currentPage = 1;
                    campusSearchInput.placeholder = '输入园区名称';
                    renderCampusGalleryWithPagination(data.data, data.pagination);
                    campusGalleryResults.style.display = 'block';
                } else {
                    galleryResultsContent.innerHTML = `<div class="message error">
                        <i class="fas fa-exclamation-triangle icon-red"></i>
                        加载园区数据失败: ${data.message || '未知错误'}
                    </div>`;
                }
            })
            .catch(() => {
                if (initialLoadingState) {
                    initialLoadingState.style.display = 'none';
                }
                galleryResultsContent.innerHTML = `<div class="message error">
                    <i class="fas fa-exclamation-triangle icon-red"></i>
                    页面初始化失败，请刷新页面重试
                </div>`;
            });
    }





    function queryCampusGallery() {
        const selectedCampus = campusSearchInput.value.trim();

        if (!selectedCampus) {
            showMessage('请选择要查询的园区', 'warning');
            return;
        }

        if (window.isRequestingCampusGallery) {
            return;
        }

        window.isRequestingCampusGallery = true;
        galleryResultsContent.innerHTML = `
            <div class="loading-container">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin icon-blue"></i>
                </div>
                <p class="loading-text">
                    <i class="fas fa-search icon-cyan"></i> 正在查询园区"${selectedCampus}"的详细信息...
                </p>
                <div class="loading-progress">
                    <div class="loading-progress-bar"></div>
                </div>
            </div>
        `;
        campusGalleryResults.style.display = 'block';

        let url = `/files/campus-gallery?community=${encodeURIComponent(selectedCampus)}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                window.isRequestingCampusGallery = false;

                if (data.success) {
                    renderSingleCampusGallery(data.data);
                } else {
                    galleryResultsContent.innerHTML = `<div class="message error">
                        <i class="fas fa-exclamation-triangle icon-red"></i>
                        ${data.message || '查询失败'}
                    </div>`;
                }
            })
            .catch(error => {
                window.isRequestingCampusGallery = false;

                galleryResultsContent.innerHTML = `<div class="message error">
                    <i class="fas fa-exclamation-triangle icon-red"></i>
                    查询失败: ${error.message}
                </div>`;
            });
    }



    function renderCampusGalleryWithPagination(data, pagination) {
        if (!data || !data.communities || data.communities.length === 0) {
            galleryResultsContent.innerHTML = '<div class="message info">没有找到任何园区图库数据</div>';
            return;
        }

        const communities = data.communities;
        const startIndex = (pagination.current_page - 1) * pagination.page_size + 1;
        const endIndex = Math.min(startIndex + communities.length - 1, pagination.total_count);

        let html = `<p><i class="fas fa-info-circle icon-blue"></i> 共找到 <strong>${pagination.total_count}</strong> 个园区的图库数据，当前显示第 <strong>${startIndex} - ${endIndex}</strong> 条</p>
                    <table class="url-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-building icon-gold"></i> 园区名称</th>
                                <th><i class="fas fa-images icon-purple"></i> 图片数量</th>
                                <th><i class="fas fa-cogs icon-green"></i> 操作</th>
                            </tr>
                        </thead>
                        <tbody>`;

        communities.forEach((community, index) => {
            const iconColorClasses = ['icon-blue', 'icon-green', 'icon-cyan', 'icon-purple', 'icon-orange', 'icon-gold'];
            const colorClass = iconColorClasses[index % iconColorClasses.length];



            html += `
                <tr>
                    <td>
                        <div class="campus-name-cell">
                            <i class="fas fa-building ${colorClass}"></i>
                            <span class="campus-name">${community.name}</span>
                        </div>
                    </td>
                    <td>
                        <span class="image-count">
                            <i class="fas fa-images icon-purple"></i>
                            ${community.image_count} 张
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-view-detail" onclick="viewCampusDetail('${community.name}')">
                                <i class="fas fa-eye icon-blue"></i> 查看详情
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += `</tbody></table>`;


        if (pagination.total_pages > 1) {
            html += renderPaginationControls(pagination);
        }

        galleryResultsContent.innerHTML = html;
    }


    function renderPaginationControls(pagination) {
        if (pagination.total_pages <= 1) {
            return '';
        }

        let html = `<div class="pagination">`;


        const prevDisabled = !pagination.has_previous;
        html += `<button ${prevDisabled ? 'disabled' : ''} onclick="changePage(${pagination.current_page - 1})">
                    <i class="fas fa-chevron-left"></i> 上一页
                </button>`;

        const maxPageButtons = 5;
        const currentPage = pagination.current_page;
        const totalPages = pagination.total_pages;

        let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
        let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);


        if (endPage - startPage + 1 < maxPageButtons && startPage > 1) {
            startPage = Math.max(1, endPage - maxPageButtons + 1);
        }


        if (startPage > 1) {
            html += `<button onclick="changePage(1)">1</button>`;
            if (startPage > 2) {
                html += `<span class="pagination-ellipsis">...</span>`;
            }
        }


        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === currentPage;
            html += `<button class="${isActive ? 'active' : ''}" onclick="changePage(${i})">${i}</button>`;
        }


        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += `<span class="pagination-ellipsis">...</span>`;
            }
            html += `<button onclick="changePage(${totalPages})">${totalPages}</button>`;
        }


        const nextDisabled = !pagination.has_next;
        html += `<button ${nextDisabled ? 'disabled' : ''} onclick="changePage(${pagination.current_page + 1})">
                    下一页 <i class="fas fa-chevron-right"></i>
                </button>`;

        html += `</div>`;
        return html;
    }


    function renderSingleCampusGallery(data) {
        if (!data || !data.images) {
            galleryResultsContent.innerHTML = '<div class="message info">没有找到该园区的图片数据</div>';
            return;
        }

        let html = `<h3><i class="fas fa-building icon-gold"></i> ${data.community} 园区详情</h3>
                    <div style="margin-bottom: 15px; padding: 10px; background-color: #f9f9f9; border-radius: 4px;">
                        <p><i class="fas fa-image icon-purple"></i> 图片总数: <strong>${data.total_image_count || 0}</strong> 张</p>
                        <p><i class="fas fa-clock icon-blue"></i> 最后更新: <strong>${data.images.length > 0 ? (new Date(data.images[0].created_at)).toLocaleString() : '未知'}</strong></p>
                    </div>`;


        html += `<h4><i class="fas fa-images icon-purple"></i> 园区图片</h4>`;

        if (data.images && data.images.length > 0) {
            html += `<div style="margin-bottom: 20px;">`;
            html += `<div style="width: 100%; margin-bottom: 15px;">
                        <h5 style="margin-bottom: 10px;"><i class="fas fa-images icon-purple"></i> 园区图片 (${data.images.length}张)</h5>
                        <div style="display: flex; flex-wrap: wrap; gap: 10px;">`;

            data.images.forEach(image => {
                    const displayUrl = image.image_url;
                    const imageFieldName = image.field_name;

                    const isPdf = displayUrl.toLowerCase().includes('.pdf') ||
                                 displayUrl.toLowerCase().includes('pdf') ||
                                 displayUrl.includes('application/pdf');

                    if (isPdf) {

                        html += `
                            <div style="position: relative; width: 150px; margin-bottom: 15px;">
                                <div style="width: 150px; height: 100px; overflow: hidden; border: 2px solid #ff4d4f; border-radius: 4px; display: flex; align-items: center; justify-content: center; background-color: #fff2f0;">
                                    <div style="text-align: center; padding: 10px;">
                                        <i class="fas fa-file-pdf icon-red" style="font-size: 24px; margin-bottom: 5px;"></i>
                                        <div style="font-size: 10px; color: #ff4d4f; font-weight: bold;">格式错误</div>
                                        <div style="font-size: 9px; color: #ff4d4f;">销售上传了PDF</div>
                                    </div>
                                </div>
                                <div style="font-size: 12px; margin-top: 5px; color: #ff4d4f;">
                                    <div>
                                        <i class="fas fa-exclamation-triangle icon-red"></i> 销售上传格式有误
                                    </div>
                                    <div style="font-size: 10px; margin-top: 2px;">
                                        <a href="${displayUrl}" target="_blank" style="color: #ff4d4f; text-decoration: none;">
                                            <i class="fas fa-download icon-red"></i> 下载PDF文件
                                        </a>
                                        ${imageFieldName ? `<button onclick="deleteImage('${data.community}', '${displayUrl}')"
                                            style="margin-left: 10px; background: #ff4d4f; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer; font-size: 10px;">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>` : ''}
                                    </div>
                                </div>
                            </div>`;
                    } else {

                        html += `
                            <div style="position: relative; width: 150px; margin-bottom: 15px;">
                                <div style="width: 150px; height: 100px; overflow: hidden; border: 1px solid #ddd; border-radius: 4px;">
                                    <img src="${displayUrl}" alt="园区图片" style="width: 100%; height: 100%; object-fit: cover;"
                                         onerror="this.parentElement.innerHTML='<div style=\\'display: flex; align-items: center; justify-content: center; height: 100%; background-color: #f5f5f5; color: #999; font-size: 12px;\\'>图片加载失败</div>'">
                                </div>
                                <div style="font-size: 12px; margin-top: 5px; color: #666;">
                                    <div>
                                        <a href="${displayUrl}" target="_blank" style="color: #1890ff; text-decoration: none;">
                                            <i class="fas fa-search-plus icon-blue"></i> 查看大图
                                        </a>
                                        ${imageFieldName ? `<button onclick="deleteImage('${data.community}', '${displayUrl}')"
                                            style="margin-left: 10px; background: #ff4d4f; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer; font-size: 10px;">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>` : ''}
                                    </div>
                                </div>
                            </div>`;
                    }
                });

            html += `</div></div>`;
        } else {
            html += `<p><i class="fas fa-exclamation-triangle icon-orange"></i> 该园区没有图片</p>`;
        }




        html += `
            <div style="margin-top: 20px;">
                <button class="secondary" onclick="returnToCampusList();">
                    <i class="fas fa-arrow-left icon-blue"></i> 返回园区列表
                </button>
            </div>`;

        galleryResultsContent.innerHTML = html;
    }



    function showMessage(message, type = 'info') {

        const existingMessages = document.querySelectorAll('.message');
        for (let i = 0; i < existingMessages.length; i++) {
            if (existingMessages[i].textContent === message) {

                return;
            }
        }

        const messageEl = document.createElement('div');
        messageEl.className = `message ${type}`;
        messageEl.textContent = message;


        const container = document.querySelector('.container') || document.body;
        container.appendChild(messageEl);

        setTimeout(() => {
             if (messageEl.parentNode) {
                 messageEl.remove();
             }
        }, 5000);
    }


    function initFlowAnimation() {

        const processBarFlow = document.querySelector('.process-bar-flow');

        if (!processBarFlow) {
            return;
        }


        processBarFlow.classList.remove('animate');


        setTimeout(() => {
            processBarFlow.classList.add('animate');
        }, 200);


        document.querySelectorAll('.process-bar-item').forEach(item => {

            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
                this.style.transition = 'transform 0.2s ease';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = '';
            });


            item.addEventListener('click', function() {
                const stepBadge = this.querySelector('.step-badge');
                if (stepBadge) {
                    const stepNumber = stepBadge.textContent;
                    if (stepNumber === '1') {

                        document.getElementById('uploadNoticeSection').scrollIntoView({behavior: 'smooth'});
                    } else if (stepNumber === '2') {

                        window.location.href = 'factory_upload.html';
                    } else if (stepNumber === '3') {

                        document.querySelector('.form-group').scrollIntoView({behavior: 'smooth'});
                    }
                }
            });
        });
    }


    initFlowAnimation();


    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {

                clearTimeout(window.flowAnimationTimeout);
                window.flowAnimationTimeout = setTimeout(() => {
                    initFlowAnimation();
                }, 100);
            }
        });
    }, { threshold: 0.3 });


    const processBarFlow = document.querySelector('.process-bar-flow');
    if (processBarFlow) {
        observer.observe(processBarFlow);
    }

    function deleteImage(community, imageUrl) {
        if (!confirm(`确定要删除这张图片吗？\n\n园区：${community}\n\n删除后无法恢复！`)) {
            return;
        }

        const deleteButtons = document.querySelectorAll(`button[onclick*="${imageUrl}"]`);
        deleteButtons.forEach(btn => {
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 删除中';
        });

        fetch('/files/campus-gallery/image', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                community: community,
                image_url: imageUrl,
                image_type: ""
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(`图片删除成功！剩余 ${data.data.remaining_count} 张图片`, 'success');

                setTimeout(() => {
                    queryCampusGallery();
                }, 1000);
            } else {
                showMessage(`删除失败：${data.message}`, 'error');

                deleteButtons.forEach(btn => {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-trash"></i> 删除';
                });
            }
        })
        .catch(error => {
            showMessage(`删除失败：${error.message}`, 'error');

            deleteButtons.forEach(btn => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-trash"></i> 删除';
            });
        });
    }

    window.viewCampusDetail = viewCampusDetail;
    window.deleteImage = deleteImage;
    window.returnToCampusList = returnToCampusList;
    window.changePage = changePage;
    window.selectCampusFromResults = selectCampusFromResults;

    setTimeout(initializePage, 100);
});