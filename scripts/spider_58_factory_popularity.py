import random
import logging
import os
import re
import asyncio
import sys
from datetime import datetime
from typing import List, Dict, Optional, Any
from bs4 import BeautifulSoup
from urllib.parse import urlparse
from playwright.async_api import async_playwright
from captcha_solver import CaptchaSolver

project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.append(project_root)
from database.db_connection import get_db_connection

class Spider58FactoryPlaywright:
    
    def __init__(self, debug=False, headless=True, auto_captcha=True,
                 retry_limit=3, wait_time=20, user_agent=None, fetch_popularity=True):
        self.city_codes = {
            "哈尔滨": "hrb", "长春": "cc", "青岛": "qd", "北京": "bj", "上海": "sh",
            "广州": "gz", "深圳": "sz", "成都": "cd", "武汉": "wh", "杭州": "hz",
            "南京": "nj", "天津": "tj", "重庆": "cq", "西安": "xa", "苏州": "su",
            "郑州": "zz", "济南": "jn", "大连": "dl", "沈阳": "sy", "厦门": "xm",
            "宁波": "nb"
        }

        self.type_codes = {
            "出租": "",
            "出售": "b5/"
        }

        self.debug = debug
        self.headless = headless
        self.auto_captcha = auto_captcha
        self.retry_limit = retry_limit
        self.wait_time = wait_time
        self.fetch_popularity = fetch_popularity

        self.db_connection = None

        if self.debug:
            self.debug_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'debug')
            os.makedirs(self.debug_dir, exist_ok=True)

        if user_agent:
            self.user_agent = user_agent
        else:
            self.user_agent = self.get_random_ua()

        self.playwright = None
        self.browser = None
        self.context = None
        self.captcha_solver = None

        self.setup_logger()
        self.init_db_connection()
    

    
    def init_db_connection(self):
        try:
            self.db_connection = get_db_connection()
        except Exception as e:
            self.logger.error(f"数据库连接失败: {str(e)}")

    def close_db_connection(self):
        if self.db_connection:
            try:
                self.db_connection.close()
            except Exception as e:
                self.logger.error(f"关闭数据库连接时出错: {str(e)}")
            finally:
                self.db_connection = None
    
    def setup_logger(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        self.logger = logging.getLogger(__name__)
    
    async def init_browser(self) -> None:
        try:
            self.playwright = await async_playwright().start()

            self.browser = await self.playwright.firefox.launch(
                headless=self.headless,
                slow_mo=100
            )

            context_options = {
                "viewport": {"width": 1280, "height": 720},
                "user_agent": self.user_agent
            }

            self.context = await self.browser.new_context(**context_options)

            cookies_str = "id58=CkwAbGgFui5Iq57dAyx2Ag==; 58tj_uuid=6a2f7a30-edc8-4e4a-b2ef-7076a212dcec; xxzlclientid=d6d01ea6-545e-4b48-91d5-*************; als=0; xxzlxxid=pfmxrzqALPGSl30xeuYMKSf7/oWjwBECxcQqWQqMryfS2+FcGPBZdYCYhXMOprys4HxK; wmda_uuid=78667bb105d5c37418f60b34f7e2fc91; wmda_visited_projects=%3B10104579731767%3B24125085974325%3B2286118353409%3B18223363302321%3B2385390625025%3B3381039819650%3B10657128711987; city=qd; 58home=qd; 58uname=drhvhkz0l; passportAccount=atype=0&bstate=0; fzq_h=66966fd30c90c57b348699929ee3e299_1746359022864_a8a7f0594eab43fb988c424fef6dc4e8_1875372777; commontopbar_new_city_info=122%7C%E9%9D%92%E5%B2%9B%7Cqd; commontopbar_ipcity=bj%7C%E5%8C%97%E4%BA%AC%7C0; f=n; new_uv=30; utm_source=; spm=; new_session=0; init_refer=; wmda_session_id_2385390625025=*************-2fcac376-1628-4e48-a183-2239334c1323; wmda_report_times=2; ppStore_fingerprint=52D4F63C147E43589C80AB3FCF6FEC18D899455F238EB700%EF%BC%***************; xxzlbbid=pfmbM3wxMDI5MnwxLjEwLjB8MTc0NjUxODExODU5MDk4MzI1NXx6QTdraDU4K1JFK00vR0xUV2FVWVFYdGdYNkxqY3MxR1ZVb1AzVUgvQ0tnPXxiMGM2N2QxN2M5ZjM3OTJhNWIwMDkzMmM3NThiYzNjYV8xNzQ2NTE4MTE2MDI4XzFkNGQyZmJkY2NkZjQ4NWI4YjdmODE4NDQzYWVlNzViXzIwODg3NTE0NjB8NGE5NTdiYTBjYjA5ZTUxODg2YmYzMzc0MDFlM2ExYTRfMTc0NjUxODExNTk4Nl8yNTY=; PPU=\"UID=**************&UN=drhvhkz0l&TT=ca419b30c3281a35e0ad257ee7def858&PBODY=gq8bHxqyGbXokicEFeILsJTsp6CwwDRRzSJ96NoGFru39KPdSimaIGyd_B07Qp1MGR7dfa8vLtOc9jtV0YUKuMYa0aJ0MwT7tVi_EWCrGldvkcU_-9UWf0FRq4RRm1MILGTj9jZnBKMW0jm6rVsOsXKgX-bEx8Zg1pNEl5zJ8L8&VER=1&CUID=Qj_mrBIeBe0FXMkfM1CXnQ\""

            cookie_list = []
            for cookie_item in cookies_str.split(';'):
                if not cookie_item.strip():
                    continue
                name, value = cookie_item.strip().split('=', 1)
                cookie_list.append({
                    "name": name.strip(),
                    "value": value.strip(),
                    "domain": ".58.com",
                    "path": "/"
                })

            await self.context.add_cookies(cookie_list)
            self.captcha_solver = CaptchaSolver(debug=self.debug)

        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {str(e)}")
            if self.browser:
                try: await self.browser.close()
                except: pass
                self.browser = None

            if self.playwright:
                try: await self.playwright.stop()
                except: pass
                self.playwright = None
            raise
    
    async def close_browser(self) -> None:
        try:
            if self.context:
                await self.context.close()
                self.context = None
            if self.browser:
                await self.browser.close()
                self.browser = None
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
        except Exception as e:
            self.logger.error(f"关闭浏览器失败: {str(e)}")
    
    def get_random_ua(self) -> str:
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36 Edg/113.0.1774.42',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/112.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/113.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/112.0',
        ]
        return random.choice(user_agents)
    
    def get_factory_list_url(self, city_code: str, page: int = 1, factory_type: str = "出租") -> str:
        type_code = self.type_codes.get(factory_type, "")
        if page == 1:
            return f"https://{city_code}.58.com/changfang/{type_code}"
        return f"https://{city_code}.58.com/changfang/{type_code}pn{page}/"
    

    
    async def fetch_page(self, url: str, max_retries: int = None, city_code: str = None) -> Optional[str]:
        if not self.browser:
            try:
                await self.init_browser()
            except Exception as e:
                self.logger.error(f"浏览器初始化失败，无法继续: {str(e)}")
                return None

        if max_retries is None:
            max_retries = self.retry_limit

        if not city_code:
            parsed_url = urlparse(url)
            domain_parts = parsed_url.netloc.split('.')
            city_code = domain_parts[0] if len(domain_parts) > 0 else "unknown"
        
        retries = 0
        
        while retries < max_retries:
            page = None
            try:
                page = await self.context.new_page()
                page.set_default_timeout(60000)
                await asyncio.sleep(random.uniform(1, 3))

                response = await page.goto(url, wait_until="domcontentloaded", timeout=60000)
                await asyncio.sleep(random.uniform(1, 2))
                
                if not response:
                    if page:
                        try:
                            await page.close()
                        except:
                            pass
                    retries += 1
                    continue

                if not response.ok:
                    if page:
                        try:
                            await page.close()
                        except:
                            pass
                    retries += 1
                    continue

                current_url = page.url
                
                page_title = await page.title()
                is_captcha_page = ("callback.58.com/antibot" in current_url or
                                  page_title == "验证码" or
                                  "验证" in page_title)

                if is_captcha_page:
                    self.logger.warning(f"遇到验证码页面: {current_url}")
                    
                    if self.auto_captcha:
                        captcha_solved = await self.captcha_solver.solve_captcha(page)

                        if captcha_solved:
                            await asyncio.sleep(3)

                            new_url = page.url
                            new_title = await page.title()
                            if "callback.58.com/antibot" in new_url or "验证" in new_title:
                                try:
                                    await page.close()
                                except:
                                    pass
                                retries += 1
                                await asyncio.sleep(random.uniform(5, 10))
                                continue
                        else:
                            try:
                                await page.close()
                            except:
                                pass
                            retries += 1
                            await asyncio.sleep(random.uniform(5, 10))
                            continue
                    else:
                        try:
                            await page.close()
                        except:
                            pass
                        retries += 1
                        await asyncio.sleep(random.uniform(5, 10))
                        continue
                
                try:
                    await page.evaluate("window.scrollTo(0, document.body.scrollHeight/2)")
                    await asyncio.sleep(1)
                    await page.evaluate("window.scrollTo(0, 0)")
                    await asyncio.sleep(2)

                    html_content = await page.content()

                    try:
                        await page.close()
                    except:
                        pass

                    return html_content
                except Exception as e:
                    self.logger.error(f"获取页面内容失败: {str(e)}")
                    if page:
                        try:
                            await page.close()
                        except:
                            pass
            except Exception as e:
                self.logger.error(f"请求异常: {str(e)}")
                if page:
                    try:
                        await page.close()
                    except:
                        self.logger.warning("关闭页面出错，但继续执行")
            
            retries += 1
            retry_delay = random.uniform(5, 10)
            await asyncio.sleep(retry_delay)
        
        self.logger.error(f"请求失败，已达到最大重试次数: {url}")
        return None
    
    def parse_factory_list(self, html: str, factory_type: str = "出租") -> List[Dict]:
        if not html:
            return []
            
        factory_list = []
        soup = BeautifulSoup(html, 'html.parser')

        factory_items = soup.select('ul.house-list-wrap > li')
        
        if len(factory_items) == 0:
            for selector in [
                '.list > .listUl > li',
                '.listUl > li',
                '.house-list-wrap > li',
                '.content-side-left .listUl > li'
            ]:
                factory_items = soup.select(selector)
                if len(factory_items) > 0:
                    break

            if len(factory_items) == 0:
                all_li = soup.select('li')
                factory_items = [li for li in all_li if any(keyword in li.get_text() for keyword in ["厂房", "仓库", "出租", "出售"])]
        
        for item in factory_items:
            try:
                if 'id' in item.attrs and 'ad_' in item['id']:
                    continue

                links = item.select('a[href]')
                url = None

                for link in links:
                    href = link.get('href', '')
                    if href and ('x.shtml' in href or 'changfang' in href):
                        url = href
                        break

                if url:
                    factory_info = {
                        "type": factory_type,
                        "url": url
                    }

                    id_match = re.search(r'/(\d+)x\.shtml', url)
                    if id_match:
                        factory_info['id'] = id_match.group(1)

                    factory_list.append(factory_info)
            except Exception as e:
                self.logger.error(f"解析厂房项目URL异常: {str(e)}")

        return factory_list
    
    def get_total_pages(self, html: str) -> int:
        if not html: return 0

        soup = BeautifulSoup(html, 'html.parser')
        page_info = soup.select('.pager a') or soup.select('.page a') or soup.select('.pager a, .page a')

        if not page_info:
            pagination_elem = soup.select_one('.pager') or soup.select_one('.page')
            if pagination_elem:
                pagination_text = pagination_elem.get_text(strip=True)
                page_matches = re.findall(r'共(\d+)页', pagination_text)
                if page_matches:
                    try: return int(page_matches[0])
                    except: pass
            return 1

        max_page = 1
        for page in page_info:
            try:
                page_text = page.get_text(strip=True)
                if not page_text.isdigit(): continue
                page_num = int(page_text)
                if page_num > max_page: max_page = page_num
            except: continue

        return max_page
    
    async def fetch_detail_page_popularity(self, url: str, max_retries: int = None) -> Optional[Dict[str, Any]]:
        if not url:
            return None

        post_id = re.search(r'/(\d+)x\.shtml', url)
        post_id = post_id.group(1) if post_id else None

        try:
            html = await self.fetch_page(url, max_retries)
            if not html:
                return None

            detail_data = self.parse_detail_page_popularity(html, post_id, url)
            return detail_data
        except Exception as e:
            self.logger.error(f"获取详情页数据异常: {e}")
            return None
    
    def parse_detail_page_popularity(self, html: str, post_id: str = None, url: str = None) -> Dict[str, Any]:
        if not html:
            return {}
            
        result = {
            "post_id": post_id,
            "url": url,
        }
        
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            image_num = 0
            title_tag = soup.find('title')
            if title_tag:
                title_text = title_tag.get_text(strip=True)
                image_match = re.search(r'[\[【](\d+)图[\]】]', title_text)
                if image_match:
                    image_num = int(image_match.group(1))

            result["image_num"] = image_num

            title_selectors = [
                'div.house-title h1', 'h1.c_000', '.house-title h1',
                'h1.f20', '.c_000.f20', '.title_content h1'
            ]

            title = None
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    if title:
                        break

            if not title:
                title = title_tag.get_text(strip=True) if title_tag else "无标题"
                title = re.sub(r'[\[【]\d+图[\]】]\s*', '', title)

            result["title"] = title
            
            wb_update_at = None
            update_span = soup.select_one('span.up')
            if update_span:
                update_text = update_span.get_text(strip=True)
                date_match = re.search(r'(\d{4}-\d{2}-\d{2})', update_text)
                if date_match:
                    wb_update_at = date_match.group(1)

            result["wb_update_at"] = wb_update_at
            
            qu = ""
            house_basic_elem = soup.select_one('div.house_basic_title_info_2')
            if house_basic_elem:
                p_text = house_basic_elem.get_text(strip=True)
                district_match = re.search(r'([\u4e00-\u9fff]{1,4}区)(?:\s*-|$|\s)', p_text)
                if district_match:
                    qu = district_match.group(1).strip()

            if not qu and house_basic_elem:
                p_tag = house_basic_elem.select_one('p')
                if p_tag:
                    p_inner_text = p_tag.get_text(strip=True)
                    if "区" in p_inner_text:
                        district_matches = re.findall(r'([\u4e00-\u9fff]{1,4}区)', p_inner_text)
                        if district_matches:
                            qu = district_matches[0]

            if qu:
                if qu in ["商区", "校区", "园区", "厂区", "景区", "地区"]:
                    qu = ""

            result["qu"] = qu
            
            description = ""
            for selector in ['.des-item article.detail', 'article.detail', '.general-item-wrap', '.des']:
                desc_elems = soup.select(selector)
                if desc_elems:
                    description = "\n".join([elem.get_text(strip=True) for elem in desc_elems])
                    if description:
                        break

            result["description"] = description

            popularity = 0
            for elem in soup.find_all(['span', 'em', 'div', 'a']):
                text = elem.get_text(strip=True)
                if '人气' in text or '收藏' in text:
                    num_match = re.search(r'\d+', text)
                    if num_match:
                        popularity = int(num_match.group())
                        break

            result["popularity"] = popularity
            
            poster_name = ""
            poster_elem = soup.select_one('.detail-info .name-text')
            if poster_elem:
                poster_name = poster_elem.get_text(strip=True)
                if poster_name and poster_name != '=' and poster_name != '$0':
                    pass

            if not poster_name or poster_name == '=' or poster_name == '$0':
                for selector in ['.poster-name span.name-text', '.user-info .username', '.poster-name']:
                    poster_elem = soup.select_one(selector)
                    if poster_elem:
                        poster_name = poster_elem.get_text(strip=True)
                        if poster_name and poster_name != '=' and poster_name != '$0':
                            break

            if not poster_name or poster_name == '=' or poster_name == '$0':
                for elem in soup.find_all(['span', 'div', 'p']):
                    text = elem.get_text(strip=True)
                    if ('联系人' in text or '发布人' in text) and (':' in text or '：' in text):
                        name_part = re.split(r'[：:]', text, 1)[1].strip()
                        if name_part and name_part != '=' and name_part != '$0':
                            poster_name = name_part
                            break

            result["poster_name"] = poster_name if poster_name and poster_name != '=' and poster_name != '$0' else ""
            
            company = ""
            company_elem = soup.select_one('.poster-company-4')
            if company_elem:
                company = company_elem.get_text(strip=True)
                if company and company != '=' and company != '$0':
                    pass

            if not company or company == '=' or company == '$0':
                for selector in ['div.poster-company-4', '.company-name', '.comp-name']:
                    company_elem = soup.select_one(selector)
                    if company_elem:
                        company = company_elem.get_text(strip=True)
                        if company and company != '=' and company != '$0':
                            break

            if not company or company == '=' or company == '$0':
                for elem in soup.find_all(['span', 'div', 'p']):
                    text = elem.get_text(strip=True)
                    if '公司' in text and (':' in text or '：' in text):
                        company_part = re.split(r'[：:]', text, 1)[1].strip()
                        if company_part and company_part != '=' and company_part != '$0':
                            company = company_part
                            break

            result["company"] = company if company and company != '=' and company != '$0' else ""

            return result
        except Exception as e:
            self.logger.error(f"解析详情页数据异常: {e}")
            return result
    
    async def crawl_city(self, city_name: str) -> List[Dict]:
        if city_name not in self.city_codes:
            self.logger.error(f"不支持的城市: {city_name}")
            return []

        city_code = self.city_codes[city_name]
        self.logger.info(f"开始爬取 {city_name}({city_code}) 的厂房信息")
        all_factories = []

        try:
            tasks = []
            for factory_type in ["出租", "出售"]:
                task = self.crawl_factory_type(city_name, city_code, factory_type)
                tasks.append(task)

            results = await asyncio.gather(*tasks)

            for result in results:
                all_factories.extend(result)

            self.logger.info(f"完成爬取 {city_name} 的厂房信息，共 {len(all_factories)} 条数据")

        except Exception as e:
            self.logger.error(f"爬取城市数据异常: {e}")

        return all_factories

    async def crawl_factory_type(self, city_name: str, city_code: str, factory_type: str) -> List[Dict]:
        max_posts_to_crawl = 100
        type_crawled = 0
        type_factories = []

        self.logger.info(f"开始爬取 {city_name} 的{factory_type}厂房信息")

        if not self.browser:
            try:
                await self.init_browser()
            except Exception as e:
                self.logger.error(f"浏览器初始化失败: {str(e)}")
                return type_factories

        list_url = self.get_factory_list_url(city_code, page=1, factory_type=factory_type)
        self.logger.info(f"获取列表页: {list_url}")

        html = await self.fetch_page(list_url, city_code=city_code)
        if not html:
            self.logger.error(f"获取列表页失败: {list_url}")
            return type_factories

        factory_list = self.parse_factory_list(html, factory_type)
        if not factory_list:
            self.logger.error(f"未找到厂房信息: {list_url}")
            return type_factories

        self.logger.info(f"找到 {len(factory_list)} 个{factory_type}厂房信息")

        for factory in factory_list:
            if type_crawled >= max_posts_to_crawl:
                self.logger.info(f"已达到{factory_type}厂房信息爬取上限({max_posts_to_crawl}个)，停止爬取")
                break

            if self.fetch_popularity and 'url' in factory:
                self.logger.info(f"获取第 {type_crawled+1}/{min(len(factory_list), max_posts_to_crawl)} 个{factory_type}帖子详情数据")
                detail_data = await self.fetch_detail_page_popularity(factory['url'])
                if detail_data:
                    for key in ['popularity', 'title', 'description', 'poster_name', 'company', 'qu', 'image_num', 'wb_update_at']:
                        if key in detail_data and detail_data[key]:
                            factory[key] = detail_data[key]

                    self.logger.info(f"更新详情数据: 标题={factory.get('title', '')[:20]}..., "
                                   f"人气={factory.get('popularity', 0)}, "
                                   f"区={factory.get('qu', '')}, "
                                   f"图片数量={factory.get('image_num', 0)}")

            factory['city_name'] = city_name
            type_factories.append(factory)
            type_crawled += 1

            await asyncio.sleep(random.uniform(1, 3))

        self.logger.info(f"完成爬取 {city_name} 的{factory_type}厂房信息，共 {type_crawled} 条数据")
        return type_factories
    
    def save_to_database(self, data: List[Dict]) -> int:
        if not data:
            self.logger.warning("没有数据需要保存到数据库")
            return 0
            
        if not self.db_connection:
            try:
                self.init_db_connection()
            except:
                self.logger.error("无法建立数据库连接，无法保存数据")
                return 0
        
        cursor = None
        inserted_count = 0
        
        try:
            cursor = self.db_connection.cursor()

            for factory_data in data:
                area = factory_data.get('city_name', '')
                listing_type = 0 if factory_data.get('type') == '出租' else 1
                title = factory_data.get('title', '')
                description = factory_data.get('description', '')
                popularity = factory_data.get('popularity', 0)
                poster_name = factory_data.get('poster_name', '')
                company = factory_data.get('company', '')
                qu = factory_data.get('qu', '')
                image_num = factory_data.get('image_num', 0)
                wb_update_at = factory_data.get('wb_update_at')

                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                url = factory_data.get('url', '')
                if url:
                    check_sql = "SELECT id FROM bidding_data WHERE description LIKE %s LIMIT 1"
                    cursor.execute(check_sql, [f"%{url}%"])
                    if cursor.fetchone():
                        continue

                insert_sql = """
                INSERT INTO bidding_data
                (area, listing_type, title, description, popularity, poster_name, company, qu, created_at,
                 image_num, wb_update_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                try:
                    cursor.execute(insert_sql, (
                        area,
                        listing_type,
                        title[:100] if title else '',
                        description + f"\n原始链接: {url}",
                        popularity,
                        poster_name[:50] if poster_name else '',
                        company[:100] if company else '',
                        qu[:50] if qu else '',
                        current_time,
                        image_num,
                        wb_update_at
                    ))
                    inserted_count += 1
                except Exception as e:
                    self.logger.error(f"插入数据失败: {e}")

            self.db_connection.commit()
            self.logger.info(f"成功保存 {inserted_count} 条厂房数据到数据库")
            
        except Exception as e:
            self.logger.error(f"保存数据到数据库过程中发生错误: {e}")
            if self.db_connection:
                try:
                    self.db_connection.rollback()
                except:
                    pass
        finally:
            if cursor:
                cursor.close()
        
        return inserted_count
    
    async def run_async(self, cities: List[str] = None) -> None:
        if not cities:
            cities = list(self.city_codes.keys())

        try:
            await self.init_browser()
            all_data = []

            for city in cities:
                try:
                    if city not in self.city_codes:
                        continue

                    factories = await self.crawl_city(city)
                    if factories:
                        all_data.extend(factories)
                    else:
                        self.logger.warning(f"{city} 未获取到数据")

                    if city != cities[-1]:
                        wait_seconds = random.uniform(self.wait_time, self.wait_time * 2)
                        self.logger.info(f"等待 {wait_seconds:.1f} 秒后处理下一个城市...")
                        await asyncio.sleep(wait_seconds)
                except Exception as e:
                    self.logger.error(f"爬取 {city} 时发生异常: {str(e)}")

            if all_data:
                self.logger.info(f"开始保存 {len(all_data)} 条数据到数据库...")
                self.save_to_database(all_data)
            else:
                self.logger.warning("没有数据需要保存到数据库")

        finally:
            await self.close_browser()
            self.close_db_connection()

    def run(self, cities: List[str] = None) -> None:
        asyncio.run(self.run_async(cities))

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='58同城厂房信息爬虫')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    parser.add_argument('--headless', type=bool, default=True, help='无头模式')
    parser.add_argument('--cities', nargs='+', help='指定要爬取的城市')

    args = parser.parse_args()

    spider = Spider58FactoryPlaywright(
        debug=args.debug,
        headless=args.headless,
        auto_captcha=True
    )

    spider.run(cities=args.cities)

if __name__ == "__main__":
    main() 