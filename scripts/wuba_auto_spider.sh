#!/bin/bash

# 自动推送厂房数据的运行脚本
# 用法: ./wuba_auto_spider.sh

# 获取脚本所在目录
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
cd "$SCRIPT_DIR"

# 设置Python路径
export PYTHONPATH=$PYTHONPATH:$(dirname "$SCRIPT_DIR")

# 创建logs目录
LOGS_DIR="$(dirname "$SCRIPT_DIR")/logs"
mkdir -p "$LOGS_DIR"

# 设置日志文件（使用固定文件名）
LOG_FILE="$LOGS_DIR/auto_spider.log"

# 确保Python脚本在正确的工作目录下运行
# 获取当前时间
CURRENT_TIME=$(date +"[%Y-%m-%d %H:%M:%S]")

# 写入启动信息到日志
echo "$CURRENT_TIME 开始自动爬取58同城厂房数据任务..." | tee -a "$LOG_FILE"

# 激活conda环境
# source conda 配置
source /root/miniconda3/etc/profile.d/conda.sh

# 激活指定环境
echo "$CURRENT_TIME 正在激活 conda industrial_chain 环境..." | tee -a "$LOG_FILE"
conda activate industrial_chain

# 运行Python脚本并捕获所有输出
cd "$SCRIPT_DIR"
python "$SCRIPT_DIR/wuba_spider.py" 2>&1 | tee -a "$LOG_FILE"

# 检查执行结果
RESULT=${PIPESTATUS[0]}
CURRENT_TIME=$(date +"[%Y-%m-%d %H:%M:%S]")

if [ $RESULT -eq 0 ]; then
    echo "$CURRENT_TIME 自动爬取任务成功完成" | tee -a "$LOG_FILE"
    exit 0
else
    echo "$CURRENT_TIME 自动爬取任务执行失败，退出码: $RESULT" | tee -a "$LOG_FILE"
    exit 1
fi 