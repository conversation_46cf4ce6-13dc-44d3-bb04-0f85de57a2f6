import redis
import requests
import re
import sys

# 连接到Redis
redis_conn = redis.StrictRedis(
    host="**************",
    port=6379,
    password="Meiyoumima333.",
    decode_responses=True
)

# Redis中使用的键名
PROXY_KEY = "factory_proxies"

# 从API获取代理IP列表
def get_proxies_from_api():
    api_url = "http://proxy.siyetian.com/apis_get.html?token=AesJWLNR1Y61keJdXTqV1dORVQ61keRpXT31STqFUeORVQx0ERNpXTqV0dOpWV41keFdnTEtmM.AMzUTMwQDM1cTM&limit=10&type=0&time=&split=1&split_text="
    try:
        response = requests.get(api_url)
        if response.status_code == 200:
            # 打印原始响应内容以便调试
            print(f"API原始响应内容: {response.text}")
            
            # 处理可能包含<br />标签的响应
            content = response.text
            
            # 如果响应包含<br />标签，按此分割
            if "<br />" in content:
                proxies_raw = content.split("<br />")
            else:
                # 否则按行分割
                proxies_raw = content.splitlines()
            
            # 清理并验证每个代理
            valid_proxies = []
            for proxy in proxies_raw:
                proxy = proxy.strip()
                if not proxy:
                    continue
                
                # 验证代理格式 (IP:PORT)
                if re.match(r'^\d+\.\d+\.\d+\.\d+:\d+$', proxy):
                    valid_proxies.append(proxy)
                    print(f"找到有效代理: {proxy}")
                else:
                    print(f"忽略无效的代理格式: {proxy}")
            
            return valid_proxies
        else:
            print(f"API请求失败，状态码: {response.status_code}")
            return []
    except Exception as e:
        print(f"获取代理时发生错误: {e}")
        return []

# 清理Redis中的所有代理
def clear_proxies():
    try:
        # 获取清理前的代理数量
        before_count = redis_conn.scard(PROXY_KEY)
        
        # 删除键
        redis_conn.delete(PROXY_KEY)
        
        print(f"已清空Redis中的{PROXY_KEY}键，清除了{before_count}个代理")
        return True
    except Exception as e:
        print(f"清空代理时发生错误: {e}")
        return False

# 打印所有代理
def print_all_proxies():
    try:
        all_proxies = redis_conn.smembers(PROXY_KEY)
        total_count = len(all_proxies)
        
        if total_count == 0:
            print(f"{PROXY_KEY}为空")
            return
        
        print(f"\n当前{PROXY_KEY}中的所有代理({total_count}个):")
        for i, proxy in enumerate(all_proxies, 1):
            print(f"{i}) \"{proxy}\"")
    except Exception as e:
        print(f"获取代理列表时发生错误: {e}")

def main():
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "list":
        print_all_proxies()
        return
    
    if len(sys.argv) > 1 and sys.argv[1] == "clear":
        clear_proxies()
        return

    try:
        # 先清空现有代理
        clear_proxies()
        
        # 从API获取代理
        proxies = get_proxies_from_api()
        
        if not proxies:
            print("未能从API获取有效的代理IP，请检查API连接或返回格式")
            exit(1)
        
        print(f"从API获取到 {len(proxies)} 个有效代理:")
        for proxy in proxies:
            print(f"- {proxy}")
        
        # 将新代理添加到Redis
        added_count = redis_conn.sadd(PROXY_KEY, *proxies)
        
        # 获取集合中的总代理数
        total_count = redis_conn.scard(PROXY_KEY)
        
        print(f"成功添加 {added_count} 个新代理IP到{PROXY_KEY}集合")
        print(f"当前{PROXY_KEY}集合中共有 {total_count} 个代理")
        
        # 显示所有代理
        print_all_proxies()
        
    except Exception as e:
        print(f"添加代理时发生错误: {e}")

if __name__ == "__main__":
    main()