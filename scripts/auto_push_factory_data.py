#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import time
import sys
import os
import base64
import random
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional

# 确保logs目录存在
logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
if not os.path.exists(logs_dir):
    os.makedirs(logs_dir)

# 配置日志（只输出到控制台，不写入文件）
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("auto_push")

# 导入数据库连接和业务逻辑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database.db_connection import db_cursor
# 避免循环导入
from api.push_routes import get_push_service
from api.client import AsyncYoutuiApiClient

# 网站ID和名称映射表
WEBSITE_NAMES = {
    '111': '58免费三网合一版本',
    '119': '搜房帮',
    '220': '百度乐居',
    '236': '搜狐焦点新端口',
    '238': '酷房网',
    '382': '网易房产',
    '402': '安居客三网合一版本',
    '403': '58付费三网合一版本',
    '404': '赶集付费三网合一版本',
    '414': '网易租房',
    '422': '今日头条房产',
    '514': '优房网付费版本',
    '521': '安居客VIP端口',
}

class AutoPushService:
    """厂房数据自动推送服务"""

    def __init__(self):
        """初始化自动推送服务"""
        pass

    def get_factory_list(self) -> List[Dict[str, Any]]:
        """
        获取需要推送的厂房数据列表

        返回:
            厂房数据列表
        """
        try:
            # 直接查询数据库获取未推送且已发布到优推的厂房数据
            with db_cursor() as cursor:
                cursor.execute("""
                    SELECT id, erp_id, youtui_house_id, city, city_name
                    FROM parsed_factory_data
                    WHERE ts_status = 0
                    AND youtui_house_id IS NOT NULL
                    AND youtui_house_id != ''
                    AND is_deleted = 0
                    AND (port_type = 'free_rent' or port_type='free_sale')
                    LIMIT 100
                """)
                factory_list = cursor.fetchall()

                logger.info(f"从数据库获取到 {len(factory_list)} 条待推送厂房数据")
                return factory_list
        except Exception as e:
            logger.error(f"获取厂房列表出错: {str(e)}")
            return []

    def get_available_websites(self, factory_id: str) -> Dict[str, Any]:
        """
        获取指定厂房数据可推送的网站列表

        参数:
            factory_id: 厂房数据ID

        返回:
            可用网站列表和网站名称
        """
        try:
            # 查询厂房数据获取城市信息
            with db_cursor() as cursor:
                cursor.execute("""
                    SELECT city, city_name
                    FROM parsed_factory_data
                    WHERE id = %s AND is_deleted = 0
                """, [factory_id])

                factory_data = cursor.fetchone()

                if not factory_data:
                    logger.error(f"未找到ID为 {factory_id} 的厂房数据")
                    return {"websites": [], "website_names": {}}

                # 获取城市信息
                city = factory_data["city"]
                city_name = factory_data["city_name"]

                # 创建推送服务实例
                service = get_push_service(city=city, city_name=city_name)

                # 获取可用网站列表
                raw_websites = service.get_website_accounts(
                    service.default_user_id,
                    service.default_user_key
                )

                logger.info(f"获取到的网站列表原始数据: {raw_websites}")

                if raw_websites:
                    # 处理所有网站数据
                    filtered_websites = []
                    website_names = {}

                    for website in raw_websites:
                        web_id = str(website.get('webID'))

                        # 获取账号信息和推广类型
                        user_name_arr = website.get('userNameArr', {})
                        act_arr = website.get('actArr', {})

                        # 创建新的网站配置
                        new_website = {
                            "webID": web_id,
                            "userNameArr": user_name_arr,
                        }

                        # 处理推广类型
                        if isinstance(act_arr, dict):
                            new_website["actArr"] = act_arr
                        else:
                            new_website["actArr"] = {"00": "上架"}

                        filtered_websites.append(new_website)
                        website_names[web_id] = WEBSITE_NAMES.get(web_id, f"网站{web_id}")

                    return {
                        "websites": filtered_websites,
                        "website_names": website_names
                    }
                else:
                    logger.error(f"未获取到城市 '{city}' 的可用网站列表")
                    return {"websites": [], "website_names": {}}
        except Exception as e:
            logger.error(f"获取可用网站列表出错: {str(e)}")
            return {"websites": [], "website_names": {}}

    async def push_factory_to_website(self, factory_id: str, website_info: Dict[str, Any]) -> bool:
        """
        推送厂房数据到指定网站

        参数:
            factory_id: 厂房数据ID
            website_info: 网站信息

        返回:
            是否推送成功
        """
        try:
            website_id = str(website_info.get("webID"))

            # 验证 website_info 格式
            if not isinstance(website_info, dict) or 'webID' not in website_info or 'actArr' not in website_info:
                logger.error("无效的网站账号信息格式")
                return False

            # 查询厂房数据
            with db_cursor() as cursor:
                cursor.execute("""
                    SELECT
                        id, erp_id, youtui_house_id, city, city_name, port_type,
                        topic, zone, street, type, towards, community, content,
                        total, square, address, type4property, floor, floors,
                        floortype, Storey, rentunitdaily, slease, Planteia,
                        Plantstructure, Typewarehouse, Plantnew, isNewHouse,
                        AgentFree, Name, Tel, ExternalTag, ts_status, infrastructure,
                        thumb, floorplans, photointerior, photooutdoor
                    FROM parsed_factory_data
                    WHERE id = %s AND is_deleted = 0
                """, [factory_id])

                factory_data = cursor.fetchone()

                if not factory_data:
                    logger.error(f"未找到ID为 {factory_id} 的厂房数据")
                    return False

                # 获取城市信息和房源ID
                city = factory_data["city"]
                city_name = factory_data["city_name"]
                erp_id = factory_data["erp_id"]
                youtui_house_id = factory_data["youtui_house_id"]

                if not youtui_house_id:
                    logger.error("该厂房数据尚未发布，请先发布后再推送")
                    return False

                # 创建推送服务实例（用于获取默认用户ID）
                service = get_push_service(city=city, city_name=city_name)

                # 创建异步API客户端
                async_client = AsyncYoutuiApiClient(environment='production', city=city)

                # 构建推送数据
                push_data = {
                    'platformID': '10007',  # 平台ID
                    'companyID': '1013',    # 公司ID
                    'userID': service.default_user_id,  # 用户ID
                    'act': 'PUSH',          # 动作类型
                    'ttid': youtui_house_id,  # 优推ID
                    'dbug': 'TRUE',         # 调试模式
                    'id': erp_id,           # ERP系统ID
                    'topic': factory_data['topic'],  # 标题
                    'zone': factory_data['zone'],  # 区域
                    'street': factory_data['street'],  # 街道
                    'type': factory_data['type'],  # 租售类型
                    'community': factory_data['address'],  # 园区名称
                    'content': factory_data['content'],  # 描述
                    'total': factory_data['total'],  # 价格
                    'square': factory_data['square'],  # 面积
                    'address': factory_data['address'],  # 地址
                    'type4property': factory_data['type4property'],  # 建筑类型
                    'floor': factory_data['floor'],  # 楼层
                    'floortype': factory_data['floortype'],  # 楼层类型
                    'Storey': factory_data['Storey'],  # 首层层高
                    'webid': website_id,  # 网站ID
                }

                # 添加可选字段（只有当字段有值时才添加）
                optional_fields = [
                    'towards', 'floors', 'rentunitdaily', 'slease', 'Planteia',
                    'Plantstructure', 'Typewarehouse', 'Plantnew', 'isNewHouse',
                    'AgentFree', 'infrastructure', 'Name', 'Tel', 'ExternalTag'
                ]

                for field in optional_fields:
                    if factory_data.get(field):  # 只有当字段存在且不为空时才添加
                        push_data[field] = factory_data[field]

                # 添加图片字段（只有当URL存在时才添加）
                image_fields_from_db = {
                    'thumb': factory_data.get('thumb'),
                    'floorplans': factory_data.get('floorplans'),
                    'photointerior': factory_data.get('photointerior'),
                    'photooutdoor': factory_data.get('photooutdoor')
                }

                for field, url_string in image_fields_from_db.items():
                    if url_string: # 只有当从数据库获取的 URL 字符串存在时才添加
                        push_data[field] = url_string

                # 从可用账号中随机选择一个账号
                available_accounts = {}
                user_name_arr = website_info.get('userNameArr', {})
                for account_id, account_info in user_name_arr.items():
                    # account_info格式为: [用户名, 密码, 状态]
                    if len(account_info) >= 3 and account_info[2] == "可用":
                        available_accounts[account_id] = account_info

                if not available_accounts:
                    logger.error(f"网站 {website_id} 没有可用的账号")
                    return False

                # 随机选择一个可用账号
                random_account_id = random.choice(list(available_accounts.keys()))
                random_account = available_accounts[random_account_id]

                logger.info(f"从 {len(available_accounts)} 个可用账号中随机选择了账号: {random_account[0]}")

                # 生成新的webcontent格式
                new_webarr = {
                    "webID": website_id,
                    "actArr": {random_account_id: "00"}  # 00代表"上架"操作
                }

                webcontent_data = {"webarr": [new_webarr]}
                webcontent_json = json.dumps(webcontent_data, ensure_ascii=False)
                push_data['webcontent'] = base64.b64encode(webcontent_json.encode('utf-8')).decode('utf-8')

                logger.info(f"生成的 webcontent JSON: {webcontent_json}")
                logger.info(f"生成的 webcontent base64: {push_data['webcontent']}")

                # 调用异步推送接口
                result = await async_client.async_sync_house(
                    user_id=service.default_user_id,
                    house_data=push_data,
                    action='PUSH',
                    ttid=youtui_house_id
                )

                if not result:
                    logger.error("推送房源失败，未收到响应")
                    return False

                # 解析推送结果
                if result.get('status') == '1':
                    process_info = result.get('processinfo')
                    house_id = result.get('houseid') or result.get('houseId') or result.get('HOUSEID')

                    logger.info(f"房源推送请求成功 - 优推科技房源ID: {house_id}")
                    logger.info(f"推送进程ID: {process_info}")

                    # 更新推送状态
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    cursor.execute("""
                        UPDATE parsed_factory_data
                        SET ts_status = 1,
                            update_time = %s
                        WHERE id = %s
                    """, [current_time, factory_id])

                    # 保存推送历史记录
                    cursor.execute("""
                        INSERT INTO push_history (
                            push_time, erp_house_id, tt_id, website_id,
                            website_name, city, process_id, account_id
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s
                        )
                    """, [
                        current_time,
                        factory_data["erp_id"],
                        factory_data["youtui_house_id"],
                        website_id,
                        WEBSITE_NAMES.get(website_id, f"网站{website_id}"),
                        factory_data["city"],
                        process_info,
                        random_account_id  # 使用账号ID而不是手机号
                    ])

                    return True
                else:
                    logger.error(f"推送失败 - 状态: {result.get('status')}")
                    logger.error(f"错误信息: {result.get('msg')}")
                    return False

        except Exception as e:
            logger.error(f"推送厂房数据出错: {str(e)}")
            return False

    async def run(self, max_retries: int = 3, retry_interval: int = 5):
        """
        运行自动推送流程

        参数:
            max_retries: 最大重试次数
            retry_interval: 重试间隔（秒）
        """
        logger.info("开始自动推送厂房数据...")

        # 获取待推送的厂房数据
        factory_list = self.get_factory_list()
        logger.info(f"获取到 {len(factory_list)} 条待推送厂房数据")

        if not factory_list:
            logger.info("没有需要推送的厂房数据，结束运行")
            return

        # 记录成功和失败的推送
        success_count = 0
        failed_count = 0

        # 处理每条厂房数据
        for factory in factory_list:
            factory_id = factory.get("id")
            erp_id = factory.get("erp_id")

            logger.info(f"正在处理厂房数据: ID={factory_id}, ERP_ID={erp_id}")

            # 获取可用网站列表
            website_data = self.get_available_websites(factory_id)
            available_websites = website_data.get("websites", [])
            website_names = website_data.get("website_names", {})

            # 筛选只处理58免费版（website_id=111）的网站
            target_websites = []
            for website in available_websites:
                if str(website.get("webID")) == "111":
                    target_websites.append(website)

            if not target_websites:
                logger.warning(f"厂房 {factory_id} 没有可用的58免费版（ID=111）网站，跳过")
                continue

            logger.info(f"厂房 {factory_id} 有 {len(target_websites)} 个可用的58免费版网站")

            # 对筛选后的网站进行推送
            for website in target_websites:
                website_id = str(website.get("webID"))
                website_name = website_names.get(website_id, f"网站{website_id}")

                logger.info(f"正在推送厂房 {factory_id} 到网站 {website_name}({website_id})")

                # 尝试推送，最多重试指定次数
                push_success = False
                retry_count = 0

                while not push_success and retry_count < max_retries:
                    if retry_count > 0:
                        logger.info(f"重试第 {retry_count} 次推送...")
                        time.sleep(retry_interval)

                    push_success = await self.push_factory_to_website(factory_id, website)
                    retry_count += 1

                if push_success:
                    success_count += 1
                    logger.info(f"成功推送厂房 {factory_id} 到网站 {website_name}")
                else:
                    failed_count += 1
                    logger.error(f"推送厂房 {factory_id} 到网站 {website_name} 失败，已重试 {retry_count} 次")

                # 每次推送后适当延迟，避免请求过于频繁
                time.sleep(2)

        # 输出统计信息
        logger.info("自动推送任务完成")
        logger.info(f"成功推送: {success_count} 次")
        logger.info(f"失败推送: {failed_count} 次")

async def main():
    """主函数"""
    try:
        # 创建并运行自动推送服务
        auto_push = AutoPushService()
        await auto_push.run()

    except Exception as e:
        logger.error(f"自动推送脚本执行出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())