#!/bin/bash

# 自动推送厂房数据的运行脚本
# 用法: ./run_auto_push.sh

# 获取脚本所在目录
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
cd "$SCRIPT_DIR"

# 设置Python路径
export PYTHONPATH=$PYTHONPATH:$(dirname "$SCRIPT_DIR")

# 创建logs目录
LOGS_DIR="$(dirname "$SCRIPT_DIR")/logs"
mkdir -p "$LOGS_DIR"

# 设置日志文件（使用固定文件名）
LOG_FILE="$LOGS_DIR/auto_push_paid.log"

# 获取当前时间
CURRENT_TIME=$(date +"[%Y-%m-%d %H:%M:%S]")

echo "$CURRENT_TIME 开始自动推送任务..." | tee -a "$LOG_FILE"

# 运行Python脚本
python3 auto_push_paid_factory_data.py 2>&1 | tee -a "$LOG_FILE"

# 检查执行结果
RESULT=$?
CURRENT_TIME=$(date +"[%Y-%m-%d %H:%M:%S]")

if [ $RESULT -eq 0 ]; then
    echo "$CURRENT_TIME 自动推送任务成功完成" | tee -a "$LOG_FILE"
    exit 0
else
    echo "$CURRENT_TIME 自动推送任务执行失败" | tee -a "$LOG_FILE"
    exit 1
fi 