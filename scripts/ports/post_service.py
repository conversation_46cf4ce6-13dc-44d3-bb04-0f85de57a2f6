#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import asyncio
import base64
import os
import sys
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .base_service import BaseService
from services.youtui_service import YoutuiService


class PostService(BaseService):
    """帖子服务类，负责获取和管理帖子信息"""
    
    def __init__(self):
        super().__init__()
    
    async def get_port_posts(self, city: str, city_name: str, website_data: List[Dict[str, Any]], user_id: str) -> str:
        """获取指定端口的帖子信息"""
        try:
            data_dir = self.get_data_dir()
            timestamp = self.get_timestamp()
            posts_filename = f"{city}_{city_name}_帖子信息_{timestamp}.txt"
            posts_filepath = os.path.join(data_dir, posts_filename)

            account_tasks = []
            for website in website_data:
                web_id = str(website.get('webID', ''))
                if web_id != '402':
                    continue

                user_name_arr = website.get('userNameArr', {})
                for account_id, account_info in user_name_arr.items():
                    if not account_info or len(account_info) < 3:
                        continue

                    username = account_info[0] if account_info[0] else '未知'
                    status = account_info[2] if len(account_info) > 2 else ""

                    account_tasks.append({
                        'web_id': web_id,
                        'account_id': account_id,
                        'username': username,
                        'status': status
                    })

            if not account_tasks:
                return posts_filepath

            semaphore = asyncio.Semaphore(5)
            results = await self.fetch_all_posts_concurrent(city, user_id, account_tasks, semaphore)

            with open(posts_filepath, 'w', encoding='utf-8') as f:
                f.write(f"城市: {city_name}({city}) 402端口帖子信息\n")
                f.write("=" * 80 + "\n")
                f.write(f"获取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"用户ID: {user_id}\n")
                f.write(f"端口类型: 402 (安居客三网合一版本)\n")
                f.write(f"并发数: 5\n")
                f.write(f"总账号数: {len(account_tasks)}\n")
                f.write(f"成功获取: {sum(1 for r in results if r['success'])}\n")
                f.write(f"失败获取: {sum(1 for r in results if not r['success'])}\n")
                f.write("\n")

                current_web_id = None
                for result in results:
                    if result['web_id'] != current_web_id:
                        current_web_id = result['web_id']
                        f.write(f"网站ID: {current_web_id}\n")
                        f.write("-" * 40 + "\n")

                    f.write(f"账号ID: {result['account_id']}\n")
                    f.write(f"用户名: {result['username']}\n")
                    f.write(f"状态: {result['status']}\n")

                    if result['success']:
                        posts_count = result.get('posts_count', 0)
                        f.write(f"帖子数量: {posts_count}\n")

                        posts_preview = result.get('posts_preview', [])
                        for i, post in enumerate(posts_preview, 1):
                            f.write(f"  帖子{i}: {post}\n")

                        if posts_count > len(posts_preview):
                            f.write(f"  ... 还有 {posts_count - len(posts_preview)} 个帖子\n")
                    else:
                        f.write(f"获取失败: {result.get('error', '未知错误')}\n")

                    f.write("\n")

            return posts_filepath

        except Exception as e:
            return ""
    
    async def fetch_all_posts_concurrent(self, city: str, user_id: str, account_tasks: List[Dict], semaphore: asyncio.Semaphore) -> List[Dict]:
        """并发获取所有账号的帖子信息"""
        async def fetch_single_account_posts(account_task):
            async with semaphore:
                try:
                    youtui_service = YoutuiService(city=city)

                    webcontent_data = {
                        "webarr": [
                            {
                                "webID": account_task['web_id'],
                                "actArr": {
                                    account_task['account_id']: "1"
                                }
                            }
                        ]
                    }

                    webcontent_json = json.dumps(webcontent_data, ensure_ascii=False)
                    webcontent = base64.b64encode(webcontent_json.encode('utf-8')).decode('utf-8')

                    result = await youtui_service.async_get_agent_post_stats(user_id, webcontent, timeout=None)

                    if result and result.get('status') == '1':
                        msg = result.get('msg', '')
                        if msg:
                            try:
                                posts_data = json.loads(msg)
                                posts_preview = []
                                for post in posts_data[:5]:
                                    title = post.get('title', '无标题')
                                    click_count = post.get('clickM', '0')
                                    posts_preview.append(f"{title} (点击量: {click_count})")

                                return {
                                    **account_task,
                                    'success': True,
                                    'posts_count': len(posts_data),
                                    'posts_preview': posts_preview
                                }
                            except:
                                return {
                                    **account_task,
                                    'success': True,
                                    'posts_count': 0,
                                    'posts_preview': [f"帖子信息: {msg[:50]}..."]
                                }
                        else:
                            return {
                                **account_task,
                                'success': True,
                                'posts_count': 0,
                                'posts_preview': []
                            }
                    else:
                        error_msg = result.get('msg', '未知错误') if result else 'API调用失败'
                        return {
                            **account_task,
                            'success': False,
                            'error': error_msg
                        }

                except Exception as e:
                    return {
                        **account_task,
                        'success': False,
                        'error': str(e)
                    }

        tasks = [fetch_single_account_posts(account_task) for account_task in account_tasks]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    **account_tasks[i],
                    'success': False,
                    'error': str(result)
                })
            else:
                processed_results.append(result)

        return processed_results

    async def get_all_available_paid_posts(self, port_results: Dict[str, Dict[str, Any]]) -> List[Dict]:
        """获取所有可用付费端口的帖子信息"""
        try:
            all_available_accounts = []
            skipped_cities = []

            for city, result in port_results.items():
                if not result.get('success'):
                    skipped_cities.append(f"{result.get('city_name', city)}({city}) - 城市处理失败")
                    continue

                city_name = result.get('city_name', city)

                try:
                    from api.client import AsyncYoutuiApiClient

                    accounts = self.cities_config.get("accounts", {})
                    city_accounts = accounts.get(city, {})
                    user_id = city_accounts.get("user_id")
                    user_key = city_accounts.get("user_key")

                    if not user_id or not user_key:
                        skipped_cities.append(f"{city_name}({city}) - 缺少用户账号信息")
                        continue

                    async_client = AsyncYoutuiApiClient(environment='production', city=city)
                    api_result = await async_client.async_get_website_accounts(user_id, user_key, timeout=None)

                    if not api_result or api_result.get('status') != '1':
                        error_msg = api_result.get('msg', 'API调用失败') if api_result else 'API调用失败'
                        skipped_cities.append(f"{city_name}({city}) - API调用失败: {error_msg}")
                        continue

                    content = api_result.get('content', '')
                    decoded_content = base64.b64decode(content).decode('utf-8')
                    website_data = json.loads(decoded_content)

                    city_402_accounts = 0
                    city_available_accounts = 0

                    for website in website_data:
                        web_id = str(website.get('webID', ''))
                        if web_id != '402':
                            continue

                        user_name_arr = website.get('userNameArr', {})
                        for account_id, account_info in user_name_arr.items():
                            if not account_info or len(account_info) < 3:
                                continue

                            city_402_accounts += 1
                            username = account_info[0] if account_info[0] else '未知'
                            status = account_info[2] if len(account_info) > 2 else ""

                            if "可用" in status or status == "可用":
                                city_available_accounts += 1
                                all_available_accounts.append({
                                    'city': city,
                                    'city_name': city_name,
                                    'user_id': user_id,
                                    'web_id': web_id,
                                    'account_id': account_id,
                                    'username': username,
                                    'status': status
                                })

                except Exception as e:
                    skipped_cities.append(f"{city_name}({city}) - 处理异常: {str(e)}")
                    continue

            if not all_available_accounts:
                return []

            semaphore = asyncio.Semaphore(20)
            results = await self.fetch_posts_for_accounts(all_available_accounts, semaphore)

            await self.save_posts_results(results)

            return results

        except Exception as e:
            return []

    async def fetch_posts_for_accounts(self, accounts: List[Dict], semaphore: asyncio.Semaphore) -> List[Dict]:
        """为账号列表获取帖子信息"""
        async def fetch_single_account_posts(account):
            async with semaphore:
                try:
                    youtui_service = YoutuiService(city=account['city'])

                    webcontent_data = {
                        "webarr": [
                            {
                                "webID": account['web_id'],
                                "actArr": {
                                    account['account_id']: "1"
                                }
                            }
                        ]
                    }

                    webcontent_json = json.dumps(webcontent_data, ensure_ascii=False)
                    webcontent = base64.b64encode(webcontent_json.encode('utf-8')).decode('utf-8')

                    result = await youtui_service.async_get_agent_post_stats(account['user_id'], webcontent, timeout=None)

                    if result and result.get('status') == '1':
                        msg = result.get('msg', '')
                        taocan = result.get('taocan', '')
                        if msg:
                            try:
                                posts_data = json.loads(msg)
                                return {
                                    **account,
                                    'success': True,
                                    'posts_count': len(posts_data),
                                    'posts_data': posts_data,
                                    'taocan': taocan
                                }
                            except:
                                return {
                                    **account,
                                    'success': True,
                                    'posts_count': 0,
                                    'posts_data': [],
                                    'raw_msg': msg,
                                    'taocan': taocan
                                }
                        else:
                            return {
                                **account,
                                'success': True,
                                'posts_count': 0,
                                'posts_data': [],
                                'taocan': taocan
                            }
                    else:
                        error_msg = result.get('msg', '未知错误') if result else 'API调用失败'
                        return {
                            **account,
                            'success': False,
                            'error': error_msg
                        }

                except Exception as e:
                    return {
                        **account,
                        'success': False,
                        'error': str(e)
                    }

        tasks = [fetch_single_account_posts(account) for account in accounts]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    **accounts[i],
                    'success': False,
                    'error': str(result)
                })
            else:
                processed_results.append(result)

        return processed_results

    async def save_posts_results(self, results: List[Dict]) -> str:
        """保存帖子获取结果到文件"""
        try:
            data_dir = self.get_data_dir()
            timestamp = self.get_timestamp()
            posts_filename = f"全部城市_402端口帖子信息_{timestamp}.txt"
            posts_filepath = os.path.join(data_dir, posts_filename)

            success_count = sum(1 for r in results if r['success'])
            failed_count = len(results) - success_count
            total_posts = sum(r.get('posts_count', 0) for r in results if r['success'])

            # 套餐类型统计
            package_stats = {
                "20额度套餐": 0,
                "40额度套餐": 0
            }

            with open(posts_filepath, 'w', encoding='utf-8') as f:
                f.write("全部城市402端口帖子信息汇总\n")
                f.write("=" * 80 + "\n")
                f.write(f"获取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"端口类型: 402 (安居客三网合一版本)\n")
                f.write(f"并发数: 20\n")
                f.write(f"总账号数: {len(results)}\n")
                f.write(f"成功获取: {success_count}\n")
                f.write(f"失败获取: {failed_count}\n")
                f.write(f"总帖子数: {total_posts}\n")
                f.write("\n")

                current_city = None
                for result in sorted(results, key=lambda x: x['city_name']):
                    if result['city_name'] != current_city:
                        current_city = result['city_name']
                        f.write(f"城市: {current_city}({result['city']})\n")
                        f.write("-" * 40 + "\n")

                    f.write(f"  账号ID: {result['account_id']}\n")
                    f.write(f"  用户名: {result['username']}\n")
                    f.write(f"  状态: {result['status']}\n")

                    if result['success']:
                        posts_count = result.get('posts_count', 0)
                        taocan = result.get('taocan', '')
                        package_info = self.get_package_info_from_taocan(taocan)
                        package_type = package_info['package_type']
                        package_stats[package_type] += 1

                        f.write(f"  帖子数量: {posts_count}\n")
                        f.write(f"  套餐类型: {package_type}\n")
                        if taocan:
                            f.write(f"  套餐信息: {taocan}\n")

                        if posts_count > 0:
                            posts_data = result.get('posts_data', [])
                            for i, post in enumerate(posts_data, 1):
                                title = post.get('Title', post.get('title', '无标题'))
                                rid = post.get('RID', post.get('InfoID', post.get('id', '未知RID')))
                                click_month = post.get('clickM', '0')
                                click_today = post.get('clickT', '0')
                                sid = post.get('SID', '')
                                house_type = post.get('Type', '6')
                                house_url = post.get('House_URL', '')
                                tags = post.get('Tags', '')
                                add_time = post.get('AddTime', post.get('addTime', ''))

                                # 转换房源类型
                                type_text = '出售' if house_type == '6' else '出租' if house_type == '3' else '未知'

                                f.write(f"    帖子{i}: {title}\n")
                                f.write(f"      RID: {rid}\n")
                                f.write(f"      月点击量: {click_month}\n")
                                f.write(f"      日点击量: {click_today}\n")
                                f.write(f"      租售类型: {type_text} ({house_type})\n")
                                if sid:
                                    f.write(f"      网站ID: {sid}\n")
                                if house_url:
                                    f.write(f"      房源链接: {house_url}\n")
                                if tags:
                                    f.write(f"      标签: {tags}\n")
                                if add_time:
                                    f.write(f"      发布时间: {add_time}\n")
                                f.write(f"      ----\n")
                    else:
                        f.write(f"  获取失败: {result.get('error', '未知错误')}\n")

                    f.write("\n")

                # 添加套餐类型统计总结
                f.write("套餐类型统计总结\n")
                f.write("=" * 80 + "\n")
                f.write(f"统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"成功获取帖子的账号总数: {success_count}\n")
                f.write("\n")

                f.write("各套餐类型分布:\n")
                f.write("-" * 40 + "\n")
                for package_type, count in package_stats.items():
                    percentage = (count / success_count * 100) if success_count > 0 else 0
                    f.write(f"{package_type}: {count}个账号 ({percentage:.1f}%)\n")

                f.write("\n")
                f.write("套餐类型说明:\n")
                f.write("-" * 40 + "\n")
                f.write("20额度套餐: 基于taocan字段总额度 ≤ 20 (如 '20-0', '15-5' 等)\n")
                f.write("40额度套餐: 基于taocan字段总额度 > 20 (如 '40-0', '30-10' 等)\n")
                f.write("注：套餐类型优先基于API返回的taocan字段判断，无taocan时默认为20额度套餐\n")

            return posts_filepath

        except Exception as e:
            return ""
