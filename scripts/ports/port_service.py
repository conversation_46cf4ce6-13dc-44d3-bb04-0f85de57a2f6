#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import asyncio
import base64
import os
import sys
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .base_service import BaseService


class PortService(BaseService):
    """端口服务类，负责获取和管理端口信息"""
    
    def __init__(self):
        super().__init__()
        self.results = {}
    
    async def get_city_ports(self, city: str, city_name: str) -> Dict[str, Any]:
        """获取单个城市的端口信息"""
        result = {
            "city": city,
            "city_name": city_name,
            "success": False,
            "free_accounts": 0,
            "paid_accounts": 0,
            "available_free_accounts": 0,
            "available_paid_accounts": 0
        }

        try:
            from api.client import AsyncYoutuiApiClient

            accounts = self.cities_config.get("accounts", {})
            city_accounts = accounts.get(city, {})
            user_id = city_accounts.get("user_id")
            user_key = city_accounts.get("user_key")

            if not user_id or not user_key:
                result["error_message"] = f"城市 {city} 没有配置用户账号信息"
                return result

            async_client = AsyncYoutuiApiClient(environment='production', city=city)
            api_result = await async_client.async_get_website_accounts(user_id, user_key, timeout=None)

            if not api_result or api_result.get('status') != '1':
                error_msg = api_result.get('msg') if api_result else "API调用失败"
                result["error_message"] = f"获取网站数据失败: {error_msg}"
                return result

            try:
                content = api_result.get('content', '')
                decoded_content = base64.b64decode(content).decode('utf-8')
                website_data = json.loads(decoded_content)

                if not isinstance(website_data, list):
                    result["error_message"] = f"API返回的数据格式不正确"
                    return result

            except Exception as e:
                result["error_message"] = f"解析API返回数据失败: {str(e)}"
                return result

            account_stats = self.analyze_accounts(website_data)
            result["free_accounts"] = account_stats["free_accounts"]
            result["paid_accounts"] = account_stats["paid_accounts"]
            result["available_free_accounts"] = account_stats["available_free_accounts"]
            result["available_paid_accounts"] = account_stats["available_paid_accounts"]
            result["success"] = True

        except Exception as e:
            result["error_message"] = f"处理城市失败: {str(e)}"

        return result
    
    async def process_all_cities(self, max_concurrent: int = 5) -> Dict[str, Dict[str, Any]]:
        """处理所有城市的端口信息"""
        domains = self.cities_config.get("domains", {})
        names = self.cities_config.get("names", {})

        if not domains:
            return {}

        tasks = []
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_city_with_semaphore(city: str):
            async with semaphore:
                city_name = names.get(city, city)
                result = await self.get_city_ports(city, city_name)
                self.results[city] = result
                return result

        for city in domains.keys():
            task = asyncio.create_task(process_city_with_semaphore(city))
            tasks.append(task)

        await asyncio.gather(*tasks, return_exceptions=True)
        return self.results

    async def process_single_city(self, city: str) -> Dict[str, Dict[str, Any]]:
        """处理单个城市的端口信息"""
        domains = self.cities_config.get("domains", {})
        names = self.cities_config.get("names", {})

        if city not in domains:
            return {}

        city_name = names.get(city, city)
        result = await self.get_city_ports(city, city_name)
        self.results[city] = result
        return {city: result}
    
    async def generate_summary_file(self) -> str:
        """生成端口统计汇总文件"""
        try:
            data_dir = self.get_data_dir()
            timestamp = self.get_timestamp()
            summary_filename = f"账号统计总结_{timestamp}.txt"
            summary_filepath = os.path.join(data_dir, summary_filename)

            total_free_accounts = sum(r.get("free_accounts", 0) for r in self.results.values() if r["success"])
            total_paid_accounts = sum(r.get("paid_accounts", 0) for r in self.results.values() if r["success"])
            total_available_free = sum(r.get("available_free_accounts", 0) for r in self.results.values() if r["success"])
            total_available_paid = sum(r.get("available_paid_accounts", 0) for r in self.results.values() if r["success"])

            with open(summary_filepath, 'w', encoding='utf-8') as f:
                f.write("各城市账号统计总结报告\n")
                f.write("=" * 80 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"处理城市总数: {len(self.results)}\n")
                f.write(f"成功处理城市: {sum(1 for r in self.results.values() if r['success'])}\n")
                f.write("\n")

                f.write("全部城市账号总计:\n")
                f.write("-" * 40 + "\n")
                f.write(f"免费账号总数: {total_free_accounts}\n")
                f.write(f"免费可用账号: {total_available_free}\n")
                f.write(f"免费账号可用率: {(total_available_free/total_free_accounts*100):.1f}%\n" if total_free_accounts > 0 else "免费账号可用率: 0%\n")
                f.write(f"付费账号总数: {total_paid_accounts}\n")
                f.write(f"付费可用账号: {total_available_paid}\n")
                f.write(f"付费账号可用率: {(total_available_paid/total_paid_accounts*100):.1f}%\n" if total_paid_accounts > 0 else "付费账号可用率: 0%\n")
                f.write("\n")

                f.write("各城市详细统计:\n")
                f.write("=" * 80 + "\n")
                f.write(f"{'城市':<15} {'免费账号':<15} {'免费可用':<15} {'付费账号':<15} {'付费可用':<15} {'状态':<10}\n")
                f.write("-" * 80 + "\n")

                sorted_results = sorted(self.results.items(), key=lambda x: x[1].get('city_name', ''))

                for city, result in sorted_results:
                    city_name = result.get('city_name', city)
                    free_total = result.get('free_accounts', 0)
                    free_available = result.get('available_free_accounts', 0)
                    paid_total = result.get('paid_accounts', 0)
                    paid_available = result.get('available_paid_accounts', 0)
                    status = "成功" if result.get('success') else "失败"

                    f.write(f"{city_name:<15} {free_total:<15} {free_available:<15} {paid_total:<15} {paid_available:<15} {status:<10}\n")

                f.write("\n")

                failed_cities = [result for result in self.results.values() if not result.get('success')]
                if failed_cities:
                    f.write("失败城市详情:\n")
                    f.write("-" * 40 + "\n")
                    for result in failed_cities:
                        f.write(f"- {result.get('city_name', '')}({result.get('city', '')}): {result.get('error_message', '未知错误')}\n")
                    f.write("\n")

                f.write("网站类型说明:\n")
                f.write("-" * 40 + "\n")
                f.write("免费网站: 111(58免费三网合一版本)\n")
                f.write("付费网站: 402(安居客三网合一版本) - 仅统计此类型\n")

            return summary_filepath

        except Exception:
            # 文件生成失败，返回空字符串
            return ""
