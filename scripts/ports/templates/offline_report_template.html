<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>帖子下架结果报告</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .summary {
            background-color: #e8f4fd;
            padding: 25px;
            border-left: 4px solid #007bff;
        }
        .summary h2 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .success-rate {
            color: #28a745 !important;
        }
        .city-section {
            margin: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }
        .city-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: bold;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .city-stats {
            font-size: 14px;
            color: #666;
        }
        .accounts-table {
            width: 100%;
            border-collapse: collapse;
        }
        .accounts-table th {
            background-color: #f1f3f4;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #e0e0e0;
        }
        .accounts-table td {
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: top;
        }
        .accounts-table tr:hover {
            background-color: #f8f9fa;
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-failed {
            color: #dc3545;
            font-weight: bold;
        }
        .post-title {
            font-size: 13px;
            line-height: 1.4;
            max-width: 200px;
        }
        .package-badge {
            background-color: #e9ecef;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: #495057;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #e0e0e0;
        }
        .footer p {
            margin: 5px 0;
        }
        .rules-section {
            margin: 30px;
            padding: 20px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
        }
        .rules-section h3 {
            margin: 0 0 15px 0;
            color: #856404;
        }
        .rules-section ul {
            margin: 0;
            padding-left: 20px;
            color: #856404;
        }
        .rules-section li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>帖子下架结果报告</h1>
            <p>{{timestamp}} | {{city_name}}</p>
        </div>

        <div class="summary">
            <h2>📊 总体统计</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{total_accounts}}</div>
                    <div class="stat-label">处理账号总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number success-rate">{{success_accounts}}</div>
                    <div class="stat-label">成功下架账号</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number success-rate">{{account_success_rate}}%</div>
                    <div class="stat-label">账号成功率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{total_posts_attempted}}</div>
                    <div class="stat-label">尝试下架帖子</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number success-rate">{{total_posts_success}}</div>
                    <div class="stat-label">成功下架帖子</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number success-rate">{{post_success_rate}}%</div>
                    <div class="stat-label">帖子成功率</div>
                </div>
            </div>
        </div>

        {{city_sections}}

        <div class="rules-section">
            <h3>📋 下架规则说明</h3>
            <ul>
                <li>每个付费账号下架月点击量最低的1个帖子</li>
                <li>下架策略：优先选择点击量最低的帖子进行下架</li>
                <li>套餐类型基于API返回的taocan字段判断，无taocan时默认为20额度套餐</li>
                <li>只处理402类型的付费账号</li>
            </ul>
        </div>

        <div class="footer">
            <p>📧 此邮件由联东ERP系统自动发送，请勿回复</p>
            <p>如有问题，请联系系统管理员</p>
            <p>报告生成时间: {{timestamp}}</p>
        </div>
    </div>
</body>
</html>
