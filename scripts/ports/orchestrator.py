#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import sys
import os
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .port_service import PortService
from .post_service import PostService
from .activation_service import ActivationService
from .offline_service import OfflineService


class PortsOrchestrator:
    """端口管理协调器，管理各个服务的协作"""

    def __init__(self):
        self.port_service = PortService()
        self.post_service = PostService()
        self.activation_service = ActivationService()
        self.offline_service = OfflineService()

    
    async def run_full_process(self, max_concurrent: int = 5) -> Dict[str, Any]:
        """运行完整的端口管理流程：获取端口 -> 获取帖子 -> 激活帖子"""
        results = {
            "port_results": {},
            "summary_file": "",
            "posts_results": [],
            "posts_file": "",
            "activation_file": "",
            "success": False,
            "error_message": ""
        }

        try:
            port_results = await self.port_service.process_all_cities(max_concurrent)
            results["port_results"] = port_results

            summary_file = await self.port_service.generate_summary_file()
            results["summary_file"] = summary_file

            posts_results = await self.post_service.get_all_available_paid_posts(port_results)
            results["posts_results"] = posts_results

            if posts_results:
                activation_file = await self.activation_service.activate_posts_by_package(posts_results)
                results["activation_file"] = activation_file

            results["success"] = True

        except Exception as e:
            results["error_message"] = str(e)
            print(f"执行过程中发生错误: {str(e)}")

        return results
    
    async def run_ports_only(self, max_concurrent: int = 5) -> Dict[str, Any]:
        """仅运行端口获取流程"""
        results = {
            "port_results": {},
            "summary_file": "",
            "success": False,
            "error_message": ""
        }
        
        try:
            port_results = await self.port_service.process_all_cities(max_concurrent)
            results["port_results"] = port_results

            summary_file = await self.port_service.generate_summary_file()
            results["summary_file"] = summary_file

            results["success"] = True
            
        except Exception as e:
            results["error_message"] = str(e)
            print(f"端口获取过程中发生错误: {str(e)}")
        
        return results
    
    async def run_posts_only(self, port_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """仅运行帖子获取流程（需要先有端口结果）"""
        results = {
            "posts_results": [],
            "posts_file": "",
            "success": False,
            "error_message": ""
        }
        
        try:
            # 如果没有提供端口结果，先获取端口信息
            if port_results is None:
                port_results = await self.port_service.process_all_cities()

            posts_results = await self.post_service.get_all_available_paid_posts(port_results)
            results["posts_results"] = posts_results

            results["success"] = True
            
        except Exception as e:
            results["error_message"] = str(e)
            print(f"帖子获取过程中发生错误: {str(e)}")
        
        return results
    
    async def run_activation_only(self, posts_results: Optional[List[Dict]] = None,
                                 port_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """仅运行激活流程（需要先有帖子结果）"""
        results = {
            "activation_file": "",
            "success": False,
            "error_message": ""
        }

        try:
            if posts_results is None:
                if port_results is None:
                    port_results = await self.port_service.process_all_cities()

                posts_results = await self.post_service.get_all_available_paid_posts(port_results)

            if not posts_results:
                results["error_message"] = "没有可激活的帖子"
                return results

            activation_file = await self.activation_service.activate_posts_by_package(posts_results)
            results["activation_file"] = activation_file

            results["success"] = True

        except Exception as e:
            results["error_message"] = str(e)
            print(f"激活过程中发生错误: {str(e)}")

        return results
    
    async def run_custom_city_posts(self, city: str, city_name: str = None) -> Dict[str, Any]:
        """为指定城市获取帖子信息"""
        results = {
            "posts_file": "",
            "success": False,
            "error_message": ""
        }
        
        try:
            if city_name is None:
                city_name = city

            port_result = await self.port_service.get_city_ports(city, city_name)

            if not port_result["success"]:
                results["error_message"] = port_result.get("error_message", "获取端口信息失败")
                return results
            
            # 获取网站数据用于帖子获取
            from api.client import AsyncYoutuiApiClient
            import base64
            import json
            
            accounts = self.port_service.cities_config.get("accounts", {})
            city_accounts = accounts.get(city, {})
            user_id = city_accounts.get("user_id")
            user_key = city_accounts.get("user_key")
            
            if not user_id or not user_key:
                results["error_message"] = f"城市 {city} 没有配置用户账号信息"
                return results
            
            async_client = AsyncYoutuiApiClient(environment='production', city=city)
            api_result = await async_client.async_get_website_accounts(user_id, user_key, timeout=None)
            
            if not api_result or api_result.get('status') != '1':
                results["error_message"] = "获取网站账号信息失败"
                return results
            
            content = api_result.get('content', '')
            decoded_content = base64.b64decode(content).decode('utf-8')
            website_data = json.loads(decoded_content)
            
            posts_file = await self.post_service.get_port_posts(city, city_name, website_data, user_id)
            results["posts_file"] = posts_file

            results["success"] = True
            
        except Exception as e:
            results["error_message"] = str(e)
            print(f"获取城市帖子过程中发生错误: {str(e)}")
        
        return results

    async def run_offline_only(self, posts_results: Optional[List[Dict]] = None,
                              port_results: Optional[Dict[str, Any]] = None,
                              city: Optional[str] = None) -> Dict[str, Any]:
        """仅运行下架流程（需要先有帖子结果）"""
        results = {
            "offline_file": "",
            "success": False,
            "error_message": ""
        }

        try:
            if posts_results is None:
                if port_results is None:
                    if city:
                        port_results = await self.port_service.process_single_city(city)
                    else:
                        port_results = await self.port_service.process_all_cities()

                posts_results = await self.post_service.get_all_available_paid_posts(port_results)

            if city:
                filtered_results = []
                for result in posts_results:
                    if result.get('city') == city:
                        filtered_results.append(result)
                posts_results = filtered_results

            if not posts_results:
                if city:
                    results["error_message"] = f"城市 {city} 没有可下架的帖子"
                else:
                    results["error_message"] = "没有可下架的帖子"
                return results

            offline_file = await self.offline_service.offline_posts_by_package(posts_results, city)
            results["offline_file"] = offline_file

            results["success"] = True

        except Exception as e:
            results["error_message"] = str(e)
            print(f"下架过程中发生错误: {str(e)}")

        return results
