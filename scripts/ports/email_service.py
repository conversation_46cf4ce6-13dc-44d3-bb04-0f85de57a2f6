#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import smtplib
import os
import sys
import time
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from email.header import Head<PERSON>
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .base_service import BaseService
from config.email_settings import (
    EMAIL_CONFIG,
    EMAIL_RECIPIENTS,
    EMAIL_TEMPLATES,
    EMAIL_SEND_CONFIG,
    EMAIL_CONTENT_CONFIG
)


class EmailService(BaseService):
    """邮件服务类，负责发送下架结果邮件"""

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)

    def send_offline_report(self, offline_results: List[Dict], stats: Dict, city: str = None, 
                          report_file_path: str = None) -> Dict:
        """发送下架结果报告邮件"""
        try:
            # 检查邮件配置
            if not EMAIL_CONFIG.get('username') or not EMAIL_CONFIG.get('password'):
                self.logger.warning("邮件配置不完整，跳过邮件发送")
                return {'success': False, 'message': '邮件配置不完整'}

            # 检查收件人
            recipients = EMAIL_RECIPIENTS.get('offline_report', [])
            if not recipients:
                self.logger.warning("未配置收件人，跳过邮件发送")
                return {'success': False, 'message': '未配置收件人'}

            # 生成邮件内容
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            city_name = city if city else '全部城市'
            
            subject = EMAIL_TEMPLATES['offline_report']['subject'].format(
                city=city_name,
                timestamp=timestamp
            )

            # 生成HTML内容
            html_content = self._generate_offline_report_html(
                offline_results, stats, city_name, timestamp
            )

            # 发送邮件
            result = self._send_email(
                recipients=recipients,
                subject=subject,
                html_content=html_content,
                attachment_path=report_file_path
            )

            if result['success']:
                self.logger.info(f"下架报告邮件发送成功，收件人: {recipients}")
            else:
                self.logger.error(f"下架报告邮件发送失败: {result['message']}")

            return result

        except Exception as e:
            error_msg = f"发送下架报告邮件时发生异常: {str(e)}"
            self.logger.error(error_msg)
            return {'success': False, 'message': error_msg}

    def _generate_offline_report_html(self, offline_results: List[Dict], stats: Dict, 
                                    city_name: str, timestamp: str) -> str:
        """生成下架报告HTML内容"""
        try:
            # 统计数据
            total_accounts = len(offline_results)
            success_accounts = len([r for r in offline_results if r.get('offline_success', False)])
            total_posts_attempted = sum(r.get('attempted_offline', 0) for r in offline_results)
            total_posts_success = sum(r.get('successful_offline', 0) for r in offline_results)

            # 计算成功率，避免除零错误
            account_success_rate = (success_accounts / total_accounts * 100) if total_accounts > 0 else 0
            post_success_rate = (total_posts_success / total_posts_attempted * 100) if total_posts_attempted > 0 else 0

            # 按城市分组
            city_groups = {}
            for result in offline_results:
                city_key = result.get('city_name', '未知城市')
                if city_key not in city_groups:
                    city_groups[city_key] = []
                city_groups[city_key].append(result)

            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>帖子下架结果报告</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
                    .summary {{ background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
                    .city-section {{ margin-bottom: 30px; }}
                    .city-title {{ background-color: #d4edda; padding: 10px; border-radius: 3px; font-weight: bold; }}
                    table {{ width: 100%; border-collapse: collapse; margin-top: 10px; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    th {{ background-color: #f2f2f2; }}
                    .success {{ color: #28a745; }}
                    .failed {{ color: #dc3545; }}
                    .footer {{ margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; font-size: 12px; color: #666; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>帖子下架结果报告</h2>
                    <p><strong>报告时间:</strong> {timestamp}</p>
                    <p><strong>目标范围:</strong> {city_name}</p>
                </div>

                <div class="summary">
                    <h3>总体统计</h3>
                    <p><strong>处理账号总数:</strong> {total_accounts}</p>
                    <p><strong>成功下架账号:</strong> <span class="success">{success_accounts}</span></p>
                    <p><strong>账号成功率:</strong> {account_success_rate:.1f}%</p>
                    <p><strong>尝试下架帖子:</strong> {total_posts_attempted}</p>
                    <p><strong>成功下架帖子:</strong> <span class="success">{total_posts_success}</span></p>
                    <p><strong>帖子成功率:</strong> {post_success_rate:.1f}%</p>
                </div>
            """

            # 添加城市详情
            for city_key, city_results in city_groups.items():
                city_success = len([r for r in city_results if r.get('offline_success', False)])
                html_content += f"""
                <div class="city-section">
                    <div class="city-title">{city_key} (账号数: {len(city_results)}, 成功: {city_success})</div>
                    <table>
                        <tr>
                            <th>账号ID</th>
                            <th>用户名</th>
                            <th>套餐类型</th>
                            <th>下架帖子</th>
                            <th>状态</th>
                        </tr>
                """
                
                for result in city_results:
                    status_class = "success" if result.get('offline_success', False) else "failed"
                    status_text = "成功" if result.get('offline_success', False) else "失败"
                    
                    # 获取下架的帖子标题
                    offline_details = result.get('offline_details', [])
                    post_titles = []
                    for detail in offline_details:
                        if detail.get('success', False):
                            post_titles.append(detail.get('title', '无标题'))
                    
                    post_info = '<br>'.join(post_titles) if post_titles else '无成功下架的帖子'
                    
                    html_content += f"""
                        <tr>
                            <td>{result.get('account_id', '未知')}</td>
                            <td>{result.get('username', '未知')}</td>
                            <td>{result.get('package_type', '未知')}</td>
                            <td>{post_info}</td>
                            <td class="{status_class}">{status_text}</td>
                        </tr>
                    """
                
                html_content += "</table></div>"

            html_content += """
                <div class="footer">
                    <p>此邮件由联东ERP系统自动发送，请勿回复。</p>
                    <p>如有问题，请联系系统管理员。</p>
                </div>
            </body>
            </html>
            """

            return html_content

        except Exception as e:
            self.logger.error(f"生成HTML内容失败: {str(e)}")
            return f"<html><body><h2>报告生成失败</h2><p>错误: {str(e)}</p></body></html>"

    def _send_email(self, recipients: List[str], subject: str, html_content: str, 
                   attachment_path: str = None) -> Dict:
        """发送邮件"""
        try:
            # 创建邮件对象
            msg = MIMEMultipart()
            msg['From'] = f"{EMAIL_CONFIG['sender_name']} <{EMAIL_CONFIG['username']}>"
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = Header(subject, EMAIL_CONTENT_CONFIG['charset'])

            # 添加HTML内容
            html_part = MIMEText(html_content, 'html', EMAIL_CONTENT_CONFIG['charset'])
            msg.attach(html_part)

            # 添加附件
            if attachment_path and os.path.exists(attachment_path):
                try:
                    with open(attachment_path, 'rb') as f:
                        attachment = MIMEBase('application', 'octet-stream')
                        attachment.set_payload(f.read())
                        encoders.encode_base64(attachment)
                        filename = os.path.basename(attachment_path)
                        attachment.add_header(
                            'Content-Disposition',
                            f'attachment; filename="{filename}"'
                        )
                        msg.attach(attachment)
                except Exception as e:
                    self.logger.warning(f"添加附件失败: {str(e)}")

            # 发送邮件
            for attempt in range(EMAIL_SEND_CONFIG['max_retries']):
                try:
                    if EMAIL_CONFIG['use_ssl']:
                        server = smtplib.SMTP_SSL(
                            EMAIL_CONFIG['smtp_server'], 
                            EMAIL_CONFIG['smtp_port'],
                            timeout=EMAIL_SEND_CONFIG['timeout']
                        )
                    else:
                        server = smtplib.SMTP(
                            EMAIL_CONFIG['smtp_server'], 
                            EMAIL_CONFIG['smtp_port'],
                            timeout=EMAIL_SEND_CONFIG['timeout']
                        )
                        server.starttls()

                    server.login(EMAIL_CONFIG['username'], EMAIL_CONFIG['password'])
                    server.send_message(msg)
                    server.quit()

                    return {'success': True, 'message': '邮件发送成功'}

                except Exception as e:
                    self.logger.warning(f"邮件发送尝试 {attempt + 1} 失败: {str(e)}")
                    if attempt < EMAIL_SEND_CONFIG['max_retries'] - 1:
                        time.sleep(EMAIL_SEND_CONFIG['retry_delay'])
                    else:
                        return {'success': False, 'message': f'邮件发送失败: {str(e)}'}

        except Exception as e:
            return {'success': False, 'message': f'邮件发送异常: {str(e)}'}
