#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import asyncio
import base64
import os
import sys
import random
from datetime import datetime, date
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .base_service import BaseService


class ActivationService(BaseService):
    """激活服务类，负责帖子激活功能"""
    
    def __init__(self):
        super().__init__()

    def is_activated_today(self, retime: str) -> bool:
        """判断帖子是否在今天已经被激活过"""
        try:
            if not retime:
                return False

            # 解析Retime字段，格式为"YYYY-MM-DD HH:MM:SS"
            retime_date = datetime.strptime(retime, "%Y-%m-%d %H:%M:%S").date()
            today = date.today()

            return retime_date == today
        except (ValueError, TypeError):
            # 如果日期格式不正确，默认认为没有今天激活过
            return False

    def get_posts_from_cache(self, webcontent: str) -> List[Dict]:
        """从Redis缓存中读取帖子数据"""
        try:
            if not self.redis_conn:
                return []

            # 从webcontent中提取账号ID
            account_id = self.extract_account_id_from_webcontent(webcontent)
            if not account_id:
                return []

            cache_key = f"agent_posts:{account_id}"
            cached_data = self.redis_conn.get(cache_key)

            if not cached_data:
                return []

            # 解析缓存数据
            cached_result = json.loads(cached_data)

            # 检查数据结构并提取帖子列表
            if isinstance(cached_result, dict) and 'msg' in cached_result:
                msg_content = cached_result['msg']

                if isinstance(msg_content, str):
                    # 解析JSON字符串
                    posts_list = json.loads(msg_content)
                    if isinstance(posts_list, list):
                        return posts_list
                elif isinstance(msg_content, list):
                    # 如果msg直接是列表
                    return msg_content

            return []

        except Exception as e:
            return []

    async def save_activated_post_to_mysql(self, account_id: str, post_data: dict):
        """将激活的帖子完整信息保存到MySQL的broker_listings表"""
        try:
            from database.db_connection import db_cursor

            # 从帖子数据中提取所有字段
            rid = post_data.get('RID', post_data.get('InfoID', post_data.get('id', '')))
            sid = post_data.get('SID', '')
            title = post_data.get('Title', post_data.get('title', '无标题'))
            tags = post_data.get('Tags', '')
            com = post_data.get('Com', '')
            house_url = post_data.get('House_URL', '')
            click_t = post_data.get('clickT', 0)
            click_m = post_data.get('clickM', 0)
            thumbnail = post_data.get('thumbnail', '')
            house_type = post_data.get('Type', '6')
            retime = post_data.get('Retime', '')
            potime = post_data.get('Potime', '')

            # 处理数字字段，确保为整数
            try:
                click_t = int(click_t) if str(click_t).isdigit() else 0
            except (ValueError, TypeError):
                click_t = 0

            try:
                click_m = int(click_m) if str(click_m).isdigit() else 0
            except (ValueError, TypeError):
                click_m = 0

            with db_cursor() as cursor:
                # 检查记录是否已存在（基于rid字段）
                cursor.execute("""
                    SELECT id FROM broker_listings
                    WHERE rid = %s
                """, [rid])

                existing_record = cursor.fetchone()

                if not existing_record:
                    # 插入新记录，包含所有字段
                    cursor.execute("""
                        INSERT INTO broker_listings
                        (rid, sid, title, tags, com, house_url, click_t, click_m, thumbnail,
                         type, retime, potime, account_id, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    """, [
                        rid, sid, title, tags, com, house_url, click_t, click_m, thumbnail,
                        house_type, retime or None, potime or None, account_id
                    ])

                    print(f"✅ 成功保存激活帖子到MySQL: RID={rid}, 标题={title}, 账号={account_id}")
                else:
                    # 更新现有记录的所有字段
                    cursor.execute("""
                        UPDATE broker_listings
                        SET sid = %s, title = %s, tags = %s, com = %s, house_url = %s,
                            click_t = %s, click_m = %s, thumbnail = %s, type = %s,
                            retime = %s, potime = %s, account_id = %s, created_at = NOW()
                        WHERE rid = %s
                    """, [
                        sid, title, tags, com, house_url, click_t, click_m, thumbnail,
                        house_type, retime or None, potime or None, account_id, rid
                    ])

                    print(f"✅ 更新激活帖子到MySQL: RID={rid}, 标题={title}, 账号={account_id}")

        except Exception as e:
            # MySQL写入失败不影响激活流程，只记录错误
            print(f"⚠️  MySQL写入失败: {str(e)}")

    async def update_post_cache_after_activation(self, city: str, webcontent: str, remote_id: str, post_data: dict):
        """激活成功后更新缓存中的帖子状态并写入MySQL"""
        try:
            # 从webcontent中提取账号ID
            account_id = self.extract_account_id_from_webcontent(webcontent)
            if not account_id:
                return

            # 更新Redis缓存
            from services.youtui_service import YoutuiService
            youtui_service = YoutuiService(city=city)
            youtui_service._update_post_in_cache_by_account_id(account_id, remote_id)

            # 同时写入MySQL（传递完整的帖子数据）
            await self.save_activated_post_to_mysql(account_id, post_data)

        except Exception as e:
            # 缓存更新失败不影响激活流程，只记录错误
            print(f"⚠️  缓存/数据库更新失败: {str(e)}")
    
    async def activate_posts_by_package(self, results: List[Dict]) -> str:
        """根据套餐类型激活帖子"""
        try:
            data_dir = self.get_data_dir()
            timestamp = self.get_timestamp()
            activation_filename = f"帖子激活结果_{timestamp}.txt"
            activation_filepath = os.path.join(data_dir, activation_filename)

            # 激活统计
            activation_stats = {
                "20额度套餐": {"总账号": 0, "成功激活账号": 0, "总激活帖子": 0, "成功激活帖子": 0},
                "40额度套餐": {"总账号": 0, "成功激活账号": 0, "总激活帖子": 0, "成功激活帖子": 0}
            }

            # 收集需要激活的账号
            accounts_to_activate = []
            for result in results:
                if not result['success'] or result.get('posts_count', 0) == 0:
                    continue

                posts_count = result.get('posts_count', 0)
                taocan = result.get('taocan', '')
                package_info = self.get_package_info_from_taocan(taocan)
                package_type = package_info['package_type']
                total_quota = package_info['total_quota']
                # 随机激活总额度的四分之一
                activate_count = min(max(1, total_quota // 4), posts_count)

                if activate_count > 0:
                    activation_stats[package_type]["总账号"] += 1
                    accounts_to_activate.append({
                        **result,
                        'package_type': package_type,
                        'activate_count': activate_count,
                        'taocan': taocan
                    })

            if not accounts_to_activate:
                return activation_filepath

            semaphore = asyncio.Semaphore(20)
            activation_results = await self.process_activations_concurrent(accounts_to_activate, semaphore)

            for result in activation_results:
                package_type = result['package_type']
                if result.get('activation_success', False):
                    activation_stats[package_type]["成功激活账号"] += 1

                activation_stats[package_type]["总激活帖子"] += result.get('attempted_activations', 0)
                activation_stats[package_type]["成功激活帖子"] += result.get('successful_activations', 0)

            await self.save_activation_results(activation_filepath, activation_results, activation_stats)
            return activation_filepath

        except Exception as e:
            return ""
    
    async def process_activations_concurrent(self, accounts: List[Dict], semaphore: asyncio.Semaphore) -> List[Dict]:
        """并发处理激活请求"""
        async def activate_single_account(account):
            async with semaphore:
                try:
                    # 构建webcontent用于缓存操作
                    webcontent_data = {
                        "webarr": [
                            {
                                "webID": account['web_id'],
                                "actArr": {
                                    account['account_id']: "1"
                                }
                            }
                        ]
                    }
                    webcontent_json = json.dumps(webcontent_data, ensure_ascii=False)
                    webcontent = base64.b64encode(webcontent_json.encode('utf-8')).decode('utf-8')

                    # 从缓存读取帖子数据
                    posts_data = self.get_posts_from_cache(webcontent)
                    if not posts_data:
                        return {
                            **account,
                            'activation_success': False,
                            'attempted_activations': 0,
                            'successful_activations': 0,
                            'activation_details': [],
                            'filtered_today_count': 0,
                            'filtered_non_numeric_count': 0,
                            'available_posts_count': 0,
                            'skip_reason': '缓存中未找到帖子数据'
                        }

                    # 过滤掉今天已激活的帖子和月点击量非数字的帖子
                    available_posts = []
                    filtered_today_count = 0
                    filtered_non_numeric_count = 0

                    for post in posts_data:
                        retime = post.get('Retime', '')
                        click_month = post.get('clickM', '0')

                        # 检查是否今天已激活
                        if self.is_activated_today(retime):
                            filtered_today_count += 1
                            continue

                        # 检查月点击量是否为有效数字
                        try:
                            int(click_month)
                            available_posts.append(post)
                        except (ValueError, TypeError):
                            # 月点击量不是数字（如"校验成功"、"待校验"等），过滤掉
                            filtered_non_numeric_count += 1

                    # 获取套餐信息和激活数量
                    package_info = self.get_package_info_from_taocan(account.get('taocan', ''))
                    total_quota = package_info.get('total_quota', 20)
                    activate_count = account.get('activate_count', max(1, total_quota // 4))

                    # 检查今天已激活数量加上本次要激活的数量是否超过每日限制
                    # 每日限制设为总额度，因为每天执行4次，每次激活1/4
                    daily_limit = total_quota

                    if filtered_today_count >= daily_limit:
                        # 今天已激活数量达到限制，跳过激活
                        return {
                            **account,
                            'activation_success': False,
                            'attempted_activations': 0,
                            'successful_activations': 0,
                            'activation_details': [],
                            'filtered_today_count': filtered_today_count,
                            'filtered_non_numeric_count': filtered_non_numeric_count,
                            'available_posts_count': len(available_posts),
                            'skip_reason': f'今天已激活{filtered_today_count}个帖子，达到每日限制{daily_limit}个'
                        }

                    # 从可用帖子中随机选择指定数量的帖子进行激活
                    if len(available_posts) <= activate_count:
                        posts_to_activate = available_posts
                    else:
                        posts_to_activate = random.sample(available_posts, activate_count)

                    activation_details = []
                    successful_count = 0

                    for post in posts_to_activate:
                        rid = post.get('RID', post.get('InfoID', post.get('id', '')))
                        house_type = post.get('Type', '6')
                        title = post.get('Title', post.get('title', '无标题'))
                        click_month = post.get('clickM', '0')

                        if not rid:
                            activation_details.append({
                                'title': title,
                                'rid': '未知',
                                'click_month': click_month,
                                'success': False,
                                'error': '缺少RID'
                            })
                            continue

                        activation_result = await self.call_activation_api(
                            account['city'], account['user_id'], rid, webcontent, house_type
                        )

                        if activation_result['success']:
                            successful_count += 1
                            # 激活成功后更新缓存并写入MySQL（传递完整帖子数据）
                            await self.update_post_cache_after_activation(
                                account['city'], webcontent, rid, post
                            )

                        activation_details.append({
                            'title': title,
                            'rid': rid,
                            'click_month': click_month,
                            'success': activation_result['success'],
                            'error': activation_result.get('error', '')
                        })

                    return {
                        **account,
                        'activation_success': successful_count > 0,
                        'attempted_activations': len(posts_to_activate),
                        'successful_activations': successful_count,
                        'activation_details': activation_details,
                        'filtered_today_count': filtered_today_count,
                        'filtered_non_numeric_count': filtered_non_numeric_count,
                        'available_posts_count': len(available_posts),
                        'skip_reason': ''
                    }

                except Exception as e:
                    return {
                        **account,
                        'activation_success': False,
                        'attempted_activations': 0,
                        'successful_activations': 0,
                        'activation_details': [],
                        'filtered_today_count': 0,
                        'filtered_non_numeric_count': 0,
                        'available_posts_count': 0,
                        'skip_reason': '',
                        'error': str(e)
                    }

        tasks = [activate_single_account(account) for account in accounts]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    **accounts[i],
                    'activation_success': False,
                    'attempted_activations': 0,
                    'successful_activations': 0,
                    'activation_details': [],
                    'filtered_today_count': 0,
                    'filtered_non_numeric_count': 0,
                    'available_posts_count': 0,
                    'skip_reason': '',
                    'error': str(result)
                })
            else:
                processed_results.append(result)

        return processed_results
    
    async def call_activation_api(self, city: str, user_id: str, remote_id: str, webcontent: str, house_type: str) -> Dict:
        """调用激活API"""
        try:
            from api.client import AsyncYoutuiApiClient
            async_client = AsyncYoutuiApiClient(environment='production', city=city)

            xml_data = {
                'platformID': async_client.platform_id,
                'companyID': async_client.company_id,
                'userID': user_id,
                'RemoteID': remote_id,
                'type': house_type,
                'type4property': '13',
                'dbug': async_client.debug,
                'webcontent': webcontent
            }

            from utils.xml_utils import dict_to_xml
            xml_payload = dict_to_xml(xml_data)
            endpoint = '?api/house/edithouse.html'
            response_xml = await async_client.http_client.post(endpoint, xml_payload)

            if response_xml:
                from utils.xml_utils import xml_to_dict
                result = xml_to_dict(response_xml)

                if result:
                    status = result.get('status', '')
                    msg = result.get('msg', '')

                    is_success = (
                        status in ['1', '9'] or
                        '更新完毕' in msg or
                        '成功' in msg
                    )

                    if is_success:
                        return {'success': True}
                    else:
                        error_msg = msg if msg else '激活失败'
                        return {'success': False, 'error': error_msg}
                else:
                    return {'success': False, 'error': '无响应'}
            else:
                return {'success': False, 'error': '请求失败'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def save_activation_results(self, filepath: str, results: List[Dict], stats: Dict) -> None:
        """保存激活结果到文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("帖子激活结果报告\n")
                f.write("=" * 80 + "\n")
                f.write(f"激活时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"处理账号总数: {len(results)}\n")
                f.write("\n")

                # 激活统计总结
                f.write("激活统计总结\n")
                f.write("-" * 40 + "\n")
                for package_type, stat in stats.items():
                    f.write(f"{package_type}:\n")
                    f.write(f"  总账号数: {stat['总账号']}\n")
                    f.write(f"  成功激活账号: {stat['成功激活账号']}\n")
                    f.write(f"  账号成功率: {(stat['成功激活账号']/stat['总账号']*100):.1f}%\n" if stat['总账号'] > 0 else "  账号成功率: 0%\n")
                    f.write(f"  尝试激活帖子: {stat['总激活帖子']}\n")
                    f.write(f"  成功激活帖子: {stat['成功激活帖子']}\n")
                    f.write(f"  帖子成功率: {(stat['成功激活帖子']/stat['总激活帖子']*100):.1f}%\n" if stat['总激活帖子'] > 0 else "  帖子成功率: 0%\n")
                    f.write("\n")

                # 详细激活结果
                f.write("详细激活结果\n")
                f.write("=" * 80 + "\n")

                current_city = None
                for result in sorted(results, key=lambda x: x['city_name']):
                    if result['city_name'] != current_city:
                        current_city = result['city_name']
                        f.write(f"城市: {current_city}({result['city']})\n")
                        f.write("-" * 40 + "\n")

                    f.write(f"  账号ID: {result['account_id']}\n")
                    f.write(f"  用户名: {result['username']}\n")
                    f.write(f"  套餐类型: {result['package_type']}\n")
                    f.write(f"  总帖子数: {result.get('posts_count', 0)}个\n")
                    f.write(f"  今天已激活: {result.get('filtered_today_count', 0)}个帖子\n")
                    f.write(f"  非数字月点击量: {result.get('filtered_non_numeric_count', 0)}个帖子\n")
                    f.write(f"  可激活帖子: {result.get('available_posts_count', 0)}个\n")
                    f.write(f"  尝试激活: {result['attempted_activations']}个帖子\n")
                    f.write(f"  成功激活: {result['successful_activations']}个帖子\n")
                    f.write(f"  激活成功率: {(result['successful_activations']/result['attempted_activations']*100):.1f}%\n" if result['attempted_activations'] > 0 else "  激活成功率: 0%\n")

                    if result.get('skip_reason'):
                        f.write(f"  跳过原因: {result['skip_reason']}\n")

                    if result.get('error'):
                        f.write(f"  处理错误: {result['error']}\n")

                    activation_details = result.get('activation_details', [])
                    if activation_details:
                        f.write(f"  激活详情:\n")
                        for i, detail in enumerate(activation_details, 1):
                            status = "成功" if detail['success'] else f"失败({detail.get('error', '未知错误')})"
                            f.write(f"    {i}. {detail['title']} (RID: {detail['rid']}, 月点击: {detail['click_month']}) - {status}\n")

                    f.write("\n")

                f.write("激活规则说明:\n")
                f.write("-" * 40 + "\n")
                f.write("激活策略: 从缓存数据中随机选择总额度四分之一数量的帖子进行激活\n")
                f.write("激活数量: 20额度套餐激活5个帖子，40额度套餐激活10个帖子\n")
                f.write("执行频率: 每天执行4次，确保所有帖子都有机会被激活\n")
                f.write("数据来源: 直接从Redis缓存读取帖子数据，确保数据实时性\n")
                f.write("过滤规则: 自动过滤掉今天已激活的帖子(基于Retime字段)和月点击量非数字的帖子\n")
                f.write("月点击量过滤: 排除clickM字段为'校验成功'、'待校验'等非数字状态的帖子\n")
                f.write("跳过规则: 如果今天已激活数量达到每日限制，则跳过该账号的激活操作\n")
                f.write("选择规则: 从可用帖子中随机选择，不再基于月点击量排序\n")
                f.write("缓存更新: 激活成功后自动更新缓存中的Retime为当前时间，update_status为已更新\n")
                f.write("注：套餐类型基于API返回的taocan字段判断，用于统计分类\n")

        except Exception:
            # 文件保存失败，静默处理
            return
