#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取402端口与园区匹配脚本
功能：加载城市配置，获取指定城市或所有城市的402端口信息，并为每个端口分配对应城市的已发布园区
用法：python get_402_ports.py [city_code] [-d]
"""

import asyncio
import argparse
import sys
import os
import base64
import json
import logging
import random
from datetime import datetime
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

from scripts.ports.base_service import BaseService
from scripts.ports.port_service import PortService
from database.db_connection import db_cursor


class Get402PortsScript(BaseService):
    """获取402端口信息的脚本类"""

    def __init__(self):
        super().__init__()
        self.port_service = PortService()
        self.setup_allocation_logger()

    def setup_allocation_logger(self):
        """设置分配日志记录器"""
        # 创建logs目录
        logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
        os.makedirs(logs_dir, exist_ok=True)

        # 设置日志文件路径
        log_filename = f"port_community_allocation_{datetime.now().strftime('%Y%m%d')}.log"
        log_filepath = os.path.join(logs_dir, log_filename)

        # 创建分配日志记录器
        self.allocation_logger = logging.getLogger('port_allocation')
        self.allocation_logger.setLevel(logging.INFO)

        # 避免重复添加handler
        if not self.allocation_logger.handlers:
            # 创建文件handler
            file_handler = logging.FileHandler(log_filepath, encoding='utf-8')
            file_handler.setLevel(logging.INFO)

            # 创建格式器
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(formatter)

            # 添加handler到logger
            self.allocation_logger.addHandler(file_handler)

        print(f"分配日志将保存到: {log_filepath}")

    def generate_webcontent_for_account(self, account_id: str) -> str:
        """
        为单个402账号生成webcontent

        Args:
            account_id: 账号ID

        Returns:
            str: base64编码的webcontent字符串
        """
        try:
            # 构建webcontent数据结构
            webcontent_data = {
                "webarr": [
                    {
                        "webID": "402",
                        "actArr": {
                            account_id: "1"
                        }
                    }
                ]
            }

            # 转换为JSON字符串
            webcontent_json = json.dumps(webcontent_data, ensure_ascii=False)

            # 进行base64编码
            webcontent_base64 = base64.b64encode(webcontent_json.encode('utf-8')).decode('utf-8')

            return webcontent_base64

        except Exception as e:
            self.allocation_logger.error(f"生成账号 {account_id} 的webcontent失败: {str(e)}")
            return ""

    def get_account_quota_from_cache(self, account_id: str) -> Dict[str, Any]:
        """
        从Redis缓存中获取账号的额度信息

        Args:
            account_id: 账号ID

        Returns:
            Dict[str, Any]: 包含额度信息的字典
        """
        quota_info = {
            "has_cache": False,
            "total_quota": 0,
            "used_quota": 0,
            "remaining_quota": 0,
            "package_type": "未知套餐",
            "taocan_raw": "",
            "error_message": ""
        }

        try:
            if not self.redis_conn:
                quota_info["error_message"] = "Redis连接不可用"
                return quota_info

            # 构建缓存键
            cache_key = f"agent_posts:{account_id}"

            # 获取缓存数据
            cached_data = self.redis_conn.get(cache_key)
            if not cached_data:
                quota_info["error_message"] = "缓存中未找到数据"
                return quota_info

            # 解析缓存数据
            cached_result = json.loads(cached_data)

            # 检查status字段，如果为"0"表示没有有效缓存
            status = cached_result.get('status', '')
            if status == "0":
                quota_info["error_message"] = "缓存状态为0，无有效数据"
                return quota_info

            # 检查是否包含taocan字段
            taocan = cached_result.get('taocan', '')
            if taocan:
                # 有taocan字段，直接解析
                quota_info["has_cache"] = True
                quota_info["taocan_raw"] = taocan

                # 解析taocan字段获取额度信息
                package_info = self.get_package_info_from_taocan(taocan)
                quota_info["package_type"] = package_info.get("package_type", "未知套餐")
                quota_info["total_quota"] = package_info.get("total_quota", 0)

                # 从taocan中解析已用和剩余额度
                if '-' in taocan:
                    parts = taocan.split('-')
                    if len(parts) == 2:
                        try:
                            quota_info["used_quota"] = int(parts[0])
                            quota_info["remaining_quota"] = int(parts[1])
                        except (ValueError, IndexError):
                            quota_info["error_message"] = f"taocan格式解析失败: {taocan}"
                    else:
                        quota_info["error_message"] = f"taocan格式不正确: {taocan}"
                else:
                    quota_info["error_message"] = f"taocan格式不正确: {taocan}"
            else:
                # 没有taocan字段，通过msg中的帖子数量推断套餐类型
                quota_info["taocan_raw"] = "无taocan字段"

                try:
                    # 获取msg字段中的帖子数量
                    msg_content = cached_result.get('msg', '')
                    post_count = 0

                    if isinstance(msg_content, str):
                        # 解析JSON字符串
                        posts_list = json.loads(msg_content)
                        if isinstance(posts_list, list):
                            post_count = len(posts_list)
                    elif isinstance(msg_content, list):
                        # 如果msg直接是列表
                        post_count = len(msg_content)

                    # 只有成功解析到帖子数量才设置has_cache为True
                    quota_info["has_cache"] = True

                    # 根据帖子数量推断套餐类型
                    if post_count <= 20:
                        quota_info["package_type"] = "20额度套餐"
                        quota_info["total_quota"] = 20
                        quota_info["used_quota"] = post_count
                        quota_info["remaining_quota"] = 20 - post_count
                    elif post_count <= 40:
                        quota_info["package_type"] = "40额度套餐"
                        quota_info["total_quota"] = 40
                        quota_info["used_quota"] = post_count
                        quota_info["remaining_quota"] = 40 - post_count
                    else:
                        quota_info["package_type"] = "超大套餐"
                        quota_info["total_quota"] = post_count
                        quota_info["used_quota"] = post_count
                        quota_info["remaining_quota"] = 0

                except (json.JSONDecodeError, TypeError) as e:
                    quota_info["error_message"] = f"无taocan字段且msg解析失败: {str(e)}"

        except json.JSONDecodeError as e:
            quota_info["error_message"] = f"缓存数据JSON解析失败: {str(e)}"
        except Exception as e:
            quota_info["error_message"] = f"查询缓存失败: {str(e)}"

        return quota_info

    def get_marketing_content_for_parks(self, park_names: List[str]) -> Dict[str, List[Dict]]:
        """
        从property_marketing_content表中查询指定园区的营销内容

        Args:
            park_names: 园区名称列表

        Returns:
            Dict[str, List[Dict]]: 按园区名称分组的营销内容字典
        """
        marketing_content_map = {}

        if not park_names:
            return marketing_content_map

        try:
            with db_cursor() as cursor:
                # 构建IN子句的占位符
                placeholders = ', '.join(['%s'] * len(park_names))

                # 查询指定园区的营销内容，只获取status=1的记录
                sql = f"""
                    SELECT id, park_name, title, selling_points, description, created_at
                    FROM property_marketing_content
                    WHERE park_name IN ({placeholders}) AND status = 1
                    ORDER BY park_name, created_at DESC
                """

                cursor.execute(sql, park_names)
                results = cursor.fetchall()

                # 按园区名称分组
                for row in results:
                    park_name = row['park_name']
                    if park_name not in marketing_content_map:
                        marketing_content_map[park_name] = []

                    marketing_content_map[park_name].append({
                        'id': row['id'],
                        'park_name': park_name,
                        'title': row['title'],
                        'selling_points': row['selling_points'],
                        'description': row['description'],
                        'created_at': row['created_at']
                    })

                self.allocation_logger.info(f"查询到 {len(results)} 条营销内容，覆盖 {len(marketing_content_map)} 个园区")

        except Exception as e:
            self.allocation_logger.error(f"查询营销内容失败: {str(e)}")

        return marketing_content_map

    def assign_marketing_content_to_communities(self, assigned_communities: List[str], marketing_content_map: Dict[str, List[Dict]], factory_data_map: Dict[str, List[Dict]]) -> List[Dict]:
        """
        为分配的园区列表分配营销内容和房源数据

        Args:
            assigned_communities: 分配的园区名称列表
            marketing_content_map: 园区营销内容映射字典
            factory_data_map: 园区房源数据映射字典

        Returns:
            List[Dict]: 包含园区、营销内容和房源数据的分配结果列表
        """
        assigned_with_content = []

        for community in assigned_communities:
            # 查找该园区的营销内容
            available_content = marketing_content_map.get(community, [])
            # 查找该园区的房源数据
            available_factory_data = factory_data_map.get(community, [])

            # 选择营销内容
            if available_content:
                selected_content = random.choice(available_content)
                title = selected_content['title']
                selling_points = selected_content['selling_points']
                description = selected_content['description']
                content_id = selected_content['id']
            else:
                title = '暂无标题'
                selling_points = '暂无营销点'
                description = '暂无描述'
                content_id = None

            # 选择房源数据
            if available_factory_data:
                selected_factory = random.choice(available_factory_data)
                factory_data = selected_factory
            else:
                factory_data = None

            assigned_with_content.append({
                'park_name': community,
                'title': title,
                'selling_points': selling_points,
                'description': description,
                'content_id': content_id,
                'factory_data': factory_data,
                'has_factory_data': factory_data is not None
            })

        return assigned_with_content

    def get_factory_data_for_parks(self, park_names: List[str], city: str) -> Dict[str, List[Dict]]:
        """
        从parsed_factory_data表中查询指定园区的房源数据

        Args:
            park_names: 园区名称列表
            city: 城市代码

        Returns:
            Dict[str, List[Dict]]: 按园区名称分组的房源数据字典
        """
        factory_data_map = {}

        if not park_names:
            return factory_data_map

        try:
            with db_cursor() as cursor:
                # 构建IN子句的占位符
                placeholders = ', '.join(['%s'] * len(park_names))

                # 查询指定园区的房源数据，只获取已发布且未删除的记录
                sql = f"""
                    SELECT
                        id, erp_id, youtui_house_id, city, city_name,
                        topic, zone, street, type, towards, community, content,
                        total, square, address, type4property, floor,
                        floortype, Storey, rentunitdaily, slease, Planteia,
                        Plantstructure, Typewarehouse, Plantnew, isNewHouse,
                        AgentFree, infrastructure, video_url, years, Type4Years,
                        Landnature, Powersupply, Loadbearing
                    FROM parsed_factory_data
                    WHERE community IN ({placeholders})
                    AND city = %s
                    AND is_deleted = 0
                    AND is_published = 1
                    AND youtui_house_id IS NOT NULL
                    ORDER BY community, id
                """

                params = park_names + [city]
                cursor.execute(sql, params)
                results = cursor.fetchall()

                # 按园区名称分组
                for row in results:
                    community = row['community']
                    if community not in factory_data_map:
                        factory_data_map[community] = []

                    factory_data_map[community].append(dict(row))

                self.allocation_logger.info(f"查询到 {len(results)} 条房源数据，覆盖 {len(factory_data_map)} 个园区")

        except Exception as e:
            self.allocation_logger.error(f"查询房源数据失败: {str(e)}")

        return factory_data_map

    async def execute_push_for_account(self, account_info: Dict, city: str, website_id: str, user_id: str) -> List[Dict]:
        """
        为单个账号的所有分配内容执行推送

        Args:
            account_info: 账号信息，包含分配的园区和内容
            city: 城市代码
            website_id: 网站ID
            user_id: 用户ID

        Returns:
            List[Dict]: 推送结果列表
        """
        push_results = []
        communities_with_content = account_info.get("assigned_communities_with_content", [])

        if not communities_with_content:
            return push_results

        try:
            # 导入推送相关模块
            from api.client import AsyncYoutuiApiClient
            from api.push_routes import get_push_service
            from ai_generate_content.sensitive_word_filter_service import async_filter_sensitive_words
            from services.gallery_image_service import gallery_image_service
            import html

            # 创建推送服务实例
            service = get_push_service(city=city)
            async_client = AsyncYoutuiApiClient(environment='production', city=city)

            for content in communities_with_content:
                if not content.get('has_factory_data'):
                    # 没有房源数据，跳过推送
                    push_results.append({
                        'park_name': content['park_name'],
                        'title': content['title'],
                        'push_success': False,
                        'error_message': '无房源数据，跳过推送',
                        'process_id': None
                    })
                    continue

                factory_data = content['factory_data']

                # 必须使用从property_marketing_content匹配的标题和内容
                title = content['title']
                description = content['description']

                # 如果没有营销内容，跳过推送
                if title == '暂无标题' or description == '暂无描述':
                    push_results.append({
                        'park_name': content['park_name'],
                        'title': title,
                        'push_success': False,
                        'error_message': '无营销内容，跳过推送',
                        'process_id': None
                    })
                    continue

                # 敏感词过滤
                try:
                    filter_result = await async_filter_sensitive_words(title, description)
                    filtered_title = filter_result.get('filtered_title', title)
                    filtered_content = filter_result.get('filtered_description', description)
                except Exception as e:
                    self.allocation_logger.warning(f"敏感词过滤失败: {str(e)}")
                    filtered_title = title
                    filtered_content = description

                # 构建推送数据
                push_data = {
                    'platformID': '10007',
                    'companyID': '1013',
                    'userID': user_id,
                    'act': 'PUSH',
                    'ttid': factory_data.get('youtui_house_id', '0'),
                    'dbug': 'TRUE',
                    'id': factory_data.get('erp_id', ''),
                    'topic': filtered_title,
                    'zone': factory_data.get('zone', '开发区'),
                    'street': factory_data.get('street', '产业园区'),
                    'type': factory_data.get('type', '6'),
                    'community': factory_data.get('address', content['park_name']),
                    'content': filtered_content,
                    'total': factory_data.get('total', '面议'),
                    'square': factory_data.get('square', '1000'),
                    'address': factory_data.get('address', content['park_name']),
                    'type4property': factory_data.get('type4property', '厂房'),
                    'floor': factory_data.get('floor', '1'),
                    'floortype': factory_data.get('floortype', '首层'),
                    'Storey': factory_data.get('Storey', '6'),
                    'webid': website_id,
                    'webcontent': account_info['port_info']['webcontent']
                }

                # 添加必填字段
                required_fields = [
                    'rentunitdaily', 'slease', 'Planteia', 'Plantstructure',
                    'Typewarehouse', 'Plantnew', 'isNewHouse', 'AgentFree',
                    'infrastructure', 'years', 'Landnature', 'Powersupply', 'Loadbearing'
                ]

                # 直接设置towards字段为固定值，不再从数据库获取
                push_data['towards'] = "东南朝向"
                push_data['Type4Yearend'] = "2073"

                # 处理其他必填字段
                for field in required_fields:
                    push_data[field] = factory_data.get(field, '')

                # 添加图片字段 - 使用与推送接口相同的逻辑
                community_name = content['park_name']
                image_fields_from_db = {
                    'thumb': '',
                    'floorplans': '',
                    'photointerior': '',
                    'photooutdoor': ''
                }

                if community_name:
                    try:
                        # 使用新的图片服务获取并分配图片
                        # 策略: 'random_table' - 随机选择一个表
                        image_fields_from_db, strategy_desc = gallery_image_service.get_assigned_gallery_images(
                            park_name=community_name,
                            strategy='random_table'
                        )
                        self.allocation_logger.info(f"图片字段从数据库获取: {image_fields_from_db}")
                    except Exception as e:
                        self.allocation_logger.warning(f"获取图片失败: {str(e)}")

                # 将图片字段添加到推送数据中
                for field, url_string in image_fields_from_db.items():
                    # 无论是否为空，都添加字段
                    push_data[field] = html.unescape(url_string) if url_string else ''

                # 添加视频URL
                video_url = factory_data.get('video_url')
                if video_url:
                    push_data['videoUrl'] = video_url

                # 执行推送
                try:
                    result = await async_client.async_sync_house(
                        user_id=user_id,
                        house_data=push_data,
                        action='PUSH',
                        ttid=factory_data.get('youtui_house_id', '0')
                    )

                    if result and result.get('status') == '1':
                        process_id = result.get('processinfo')
                        house_id = result.get('houseid') or result.get('houseId') or result.get('HOUSEID')

                        # 保存推送记录和更新缓存
                        try:
                            # 提取账号ID
                            account_id = account_info['port_info']['account_id']

                            # 更新parsed_factory_data表的推送状态
                            with db_cursor() as cursor:
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                cursor.execute("""
                                    UPDATE parsed_factory_data
                                    SET ts_status = 1,
                                        update_time = %s
                                    WHERE id = %s
                                """, [current_time, factory_data.get('id')])

                                # 保存推送历史记录
                                from services.push_service import WEBSITE_NAMES
                                cursor.execute("""
                                    INSERT INTO push_history (
                                        push_time, erp_house_id, tt_id, website_id,
                                        website_name, city, process_id, account_id,
                                        push_topic, push_content, community
                                    ) VALUES (
                                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                                    )
                                """, [
                                    current_time,
                                    factory_data.get("erp_id"),
                                    factory_data.get("youtui_house_id"),
                                    website_id,
                                    WEBSITE_NAMES.get(website_id, f"网站{website_id}"),
                                    city,
                                    process_id,
                                    account_id,
                                    filtered_title,
                                    filtered_content,
                                    content['park_name']
                                ])

                            # 更新Redis缓存
                            if account_id and house_id:
                                try:
                                    from services.youtui_service import YoutuiService
                                    youtui_service = YoutuiService(city=city)
                                    youtui_service._add_pushed_post_to_cache(
                                        account_id=account_id,
                                        house_id=house_id,
                                        title=filtered_title,
                                        website_id=website_id,
                                        process_id=process_id,
                                        city=city,
                                        house_type=factory_data.get("type", "6")
                                    )
                                    self.allocation_logger.info(f"缓存更新成功 - 账号: {account_id}, 房源ID: {house_id}")
                                except Exception as cache_error:
                                    self.allocation_logger.warning(f"推送成功但缓存更新失败: {str(cache_error)}")

                        except Exception as record_error:
                            self.allocation_logger.warning(f"推送成功但记录保存失败: {str(record_error)}")

                        push_results.append({
                            'park_name': content['park_name'],
                            'title': filtered_title,
                            'push_success': True,
                            'process_id': process_id,
                            'house_id': house_id,
                            'error_message': None
                        })

                        self.allocation_logger.info(f"推送成功 - 园区: {content['park_name']}, 进程ID: {process_id}, 房源ID: {house_id}")
                    else:
                        error_msg = result.get('msg', '未知错误') if result else '无响应'
                        push_results.append({
                            'park_name': content['park_name'],
                            'title': filtered_title,
                            'push_success': False,
                            'error_message': f"推送失败: {error_msg}",
                            'process_id': None
                        })

                        self.allocation_logger.error(f"推送失败 - 园区: {content['park_name']}, 错误: {error_msg}")

                except Exception as push_error:
                    push_results.append({
                        'park_name': content['park_name'],
                        'title': filtered_title,
                        'push_success': False,
                        'error_message': f"推送异常: {str(push_error)}",
                        'process_id': None
                    })

                    self.allocation_logger.error(f"推送异常 - 园区: {content['park_name']}, 异常: {str(push_error)}")

        except Exception as e:
            self.allocation_logger.error(f"账号推送执行失败: {str(e)}")

        return push_results

    async def batch_push_communities_content(self, results: List[Dict], website_id: str, max_concurrent: int = 3) -> List[Dict]:
        """
        批量处理所有账号的推送任务

        Args:
            results: 城市处理结果列表
            website_id: 网站ID
            max_concurrent: 最大并发数

        Returns:
            List[Dict]: 批量推送结果
        """
        all_push_results = []

        try:
            # 收集所有需要推送的账号
            push_tasks = []

            for city_result in results:
                if not city_result.get("success"):
                    continue

                city = city_result.get("city")
                city_name = city_result.get("city_name")
                port_community_pairs = city_result.get("port_community_pairs", [])

                # 获取城市的用户ID
                try:
                    from api.push_routes import get_push_service
                    service = get_push_service(city=city)
                    user_id = service.default_user_id
                except Exception as e:
                    self.allocation_logger.error(f"获取城市 {city} 的用户ID失败: {str(e)}")
                    continue

                for pair in port_community_pairs:
                    port_info = pair["port_info"]
                    communities_with_content = pair.get("assigned_communities_with_content", [])

                    # 只处理有分配内容且有房源数据的账号
                    if communities_with_content and any(c.get('has_factory_data') for c in communities_with_content):
                        push_tasks.append({
                            'city': city,
                            'city_name': city_name,
                            'account_id': port_info['account_id'],
                            'username': port_info['username'],
                            'account_info': pair,
                            'user_id': user_id
                        })

            if not push_tasks:
                self.allocation_logger.info("没有找到需要推送的账号")
                return all_push_results

            self.allocation_logger.info(f"开始批量推送，共 {len(push_tasks)} 个账号需要推送")

            # 使用信号量控制并发数
            semaphore = asyncio.Semaphore(max_concurrent)

            async def push_single_account(task):
                async with semaphore:
                    try:
                        self.allocation_logger.info(f"开始推送账号: {task['account_id']} ({task['username']})")

                        push_results = await self.execute_push_for_account(
                            task['account_info'],
                            task['city'],
                            website_id,
                            task['user_id']
                        )

                        return {
                            'city': task['city'],
                            'city_name': task['city_name'],
                            'account_id': task['account_id'],
                            'username': task['username'],
                            'push_results': push_results,
                            'success': True,
                            'error_message': None
                        }

                    except Exception as e:
                        self.allocation_logger.error(f"账号 {task['account_id']} 推送失败: {str(e)}")
                        return {
                            'city': task['city'],
                            'city_name': task['city_name'],
                            'account_id': task['account_id'],
                            'username': task['username'],
                            'push_results': [],
                            'success': False,
                            'error_message': str(e)
                        }

            # 并发执行推送任务
            push_tasks_coroutines = [push_single_account(task) for task in push_tasks]
            batch_results = await asyncio.gather(*push_tasks_coroutines, return_exceptions=True)

            # 处理结果
            for result in batch_results:
                if isinstance(result, dict):
                    all_push_results.append(result)
                else:
                    self.allocation_logger.error(f"推送任务异常: {result}")

            # 统计推送结果
            total_accounts = len(all_push_results)
            successful_accounts = sum(1 for r in all_push_results if r['success'])
            total_pushes = sum(len(r['push_results']) for r in all_push_results)
            successful_pushes = sum(len([p for p in r['push_results'] if p['push_success']]) for r in all_push_results)

            self.allocation_logger.info(f"批量推送完成 - 账号: {successful_accounts}/{total_accounts}, 推送: {successful_pushes}/{total_pushes}")

        except Exception as e:
            self.allocation_logger.error(f"批量推送失败: {str(e)}")

        return all_push_results

    def display_push_results(self, push_results: List[Dict]):
        """显示推送结果"""
        if not push_results:
            print("没有推送结果")
            return

        print("\n" + "="*100)
        print("推送结果汇总")
        print("="*100)

        # 统计信息
        total_accounts = len(push_results)
        successful_accounts = sum(1 for r in push_results if r['success'])
        total_pushes = sum(len(r['push_results']) for r in push_results)
        successful_pushes = sum(len([p for p in r['push_results'] if p['push_success']]) for r in push_results)

        print(f"推送账号总数: {total_accounts}")
        print(f"成功推送账号: {successful_accounts}")
        print(f"推送任务总数: {total_pushes}")
        print(f"成功推送任务: {successful_pushes}")
        if total_pushes > 0:
            success_rate = (successful_pushes / total_pushes) * 100
            print(f"推送成功率: {success_rate:.1f}%")
        print()

        # 详细结果
        print("详细推送结果:")
        print("─" * 100)

        for result in push_results:
            city_name = result['city_name']
            account_id = result['account_id']
            username = result['username']
            account_success = "✅" if result['success'] else "❌"

            print(f"\n🏙️ 城市: {city_name} | 账号: {account_id} ({username}) {account_success}")

            if not result['success']:
                print(f"   ❌ 账号推送失败: {result['error_message']}")
                continue

            push_results_list = result['push_results']
            if not push_results_list:
                print("   ℹ️ 该账号没有推送任务")
                continue

            for i, push_result in enumerate(push_results_list, 1):
                park_name = push_result['park_name']
                title = push_result['title'][:30] + "..." if len(push_result['title']) > 30 else push_result['title']
                success_icon = "✅" if push_result['push_success'] else "❌"

                print(f"   {i}. {success_icon} 园区: {park_name}")
                print(f"      标题: {title}")

                if push_result['push_success']:
                    process_id = push_result.get('process_id', '未知')
                    print(f"      进程ID: {process_id}")
                else:
                    error_msg = push_result.get('error_message', '未知错误')
                    print(f"      错误: {error_msg}")
                print()

        print("="*100)

    def get_city_factory_info(self, city: str) -> Dict[str, Any]:
        """获取城市房源信息"""
        info = {
            "total_factories": 0,
            "published_factories": 0,
            "published_communities": [],
            "error_message": ""
        }

        try:
            with db_cursor() as cursor:
                # 查询房源统计信息
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_count,
                        SUM(CASE WHEN is_published = 1 THEN 1 ELSE 0 END) as published_count
                    FROM parsed_factory_data
                    WHERE city = %s AND is_deleted = 0
                """, [city])

                result = cursor.fetchone()
                if result:
                    # 确保数值类型正确，处理Decimal和None值
                    info["total_factories"] = int(result.get("total_count") or 0)
                    info["published_factories"] = int(result.get("published_count") or 0)

                # 查询已发布的园区列表
                cursor.execute("""
                    SELECT DISTINCT community
                    FROM parsed_factory_data
                    WHERE city = %s AND is_deleted = 0 AND is_published = 1
                    AND community IS NOT NULL AND community != ''
                    ORDER BY community
                """, [city])

                communities = cursor.fetchall()
                info["published_communities"] = [c["community"] for c in communities if c["community"]]

        except Exception as e:
            info["error_message"] = f"查询房源信息失败: {str(e)}"

        return info
    
    async def get_city_402_ports_with_communities(self, city: str, city_name: str) -> Dict[str, Any]:
        """获取单个城市的402端口详细信息并分配园区"""
        result = {
            "city": city,
            "city_name": city_name,
            "success": False,
            "port_community_pairs": [],
            "error_message": ""
        }

        try:
            from api.client import AsyncYoutuiApiClient

            # 获取城市账号配置
            accounts = self.cities_config.get("accounts", {})
            city_accounts = accounts.get(city, {})
            user_id = city_accounts.get("user_id")
            user_key = city_accounts.get("user_key")

            if not user_id or not user_key:
                result["error_message"] = f"城市 {city} 没有配置用户账号信息"
                return result

            # 获取端口信息
            async_client = AsyncYoutuiApiClient(environment='production', city=city)
            api_result = await async_client.async_get_website_accounts(user_id, user_key, timeout=None)

            if not api_result or api_result.get('status') != '1':
                error_msg = api_result.get('msg') if api_result else "API调用失败"
                result["error_message"] = f"获取网站数据失败: {error_msg}"
                return result

            # 解析端口数据
            content = api_result.get('content', '')
            decoded_content = base64.b64decode(content).decode('utf-8')
            website_data = json.loads(decoded_content)

            if not isinstance(website_data, list):
                result["error_message"] = f"API返回的数据格式不正确"
                return result

            # 获取已发布的园区列表
            factory_info = self.get_city_factory_info(city)
            published_communities = factory_info.get("published_communities", [])

            # 查询园区的营销内容
            marketing_content_map = self.get_marketing_content_for_parks(published_communities)

            # 查询园区的房源数据
            factory_data_map = self.get_factory_data_for_parks(published_communities, city)

            # 记录城市基础信息到日志
            self.allocation_logger.info(f"开始处理城市: {city_name} ({city})")
            self.allocation_logger.info(f"该城市已发布园区数量: {len(published_communities)}")
            if published_communities:
                self.allocation_logger.info(f"已发布园区列表: {', '.join(published_communities)}")
            else:
                self.allocation_logger.warning(f"城市 {city_name} 暂无已发布园区")

            # 提取402端口信息
            port_accounts = []
            for website in website_data:
                web_id = str(website.get('webID', ''))
                if web_id == '402':  # 只处理402端口
                    user_name_arr = website.get('userNameArr', {})
                    for account_id, account_info in user_name_arr.items():
                        if not account_info or len(account_info) < 3:
                            continue

                        username = account_info[0] if account_info[0] else '未知'
                        status = account_info[2] if len(account_info) > 2 else ""
                        is_available = "可用" in status or status == "可用"

                        # 为每个402账号生成webcontent
                        webcontent = self.generate_webcontent_for_account(account_id)

                        # 查询账号的额度信息
                        quota_info = self.get_account_quota_from_cache(account_id)

                        port_accounts.append({
                            "web_id": web_id,
                            "account_id": account_id,
                            "username": username,
                            "status": status,
                            "is_available": is_available,
                            "webcontent": webcontent,
                            "quota_info": quota_info
                        })

            self.allocation_logger.info(f"城市 {city_name} 找到402端口数量: {len(port_accounts)}")
            available_count = sum(1 for p in port_accounts if p["is_available"])
            cached_count = sum(1 for p in port_accounts if p["quota_info"]["has_cache"])
            self.allocation_logger.info(f"城市 {city_name} 可用402端口数量: {available_count}")
            self.allocation_logger.info(f"城市 {city_name} 有缓存数据的402端口数量: {cached_count}")

            # 为每个402端口分配园区
            port_community_pairs = []

            self.allocation_logger.info(f"开始为城市 {city_name} 的402端口分配园区...")

            for i, port in enumerate(port_accounts):
                quota_info = port["quota_info"]
                quota_desc = f"额度: {quota_info['remaining_quota']}/{quota_info['total_quota']} ({quota_info['package_type']})" if quota_info["has_cache"] else "额度: 无缓存数据"

                # 判断是否应该分配园区：账号可用 且 有缓存数据 且 剩余额度大于0
                should_assign_community = (
                    port["is_available"] and
                    quota_info.get("has_cache", False) and
                    quota_info.get("remaining_quota", 0) > 0
                )

                if should_assign_community:
                    # 根据剩余额度数量分配相应数量的园区
                    remaining_quota = quota_info.get("remaining_quota", 0)
                    assigned_communities = []
                    assigned_communities_with_content = []

                    if published_communities:
                        # 为每个剩余额度分配一个园区（允许重复）
                        for _ in range(remaining_quota):
                            assigned_communities.append(random.choice(published_communities))

                        # 为分配的园区分配营销内容和房源数据
                        assigned_communities_with_content = self.assign_marketing_content_to_communities(
                            assigned_communities, marketing_content_map, factory_data_map
                        )

                        assigned_community = f"分配{len(assigned_communities)}个园区: {', '.join(assigned_communities)}"
                    else:
                        assigned_community = "暂无可用园区"

                    # 记录分配信息到日志
                    content_summary = []
                    for content in assigned_communities_with_content:
                        content_summary.append(f"{content['park_name']}({content['title'][:10]}...)")

                    self.allocation_logger.info(
                        f"端口分配 - 城市: {city_name}, "
                        f"账号ID: {port['account_id']}, "
                        f"用户名: {port['username']}, "
                        f"状态: 可用, "
                        f"{quota_desc}, "
                        f"分配园区: {assigned_community}, "
                        f"营销内容: {', '.join(content_summary) if content_summary else '无'}, "
                        f"webcontent: {port['webcontent'][:50]}..."
                    )
                else:
                    # 不符合条件的账号不分配园区
                    assigned_communities_with_content = []
                    if not port["is_available"]:
                        assigned_community = "账号不可用，无需分配园区"
                        skip_reason = "账号不可用"
                    elif not quota_info.get("has_cache", False):
                        assigned_community = "无缓存数据，暂不分配园区"
                        skip_reason = "无缓存数据"
                    elif quota_info.get("remaining_quota", 0) <= 0:
                        assigned_community = "剩余额度为0，暂不分配园区"
                        skip_reason = "剩余额度为0"
                    else:
                        assigned_community = "未知原因，暂不分配园区"
                        skip_reason = "未知原因"

                    # 记录跳过信息到日志
                    self.allocation_logger.info(
                        f"端口跳过 - 城市: {city_name}, "
                        f"账号ID: {port['account_id']}, "
                        f"用户名: {port['username']}, "
                        f"状态: {'可用' if port['is_available'] else '不可用'}, "
                        f"{quota_desc}, "
                        f"跳过原因: {skip_reason}"
                    )

                port_community_pairs.append({
                    "port_info": port,
                    "assigned_community": assigned_community,
                    "assigned_communities_with_content": assigned_communities_with_content
                })

            # 统计实际分配园区的账号数量
            actually_assigned_count = sum(1 for p in port_community_pairs
                                         if p["port_info"]["is_available"]
                                         and p["port_info"].get("quota_info", {}).get("has_cache", False)
                                         and p["port_info"].get("quota_info", {}).get("remaining_quota", 0) > 0
                                         and not p["assigned_community"].startswith(("账号不可用", "无缓存数据", "剩余额度为0", "未知原因")))
            available_count = sum(1 for p in port_community_pairs if p["port_info"]["is_available"])

            self.allocation_logger.info(f"城市 {city_name} 端口园区分配完成，共处理 {len(port_community_pairs)} 个端口，其中 {available_count} 个可用端口，{actually_assigned_count} 个端口实际分配了园区")

            result["port_community_pairs"] = port_community_pairs
            result["success"] = True

        except Exception as e:
            result["error_message"] = f"处理城市失败: {str(e)}"

        return result
    
    async def process_cities(self, target_city: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理城市列表，获取402端口信息"""
        domains = self.cities_config.get("domains", {})
        names = self.cities_config.get("names", {})
        
        if not domains:
            print("未找到城市配置信息")
            return []

        # 确定要处理的城市列表
        if target_city:
            if target_city not in domains:
                print(f"城市代码 '{target_city}' 不存在")
                available_cities = ", ".join(list(domains.keys())[:10])
                print(f"可用的城市代码示例: {available_cities}...")
                return []
            cities_to_process = [target_city]
        else:
            cities_to_process = list(domains.keys())

        print(f"开始获取 {len(cities_to_process)} 个城市的402端口信息...")
        
        # 并发处理城市
        tasks = []
        semaphore = asyncio.Semaphore(5)  # 限制并发数
        
        async def process_single_city(city: str):
            async with semaphore:
                city_name = names.get(city, city)
                return await self.get_city_402_ports_with_communities(city, city_name)
        
        for city in cities_to_process:
            task = asyncio.create_task(process_single_city(city))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤异常结果
        valid_results = []
        for result in results:
            if isinstance(result, dict):
                valid_results.append(result)
            else:
                print(f"处理城市时发生异常: {result}")

        return valid_results

    def display_results(self, results: List[Dict[str, Any]], show_details: bool = False, show_webcontent: bool = False):
        """显示402端口与园区匹配结果"""
        if not results:
            print("没有获取到任何402端口信息")
            return

        # 统计汇总信息
        total_cities = len(results)
        success_cities = sum(1 for r in results if r["success"])
        total_port_pairs = sum(len(r.get("port_community_pairs", [])) for r in results if r["success"])
        available_port_pairs = sum(
            len([p for p in r.get("port_community_pairs", []) if p["port_info"]["is_available"]])
            for r in results if r["success"]
        )

        # 统计实际分配园区的端口数量
        assigned_port_pairs = sum(
            len([p for p in r.get("port_community_pairs", [])
                 if p["port_info"]["is_available"]
                 and p["port_info"].get("quota_info", {}).get("has_cache", False)
                 and p["port_info"].get("quota_info", {}).get("remaining_quota", 0) > 0
                 and not p["assigned_community"].startswith(("账号不可用", "无缓存数据", "剩余额度为0", "未知原因"))])
            for r in results if r["success"]
        )

        # 统计额度相关信息
        cached_port_pairs = sum(
            len([p for p in r.get("port_community_pairs", []) if p["port_info"].get("quota_info", {}).get("has_cache", False)])
            for r in results if r["success"]
        )
        total_remaining_quota = sum(
            sum(p["port_info"].get("quota_info", {}).get("remaining_quota", 0)
                for p in r.get("port_community_pairs", [])
                if p["port_info"].get("quota_info", {}).get("has_cache", False))
            for r in results if r["success"]
        )
        total_used_quota = sum(
            sum(p["port_info"].get("quota_info", {}).get("used_quota", 0)
                for p in r.get("port_community_pairs", [])
                if p["port_info"].get("quota_info", {}).get("has_cache", False))
            for r in results if r["success"]
        )



        # 显示详细信息
        if show_details:
            print("\n" + "="*100)
            print("🏭 各城市402端口与园区分配详情")
            print("="*100)

            # 按城市名称排序
            sorted_results = sorted(results, key=lambda x: x.get('city_name', ''))

            for result in sorted_results:
                city_name = result.get('city_name', result.get('city', ''))
                port_pairs = result.get('port_community_pairs', [])
                status = "✅ 成功" if result.get('success') else "❌ 失败"

                print(f"\n🏙️  城市: {city_name} ({status})")
                print("─" * 100)

                if not port_pairs:
                    if result.get('error_message'):
                        print(f"   ⚠️  错误: {result['error_message']}")
                    else:
                        print("   ℹ️  该城市没有402端口")
                    continue

                if show_webcontent:
                    print(f"{'序号':<6} {'账号ID':<18} {'用户名':<20} {'状态':<12} {'剩余/总额度':<15} {'套餐类型':<15}")
                    print("─" * 100)
                    print(f"{'分配的园区和营销内容详情'}")
                    print("─" * 100)
                    print(f"{'Webcontent (Base64编码的推送标识)'}")
                    print("─" * 100)
                else:
                    print(f"{'序号':<6} {'账号ID':<18} {'用户名':<20} {'状态':<12} {'剩余/总额度':<15} {'套餐类型':<15}")
                    print("─" * 100)
                    print(f"{'分配的园区和营销内容详情'}")
                    print("─" * 100)

                for i, pair in enumerate(port_pairs, 1):
                    port_info = pair["port_info"]
                    community = pair["assigned_community"]
                    quota_info = port_info.get("quota_info", {})
                    communities_with_content = pair.get("assigned_communities_with_content", [])

                    account_id = port_info["account_id"]
                    username = port_info["username"][:18] + "..." if len(port_info["username"]) > 18 else port_info["username"]
                    status_text = "🟢 可用" if port_info["is_available"] else "🔴 不可用"

                    # 额度信息
                    if quota_info.get("has_cache", False):
                        quota_text = f"{quota_info['remaining_quota']}/{quota_info['total_quota']}"
                        package_text = quota_info.get("package_type", "未知")[:13] + "..." if len(quota_info.get("package_type", "")) > 13 else quota_info.get("package_type", "未知")
                    else:
                        quota_text = "无缓存"
                        package_text = "无缓存"

                    # 基本信息行
                    print(f"{i:<6} {account_id:<18} {username:<20} {status_text:<12} {quota_text:<15} {package_text:<15}")

                    # 显示分配的园区和营销内容详情
                    if communities_with_content:
                        for j, content in enumerate(communities_with_content, 1):
                            park_name = content['park_name']
                            title = content['title'][:30] + "..." if len(content['title']) > 30 else content['title']
                            description = content['description'][:50] + "..." if len(content['description']) > 50 else content['description']
                            has_factory_data = content.get('has_factory_data', False)
                            factory_status = "✅ 有房源数据" if has_factory_data else "❌ 无房源数据"

                            print(f"       园区{j}: {park_name} ({factory_status})")
                            print(f"       标题: {title}")
                            print(f"       描述: {description}")
                            print()
                    else:
                        # 没有分配园区的情况
                        print(f"       {community}")
                        print()

                    # webcontent信息行（如果启用）
                    if show_webcontent:
                        webcontent = port_info.get("webcontent", "")
                        print(f"       Webcontent: {webcontent}")
                        if quota_info.get("error_message"):
                            print(f"       额度查询错误: {quota_info['error_message']}")
                        print()

                available_count = sum(1 for p in port_pairs if p["port_info"]["is_available"])
                cached_count = sum(1 for p in port_pairs if p["port_info"].get("quota_info", {}).get("has_cache", False))
                city_assigned_count = sum(1 for p in port_pairs
                                        if p["port_info"]["is_available"]
                                        and p["port_info"].get("quota_info", {}).get("has_cache", False)
                                        and p["port_info"].get("quota_info", {}).get("remaining_quota", 0) > 0
                                        and not p["assigned_community"].startswith(("账号不可用", "无缓存数据", "剩余额度为0", "未知原因")))
                city_remaining_quota = sum(p["port_info"].get("quota_info", {}).get("remaining_quota", 0)
                                         for p in port_pairs if p["port_info"].get("quota_info", {}).get("has_cache", False))

                print(f"\n   📊 小计: 402端口 {len(port_pairs)} 个，可用 {available_count} 个，有缓存 {cached_count} 个，分配园区 {city_assigned_count} 个，总剩余额度 {city_remaining_quota}")
                print()

        # 显示失败的城市
        failed_results = [r for r in results if not r.get('success')]
        if failed_results:
            print(f"\n失败的城市 ({len(failed_results)}个):")
            print("-"*50)
            for result in failed_results:
                city_name = result.get('city_name', result.get('city', ''))
                error_msg = result.get('error_message', '未知错误')
                print(f"  - {city_name}: {error_msg}")




async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='获取402端口与园区匹配信息')
    parser.add_argument('city', nargs='?', help='城市代码（可选，不指定则获取所有城市）')
    parser.add_argument('-s', '--summary', action='store_true', help='仅显示汇总统计信息（默认显示详细分配信息）')
    parser.add_argument('--push', action='store_true', help='启用推送功能，将分配的内容推送到指定网站')
    parser.add_argument('--website-id', type=str, help='推送的目标网站ID（启用推送时必须指定）')
    parser.add_argument('--concurrent', type=int, default=3, help='推送并发数量（默认3）')
    
    args = parser.parse_args()
    
    try:
        script = Get402PortsScript()
        
        # 显示城市配置加载状态
        domains = script.cities_config.get("domains", {})
        if not domains:
            print("城市配置加载失败，请检查config/cities.json文件")
            return

        print(f"成功加载 {len(domains)} 个城市配置")

        # 验证推送参数
        if args.push and not args.website_id:
            print("错误: 启用推送功能时必须指定 --website-id 参数")
            return

        # 处理城市
        results = await script.process_cities(args.city)

        # 显示结果 - 默认显示详细信息，除非指定了-s参数
        show_details = not args.summary
        show_webcontent = True  # 始终显示webcontent
        script.display_results(results, show_details, show_webcontent)

        # 执行推送功能（如果启用）
        if args.push:
            print(f"\n开始推送到网站 {args.website_id}...")
            push_results = await script.batch_push_communities_content(
                results,
                args.website_id,
                args.concurrent
            )

            # 显示推送结果
            script.display_push_results(push_results)

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"脚本执行失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
