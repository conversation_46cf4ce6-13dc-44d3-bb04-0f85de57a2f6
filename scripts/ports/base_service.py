#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import redis
import base64
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path


class BaseService:
    """基础服务类，提供共享功能和配置管理"""
    
    def __init__(self):
        self.cities_config = self.load_city_config()
        # 初始化Redis连接
        self.redis_conn = redis.StrictRedis(
            host="**************", 
            port=6379, 
            password="Meiyoumima333.", 
            decode_responses=True
        )
    
    def load_city_config(self) -> Dict[str, Any]:
        """加载城市配置"""
        try:
            cities_json_path = Path(__file__).parent.parent.parent / "config" / "cities.json"
            if not cities_json_path.exists():
                return {"domains": {}, "names": {}, "accounts": {}}
            with open(cities_json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return {"domains": {}, "names": {}, "accounts": {}}
    
    def get_package_info_from_taocan(self, taocan: str) -> dict:
        """根据taocan字段获取套餐信息"""
        try:
            if not taocan or '-' not in taocan:
                return {"package_type": "20额度套餐", "total_quota": 20, "activate_count": 5, "daily_limit": 20}

            parts = taocan.split('-')
            if len(parts) != 2:
                return {"package_type": "20额度套餐", "total_quota": 20, "activate_count": 5, "daily_limit": 20}

            used = int(parts[0])
            remaining = int(parts[1])
            total_quota = used + remaining

            if total_quota <= 20:
                # 每次激活总额度的1/4，每日限制为总额度（因为每天执行4次）
                activate_count = max(1, total_quota // 4)
                return {"package_type": "20额度套餐", "total_quota": total_quota, "activate_count": activate_count, "daily_limit": total_quota}
            else:
                # 每次激活总额度的1/4，每日限制为总额度（因为每天执行4次）
                activate_count = max(1, total_quota // 4)
                return {"package_type": "40额度套餐", "total_quota": total_quota, "activate_count": activate_count, "daily_limit": total_quota}

        except (ValueError, IndexError):
            return {"package_type": "20额度套餐", "total_quota": 20, "activate_count": 5, "daily_limit": 20}

    def extract_account_id_from_webcontent(self, webcontent: str) -> Optional[str]:
        """
        从webcontent中提取账号ID

        Args:
            webcontent: Base64编码的JSON字符串

        Returns:
            str: 账号ID，如果解析失败返回None
        """
        try:
            if not webcontent:
                return None

            # 解码base64
            decoded_bytes = base64.b64decode(webcontent)
            decoded_str = decoded_bytes.decode('utf-8')

            # 解析JSON
            webcontent_data = json.loads(decoded_str)

            # 提取账号ID
            # 格式: {"webarr":[{"webID":"402","actArr":{"1186881":"1"}}]}
            if isinstance(webcontent_data, dict) and 'webarr' in webcontent_data:
                webarr = webcontent_data['webarr']
                if isinstance(webarr, list) and len(webarr) > 0:
                    first_web = webarr[0]
                    if isinstance(first_web, dict) and 'actArr' in first_web:
                        act_arr = first_web['actArr']
                        if isinstance(act_arr, dict):
                            # 获取第一个账号ID
                            account_ids = list(act_arr.keys())
                            if account_ids:
                                return account_ids[0]

            return None

        except Exception as e:
            return None
    
    def get_data_dir(self) -> str:
        """获取数据目录路径"""
        data_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
            'data', 
            'city_contents'
        )
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
        return data_dir
    
    def get_timestamp(self) -> str:
        """获取当前时间戳字符串"""
        return datetime.now().strftime('%Y%m%d_%H%M%S')
    
    def analyze_accounts(self, website_data: list) -> Dict[str, int]:
        """分析账号统计信息"""
        free_website_ids = ['111']
        paid_website_ids = ['402']  # 只统计402类型的付费端口

        stats = {
            "free_accounts": 0,
            "paid_accounts": 0,
            "available_free_accounts": 0,
            "available_paid_accounts": 0
        }

        for website in website_data:
            web_id = str(website.get('webID', ''))
            user_name_arr = website.get('userNameArr', {})

            for account_id, account_info in user_name_arr.items():
                if not account_info or len(account_info) < 3:
                    continue

                status = account_info[2] if len(account_info) > 2 else ""
                is_available = "可用" in status or status == "可用"

                if web_id in free_website_ids:
                    stats["free_accounts"] += 1
                    if is_available:
                        stats["available_free_accounts"] += 1
                elif web_id in paid_website_ids:
                    stats["paid_accounts"] += 1
                    if is_available:
                        stats["available_paid_accounts"] += 1

        return stats

