#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
清空Redis中agent_posts相关缓存的脚本
"""

import sys
import os
import redis

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def main():
    """清空所有agent_posts:*缓存键"""
    try:
        # 连接Redis
        redis_conn = redis.StrictRedis(
            host="**************",
            port=6379,
            password="Meiyoumima333.",
            decode_responses=True
        )

        # 测试连接
        redis_conn.ping()
        print("Redis连接成功")

        # 查找所有agent_posts:*键
        keys = []
        cursor = 0
        pattern = "agent_posts:*"

        while True:
            cursor, partial_keys = redis_conn.scan(cursor=cursor, match=pattern, count=100)
            keys.extend(partial_keys)
            if cursor == 0:
                break

        if not keys:
            print("未找到任何agent_posts:*缓存键")
            return

        print(f"找到 {len(keys)} 个agent_posts缓存键")

        # 删除所有键
        if keys:
            deleted_count = redis_conn.delete(*keys)
            print(f"成功删除 {deleted_count} 个缓存键")

    except Exception as e:
        print(f"操作失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
