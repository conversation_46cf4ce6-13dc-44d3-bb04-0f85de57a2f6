#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import asyncio
import base64
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .base_service import BaseService
from .email_service import EmailService


class OfflineService(BaseService):
    """下架服务类，负责帖子下架功能"""

    def __init__(self):
        super().__init__()
        self.email_service = EmailService()

    def calculate_days_since_publish(self, publish_time: str) -> int:
        """计算发布时间到现在的天数"""
        try:
            if not publish_time:
                return 0

            # 尝试解析发布时间，格式为 "YYYY-MM-DD HH:MM:SS"
            publish_date = datetime.strptime(publish_time, "%Y-%m-%d %H:%M:%S")
            current_date = datetime.now()

            # 计算天数差
            days_diff = (current_date - publish_date).days
            return max(0, days_diff)  # 确保不返回负数

        except (ValueError, TypeError):
            # 如果时间格式不正确，返回0天
            return 0

    def should_offline_post(self, post: Dict) -> bool:
        """判断帖子是否应该下架"""
        try:
            # 获取发布时间，优先使用Potime，其次使用AddTime
            publish_time = post.get('Potime') or post.get('AddTime', '')
            if not publish_time:
                return False

            # 计算发布天数
            days_since_publish = self.calculate_days_since_publish(publish_time)

            # 获取点击量，确保是数字
            try:
                click_month = int(post.get('clickM', '0'))
            except (ValueError, TypeError):
                # 如果点击量不是数字，不下架
                return False

            # 下架条件判断
            # 条件1：发布超过3天 且 点击量为0 或 近90天点击量不超过5个
            if days_since_publish > 3 and click_month <= 5:
                return True

            # 条件2：发布超过10天 且 近90天点击量不超过10个
            if days_since_publish > 10 and click_month <= 10:
                return True

            # 条件3：发布超过30天 且 近90天点击量不超过100个
            if days_since_publish > 30 and click_month <= 100:
                return True

            return False

        except Exception:
            # 出现任何异常，不下架
            return False

    async def offline_posts_by_package(self, results: List[Dict], city: str = None) -> str:
        """根据套餐类型下架帖子"""
        try:
            data_dir = self.get_data_dir()
            timestamp = self.get_timestamp()

            # 根据城市生成文件名
            if city:
                offline_filename = f"帖子下架结果_{city}_{timestamp}.txt"
            else:
                offline_filename = f"帖子下架结果_{timestamp}.txt"

            offline_filepath = os.path.join(data_dir, offline_filename)

            # 下架统计
            offline_stats = {
                "20额度套餐": {"总账号": 0, "成功下架账号": 0, "总下架帖子": 0, "成功下架帖子": 0},
                "40额度套餐": {"总账号": 0, "成功下架账号": 0, "总下架帖子": 0, "成功下架帖子": 0}
            }

            # 收集需要下架的账号
            accounts_to_offline = []
            for result in results:
                if not result['success'] or result.get('posts_count', 0) == 0:
                    continue

                posts_count = result.get('posts_count', 0)
                taocan = result.get('taocan', '')
                package_info = self.get_package_info_from_taocan(taocan)
                package_type = package_info['package_type']
                offline_count = 2  # 每个付费账号最多下架2个满足条件的帖子

                offline_stats[package_type]["总账号"] += 1
                accounts_to_offline.append({
                    **result,
                    'package_type': package_type,
                    'offline_count': offline_count,
                    'taocan': taocan
                })

            if not accounts_to_offline:
                return offline_filepath

            semaphore = asyncio.Semaphore(20)
            offline_results = await self.process_offline_concurrent(accounts_to_offline, semaphore)

            for result in offline_results:
                package_type = result['package_type']
                if result.get('offline_success', False):
                    offline_stats[package_type]["成功下架账号"] += 1

                offline_stats[package_type]["总下架帖子"] += result.get('attempted_offline', 0)
                offline_stats[package_type]["成功下架帖子"] += result.get('successful_offline', 0)

            await self.save_offline_results(offline_filepath, offline_results, offline_stats, city)

            # 发送邮件报告
            try:
                email_result = self.email_service.send_offline_report(
                    offline_results=offline_results,
                    stats=offline_stats,
                    city=city,
                    report_file_path=offline_filepath
                )
                if email_result['success']:
                    print(f"下架报告邮件发送成功")
                else:
                    print(f"下架报告邮件发送失败: {email_result['message']}")
            except Exception as e:
                print(f"发送邮件时发生异常: {str(e)}")

            return offline_filepath

        except Exception as e:
            return ""

    async def process_offline_concurrent(self, accounts: List[Dict], semaphore: asyncio.Semaphore) -> List[Dict]:
        """并发处理下架请求"""

        async def offline_single_account(account):
            async with semaphore:
                try:
                    posts_data = account.get('posts_data', [])
                    if not posts_data:
                        return {
                            **account,
                            'offline_success': False,
                            'attempted_offline': 0,
                            'successful_offline': 0,
                            'offline_details': []
                        }

                    # 新的下架逻辑：筛选满足下架条件的帖子
                    eligible_posts = []
                    for post in posts_data:
                        if self.should_offline_post(post):
                            eligible_posts.append(post)

                    # 如果没有满足条件的帖子，则不下架任何帖子
                    if not eligible_posts:
                        posts_to_offline = []
                    else:
                        # 对满足条件的帖子进行排序：
                        # 1. 按发布时间从早到晚排序（优先下架老帖子）
                        # 2. 同等发布时间下，按点击量从低到高排序
                        def sort_key(post):
                            publish_time = post.get('Potime') or post.get('AddTime', '')
                            try:
                                # 解析发布时间用于排序
                                publish_date = datetime.strptime(publish_time, "%Y-%m-%d %H:%M:%S") if publish_time else datetime.min
                            except (ValueError, TypeError):
                                publish_date = datetime.min

                            try:
                                click_month = int(post.get('clickM', '0'))
                            except (ValueError, TypeError):
                                click_month = 0

                            # 返回排序键：(发布时间, 点击量)
                            return (publish_date, click_month)

                        sorted_eligible_posts = sorted(eligible_posts, key=sort_key)
                        # 最多选择2个帖子进行下架
                        posts_to_offline = sorted_eligible_posts[:account['offline_count']]

                    webcontent_data = {
                        "webarr": [
                            {
                                "webID": account['web_id'],
                                "actArr": {
                                    account['account_id']: "1"
                                }
                            }
                        ]
                    }

                    webcontent_json = json.dumps(webcontent_data, ensure_ascii=False)
                    webcontent = base64.b64encode(webcontent_json.encode('utf-8')).decode('utf-8')

                    offline_details = []
                    successful_count = 0

                    for post in posts_to_offline:
                        rid = post.get('RID', post.get('InfoID', post.get('id', '')))
                        house_type = post.get('Type', '6')
                        title = post.get('Title', post.get('title', '无标题'))
                        click_month = post.get('clickM', '0')

                        if not rid:
                            offline_details.append({
                                'title': title,
                                'rid': '未知',
                                'click_month': click_month,
                                'success': False,
                                'error': '缺少RID'
                            })
                            continue

                        offline_result = await self.call_offline_api(
                            account['city'], account['user_id'], rid, webcontent, house_type
                        )

                        if offline_result['success']:
                            successful_count += 1

                        offline_details.append({
                            'title': title,
                            'rid': rid,
                            'click_month': click_month,
                            'success': offline_result['success'],
                            'error': offline_result.get('error', '')
                        })

                    return {
                        **account,
                        'offline_success': successful_count > 0,
                        'attempted_offline': len(posts_to_offline),
                        'successful_offline': successful_count,
                        'offline_details': offline_details
                    }

                except Exception as e:
                    return {
                        **account,
                        'offline_success': False,
                        'attempted_offline': 0,
                        'successful_offline': 0,
                        'offline_details': [],
                        'error': str(e)
                    }

        tasks = [offline_single_account(account) for account in accounts]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    **accounts[i],
                    'offline_success': False,
                    'attempted_offline': 0,
                    'successful_offline': 0,
                    'offline_details': [],
                    'error': str(result)
                })
            else:
                processed_results.append(result)

        return processed_results

    async def call_offline_api(self, city: str, user_id: str, remote_id: str, webcontent: str,
                               house_type: str) -> Dict:
        """调用下架API"""
        try:
            from api.client import AsyncYoutuiApiClient
            async_client = AsyncYoutuiApiClient(environment='production', city=city)

            xml_data = {
                'platformID': async_client.platform_id,
                'companyID': async_client.company_id,
                'userID': user_id,
                'RemoteID': remote_id,
                'type': house_type,
                'type4property': '13',
                'dbug': async_client.debug,
                'webcontent': webcontent
            }

            from utils.xml_utils import dict_to_xml
            xml_payload = dict_to_xml(xml_data)
            endpoint = '?api/house/offline.html'
            response_xml = await async_client.http_client.post(endpoint, xml_payload)

            if response_xml:
                from utils.xml_utils import xml_to_dict
                result = xml_to_dict(response_xml)

                if result:
                    # 检查新的XML格式：根据xml_utils.py的解析逻辑
                    # 对于<profile><houseList><houseId>xxx</houseId></houseList></profile>
                    # 解析后可能是列表格式或字典格式

                    # 情况1：解析结果是列表格式（最常见）
                    if isinstance(result, list) and len(result) > 0:
                        house_item = result[0]
                        # 检查是否有houseid字段（XML解析后标签名变为小写）
                        if house_item.get('houseid'):
                            return {'success': True}

                    # 情况2：解析结果是字典格式，直接包含houseid
                    elif isinstance(result, dict) and result.get('houseid'):
                        return {'success': True}

                    # 情况3：检查是否有嵌套的houselist结构
                    elif isinstance(result, dict) and 'houselist' in result:
                        house_list = result['houselist']
                        if isinstance(house_list, dict) and house_list.get('houseid'):
                            return {'success': True}
                        elif isinstance(house_list, list) and len(house_list) > 0:
                            house_item = house_list[0]
                            if house_item.get('houseid'):
                                return {'success': True}

                    # 检查传统格式：直接的status和msg字段
                    if isinstance(result, dict):
                        status = result.get('status', '')
                        msg = result.get('msg', '')

                        is_success = (
                                status in ['1', '9'] or
                                (msg and '下架完毕' in msg) or
                                (msg and '成功' in msg)
                        )

                        if is_success:
                            return {'success': True}
                        else:
                            error_msg = msg if msg else '下架失败'
                            return {'success': False, 'error': error_msg}

                    # 如果都没有匹配，返回失败
                    return {'success': False, 'error': '下架失败：无法识别响应格式'}
                else:
                    return {'success': False, 'error': '无响应'}
            else:
                return {'success': False, 'error': '请求失败'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def save_offline_results(self, filepath: str, results: List[Dict], stats: Dict, city: str = None) -> None:
        """保存下架结果到文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("帖子下架结果报告\n")
                f.write("=" * 80 + "\n")
                f.write(f"下架时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

                if city:
                    f.write(f"目标城市: {city}\n")
                    city_402_ports = len([r for r in results if r.get('city') == city])
                    f.write(f"城市 {city} 的402端口总数: {city_402_ports}\n")
                    f.write(f"成功下架的402端口数: {len([r for r in results if r.get('offline_success', False)])}\n")
                else:
                    f.write("目标范围: 所有城市\n")
                    total_402_ports = len(results)
                    f.write(f"全部402端口总数: {total_402_ports}\n")
                    f.write(f"成功下架的402端口数: {len([r for r in results if r.get('offline_success', False)])}\n")

                f.write(f"处理账号总数: {len(results)}\n")
                f.write("\n")

                # 下架统计总结
                f.write("下架统计总结\n")
                f.write("-" * 40 + "\n")
                for package_type, stat in stats.items():
                    f.write(f"{package_type}:\n")
                    f.write(f"  总账号数: {stat['总账号']}\n")
                    f.write(f"  成功下架账号: {stat['成功下架账号']}\n")
                    f.write(f"  账号成功率: {(stat['成功下架账号'] / stat['总账号'] * 100):.1f}%\n" if stat[
                                                                                                           '总账号'] > 0 else "  账号成功率: 0%\n")
                    f.write(f"  尝试下架帖子: {stat['总下架帖子']}\n")
                    f.write(f"  成功下架帖子: {stat['成功下架帖子']}\n")
                    f.write(f"  帖子成功率: {(stat['成功下架帖子'] / stat['总下架帖子'] * 100):.1f}%\n" if stat[
                                                                                                               '总下架帖子'] > 0 else "  帖子成功率: 0%\n")
                    f.write("\n")

                # 详细下架结果
                f.write("详细下架结果\n")
                f.write("=" * 80 + "\n")

                current_city = None
                for result in sorted(results, key=lambda x: x['city_name']):
                    if result['city_name'] != current_city:
                        current_city = result['city_name']
                        f.write(f"城市: {current_city}({result['city']})\n")
                        f.write("-" * 40 + "\n")

                    f.write(f"  账号ID: {result['account_id']}\n")
                    f.write(f"  用户名: {result['username']}\n")
                    f.write(f"  套餐类型: {result['package_type']}\n")
                    f.write(f"  尝试下架: {result['attempted_offline']}个帖子\n")
                    f.write(f"  成功下架: {result['successful_offline']}个帖子\n")
                    # 安全的除法运算，避免除零错误
                    attempted = result.get('attempted_offline', 0)
                    successful = result.get('successful_offline', 0)
                    if attempted > 0:
                        success_rate = (successful / attempted * 100)
                        f.write(f"  下架成功率: {success_rate:.1f}%\n")
                    else:
                        f.write("  下架成功率: 0%\n")

                    if result.get('error'):
                        f.write(f"  处理错误: {result['error']}\n")

                    offline_details = result.get('offline_details', [])
                    if offline_details:
                        f.write(f"  下架详情:\n")
                        for i, detail in enumerate(offline_details, 1):
                            status = "成功" if detail['success'] else f"失败({detail.get('error', '未知错误')})"
                            f.write(
                                f"    {i}. {detail['title']} (RID: {detail['rid']}, 月点击: {detail['click_month']}) - {status}\n")

                    f.write("\n")

                f.write("下架规则说明:\n")
                f.write("-" * 40 + "\n")
                f.write("下架条件:\n")
                f.write("1. 发布超过3天 且 近90天点击量≤5个\n")
                f.write("2. 发布超过10天 且 近90天点击量≤10个\n")
                f.write("3. 发布超过30天 且 近90天点击量≤100个\n")
                f.write("下架数量: 每个付费账号最多下架2个满足条件的帖子\n")
                f.write("下架策略: 优先下架发布时间较早的帖子，同等时间下优先下架点击量较低的帖子\n")
                f.write("保护机制: 如果没有帖子满足下架条件，则不下架任何帖子\n")
                f.write("注：套餐类型基于API返回的taocan字段判断，无taocan时默认为20额度套餐\n")

        except Exception:
            # 文件保存失败，静默处理
            return
