import time
import random
import logging
import os
import json
import re
import asyncio
import pymysql
import sys
import redis
import subprocess
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Tuple
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, Playwright
from captcha_solver import CaptchaSolver

# 添加项目根目录到Python路径，确保可以导入模块
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入数据库连接模块
from database.db_connection import get_db_connection

class Spider58FactoryPlaywright:
    """58同城厂房信息爬虫 (使用Playwright)"""

    def __init__(self, debug=False, headless=True, auto_captcha=True,
                 retry_limit=3, wait_time=20, user_agent=None, max_concurrent_cities=10,
                 proxies=None):
        """初始化爬虫"""
        # 城市代码映射
        self.city_codes = {
            "北京": "bj", "长春": "cc", "深圳": "sz", "广州": "gz", "天津": "tj", 
            "包头": "bt", "宝鸡": "baoji", "保定": "bd", "常州": "cz", 
            "成都": "cd", "承德": "chengde", "滁州": "chuzhou", "大连": "dl", 
            "德阳": "deyang", "德州": "dz", "东莞": "dg", "佛山": "fs", 
            "福州": "fz", "贵阳": "gy", "哈尔滨": "hrb", "杭州": "hz", "合肥": "hf", 
            "和田": "ht", "湖州": "huzhou", "淮安": "ha", "惠州": "huizhou", 
            "济南": "jn", "嘉兴": "jx", "江门": "jm", "金华": "jh", 
            "九江": "jj", "昆明": "km", "廊坊": "lf", "连云港": "lyg", 
            "柳州": "liuzhou", "洛阳": "luoyang", "眉山": "ms", "绵阳": "mianyang", 
            "南昌": "nc", "南京": "nj", "南宁": "nn", "南通": "nt", 
            "南阳": "ny", "宁波": "nb", "莆田": "pt", "青岛": "qd", 
            "清远": "qingyuan", "泉州": "qz", "厦门": "xm", "汕头": "st", 
            "汕尾": "sw", "上海": "sh", "绍兴": "sx", "沈阳": "sy", 
            "石家庄": "sjz", "苏州": "su", "宿迁": "suqian", "遂宁": "suining", 
            "台州": "tz", "太原": "ty", "泰州": "taizhou", "唐山": "ts", 
            "威海": "weihai", "潍坊": "wf", "渭南": "wn", "无锡": "wx", 
            "芜湖": "wuhu", "武汉": "wh", "西安": "xa", "湘潭": "xiangtan", 
            "新乡": "xx", "新余": "xinyu", "徐州": "xz", "许昌": "xc", 
            "烟台": "yt", "盐城": "yancheng", "扬州": "yz", "宜昌": "yc", 
            "岳阳": "yy", "漳州": "zhangzhou", "长沙": "cs", "镇江": "zj", 
            "郑州": "zz", "中山": "zs", "重庆": "cq", "周口": "zk", 
            "珠海": "zh", "淄博": "sdzb"
        }

        # 类型映射
        self.type_codes = {
            "出租": "",
            "出售": "b5/"
        }

        # 基础属性设置
        self.debug = debug
        self.headless = headless
        self.auto_captcha = auto_captcha
        self.retry_limit = retry_limit
        self.wait_time = wait_time
        self.user_agent = user_agent
        self.max_concurrent_cities = max_concurrent_cities
        
        # 代理设置
        self.proxies = proxies or {}
        
        # 默认的出租和出售代理
        self.default_rent_proxy = None
        self.default_sale_proxy = None
        
        # 可用代理列表
        self.available_proxies = []

        # 设置日志
        self.setup_logger()

        # 创建所需目录
        self.data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
        os.makedirs(self.data_dir, exist_ok=True)

        if self.debug:
            self.debug_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'debug')
            os.makedirs(self.debug_dir, exist_ok=True)

        # Playwright相关属性
        self.playwright = self.browser = self.context = None

        # 初始化验证码处理器
        if self.auto_captcha:
            self.captcha_solver = CaptchaSolver(debug=self.debug)

        # 数据库连接
        self.db_connection = None
        
        # Redis连接
        self.redis_conn = None
        
        # 创建一个信号量限制同时爬取的城市数量
        self.city_semaphore = None
        
        # 数据库锁，确保数据库操作线程安全
        self.db_lock = asyncio.Lock()
        
        # 初始化Redis连接并获取代理
        self.init_redis_connection()
        self.update_proxy_pool()

        # 时间戳作为任务ID，表示一批数据，在每次运行时生成一次
        self.task_id = None

    def setup_logger(self):
        """设置日志记录器"""
        # 只使用控制台输出，不创建单独的日志文件
        # 日志记录由shell脚本统一管理
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler()  # 只使用控制台输出
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("58同城厂房信息爬虫初始化完成")

    async def init_browser(self, proxy=None) -> None:
        """
        初始化浏览器
        
        Args:
            proxy: 代理服务器地址，格式为"host:port"
        """
        try:
            self.playwright = await async_playwright().start()
            browser_type = self.playwright.firefox

            # 浏览器启动参数
            browser_args = [
                '--disable-extensions',
                '--disable-notifications',
                '--disable-popup-blocking',
                '--no-sandbox',
                '--disable-setuid-sandbox',
            ]

            # 启动浏览器
            self.browser = await browser_type.launch(
                headless=self.headless,
                slow_mo=100  # 更慢的操作，确保稳定性
            )

            # 上下文选项
            context_options = {
                "viewport": {"width": 1280, "height": 720},
                "user_agent": self.user_agent if self.user_agent else self.get_random_ua()
            }
            
            # 如果提供了代理，添加到上下文选项中
            if proxy:
                self.logger.info(f"使用代理: {proxy}")
                context_options["proxy"] = {
                    "server": f"http://{proxy}"
                }

            # 创建上下文
            self.context = await self.browser.new_context(**context_options)

            # 设置内置的58同城cookie，避免登录验证
            self.logger.info("正在设置58同城内置cookie...")
            try:
                # 使用固定的完整cookie字符串
                cookies_str = "spm=u-2gn98s8wb98zccv46g.2gn99j5y23vhwqfjjkg; 58home=bj; id58=CkwAdmhVDFVlPYEvCCkaAg==; city=bj; 58tj_uuid=0d4ab4e5-1248-4a6a-b574-f4416a0a351d; new_uv=1; utm_source=; init_refer=https%253A%252F%252Fwww.google.com%252F; als=0; wmda_uuid=8fcd465c236633f8a6284bac75150f1b; wmda_session_id_10104579731767=*************-6035c00b-8967-46a0-a951-361f6c1efc37; wmda_visited_projects=%3B10104579731767%3B2385390625025%3B3381039819650; xxzlclientid=ae837494-4f02-4842-8780-*************; xxzlxxid=pfmxizd3b/2FZAp6lK3Gb2TaleX7I255XMJahYZUpoAlHdf2OaDAGf9fyI/TnK0kEO5D; www58com=\"UserID=**************&UserName=74ju9l1tp\"; 58cooper=\"userid=**************&username=74ju9l1tp\"; 58uname=74ju9l1tp; passportAccount=\"atype=0&bstate=0\"; new_session=0; wmda_report_times=1; wmda_session_id_2385390625025=*************-8a466646-ac23-437f-b51d-82007c9d440f; PPU=UID=**************&UN=74ju9l1tp&TT=1b0c13c2690d15e88816f16bd6b70a28&PBODY=awAUvi1Q171HWDEDLoTJozmTcM8NXIjYRHrpLYSI9N1lfu3Q0IZsUYY9scQ-5sEnOC41eiGSsndAfw9UOmybp3YLPQTLtI4F5ELZpso1aIVLIE3iE1T5-seWetUUU3sWWWQgHRQw5byuV8DmQqaxuIviime6E4YAe34sb7qVSLA&VER=1&CUID=WHtwhOArc6cdoTkcJB2fOQ; xxzlbbid=pfmbM3wxMDI5MnwxLjEwLjB8MTc1MDQwNDI4NjIyMDUzNTc3NXxheFR3RW5reDdPR1h5TVIreVdvSzIrYUlzOStyaytLZWZHL3hQVGFWNzdZPXw2OWYxZTYwNDFjNTEyOWQ1MWMzMTVmYzhlZjRjMjAwYl8xNzUwNDA0Mjg2NjU5XzU4MDhhNWJiN2MwZjQzNzE5MjA0MmY0MzRjZDNiMWEwXzIwODg3NTE0NjB8OGNlOTcyZGNkMmNhNDUxNDk4YTdjZTllNTg4NDAzYTZfMTc1MDQwNDI4NTczN18yNTY="

                # 解析cookie字符串
                cookie_list = []
                for cookie_item in cookies_str.split(';'):
                    if not cookie_item.strip():
                        continue
                    name, value = cookie_item.strip().split('=', 1)
                    cookie_list.append({
                        "name": name.strip(),
                        "value": value.strip(),
                        "domain": ".58.com",
                        "path": "/"
                    })

                # 添加所有cookie到浏览器上下文
                await self.context.add_cookies(cookie_list)
                self.logger.info(f"成功设置{len(cookie_list)}个cookie项")
            except Exception as e:
                self.logger.error(f"设置cookie失败: {str(e)}")

            # 最小化指纹修改
            await self.context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
            """)

            self.logger.info("浏览器初始化成功")
        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {str(e)}")
            # 清理资源
            if self.browser:
                try: await self.browser.close()
                except: pass
                self.browser = None

            if self.playwright:
                try: await self.playwright.stop()
                except: pass
                self.playwright = None
            raise

    async def close_browser(self) -> None:
        """关闭浏览器"""
        try:
            if self.context:
                await self.context.close()
                self.context = None
            if self.browser:
                await self.browser.close()
                self.browser = None
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
            self.logger.info("浏览器已关闭")
        except Exception as e:
            self.logger.error(f"关闭浏览器失败: {str(e)}")

    def init_db_connection(self) -> None:
        """初始化数据库连接"""
        try:
            self.db_connection = get_db_connection()
            self.logger.info("数据库连接已建立")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {str(e)}")
            raise

    def close_db_connection(self) -> None:
        """关闭数据库连接"""
        if self.db_connection:
            try:
                self.db_connection.close()
                self.db_connection = None
                self.logger.info("数据库连接已关闭")
            except Exception as e:
                self.logger.error(f"关闭数据库连接失败: {str(e)}")

    def format_publish_time(self, time_str: str) -> str:
        """
        将发布时间转换为标准格式（MM-DD）

        Args:
            time_str: 原始时间字符串，可能是"今天"、"昨天"、"X小时前"等

        Returns:
            str: 格式化后的时间字符串（MM-DD）
        """
        if not time_str:
            # 如果没有时间字符串，返回当天日期
            return datetime.now().strftime('%m-%d')

        # 处理"今天"、"昨天"等相对时间
        if '今天' in time_str or '小时前' in time_str or '分钟前' in time_str or '刚刚' in time_str:
            # 今天或者几小时前，使用当天日期
            return datetime.now().strftime('%m-%d')
        elif '昨天' in time_str:
            # 昨天，使用昨天的日期
            yesterday = datetime.now() - timedelta(days=1)
            return yesterday.strftime('%m-%d')
        elif '前天' in time_str:
            # 前天，使用前天的日期
            day_before_yesterday = datetime.now() - timedelta(days=2)
            return day_before_yesterday.strftime('%m-%d')

        # 处理"X天前"的格式
        days_ago_match = re.search(r'(\d+)[天日][前以内]', time_str)
        if days_ago_match:
            try:
                days_ago = int(days_ago_match.group(1))
                past_date = datetime.now() - timedelta(days=days_ago)
                return past_date.strftime('%m-%d')
            except (AttributeError, ValueError):
                # 如果无法解析，返回原始字符串
                pass

        # 尝试匹配常见的日期格式
        date_patterns = [
            # MM-DD格式
            r'(\d{1,2})-(\d{1,2})',
            # MM/DD格式
            r'(\d{1,2})/(\d{1,2})',
            # MM月DD日格式
            r'(\d{1,2})月(\d{1,2})[日号]',
            # YYYY-MM-DD格式
            r'\d{4}-(\d{1,2})-(\d{1,2})',
            # YYYY/MM/DD格式
            r'\d{4}/(\d{1,2})/(\d{1,2})',
            # YYYY年MM月DD日格式
            r'\d{4}年(\d{1,2})月(\d{1,2})[日号]'
        ]

        for pattern in date_patterns:
            match = re.search(pattern, time_str)
            if match:
                # 根据不同的模式提取月和日
                if len(match.groups()) == 2:
                    month, day = match.groups()
                    try:
                        # 格式化为MM-DD
                        month = int(month)
                        day = int(day)
                        if 1 <= month <= 12 and 1 <= day <= 31:
                            return f"{month:02d}-{day:02d}"
                    except ValueError:
                        pass

        # 如果所有匹配都失败，返回原始字符串
        return time_str

    def get_random_ua(self) -> str:
        """获取随机User-Agent"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36 Edg/113.0.1774.42',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/112.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/113.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/112.0',
        ]
        return random.choice(user_agents)

    def get_factory_list_url(self, city_code: str, page: int = 1, factory_type: str = "出租") -> str:
        """获取厂房列表页URL"""
        type_code = self.type_codes.get(factory_type, "")
        if page == 1:
            return f"https://{city_code}.58.com/changfang/{type_code}"
        return f"https://{city_code}.58.com/changfang/{type_code}pn{page}/"

    def save_debug_html(self, html: str, city_code: str, page: int = 1) -> None:
        """保存HTML到调试文件"""
        if not self.debug: return
        try:
            debug_file = os.path.join(self.debug_dir, f"{city_code}_page{page}.html")
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(html)
            self.logger.info(f"已保存调试HTML到: {debug_file}")
        except Exception as e:
            self.logger.error(f"保存调试HTML失败: {str(e)}")

    async def save_screenshot(self, page: Page, city_code: str, pg: int = 1) -> None:
        """保存页面截图"""
        if not self.debug: return
        try:
            screenshot_file = os.path.join(self.debug_dir, f"{city_code}_page{pg}.png")
            await page.screenshot(path=screenshot_file, full_page=True)
            self.logger.info(f"已保存页面截图到: {screenshot_file}")
        except Exception as e:
            self.logger.error(f"保存截图失败: {str(e)}")

    async def fetch_page(self, url: str, max_retries: int = None, city_code: str = None, proxy: str = None) -> Optional[str]:
        """
        获取页面内容

        Args:
            url: 请求URL
            max_retries: 最大重试次数
            city_code: 城市代码
            proxy: 代理服务器地址，格式为"host:port"

        Returns:
            Optional[str]: 页面内容
        """
        # 是否需要重新初始化浏览器（使用新代理）
        need_new_browser = False
        
        if not self.browser:
            need_new_browser = True
        
        if need_new_browser:
            try:
                # 关闭可能存在的旧浏览器
                if self.browser:
                    await self.close_browser()
                # 使用代理初始化浏览器
                await self.init_browser(proxy=proxy)
                if proxy:
                    self.logger.info(f"使用代理 {proxy} 初始化浏览器")
            except Exception as e:
                self.logger.error(f"浏览器初始化失败，无法继续: {str(e)}")
                return None

        # 使用实例的retry_limit作为默认值
        if max_retries is None:
            max_retries = self.retry_limit

        # 解析URL中的城市代码
        if not city_code:
            parsed_url = urlparse(url)
            domain_parts = parsed_url.netloc.split('.')
            city_code = domain_parts[0] if len(domain_parts) > 0 else "unknown"

        retries = 0
        current_proxy = proxy

        while retries < max_retries:
            page = None
            try:
                # 创建新页面
                self.logger.info("创建新页面...")
                page = await self.context.new_page()

                # 设置页面超时
                page.set_default_timeout(60000)  # 60秒，增加超时时间

                # 随机延迟，防止被封
                await asyncio.sleep(random.uniform(1, 3))

                self.logger.info(f"正在请求URL: {url}")

                # 请求页面
                response = await page.goto(url, wait_until="domcontentloaded", timeout=60000)

                # 随机等待，模拟用户行为
                await asyncio.sleep(random.uniform(1, 2))

                # 检查响应状态
                if not response:
                    self.logger.error("请求失败，未获得响应")
                    if page:
                        try:
                            await page.close()
                        except:
                            pass
                    retries += 1
                    continue

                if not response.ok:
                    self.logger.error(f"请求失败，状态码: {response.status}")
                    if page:
                        try:
                            await page.close()
                        except:
                            pass
                    retries += 1
                    continue

                # 检查是否是验证码页面
                current_url = page.url
                self.logger.info(f"当前URL: {current_url}")

                # 解析URL中的页码
                page_match = re.search(r'/pn(\d+)/', url)
                pg = int(page_match.group(1)) if page_match else 1

                # 检查是否是验证码页面
                page_title = await page.title()
                is_captcha_page = ("callback.58.com/antibot" in current_url or
                                  page_title == "验证码" or
                                  "验证" in page_title)

                if is_captcha_page:
                    self.logger.warning(f"遇到验证码页面: {current_url}, 标题: {page_title}")

                    # 保存验证码页面截图
                    await self.save_screenshot(page, f"antibot_{city_code}", pg)

                    # 保存当前DOM便于分析
                    if self.debug:
                        html = await page.content()
                        captcha_file = os.path.join(self.debug_dir, f"captcha_{city_code}_{int(time.time())}.html")
                        with open(captcha_file, 'w', encoding='utf-8') as f:
                            f.write(html)
                        self.logger.info(f"已保存验证码页面到: {captcha_file}")

                    # 检查验证码类型
                    is_image_captcha = await page.query_selector('input[name="verify_code"]') is not None
                    if is_image_captcha:
                        self.logger.info("检测到图片验证码输入框")

                    # 尝试自动处理验证码
                    if self.auto_captcha:
                        self.logger.info("尝试自动处理验证码...")
                        captcha_solved = await self.captcha_solver.solve_captcha(page)

                        if captcha_solved:
                            self.logger.info("自动处理验证码成功！")

                            # 等待页面加载
                            await asyncio.sleep(3)

                            # 检查是否仍在验证页面
                            new_url = page.url
                            new_title = await page.title()
                            if "callback.58.com/antibot" in new_url or "验证" in new_title:
                                self.logger.warning("验证码处理可能失败，页面仍在验证状态")
                                try:
                                    await page.close()
                                except:
                                    pass
                                retries += 1
                                # 增加重试间隔时间，避免连续触发验证
                                await asyncio.sleep(random.uniform(5, 10))
                                continue
                        else:
                            self.logger.warning("自动处理验证码失败，尝试重试...")
                            try:
                                await page.close()
                            except:
                                pass
                            retries += 1
                            # 增加重试间隔时间，避免连续触发验证
                            await asyncio.sleep(random.uniform(5, 10))
                            continue
                    else:
                        self.logger.warning("自动验证码处理功能已禁用，尝试重试...")
                        try:
                            await page.close()
                        except:
                            pass
                        retries += 1
                        # 增加重试间隔时间，避免连续触发验证
                        await asyncio.sleep(random.uniform(5, 10))
                        continue

                # 等待页面内容完全加载
                try:
                    await page.evaluate("window.scrollTo(0, document.body.scrollHeight/2)")
                    await asyncio.sleep(1)
                    await page.evaluate("window.scrollTo(0, 0)")

                    # 等待主要内容
                    await asyncio.sleep(2)

                    # 获取HTML内容
                    html_content = await page.content()

                    # 保存HTML内容
                    self.save_debug_html(html_content, city_code, pg)

                    # 保存截图
                    await self.save_screenshot(page, city_code, pg)

                    # 关闭页面
                    try:
                        await page.close()
                    except:
                        self.logger.warning("关闭页面时出错，但继续执行")

                    self.logger.info(f"成功获取页面内容，长度: {len(html_content)}")
                    return html_content
                except Exception as e:
                    self.logger.error(f"获取页面内容失败: {str(e)}")
                    if page:
                        try:
                            await page.close()
                        except:
                            pass
            except Exception as e:
                self.logger.error(f"请求异常: {str(e)}")
                if page:
                    try:
                        await page.close()
                    except:
                        self.logger.warning("关闭页面出错，但继续执行")

            retries += 1
            self.logger.info(f"重试第 {retries} 次...")
            # 增加重试间隔，避免被识别为爬虫
            retry_delay = random.uniform(5, 10)
            self.logger.info(f"等待 {retry_delay:.1f} 秒后重试...")
            await asyncio.sleep(retry_delay)

            # 如果有代理并且重试次数达到一半，尝试更换代理
            if current_proxy and retries >= max_retries // 2:
                new_proxy = self.get_random_proxy(exclude=current_proxy)
                if new_proxy and new_proxy != current_proxy:
                    self.logger.info(f"更换代理: {current_proxy} -> {new_proxy}")
                    current_proxy = new_proxy
                    # 重新初始化浏览器
                    try:
                        await self.close_browser()
                        await self.init_browser(proxy=current_proxy)
                        # 重置重试计数
                        retries = max_retries // 2
                        continue
                    except Exception as browser_err:
                        self.logger.error(f"重新初始化浏览器失败: {str(browser_err)}")

        self.logger.error(f"请求失败，已达到最大重试次数: {url}")
        return None

    def parse_factory_list(self, html: str, factory_type: str = "出租") -> List[Dict]:
        """
        解析厂房列表页

        Args:
            html: 页面内容
            factory_type: 厂房类型（出租或出售）

        Returns:
            List[Dict]: 厂房信息列表
        """
        if not html:
            return []

        factory_list = []
        soup = BeautifulSoup(html, 'html.parser')

        # 输出调试信息 - 页面结构概览
        self.logger.info(f"页面内容类型: {type(html)}")
        self.logger.info(f"页面长度: {len(html)}")
        self.logger.info(f"页面标题: {soup.title.string if soup.title else '无标题'}")

        # 查找所有厂房信息项 - 尝试适应58同城新版页面结构
        factory_items = soup.select('ul.house-list-wrap > li')
        self.logger.info(f"找到厂房项目数量: {len(factory_items)}")

        if len(factory_items) == 0:
            self.logger.info("尝试使用备用选择器...")
            factory_items = soup.select('.list > .listUl > li')
            self.logger.info(f"备用选择器1 (.list > .listUl > li) 找到数量: {len(factory_items)}")

            if len(factory_items) == 0:
                factory_items = soup.select('.listUl > li')
                self.logger.info(f"备用选择器2 (.listUl > li) 找到数量: {len(factory_items)}")

            if len(factory_items) == 0:
                factory_items = soup.select('.house-list-wrap > li')
                self.logger.info(f"备用选择器3 (.house-list-wrap > li) 找到数量: {len(factory_items)}")

            if len(factory_items) == 0:
                factory_items = soup.select('.content-side-left .listUl > li')
                self.logger.info(f"备用选择器4 (.content-side-left .listUl > li) 找到数量: {len(factory_items)}")

            if len(factory_items) == 0:
                # 在正文中搜索关键词，检查页面是否确实是厂房页面
                body_text = soup.body.get_text() if soup.body else ""
                if "暂无" in body_text and "厂房" in body_text:
                    self.logger.info("页面可能显示'暂无厂房'信息")

                # 尝试查找所有li标签
                all_li = soup.select('li')
                self.logger.info(f"页面中所有li元素数量: {len(all_li)}")

                # 尝试查找可能包含房产信息的元素
                property_items = []
                for li in all_li:
                    if any(keyword in li.get_text() for keyword in ["厂房", "仓库", "㎡", "元/㎡", "平方", "出租", "出售"]):
                        property_items.append(li)

                self.logger.info(f"可能包含厂房信息的li元素数量: {len(property_items)}")
                if property_items:
                    factory_items = property_items

        # 当前58同城厂房页面结构分析
        if len(factory_items) > 0:
            sample_item = factory_items[0]
            item_html = str(sample_item)
            self.logger.info(f"厂房项目样例HTML: {item_html[:200]}...")  # 只显示前200个字符

        for i, item in enumerate(factory_items):
            try:
                # 获取项目文本内容，用于后续提取信息
                item_text = item.get_text(strip=True)
                
                # 添加排名字段 - 基于元素在页面中的位置
                factory_info = {
                    "type": factory_type,
                    "ranking": i + 1  # 排名从1开始
                }
                
                self.logger.info(f"解析第 {i+1} 个厂房项: 类名={item.get('class', '无类名')}, 排名={factory_info['ranking']}")

                # 跳过广告项
                if 'id' in item.attrs and 'ad_' in item['id']:
                    self.logger.info(f"跳过广告项: {item['id']}")
                    continue

                # 首先尝试获取真正的标题 - 从title_des类元素中获取
                title_des_elem = item.select_one('.title_des, span.title_des')
                if title_des_elem:
                    factory_info['title'] = title_des_elem.get_text(strip=True)
                    self.logger.info(f"找到真正的标题: {factory_info['title']}")

                # 提取链接 - 适应58同城新版结构
                links = item.select('a[href]')
                link_elem = None
                for link in links:
                    href = link.get('href', '')
                    if href and ('x.shtml' in href or 'changfang' in href):
                        link_elem = link
                        break

                if link_elem:
                    # 1. 提取链接URL
                    factory_info['url'] = link_elem.get('href', '')

                    # 2. 尝试从链接中提取ID
                    if factory_info['url']:
                        id_match = re.search(r'/(\d+)x\.shtml', factory_info['url'])
                        if id_match:
                            factory_info['id'] = id_match.group(1)

                    # 3. 如果之前没有从title_des找到标题，尝试其他方式
                    if 'title' not in factory_info:
                        # 尝试从链接的title属性获取标题
                        if link_elem.get('title'):
                            factory_info['title'] = link_elem.get('title')
                        # 或者从链接文本获取标题
                        elif link_elem.get_text(strip=True):
                            factory_info['title'] = link_elem.get_text(strip=True)
                        # 或者从包含链接的第一个h2或h3元素获取标题
                        else:
                            title_elem = item.select_one('h3, h2, .title')
                            if title_elem:
                                factory_info['title'] = title_elem.get_text(strip=True)

                # 如果上述方法无法获取标题和链接，尝试直接解析列表项内容
                if 'title' not in factory_info or 'url' not in factory_info:
                    # 再次尝试查找title_des类元素，因为它可能不在链接内部
                    title_des_any = soup.select_one('.title_des, span.title_des, [class*="title_des"]')
                    if title_des_any:
                        factory_info['title'] = title_des_any.get_text(strip=True)
                    else:
                        # 查找包含"厂房"关键词且不超过50个字符的文本块作为标题
                        for text_node in item.find_all(text=True):
                            text = text_node.strip()
                            if "厂房" in text and len(text) < 50:
                                factory_info['title'] = text
                                break

                    # 查找价格
                    price_texts = re.findall(r'(\d+(\.\d+)?元\/㎡\/[天月年]|\d+(\.\d+)?万\/[天月年])', item_text)
                    if price_texts:
                        factory_info['price'] = price_texts[0][0]

                    # 提取面积
                    area_matches = re.findall(r'(\d+(\.\d+)?)[平㎡平方米]', item_text)
                    if area_matches:
                        factory_info['area'] = area_matches[0][0] + "㎡"

                    # 提取地址
                    for text_node in item.find_all(text=True):
                        text = text_node.strip()
                        # 地址通常包含"区"，"路"，"街"等关键字
                        if any(keyword in text for keyword in ["区", "路", "街", "小区", "广场", "大厦"]) and len(text) < 30:
                            factory_info['address'] = text
                            break

                # 提取价格
                if 'price' not in factory_info:
                    price_elem = None
                    # 尝试多种选择器定位价格元素
                    for selector in ['.money', '.price', 'b.pri', '.sum', '.price-wrap']:
                        elem = item.select_one(selector)
                        if elem:
                            price_elem = elem
                            break

                    if price_elem:
                        factory_info['price'] = price_elem.get_text(strip=True)
                        self.logger.info(f"找到价格: {factory_info.get('price', '无价格')}")
                    else:
                        # 尝试直接从文本中提取价格
                        price_pattern = r'(\d+(\.\d+)?元\/㎡|\d+(\.\d+)?万\/月|\d+(\.\d+)?元\/月|\d+(\.\d+)?万|\d+(\.\d+)?元|\面议)'
                        price_matches = re.findall(price_pattern, item_text)
                        if price_matches:
                            factory_info['price'] = price_matches[0][0]
                            self.logger.info(f"从文本中提取价格: {factory_info.get('price', '无价格')}")

                # 提取面积、楼层等信息
                if 'area' not in factory_info:
                    info_elem = (
                        item.select_one('.showroom, .area, .introduce, .base, .detail-item') or
                        item.select_one('[class*="area"], [class*="info"]')
                    )

                    if info_elem:
                        factory_info['info'] = info_elem.get_text(strip=True)
                        # 尝试从info中提取面积
                        area_matches = re.findall(r'(\d+(\.\d+)?)[平㎡平方米]', factory_info['info'])
                        if area_matches:
                            factory_info['area'] = area_matches[0][0] + "㎡"

                # 提取地址
                if 'address' not in factory_info:
                    address_elem = (
                        item.select_one('.add, .address, .district, .jjr, .location') or
                        item.select_one('[class*="address"], [class*="location"]')
                    )

                    if address_elem:
                        factory_info['address'] = address_elem.get_text(strip=True)

                # 提取区域信息
                qu = None
                # 尝试从span.withi中提取区域信息（这是58同城常用的区域标识元素）
                qu_elem = item.select_one('span.withi')
                if qu_elem:
                    qu_text = qu_elem.get_text(strip=True)
                    self.logger.info(f"从span.withi找到原始文本: {qu_text}")
                    
                    # 区域通常是第一部分，可能有空格、中横线或其他分隔符
                    for delimiter in [' ', '-', '－', '—']:
                        if delimiter in qu_text:
                            qu = qu_text.split(delimiter)[0].strip()
                            self.logger.info(f"通过分隔符'{delimiter}'提取区域: {qu}")
                            break
                    
                    # 如果没有分隔符，使用整个文本
                    if not qu:
                        qu = qu_text
                        self.logger.info(f"使用完整文本作为区域: {qu}")
                    
                    # 确保提取出的区域有效（不是空字符串且不太长）
                    if qu and len(qu) <= 5:
                        factory_info['qu'] = qu
                        self.logger.info(f"成功提取区域: {qu}")
                    else:
                        qu = qu_text
                    self.logger.info(f"从span.with1找到区域: {qu}")
                
                # 提取联系人
                contact_elem = (
                    item.select_one('.listInfor, .owner-name, .username, .user-name') or
                    item.select_one('[class*="user"], [class*="owner"]')
                )

                if contact_elem:
                    factory_info['contact'] = contact_elem.get_text(strip=True)

                # 提取发布时间
                time_elem = (
                    item.select_one('.time, .publish-time, .sendTime, .date') or
                    item.select_one('[class*="time"]')
                )

                if time_elem:
                    factory_info['publish_time'] = time_elem.get_text(strip=True)

                # 提取描述
                desc_elem = item.select_one('.desc, .description, .house-details, .list-info, .content')
                if desc_elem:
                    factory_info['description'] = desc_elem.get_text(strip=True)

                # 提取图片URL
                img_elem = item.select_one('img')
                if img_elem and img_elem.get('src'):
                    factory_info['image_url'] = img_elem.get('src')

                # 提取经纪人信息 - 添加这部分新代码
                # 提取经纪人姓名
                manager_elem = item.select_one('span.manager')
                if manager_elem:
                    factory_info['agent_name'] = manager_elem.get_text(strip=True)
                    self.logger.info(f"找到经纪人: {factory_info.get('agent_name', '无经纪人')}")

                # 提取经纪公司
                company_elem = item.select_one('span.managercompany')
                if company_elem:
                    factory_info['agent_company'] = company_elem.get_text(strip=True)
                    self.logger.info(f"找到经纪公司: {factory_info.get('agent_company', '无公司信息')}")

                # 添加到列表
                if factory_info:
                    # 确保至少有title和url才添加
                    if 'url' in factory_info:
                        if 'title' not in factory_info:
                            # 使用URL中的部分信息作为标题
                            url_path = factory_info['url'].split('/')[-1] if '/' in factory_info['url'] else factory_info['url']
                            factory_info['title'] = f"厂房信息 - {url_path}"

                        factory_list.append(factory_info)
                        self.logger.info(f"成功解析第 {i+1} 个厂房信息")
                    else:
                        self.logger.warning(f"第 {i+1} 个厂房信息不完整，跳过")
            except Exception as e:
                self.logger.error(f"解析厂房项目 {i+1} 时发生异常: {str(e)}")
                continue

        self.logger.info(f"成功解析 {len(factory_list)} 条厂房信息")
        return factory_list

    def get_total_pages(self, html: str) -> int:
        """获取总页数"""
        if not html: return 0

        soup = BeautifulSoup(html, 'html.parser')
        page_info = soup.select('.pager a') or soup.select('.page a') or soup.select('.pager a, .page a')
        self.logger.info(f"找到页码元素数量: {len(page_info)}")

        if not page_info:
            # 检查是否有分页提示
            pagination_elem = soup.select_one('.pager') or soup.select_one('.page')
            if pagination_elem:
                pagination_text = pagination_elem.get_text(strip=True)
                self.logger.info(f"分页文本: {pagination_text}")
                # 尝试从文本中提取页数
                page_matches = re.findall(r'共(\d+)页', pagination_text)
                if page_matches:
                    try: return int(page_matches[0])
                    except: pass
            return 1

        max_page = 1
        for page in page_info:
            try:
                page_text = page.get_text(strip=True)
                if not page_text.isdigit(): continue
                page_num = int(page_text)
                if page_num > max_page: max_page = page_num
            except: continue

        return max_page

    async def crawl_city_full(self, city_name: str, max_pages: int = 1) -> None:
        """
        爬取指定城市的出租和出售厂房信息，并存入数据库
        
        Args:
            city_name: 城市名称
            max_pages: 参数保留但不再使用，只爬取第一页
        """
        if city_name not in self.city_codes:
            self.logger.error(f"不支持的城市: {city_name}")
            return
            
        # 确保有任务ID
        if not self.task_id:
            self.task_id = str(int(time.time()))
            self.logger.info(f"为当前爬取任务生成新的唯一ID: {self.task_id}")
            
        city_code = self.city_codes[city_name]
        self.logger.info(f"开始爬取 {city_name}({city_code}) 的厂房信息")
        
        # 获取该城市的代理
        rent_proxy, sale_proxy = self.get_city_proxies(city_name)
        
        # 在同一个城市处理中，为不同类型的爬取引入随机延迟，减轻服务器压力
        all_factories = []
        
        try:
            # 爬取出租和出售两种类型
            for factory_type in ["出租", "出售"]:
                self.logger.info(f"开始爬取 {city_name} 的厂房{factory_type}信息")
                
                # 在爬取前增加一个随机延迟，避免连续请求
                await asyncio.sleep(random.uniform(3, 7))
                
                # 根据类型选择对应的代理
                proxy = rent_proxy if factory_type == "出租" else sale_proxy
                self.logger.info(f"{city_name} {factory_type} 使用代理: {proxy}")
                
                # 只爬取第一页
                factories = await self.crawl_city(city_name, max_pages, factory_type, proxy)
                
                if factories:
                    all_factories.extend(factories)
                    self.logger.info(f"已爬取 {city_name} {factory_type}，数据条数: {len(factories)}")
                
                # 不同类型之间增加等待时间
                if factory_type != "出售":
                    wait_seconds = random.uniform(5, 10)
                    self.logger.info(f"爬取完{factory_type}，等待 {wait_seconds:.1f} 秒后爬取下一种类型...")
                    await asyncio.sleep(wait_seconds)
            
            # 保存数据到数据库
            if all_factories:
                # 使用异步锁保证数据库操作的安全性
                async with self.db_lock:
                    self.save_to_database(all_factories, city_name)
            else:
                self.logger.warning(f"{city_name} 未获取到数据")
                
        except Exception as e:
            self.logger.error(f"爬取 {city_name} 时发生异常: {str(e)}")
            
    async def crawl_city(self, city_name: str, max_pages: int = 1, factory_type: str = None, proxy: str = None) -> List[Dict]:
        """
        爬取指定城市和类型的厂房信息
        
        Args:
            city_name: 城市名称
            max_pages: 参数保留但不再使用，只爬取第一页
            factory_type: 厂房类型（出租或出售），如果为None则爬取两种类型
            proxy: 代理服务器地址，格式为"host:port"

        Returns:
            List[Dict]: 厂房信息列表
        """
        if city_name not in self.city_codes:
            self.logger.error(f"不支持的城市: {city_name}")
            return []

        city_code = self.city_codes[city_name]
        
        # 如果没有指定类型，使用原来的逻辑爬取两种类型
        if factory_type is None:
            self.logger.info(f"开始爬取 {city_name}({city_code}) 的所有类型厂房信息")
            all_factories = []

            # 获取该城市的代理
            rent_proxy, sale_proxy = self.get_city_proxies(city_name)

            try:
                # 初始化浏览器
                if not self.browser:
                    # 默认使用出租代理
                    await self.init_browser(proxy=rent_proxy)

                # 爬取出租和出售两种类型
                for factory_type in ["出租", "出售"]:
                    self.logger.info(f"开始爬取 {city_name} 的厂房{factory_type}信息")

                    # 在爬取前增加一个随机延迟，避免连续请求
                    await asyncio.sleep(random.uniform(5, 10))
                    
                    # 根据类型选择对应的代理
                    current_proxy = rent_proxy if factory_type == "出租" else sale_proxy
                    self.logger.info(f"{city_name} {factory_type} 使用代理: {current_proxy}")

                    # 只爬取第一页
                    first_page_url = self.get_factory_list_url(city_code, 1, factory_type)
                    self.logger.info(f"请求页面: {first_page_url}")
                    first_page_html = await self.fetch_page(first_page_url, city_code=city_code, proxy=current_proxy)

                    if not first_page_html:
                        self.logger.error(f"无法获取 {city_name} 的{factory_type}页面内容")
                        continue

                    # 解析厂房列表
                    factories = self.parse_factory_list(first_page_html, factory_type)
                    all_factories.extend(factories)
                    self.logger.info(f"已爬取 {city_name} {factory_type}，数据条数: {len(factories)}")

                    # 不同类型之间增加等待时间
                    if factory_type != "出售":
                        wait_seconds = random.uniform(15, 20)
                        self.logger.info(f"爬取完{factory_type}，等待 {wait_seconds:.1f} 秒后爬取下一种类型...")
                        await asyncio.sleep(wait_seconds)

            except Exception as e:
                self.logger.error(f"爬取 {city_name} 时发生异常: {str(e)}")
            finally:
                self.logger.info(f"完成爬取 {city_name} 的厂房信息，共 {len(all_factories)} 条")

            return all_factories
        else:
            # 爬取指定类型的厂房信息
            self.logger.info(f"开始爬取 {city_name}({city_code}) 的厂房{factory_type}信息")
            factories = []
            
            # 如果没有提供代理，获取一个
            if not proxy:
                rent_proxy, sale_proxy = self.get_city_proxies(city_name)
                proxy = rent_proxy if factory_type == "出租" else sale_proxy
                self.logger.info(f"{city_name} {factory_type} 使用代理: {proxy}")
            
            try:
                # 初始化浏览器
                if not self.browser:
                    await self.init_browser(proxy=proxy)
                
                # 只爬取第一页
                first_page_url = self.get_factory_list_url(city_code, 1, factory_type)
                self.logger.info(f"请求页面: {first_page_url}")
                first_page_html = await self.fetch_page(first_page_url, city_code=city_code, proxy=proxy)
                
                if not first_page_html:
                    self.logger.error(f"无法获取 {city_name} 的{factory_type}页面内容")
                    return []
                
                # 解析厂房列表
                factories = self.parse_factory_list(first_page_html, factory_type)
                self.logger.info(f"已爬取 {city_name} {factory_type}，数据条数: {len(factories)}")
                
            except Exception as e:
                self.logger.error(f"爬取 {city_name} 的{factory_type}信息时发生异常: {str(e)}")
            
            return factories

    def save_to_database(self, data: List[Dict], city_name: str) -> None:
        """保存数据到MySQL数据库"""
        if not data:
            self.logger.warning(f"{city_name} 没有数据需要保存")
            return

        # 确保数据库连接已初始化
        if not self.db_connection:
            try:
                self.init_db_connection()
            except Exception as e:
                self.logger.error(f"初始化数据库连接失败: {str(e)}")
                return

        # 使用全局task_id而不是每批次重新生成
        if not self.task_id:
            self.logger.error("未找到有效的task_id，无法保存数据")
            return
            
        self.logger.info(f"使用当前批次task_id: {self.task_id}")

        cursor = None
        try:
            cursor = self.db_connection.cursor()

            # 插入记录计数
            inserted_count = 0
            skipped_count = 0

            for item in data:
                # 准备数据字段
                property_id = item.get('id', '')
                if not property_id and 'url' in item:
                    # 尝试从 URL 提取 ID
                    id_match = re.search(r'/([0-9]+)x\.shtml', item['url'])
                    if id_match:
                        property_id = id_match.group(1)

                # 如果仍然没有ID，生成一个基于时间的唯一ID
                if not property_id:
                    property_id = f"58_{int(time.time())}_{random.randint(1000, 9999)}"

                # 准备其他字段
                property_type = item.get('type', '')
                title = item.get('title', '')
                url = item.get('url', '')
                price = item.get('price', '')

                # 处理面积字段
                building_area = None
                if 'area' in item:
                    area_str = item['area'].replace('㎡', '').replace('平', '').replace('平方米', '').strip()
                    try:
                        building_area = float(area_str)
                    except (ValueError, TypeError):
                        # 如果无法转换，保持为 None
                        pass

                # 处理发布时间，将相对时间转换为当天日期格式（MM-DD）
                raw_publish_time = item.get('publish_time', '')
                publish_time = self.format_publish_time(raw_publish_time)
                image_url = item.get('image_url', '')
                agent_name = item.get('agent_name', '')
                agent_company = item.get('agent_company', '')
                
                # 添加城市名称
                city = city_name
                
                # 获取排名字段
                ranking = item.get('ranking', None)
                if ranking:
                    self.logger.info(f"帖子 {title} (ID: {property_id}) 的排名为: {ranking}")
                
                # 获取区域字段
                qu = item.get('qu', '')
                if qu:
                    self.logger.info(f"帖子 {title} (ID: {property_id}) 的区域为: {qu}")

                # 检查记录是否已存在
                # 1. 检查ID是否已存在（任何批次中）- 这是因为ID是主键，必须唯一
                # 2. 检查【同一task_id】内是否有相同标题 - 这允许不同task_id之间可以有相同标题
                cursor.execute("""
                    SELECT id, task_id FROM property_listing 
                    WHERE (id = %s) OR (title = %s AND task_id = %s)
                """, (property_id, title, self.task_id))
                existing_record = cursor.fetchone()

                if existing_record:
                    # 区分是ID重复还是标题重复
                    if existing_record['id'] == property_id:
                        self.logger.info(f"检测到ID重复: {title} (ID: {property_id})，尝试修改ID并插入")
                        # 生成新ID：原ID加上时间戳和随机数后缀
                        new_property_id = f"{property_id}_{int(time.time())}_{random.randint(1000, 9999)}"
                        self.logger.info(f"生成新ID: {new_property_id}")
                        
                        # 使用新ID重新尝试插入
                        try:
                            cursor.execute("""
                                INSERT INTO property_listing (
                                    id, type, title, url, price, building_area,
                                    publish_time, image_url, agent_name, agent_company, created_at, city, ranking, qu, task_id
                                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s, %s)
                            """, (
                                new_property_id, property_type, title, url, price,
                                building_area, publish_time, image_url, agent_name, agent_company, city, ranking, qu, self.task_id
                            ))
                            inserted_count += 1
                            self.logger.info(f"使用新ID成功插入记录: {title} (新ID: {new_property_id})")
                        except Exception as e:
                            self.logger.error(f"使用新ID插入记录失败: {title} (新ID: {new_property_id}), 错误: {str(e)}")
                            skipped_count += 1
                    else:
                        self.logger.info(f"跳过重复标题记录: {title} (ID: {property_id})，在当前task_id({self.task_id})中已存在相同标题")
                        skipped_count += 1
                else:
                    # 记录不存在，直接插入
                    try:
                        cursor.execute("""
                            INSERT INTO property_listing (
                                id, type, title, url, price, building_area,
                                publish_time, image_url, agent_name, agent_company, created_at, city, ranking, qu, task_id
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s, %s)
                        """, (
                            property_id, property_type, title, url, price,
                            building_area, publish_time, image_url, agent_name, agent_company, city, ranking, qu, self.task_id
                        ))
                        inserted_count += 1
                    except pymysql.err.IntegrityError as e:
                        # 捕获可能的唯一键冲突错误（可能是并发插入导致）
                        self.logger.warning(f"插入过程中发生唯一键冲突: {title} (ID: {property_id}), 尝试使用新ID")
                        
                        # 生成新ID并重试
                        new_property_id = f"{property_id}_{int(time.time())}_{random.randint(1000, 9999)}"
                        try:
                            cursor.execute("""
                                INSERT INTO property_listing (
                                    id, type, title, url, price, building_area,
                                    publish_time, image_url, agent_name, agent_company, created_at, city, ranking, qu, task_id
                                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s, %s)
                            """, (
                                new_property_id, property_type, title, url, price,
                                building_area, publish_time, image_url, agent_name, agent_company, city, ranking, qu, self.task_id
                            ))
                            inserted_count += 1
                            self.logger.info(f"使用新ID成功插入记录: {title} (新ID: {new_property_id})")
                        except Exception as retry_e:
                            self.logger.error(f"使用新ID重试插入失败: {title}, 错误: {str(retry_e)}")
                            skipped_count += 1

            # 提交事务
            self.db_connection.commit()
            self.logger.info(f"成功保存 {city_name} 的数据到数据库: 新增 {inserted_count} 条, 跳过 {skipped_count} 条, 批次ID: {self.task_id}")

        except Exception as e:
            # 回滚事务
            if self.db_connection:
                self.db_connection.rollback()
            self.logger.error(f"保存数据到数据库失败: {str(e)}")
        finally:
            # 关闭游标
            if cursor:
                cursor.close()

    async def run_async(self, cities: List[str] = None, max_pages: int = 1) -> None:
        """
        异步运行爬虫，支持同时爬取多个城市
        
        Args:
            cities: 要爬取的城市列表，None表示爬取所有支持的城市
            max_pages: 每个城市最大爬取页数，默认为1
        """
        if not cities:
            cities = list(self.city_codes.keys())
        
        # 只有在没有task_id时才生成新的
        if not self.task_id:
            self.task_id = str(int(time.time()))
            self.logger.info(f"本次爬取任务的唯一ID: {self.task_id}")
        else:
            self.logger.info(f"使用已有的任务ID: {self.task_id}")
            
        try:
            # 更新代理池
            self.logger.info("开始更新代理池...")
            if self.update_proxy_pool():
                self.logger.info(f"代理池更新成功，获取到 {len(self.available_proxies)} 个有效代理")
            else:
                self.logger.warning("代理池更新失败，将使用直接连接")
            
            # 初始化数据库连接
            self.init_db_connection()
            
            # 初始化浏览器 - 这里不传入代理，每个城市爬取时会根据类型选择不同的代理
            await self.init_browser()
            
            # 创建信号量，限制同时爬取的城市数量为10个
            self.city_semaphore = asyncio.Semaphore(self.max_concurrent_cities)
            self.db_lock = asyncio.Lock()
            
            # 创建任务列表
            tasks = []
            
            async def crawl_with_semaphore(city):
                # 使用信号量控制并发数量
                async with self.city_semaphore:
                    if city not in self.city_codes:
                        self.logger.warning(f"不支持的城市: {city}，已跳过")
                        return
                        
                    await self.crawl_city_full(city, max_pages)
                    
                    # 添加一些随机等待，避免请求过于集中
                    await asyncio.sleep(random.uniform(1, 5))
            
            # 为每个城市创建一个任务
            for city in cities:
                task = asyncio.create_task(crawl_with_semaphore(city))
                tasks.append(task)
                
            # 等待所有任务完成
            await asyncio.gather(*tasks)
            
        except Exception as e:
            self.logger.error(f"运行爬虫时发生异常: {str(e)}")
        finally:
            # 关闭浏览器
            await self.close_browser()
            
            # 关闭数据库连接
            self.close_db_connection()

    def run(self, cities: List[str] = None, max_pages: int = 1) -> None:
        """运行爬虫"""
        # 设置全局任务ID（时间戳），确保每次运行脚本只生成一个时间戳
        self.task_id = str(int(time.time()))
        self.logger.info(f"本次爬取任务的唯一ID: {self.task_id}")
        
        asyncio.run(self.run_async(cities, max_pages))

    def init_redis_connection(self):
        """初始化Redis连接"""
        try:
            self.redis_conn = redis.StrictRedis(host="**************", port=6379, password="Meiyoumima333." , decode_responses=True)
            self.logger.info("Redis连接已建立")
        except Exception as e:
            self.logger.error(f"Redis连接失败: {str(e)}")
            self.redis_conn = None
    
    def update_proxy_pool(self):
        """从Redis获取最新的代理池"""
        if not self.redis_conn:
            self.logger.warning("Redis连接未建立，无法获取代理")
            return False
        
        try:
            # 使用Redis键名
            redis_key = "factory_proxies"
            
            # 从Redis中获取所有代理
            all_proxies = self.redis_conn.smembers(redis_key)
            
            if not all_proxies:
                self.logger.warning(f"Redis中的{redis_key}没有可用代理，将自动获取新代理")
                if not self.generate_new_proxies():
                    self.logger.error("自动获取新代理失败")
                    return False
                # 重新获取代理
                all_proxies = self.redis_conn.smembers(redis_key)
                if not all_proxies:
                    self.logger.error("获取新代理后仍然没有可用代理")
                    return False
            
            # 筛选格式正确的代理 (IP:PORT)
            valid_proxies = [proxy for proxy in all_proxies if re.match(r'^\d+\.\d+\.\d+\.\d+:\d+$', proxy)]
            
            if not valid_proxies:
                self.logger.warning(f"Redis中的{redis_key}没有格式正确的代理，将自动获取新代理")
                if not self.generate_new_proxies():
                    self.logger.error("自动获取新代理失败")
                    return False
                # 重新获取代理
                all_proxies = self.redis_conn.smembers(redis_key)
                valid_proxies = [proxy for proxy in all_proxies if re.match(r'^\d+\.\d+\.\d+\.\d+:\d+$', proxy)]
                if not valid_proxies:
                    self.logger.error("获取新代理后仍然没有格式正确的代理")
                    return False
            
            self.logger.info(f"从Redis的{redis_key}获取到 {len(valid_proxies)} 个有效代理")
            
            # 随机选取两个不同的代理作为默认出租和出售代理
            if len(valid_proxies) >= 2:
                self.default_rent_proxy, self.default_sale_proxy = random.sample(valid_proxies, 2)
            else:
                # 如果只有一个代理，两种类型使用相同的代理
                self.default_rent_proxy = self.default_sale_proxy = valid_proxies[0]
            
            # 更新可用代理列表
            self.available_proxies = valid_proxies
            
            self.logger.info(f"默认出租代理: {self.default_rent_proxy}")
            self.logger.info(f"默认出售代理: {self.default_sale_proxy}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"获取Redis代理失败: {str(e)}，将尝试自动获取新代理")
            # 尝试生成新代理
            return self.generate_new_proxies() and self.update_proxy_pool()
    
    def generate_new_proxies(self):
        """
        调用add_proxy.py生成新的代理
        
        Returns:
            bool: 是否成功生成新代理
        """
        self.logger.info("正在调用add_proxy.py生成新的代理...")
        
        try:
            # 构建add_proxy.py的完整路径
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'add_proxy.py')
            
            # 调用add_proxy.py
            result = subprocess.run([sys.executable, script_path], 
                                   stdout=subprocess.PIPE, 
                                   stderr=subprocess.PIPE,
                                   encoding='utf-8')
            
            if result.returncode == 0:
                self.logger.info("成功生成新的代理")
                return True
            else:
                self.logger.error(f"生成新代理失败，返回码: {result.returncode}")
                self.logger.error(f"错误信息: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"生成新代理时发生异常: {str(e)}")
            return False
    
    def get_random_proxy(self, exclude=None) -> str:
        """
        从代理池中获取一个随机代理
        
        Args:
            exclude: 排除的代理
            
        Returns:
            str: 随机代理
        """
        if not self.available_proxies:
            return None
            
        # 排除指定代理
        available = [p for p in self.available_proxies if p != exclude]
        
        if not available:
            # 如果没有可用代理，返回None
            return None
            
        return random.choice(available)
    
    def get_city_proxies(self, city_name: str) -> Tuple[str, str]:
        """
        为指定城市获取出租和出售两种不同的代理
        
        Args:
            city_name: 城市名称
            
        Returns:
            Tuple[str, str]: (出租代理, 出售代理)
        """
        # 如果可用代理不足，直接返回默认代理
        if len(self.available_proxies) < 2:
            return self.default_rent_proxy, self.default_sale_proxy
        
        # 为每个城市分配不同的代理
        city_hash = sum(ord(c) for c in city_name) % len(self.available_proxies)
        rent_proxy_index = city_hash
        sale_proxy_index = (city_hash + len(self.available_proxies) // 2) % len(self.available_proxies)
        
        rent_proxy = self.available_proxies[rent_proxy_index]
        sale_proxy = self.available_proxies[sale_proxy_index]
        
        # 确保两个代理不同
        if rent_proxy == sale_proxy and len(self.available_proxies) > 1:
            sale_proxy = self.get_random_proxy(exclude=rent_proxy)
        
        return rent_proxy, sale_proxy

def main():
    """
    主函数 - 58同城厂房信息爬虫
    
    已内置58同城cookie，无需额外登录，可直接访问厂房信息
    数据将保存到MySQL数据库的property_listing表中
    支持同时爬取多个城市的出租和出售信息
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='58同城厂房信息爬虫 (Playwright版) - 支持出租和出售 - 已内置登录cookie - 数据存入MySQL')
    parser.add_argument('--cities', type=str, nargs='+', help='要爬取的城市列表，例如: 哈尔滨 长春 青岛')
    parser.add_argument('--max-pages', type=int, default=1, help='参数保留但不再使用，脚本只爬取第一页数据')
    parser.add_argument('--debug', action='store_true', help='开启调试模式，保存HTML和截图到debug目录')
    parser.add_argument('--visible', action='store_true', help='显示浏览器窗口，不使用无头模式')
    parser.add_argument('--no-auto-captcha', action='store_true', help='禁用自动处理验证码功能')
    parser.add_argument('--retry-limit', type=int, default=3, help='验证码处理失败时的最大重试次数，默认为3')
    parser.add_argument('--wait-time', type=int, default=20, help='城市之间的等待时间(秒)，默认为20-45秒范围内随机')
    parser.add_argument('--user-agent', type=str, help='自定义User-Agent字符串')
    parser.add_argument('--max-concurrent', type=int, default=10, help='同时爬取的最大城市数量，默认为10')
    
    args = parser.parse_args()
    
    # 创建爬虫实例
    print(f"\n开始爬取厂房数据，同时爬取{args.max_concurrent}个城市的出租和出售信息")
    print(f"数据将保存到MySQL数据库的 property_listing 表")
    print(f"注意: 如果标题重复，将跳过该记录而不会更新\n")
    
    spider = Spider58FactoryPlaywright(
        debug=args.debug,
        headless=not args.visible,
        auto_captcha=not args.no_auto_captcha,
        retry_limit=args.retry_limit,
        wait_time=args.wait_time,
        user_agent=args.user_agent,
        max_concurrent_cities=args.max_concurrent
    )
    spider.run(cities=args.cities, max_pages=args.max_pages)

if __name__ == "__main__":
    main()