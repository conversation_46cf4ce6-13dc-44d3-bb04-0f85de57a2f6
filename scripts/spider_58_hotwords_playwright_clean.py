#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""58同城热搜词爬虫"""

import random
import asyncio
import sys
import os
from datetime import datetime
from typing import List, Dict
from bs4 import BeautifulSoup

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from scripts.spider_58_factory_playwright import Spider58FactoryPlaywright

class Spider58HotwordsPlaywright(Spider58FactoryPlaywright):
    def __init__(self, headless=True, max_concurrent=3, debug=False, auto_captcha=True,
                 retry_limit=3, wait_time=20, user_agent=None, proxies=None):
        # 调用父类初始化，获得完整的代理管理功能
        super().__init__(
            debug=debug,
            headless=headless,
            auto_captcha=auto_captcha,
            retry_limit=retry_limit,
            wait_time=wait_time,
            user_agent=user_agent,
            max_concurrent_cities=max_concurrent,
            proxies=proxies
        )

        # 热搜词爬虫特有的属性
        self.max_concurrent = max_concurrent


    def get_hotwords_url(self, city_code: str, property_type: str = "出租") -> str:
        if property_type == "出售":
            return f"https://{city_code}.58.com/changfang/b5/?PGTID=0d30576d-0000-1dfb-dc07-01004bdd4d48&ClickID=2"
        else:
            return f"https://{city_code}.58.com/changfang/?PGTID=0d200001-0000-1d5a-5978-05e5d4a61926&ClickID=1"


    def save_hotwords_to_db(self, hotwords: List[Dict]) -> bool:
        if not hotwords or not self.db_connection:
            return False

        try:
            cursor = self.db_connection.cursor()
            insert_sql = """
                INSERT INTO hot_search_keywords (city, city_code, zs_type, keyword, create_time)
                VALUES (%s, %s, %s, %s, %s)
            """

            data_to_insert = []
            current_time = datetime.now()

            for hotword in hotwords:
                data_to_insert.append((
                    hotword.get('city', ''),
                    hotword.get('city_code', ''),
                    hotword.get('property_type', ''),
                    hotword.get('keyword', ''),
                    current_time
                ))

            cursor.executemany(insert_sql, data_to_insert)
            self.db_connection.commit()
            return True

        except Exception as e:
            self.logger.error(f"保存热搜词失败: {str(e)}")
            if self.db_connection:
                self.db_connection.rollback()
            return False
        finally:
            if cursor:
                cursor.close()

    def parse_hotwords(self, html: str, city_name: str, property_type: str) -> List[Dict]:
        if not html:
            return []

        hotwords_list = []
        soup = BeautifulSoup(html, 'html.parser')
        hot_search_ul = soup.find('ul', class_='hot_search')

        print(f"查找热搜元素: {'找到' if hot_search_ul else '未找到'}")

        if hot_search_ul:
            hotword_links = hot_search_ul.find_all('li')
            print(f"找到 {len(hotword_links)} 个热搜链接")
            for li in hotword_links:
                try:
                    link = li.find('a')
                    if link:
                        keyword = link.get_text(strip=True)
                        hotword_info = {
                            'city': city_name,
                            'city_code': self.city_codes.get(city_name, ''),
                            'keyword': keyword,
                            'property_type': property_type
                        }
                        hotwords_list.append(hotword_info)
                except Exception:
                    continue

        return hotwords_list



    async def crawl_city_hotwords(self, city_name: str, property_type: str, proxy=None) -> List[Dict]:
        """爬取指定城市和类型的热搜词，使用父类的fetch_page方法"""
        if city_name not in self.city_codes:
            return []

        city_code = self.city_codes[city_name]
        try:
            # 在请求前增加随机延迟，避免请求过于频繁
            await asyncio.sleep(random.uniform(2, 5))

            url = self.get_hotwords_url(city_code, property_type)
            # 使用父类的fetch_page方法，它包含完整的代理支持和错误处理
            html_content = await self.fetch_page(url, city_code=city_code, proxy=proxy)
            if not html_content:
                return []

            hotwords = self.parse_hotwords(html_content, city_name, property_type)

            # 如果没有找到热搜词，保存页面内容用于调试
            if len(hotwords) == 0:
                debug_filename = f"debug_{city_name}_{property_type}.html"
                with open(debug_filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                self.logger.warning(f"⚠ {city_name} {property_type}: 0 个热搜词 (已保存调试文件: {debug_filename})")
            else:
                self.logger.info(f"✓ {city_name} {property_type}: {len(hotwords)} 个热搜词")

            return hotwords

        except Exception as e:
            self.logger.error(f"爬取 {city_name} {property_type} 异常: {str(e)}")
            return []

    async def crawl_single_city(self, city_name: str, semaphore: asyncio.Semaphore) -> List[Dict]:
        """爬取单个城市的热搜词，使用代理机制"""
        async with semaphore:
            try:
                all_hotwords = []

                # 在开始爬取前增加随机延迟，避免请求过于集中
                await asyncio.sleep(random.uniform(3, 8))

                # 获取该城市的代理
                rent_proxy, sale_proxy = self.get_city_proxies(city_name)
                self.logger.info(f"{city_name} 分配代理 - 出租: {rent_proxy}, 出售: {sale_proxy}")

                for property_type in ["出租", "出售"]:
                    self.logger.info(f"{city_name} {property_type} 开始爬取")

                    # 根据类型选择对应的代理
                    proxy = rent_proxy if property_type == "出租" else sale_proxy

                    # 直接使用父类的fetch_page方法，不需要创建独立的context
                    hotwords = await self.crawl_city_hotwords(city_name, property_type, proxy=proxy)

                    # 边爬边插：立即保存当前城市类型的热搜词
                    if hotwords:
                        async with self.db_lock:
                            success = self.save_hotwords_to_db(hotwords)
                            if success:
                                self.logger.info(f"✓ {city_name} {property_type}: 已保存 {len(hotwords)} 个热搜词到数据库")
                            else:
                                self.logger.error(f"✗ {city_name} {property_type}: 保存失败")

                    all_hotwords.extend(hotwords)

                    if property_type == "出租":
                        await asyncio.sleep(random.uniform(5, 10))

                return all_hotwords

            except Exception as e:
                self.logger.error(f"爬取城市 {city_name} 失败: {str(e)}")
                return []

    async def run_async(self, cities: List[str] = None, use_concurrent: bool = True):
        try:
            # 更新代理池
            self.logger.info("开始更新代理池...")
            if self.update_proxy_pool():
                self.logger.info(f"代理池更新成功，获取到 {len(self.available_proxies)} 个有效代理")
            else:
                self.logger.warning("代理池更新失败，将使用直接连接")

            self.init_db_connection()

            # 初始化浏览器 - 这里不传入代理，每个城市爬取时会根据类型选择不同的代理
            await self.init_browser()

            self.db_lock = asyncio.Lock()

            if not cities:
                cities = list(self.city_codes.keys())

            concurrent_limit = self.max_concurrent if use_concurrent else 1
            self.logger.info(f"开始爬取 {len(cities)} 个城市，并发数: {concurrent_limit}")

            semaphore = asyncio.Semaphore(concurrent_limit)

            tasks = []
            for city_name in cities:
                task = self.crawl_single_city(city_name, semaphore)
                tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            all_hotwords = []
            for i, result in enumerate(results):
                if isinstance(result, list):
                    all_hotwords.extend(result)
                else:
                    self.logger.error(f"城市 {cities[i]} 爬取失败: {result}")

            self.logger.info(f"\n爬取完成！总共获取 {len(all_hotwords)} 个热搜词")

            self.logger.info(f"爬取完成！总共获取 {len(all_hotwords)} 个热搜词")

        except Exception as e:
            self.logger.error(f"运行异常: {str(e)}")
        finally:
            await self.close_browser()
            self.close_db_connection()

    def run(self, cities: List[str] = None, use_concurrent: bool = True):
        asyncio.run(self.run_async(cities, use_concurrent))


def main():
    import argparse
    parser = argparse.ArgumentParser(description='58同城热搜词爬虫')
    parser.add_argument('--cities', type=str, nargs='+', help='要爬取的城市列表')
    parser.add_argument('--all-cities', action='store_true', help='爬取所有配置的城市')
    parser.add_argument('--concurrent', type=int, default=5, help='并发数量 (默认: 5)')
    parser.add_argument('--no-concurrent', action='store_true', help='禁用并发模式，使用串行模式')
    parser.add_argument('--visible', action='store_true', help='显示浏览器')
    args = parser.parse_args()

    spider = Spider58HotwordsPlaywright(
        headless=not args.visible,
        max_concurrent=args.concurrent
    )

    cities_to_crawl = None
    if args.cities:
        cities_to_crawl = args.cities
    elif getattr(args, 'all_cities', False):
        cities_to_crawl = list(spider.city_codes.keys())

    use_concurrent = not args.no_concurrent

    spider.run(cities=cities_to_crawl, use_concurrent=use_concurrent)

if __name__ == "__main__":
    main()
