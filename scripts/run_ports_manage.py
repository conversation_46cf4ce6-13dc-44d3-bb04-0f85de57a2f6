#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
端口管理运行脚本
提供多种运行模式的入口点
"""

import asyncio
import sys
import os
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ports.orchestrator import PortsOrchestrator


async def run_full_process():
    """运行完整流程：获取端口 -> 获取帖子 -> 激活帖子"""
    print("开始运行完整的端口管理流程...")
    orchestrator = PortsOrchestrator()
    results = await orchestrator.run_full_process()

    if results["success"]:
        print("✅ 完整流程执行成功！")
        if results["summary_file"]:
            print(f"📊 端口统计文件: {results['summary_file']}")
        if results["posts_file"]:
            print(f"📝 帖子信息文件: {results['posts_file']}")
        if results["activation_file"]:
            print(f"🚀 激活结果文件: {results['activation_file']}")
    else:
        print(f"❌ 执行失败: {results['error_message']}")


async def run_ports_only():
    """仅获取端口信息"""
    print("开始获取端口信息...")
    orchestrator = PortsOrchestrator()
    results = await orchestrator.run_ports_only()

    if results["success"]:
        print("✅ 端口获取成功！")
        if results["summary_file"]:
            print(f"📊 端口统计文件: {results['summary_file']}")
    else:
        print(f"❌ 端口获取失败: {results['error_message']}")


async def run_posts_only():
    """仅获取帖子信息"""
    print("开始获取帖子信息...")
    orchestrator = PortsOrchestrator()
    results = await orchestrator.run_posts_only()

    if results["success"]:
        print("✅ 帖子获取成功！")
        print(f"📝 获取到 {len(results['posts_results'])} 个账号的帖子信息")
    else:
        print(f"❌ 帖子获取失败: {results['error_message']}")


async def run_activation_only():
    """仅运行激活流程"""
    print("开始激活帖子...")
    orchestrator = PortsOrchestrator()
    results = await orchestrator.run_activation_only()

    if results["success"]:
        print("✅ 激活流程成功！")
        if results["activation_file"]:
            print(f"🚀 激活结果文件: {results['activation_file']}")
    else:
        print(f"❌ 激活失败: {results['error_message']}")


async def run_offline_only(city: str = None):
    """仅运行下架流程"""
    if city:
        print(f"开始下架城市 {city} 的帖子...")
    else:
        print("开始下架所有城市的帖子...")

    orchestrator = PortsOrchestrator()
    results = await orchestrator.run_offline_only(city=city)

    if results["success"]:
        print("✅ 下架流程成功！")
        if results["offline_file"]:
            print(f"📤 下架结果文件: {results['offline_file']}")
    else:
        print(f"❌ 下架失败: {results['error_message']}")


async def run_city_posts(city: str, city_name: str = None):
    """为指定城市获取帖子信息"""
    print(f"开始获取城市 {city} 的帖子信息...")
    orchestrator = PortsOrchestrator()
    results = await orchestrator.run_custom_city_posts(city, city_name)

    if results["success"]:
        print(f"✅ 城市 {city} 帖子获取成功！")
        if results["posts_file"]:
            print(f"📝 帖子信息文件: {results['posts_file']}")
    else:
        print(f"❌ 城市 {city} 帖子获取失败: {results['error_message']}")


def main():
    parser = argparse.ArgumentParser(description='端口管理脚本')
    parser.add_argument('mode', choices=['full', 'ports', 'posts', 'activation', 'offline', 'city'],
                       help='运行模式')
    parser.add_argument('--city', help='城市代码（在city模式下必需，在offline模式下可选）')
    parser.add_argument('--city-name', help='城市名称（可选）')
    
    args = parser.parse_args()
    
    try:
        if args.mode == 'full':
            asyncio.run(run_full_process())
        elif args.mode == 'ports':
            asyncio.run(run_ports_only())
        elif args.mode == 'posts':
            asyncio.run(run_posts_only())
        elif args.mode == 'activation':
            asyncio.run(run_activation_only())
        elif args.mode == 'offline':
            asyncio.run(run_offline_only(args.city))
        elif args.mode == 'city':
            if not args.city:
                print("❌ 错误: city模式需要指定 --city 参数")
                sys.exit(1)
            asyncio.run(run_city_posts(args.city, args.city_name))
    except KeyboardInterrupt:
        print("\n⚠️  用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 执行过程中发生错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    print("🚀 端口管理系统")
    print("=" * 50)
    print("使用方法:")
    print("  python run_ports_manage.py full          # 运行完整流程（激活）")
    print("  python run_ports_manage.py ports         # 仅获取端口信息")
    print("  python run_ports_manage.py posts         # 仅获取帖子信息")
    print("  python run_ports_manage.py activation    # 仅运行激活流程")
    print("  python run_ports_manage.py offline       # 仅运行下架流程（所有城市）")
    print("  python run_ports_manage.py offline --city=bj # 下架指定城市")
    print("  python run_ports_manage.py city --city=bj # 获取指定城市帖子")
    print("=" * 50)
    print()

    main()
