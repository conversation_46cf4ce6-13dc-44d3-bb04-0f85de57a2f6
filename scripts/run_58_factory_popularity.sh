#!/bin/bash

# 58同城厂房信息爬虫运行脚本
# 用法: ./run_58_factory_popularity.sh

# 创建logs目录
# 兼容MacOS和Linux的脚本路径获取方式
if [[ "$OSTYPE" == "darwin"* ]]; then
  # MacOS系统
  SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
else
  # Linux系统
  SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
fi

LOG_DIR="${SCRIPT_DIR}/logs"
mkdir -p "$LOG_DIR"

# 使用固定的日志文件名，不再使用时间戳
LOG_FILE="${LOG_DIR}/spider_58_popularity_run.log"

# 添加分隔线，标记新的日志会话开始
echo -e "\n\n===================================================" >> "$LOG_FILE"
echo -e "====== 新日志会话开始于: $(date) ======" >> "$LOG_FILE"
echo -e "===================================================\n" >> "$LOG_FILE"

# 日志函数
log() {
  local message="[$(date +"%Y-%m-%d %H:%M:%S")] $1"
  echo -e "$message" | tee -a "$LOG_FILE"
}

# 显示简化的帮助信息
show_help() {
  echo "58同城厂房信息人气数据爬虫运行脚本"
  echo "用法: $0"
  echo "默认爬取城市: 长春、哈尔滨、青岛"
  echo "每种类型爬取40条数据"
  exit 0
}

# 检查帮助参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
  show_help
fi

log "脚本开始执行，日志保存在: $LOG_FILE"

# 默认配置
MAX_PAGES=1

# 构建命令
CMD="python ${SCRIPT_DIR}/spider_58_factory_popularity.py --headless true $DEBUG"

# 显示执行信息
log "开始运行58同城厂房信息人气数据爬虫..."
log "当前时间: $(date)"
log "爬取城市: 长春、哈尔滨、宁波 (已在脚本中设置)"
log "每种类型爬取: 40条数据 (已在脚本中设置)"
log "运行命令: $CMD"
log "开始执行..."

# 执行命令
eval $CMD 2>&1 | tee -a "$LOG_FILE"

# 获取退出状态
EXIT_CODE=${PIPESTATUS[0]}
if [ $EXIT_CODE -eq 0 ]; then
  log "爬虫任务成功完成！"
else
  log "爬虫任务失败，退出代码: $EXIT_CODE"
fi
log "结束时间: $(date)"

exit $EXIT_CODE 