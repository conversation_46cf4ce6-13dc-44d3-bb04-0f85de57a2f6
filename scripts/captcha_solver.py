#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import random
import logging
import asyncio
from playwright.async_api import Page

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('captcha_solver')

class CaptchaSolver:
    """58同城验证码自动处理类"""
    
    def __init__(self, debug: bool = False):
        """
        初始化验证码处理器
        
        Args:
            debug: 调试模式，保存中间图片
        """
        self.debug = debug
        # 创建调试目录
        if self.debug:
            self.debug_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'captcha_debug')
            os.makedirs(self.debug_dir, exist_ok=True)
        
        logger.info("验证码处理器初始化完成")
    
    async def solve_captcha(self, page: Page) -> bool:
        """
        自动处理验证码
        
        Args:
            page: Playwright页面对象
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 等待页面加载完成
            await asyncio.sleep(2)
            
            # 检查是否是按钮点击型验证码（访问过于频繁类型）
            button_selectors = [
                '.btn', 
                'button', 
                '.button', 
                '[class*="btn"]',
                '//button[contains(text(), "点击")]',
                '//button[contains(text(), "验证")]',
                '//a[contains(text(), "点击")]',
                '//a[contains(text(), "验证")]',
                # 增加特定选择器
                '//button[contains(@class, "btn")]',
                '.verifyBtn',
                '.verify-btn',
                '//div[contains(@class, "btn")]',
                # 58同城常用的验证按钮选择器
                '.JDJRV-btn',
                '.JDJRV-suspend-slide',
                '.btn-primary',
                '.antiBotBtn'
            ]
            
            # 记录页面内容，便于分析
            if self.debug:
                html_content = await page.content()
                with open(os.path.join(self.debug_dir, f"before_solve_{int(time.time())}.html"), "w", encoding="utf-8") as f:
                    f.write(html_content)
            
            # 尝试查找验证按钮
            button = None
            for selector in button_selectors:
                try:
                    if selector.startswith('//'):
                        # XPath选择器
                        element = await page.wait_for_selector(selector, state="visible", timeout=1000)
                    else:
                        # CSS选择器
                        element = await page.query_selector(selector)
                    
                    if element:
                        # 检查元素是否可见且是否可能是验证按钮
                        is_visible = await element.is_visible()
                        if not is_visible:
                            continue
                            
                        element_text = await element.text_content()
                        if not element_text:
                            element_text = "(无文本)"
                            
                        # 检查是否是验证按钮
                        if "验证" in element_text or "点击" in element_text or "按钮" in element_text or len(element_text.strip()) < 10:
                            button = element
                            logger.info(f"找到可能的验证按钮: '{element_text}' 使用选择器: {selector}")
                            break
                except Exception as e:
                    logger.debug(f"尝试选择器 {selector} 时出错: {str(e)}")
                    continue
            
            # 如果找不到按钮，检查页面中所有可点击元素
            if not button:
                logger.info("未找到明确的验证按钮，尝试查找所有可点击元素...")
                clickable_selectors = [
                    'button', 'a', '[role="button"]', '[type="button"]', 
                    '[class*="btn"]', '[class*="button"]', '[onclick]'
                ]
                
                for selector in clickable_selectors:
                    elements = await page.query_selector_all(selector)
                    for element in elements:
                        try:
                            is_visible = await element.is_visible()
                            if not is_visible:
                                continue
                                
                            element_text = await element.text_content()
                            if not element_text:
                                element_text = "(无文本)"
                                
                            # 如果文本内容看起来像是提交或验证按钮
                            if any(keyword in element_text for keyword in ["验证", "点击", "确认", "提交", "进行"]):
                                button = element
                                logger.info(f"从所有可点击元素中找到可能的验证按钮: '{element_text}'")
                                break
                        except:
                            continue
                    
                    if button:
                        break
            
            # 如果仍然找不到按钮，尝试基于页面内容做特殊处理
            if not button:
                page_content = await page.content()
                page_text = await page.evaluate("document.body.innerText")
                
                # 检查是否58同城特定验证页面
                if "访问过于频繁" in page_text and "请在五分钟内完成验证" in page_text:
                    logger.info("检测到58同城'访问过于频繁'验证页面")
                    
                    # 尝试特定的选择器
                    try:
                        # 常见的58同城验证按钮
                        verify_btn = await page.wait_for_selector('.verifycode-btn', timeout=1000)
                        if verify_btn:
                            button = verify_btn
                            logger.info("找到58同城验证按钮")
                    except:
                        logger.warning("未找到58同城验证按钮")
                        
                    # 最后尝试直接定位
                    if not button:
                        try:
                            # 尝试直接通过中心位置点击
                            button_candidates = await page.query_selector_all('div, button, a')
                            for elem in button_candidates:
                                text = await elem.text_content()
                                if "点击" in text and "验证" in text and await elem.is_visible():
                                    button = elem
                                    logger.info(f"通过文本内容找到按钮: {text}")
                                    break
                        except:
                            pass
            
            # 如果找到验证按钮，点击它
            if button:
                logger.info("检测到按钮点击型验证码，尝试点击...")
                
                # 保存点击前的截图
                if self.debug:
                    await page.screenshot(path=os.path.join(self.debug_dir, f"click_before_{int(time.time())}.png"))
                
                # 随机延迟，模拟人类行为
                await asyncio.sleep(random.uniform(0.8, 1.5))
                
                # 尝试在按钮上方悬停一会儿，更像人类行为
                try:
                    await button.hover()
                    await asyncio.sleep(random.uniform(0.2, 0.5))
                except:
                    logger.warning("无法悬停在按钮上")
                
                # 尝试直接点击
                try:
                    logger.info("点击验证按钮...")
                    await button.click()
                except Exception as e:
                    logger.warning(f"常规点击按钮失败: {str(e)}")
                    
                    # 尝试使用JavaScript点击
                    try:
                        logger.info("尝试使用JavaScript点击按钮...")
                        await page.evaluate("(element) => element.click()", button)
                    except Exception as js_e:
                        logger.warning(f"JavaScript点击按钮失败: {str(js_e)}")
                        
                        # 尝试通过坐标点击
                        try:
                            logger.info("尝试通过坐标点击按钮...")
                            bounding_box = await button.bounding_box()
                            if bounding_box:
                                x = bounding_box["x"] + bounding_box["width"] / 2
                                y = bounding_box["y"] + bounding_box["height"] / 2
                                await page.mouse.click(x, y)
                        except Exception as click_e:
                            logger.warning(f"通过坐标点击按钮失败: {str(click_e)}")
                
                # 等待验证结果，增加等待时间
                await asyncio.sleep(5)
                
                # 保存点击后的截图
                if self.debug:
                    await page.screenshot(path=os.path.join(self.debug_dir, f"click_after_{int(time.time())}.png"))
                
                # 检查URL是否已经改变
                current_url = page.url
                if "callback.58.com/antibot" not in current_url:
                    logger.info("按钮点击验证成功！")
                    return True
                
                # 检查页面内容是否仍包含验证码
                page_content = await page.content()
                if "验证" not in page_content or "请点击" not in page_content:
                    logger.info("按钮点击验证可能成功！")
                    return True
                    
                logger.warning("按钮点击验证似乎失败，继续检查其他类型验证码...")
            
            # 如果不是按钮点击型验证码，继续检查其他类型
            is_slider = await page.query_selector('.drag-button, .slider, .slide-btn') is not None
            is_image_captcha = await page.query_selector('.code-img, .captcha-img, .verify-img, input[name="verify_code"]') is not None
            
            if is_slider:
                logger.info("检测到滑块验证码，尝试自动处理...")
                return await self.solve_slider(page)
            elif is_image_captcha:
                logger.info("检测到图形验证码，尝试自动处理...")
                return await self.solve_image_captcha(page)
            else:
                logger.warning("未检测到已知类型的验证码")
                if self.debug:
                    await page.screenshot(path=os.path.join(self.debug_dir, f"unknown_captcha_{int(time.time())}.png"))
                return False
        except Exception as e:
            logger.error(f"处理验证码时出错: {str(e)}")
            return False
    
    async def solve_slider(self, page: Page) -> bool:
        """
        处理滑块验证码
        
        Args:
            page: Playwright页面对象
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 保存处理前的截图
            if self.debug:
                await page.screenshot(path=os.path.join(self.debug_dir, f"slider_before_{int(time.time())}.png"))
            
            # 定位滑块和轨道
            slider_selector = '.drag-button, .slider, .slide-btn'
            track_selector = '.track, .slider-track, .slide-track'
            
            slider = await page.wait_for_selector(slider_selector, timeout=5000)
            track = await page.query_selector(track_selector)
            
            if not slider or not track:
                logger.warning("无法找到滑块或轨道元素")
                return False
            
            # 获取元素位置和大小
            slider_box = await slider.bounding_box()
            track_box = await track.bounding_box()
            
            if not slider_box or not track_box:
                logger.warning("无法获取滑块或轨道的位置信息")
                return False
            
            # 计算滑动距离
            # 这里使用了一个启发式方法：滑动到轨道的80%位置
            # 更精确的方法需要分析滑块和背景图之间的差异
            start_x = slider_box['x'] + slider_box['width'] / 2
            start_y = slider_box['y'] + slider_box['height'] / 2
            target_distance = track_box['width'] * 0.8
            
            # 生成人工轨迹
            tracks = self.generate_tracks(target_distance)
            
            # 执行滑动
            await page.mouse.move(start_x, start_y)
            await page.mouse.down()
            
            # 按照轨迹移动
            current_x = start_x
            for track in tracks:
                current_x += track
                # 添加随机的垂直方向抖动，模拟人类行为
                current_y = start_y + random.uniform(-2, 2)
                await page.mouse.move(current_x, current_y)
                # 随机停顿，模拟人类行为
                await asyncio.sleep(random.uniform(0.005, 0.02))
            
            # 最后抖动一下，模拟人类松手时的抖动
            await page.mouse.move(current_x + random.uniform(-3, 3), start_y + random.uniform(-2, 2))
            await asyncio.sleep(random.uniform(0.05, 0.1))
            await page.mouse.up()
            
            # 等待验证结果
            await asyncio.sleep(3)
            
            # 保存处理后的截图
            if self.debug:
                await page.screenshot(path=os.path.join(self.debug_dir, f"slider_after_{int(time.time())}.png"))
            
            # 检查是否验证成功
            current_url = page.url
            if "callback.58.com/antibot" not in current_url:
                logger.info("滑块验证成功！")
                return True
            else:
                logger.warning("滑块验证失败")
                return False
        except Exception as e:
            logger.error(f"处理滑块验证码时出错: {str(e)}")
            return False
    
    def generate_tracks(self, distance: float) -> list:
        """
        生成模拟人类行为的滑动轨迹
        
        Args:
            distance: 滑动总距离
            
        Returns:
            list: 轨迹列表，每个元素表示一次移动的距离
        """
        # 匀变速运动模型
        tracks = []
        
        # 当前已经移动的距离
        current = 0
        # 减速阈值，表示开始减速的位置
        mid = distance * 3 / 4
        # 计算间隔
        t = 0.2
        # 初速度
        v = 0
        # 滑动过程中的加速度，单位像素/秒^2
        a = 15
        # 滑动过程中的减速度，单位像素/秒^2
        d = -12
        
        # 循环生成轨迹
        while current < distance:
            # 根据当前位置确定加速度
            if current < mid:
                # 加速阶段
                a_temp = a
            else:
                # 减速阶段
                a_temp = d
            
            # 计算当前速度
            v_0 = v
            v = v_0 + a_temp * t
            # 计算当前位移
            move = v_0 * t + 1/2 * a_temp * t * t
            # 当前位移加入轨迹
            current += move
            tracks.append(move)
        
        # 调整轨迹，确保最终距离正确
        sum_tracks = sum(tracks)
        # 计算误差
        error = distance - sum_tracks
        # 将误差均匀分配到轨迹中
        track_len = len(tracks)
        for i in range(track_len):
            tracks[i] += error / track_len
        
        # 添加一些小的随机抖动
        for i in range(len(tracks)):
            tracks[i] += random.uniform(-0.5, 0.5)
        
        return tracks
    
    async def solve_image_captcha(self, page: Page) -> bool:
        """
        处理图形验证码
        
        Args:
            page: Playwright页面对象
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 保存验证码图片
            if self.debug:
                await page.screenshot(path=os.path.join(self.debug_dir, f"image_captcha_{int(time.time())}.png"))
            
            # 尝试查找验证码图片元素
            img_selector = '.code-img, .captcha-img, .verify-img'
            img_element = await page.query_selector(img_selector)
            
            if not img_element:
                logger.warning("未找到验证码图片元素")
                return False
            
            # 获取验证码图片的base64数据
            img_base64 = await page.evaluate(f"""() => {{
                const img = document.querySelector('{img_selector}');
                if (!img) return null;
                
                // 创建一个canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // 设置canvas尺寸与图片相同
                canvas.width = img.width;
                canvas.height = img.height;
                
                // 将图片绘制到canvas上
                ctx.drawImage(img, 0, 0, img.width, img.height);
                
                // 获取base64数据
                return canvas.toDataURL('image/png').split(',')[1];
            }}""")
            
            if not img_base64:
                logger.warning("无法获取验证码图片数据")
                return False
            
            # TODO: 这里需要接入OCR识别服务
            # 以下是一个示例，实际应用中应该使用OCR API
            captcha_text = await self.recognize_captcha(img_base64)
            
            if not captcha_text:
                logger.warning("验证码识别失败")
                return False
            
            # 查找验证码输入框
            input_selector = 'input[name="code"], input[placeholder*="验证码"], input.captcha-input'
            input_element = await page.query_selector(input_selector)
            
            if not input_element:
                logger.warning("未找到验证码输入框")
                return False
            
            # 输入验证码
            await input_element.fill(captcha_text)
            
            # 查找提交按钮
            submit_selector = 'button[type="submit"], button.submit, button.verify, input[type="submit"]'
            submit_element = await page.query_selector(submit_selector)
            
            if not submit_element:
                logger.warning("未找到提交按钮")
                return False
            
            # 点击提交
            await submit_element.click()
            
            # 等待验证结果
            await asyncio.sleep(3)
            
            # 保存处理后的截图
            if self.debug:
                await page.screenshot(path=os.path.join(self.debug_dir, f"image_captcha_after_{int(time.time())}.png"))
            
            # 检查是否验证成功
            current_url = page.url
            if "callback.58.com/antibot" not in current_url:
                logger.info("图形验证码验证成功！")
                return True
            else:
                logger.warning("图形验证码验证失败")
                return False
        except Exception as e:
            logger.error(f"处理图形验证码时出错: {str(e)}")
            return False
    
    async def recognize_captcha(self, img_base64: str) -> str:
        """
        识别验证码图片
        
        Args:
            img_base64: 验证码图片的base64编码
            
        Returns:
            str: 识别出的验证码文本
        """
        # 开发阶段，临时返回空，提示需要配置OCR服务
        logger.warning("未配置OCR服务，请在代码中配置验证码识别服务")
        return ""


# 使用示例
async def main():
    """测试验证码处理器"""
    from playwright.async_api import async_playwright
    
    try:
        # 初始化验证码处理器
        solver = CaptchaSolver(debug=True)
        
        # 启动浏览器
        async with async_playwright() as playwright:
            browser = await playwright.firefox.launch(headless=False)
            context = await browser.new_context()
            page = await context.new_page()
            
            # 访问58同城
            await page.goto('https://bj.58.com/changfang/')
            
            # 等待页面加载
            await asyncio.sleep(5)
            
            # 检查是否出现验证码
            if "callback.58.com/antibot" in page.url:
                logger.info("检测到验证码页面，开始自动处理...")
                
                # 处理验证码
                result = await solver.solve_captcha(page)
                
                if result:
                    logger.info("验证码处理成功！")
                else:
                    logger.error("验证码处理失败！")
            else:
                logger.info("未检测到验证码页面")
            
            # 等待查看结果
            await asyncio.sleep(5)
            
            # 关闭浏览器
            await browser.close()
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main()) 