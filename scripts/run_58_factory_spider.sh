#!/bin/bash

# 58同城厂房信息爬虫运行脚本
# 用法: ./run_58_factory_spider.sh

# 创建logs目录
# 兼容MacOS和Linux的脚本路径获取方式
if [[ "$OSTYPE" == "darwin"* ]]; then
  # MacOS系统
  SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
else
  # Linux系统
  SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
fi

LOG_DIR="${SCRIPT_DIR}/logs"
mkdir -p "$LOG_DIR"

# 使用固定的日志文件名，不再使用时间戳
LOG_FILE="${LOG_DIR}/spider_58_run.log"

# 添加分隔线，标记新的日志会话开始
echo -e "\n\n===================================================" >> "$LOG_FILE"
echo -e "====== 新日志会话开始于: $(date) ======" >> "$LOG_FILE"
echo -e "===================================================\n" >> "$LOG_FILE"

# 日志函数
log() {
  local message="[$(date +"%Y-%m-%d %H:%M:%S")] $1"
  echo -e "$message" | tee -a "$LOG_FILE"
}

# 显示简化的帮助信息
show_help() {
  echo "58同城厂房信息爬虫运行脚本"
  echo "用法: $0"
  echo "默认爬取所有支持的城市（约90个城市）"
  exit 0
}

# 检查帮助参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
  show_help
fi

log "脚本开始执行，日志保存在: $LOG_FILE"

# 默认配置 - 爬取所有城市
# CITIES="苏州 重庆 武汉 杭州 广州 上海 南京"  # 注释掉特定城市列表
MAX_PAGES=1
# DEBUG="--debug"  # 默认启用调试模式

# 构建命令 - 不指定cities参数，让爬虫使用所有支持的城市
CMD="python ${SCRIPT_DIR}/spider_58_factory_playwright.py --max-pages $MAX_PAGES $DEBUG"

# 显示执行信息
log "开始运行58同城厂房信息爬虫..."
log "当前时间: $(date)"
log "爬取城市: 所有支持的城市（约90个）"
log "每个城市最大页数: $MAX_PAGES"
log "运行命令: $CMD"
log "开始执行..."

# 执行命令
eval $CMD 2>&1 | tee -a "$LOG_FILE"

# 获取退出状态
EXIT_CODE=${PIPESTATUS[0]}
if [ $EXIT_CODE -eq 0 ]; then
  log "爬虫任务成功完成！"
else
  log "爬虫任务失败，退出代码: $EXIT_CODE"
fi
log "结束时间: $(date)"

exit $EXIT_CODE 