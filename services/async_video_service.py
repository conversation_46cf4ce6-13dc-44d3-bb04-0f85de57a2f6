#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
异步视频生成服务
处理视频生成的异步任务，避免阻塞主线程
"""

import threading
import time
import logging
import uuid
import asyncio
from datetime import datetime
from typing import Dict, Optional
import traceback

from services.remote_video_service import generate_campus_video_remote, generate_video_with_assigned_images
from services.task_status_manager import update_task_status

# 配置日志
logger = logging.getLogger(__name__)

# 任务状态
class AsyncVideoTaskStatus:
    PENDING = "pending"
    PROCESSING = "processing" 
    COMPLETED = "completed"
    FAILED = "failed"

# 全局任务存储
async_video_tasks = {}
task_lock = threading.Lock()

def submit_video_generation_task(
    campus_name: str,
    fps: int = 30,
    duration_per_image: float = 3.0,
    transition_type: str = "push_right",
    transition_duration: float = 1.0
) -> str:
    task_id = str(uuid.uuid4())
    

    task_info = {
        "task_id": task_id,
        "campus_name": campus_name,
        "status": AsyncVideoTaskStatus.PENDING,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "result": None,
        "error_message": None,
        "params": {
            "fps": fps,
            "duration_per_image": duration_per_image,
            "transition_type": transition_type,
            "transition_duration": transition_duration
        }
    }
    

    with task_lock:
        async_video_tasks[task_id] = task_info
    

    update_task_status(f"video_{campus_name}", 0, 0, None)
    

    thread = threading.Thread(
        target=_process_video_task,
        args=(task_id,),
        daemon=True,
        name=f"VideoTask-{task_id[:8]}"
    )
    thread.start()
    
    logger.info(f"已提交异步视频生成任务: {task_id} (园区: {campus_name})")
    return task_id

def submit_video_generation_with_images(
    campus_name: str,
    image_urls: list,
    factory_id: Optional[str] = None,
    fps: int = 30,
    duration_per_image: float = 3.0,
    transition_type: str = "push_right",
    transition_duration: float = 1.0
) -> str:
    task_id = str(uuid.uuid4())


    task_info = {
        "task_id": task_id,
        "campus_name": campus_name,
        "status": AsyncVideoTaskStatus.PENDING,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "result": None,
        "error_message": None,
        "task_type": "with_images",
        "factory_id": factory_id,
        "image_urls": image_urls,
        "params": {
            "fps": fps,
            "duration_per_image": duration_per_image,
            "transition_type": transition_type,
            "transition_duration": transition_duration
        }
    }


    with task_lock:
        async_video_tasks[task_id] = task_info


    update_task_status(f"video_{campus_name}", 0, 0, None)


    thread = threading.Thread(
        target=_process_video_task_with_images,
        args=(task_id,),
        daemon=True,
        name=f"VideoTaskImages-{task_id[:8]}"
    )
    thread.start()

    logger.info(f"已提交异步视频生成任务(指定图片): {task_id} (园区: {campus_name}, 图片数量: {len(image_urls)})")
    return task_id

def _process_video_task(task_id: str):
    thread_name = threading.current_thread().name
    logger.info(f"[{thread_name}] 开始处理视频生成任务: {task_id}")
    
    try:

        with task_lock:
            if task_id not in async_video_tasks:
                logger.error(f"[{thread_name}] 任务 {task_id} 不存在")
                return
            
            task_info = async_video_tasks[task_id].copy()
        
        campus_name = task_info["campus_name"]
        params = task_info["params"]
        

        _update_task_status(task_id, AsyncVideoTaskStatus.PROCESSING)
        update_task_status(f"video_{campus_name}", 1, 0, None)
        
        logger.info(f"[{thread_name}] 开始为园区 '{campus_name}' 生成视频")
        

        result = generate_campus_video_remote(
            campus_name=campus_name,
            fps=params["fps"],
            duration_per_image=params["duration_per_image"],
            transition_type=params["transition_type"],
            transition_duration=params["transition_duration"],
            thread_id=thread_name
        )
        

        if result.get("success"):
            _update_task_status(task_id, AsyncVideoTaskStatus.COMPLETED, result)
            update_task_status(f"video_{campus_name}", 2, 100, None)
            logger.info(f"[{thread_name}] 园区 '{campus_name}' 视频生成成功")
        else:
            error_msg = result.get("message", "视频生成失败")
            _update_task_status(task_id, AsyncVideoTaskStatus.FAILED, result, error_msg)
            update_task_status(f"video_{campus_name}", 3, 0, error_msg)
            logger.error(f"[{thread_name}] 园区 '{campus_name}' 视频生成失败: {error_msg}")
    
    except Exception as e:
        error_msg = f"处理视频生成任务时发生异常: {str(e)}"
        logger.error(f"[{thread_name}] {error_msg}")
        logger.error(f"[{thread_name}] 详细错误: {traceback.format_exc()}")
        

        _update_task_status(task_id, AsyncVideoTaskStatus.FAILED, None, error_msg)
        

        try:
            with task_lock:
                if task_id in async_video_tasks:
                    campus_name = async_video_tasks[task_id]["campus_name"]
                    update_task_status(f"video_{campus_name}", 3, 0, error_msg)
        except:
            pass

def _process_video_task_with_images(task_id: str):
    thread_name = threading.current_thread().name
    logger.info(f"[{thread_name}] 开始处理指定图片的视频生成任务: {task_id}")

    try:

        with task_lock:
            if task_id not in async_video_tasks:
                logger.error(f"[{thread_name}] 任务 {task_id} 不存在")
                return

            task_info = async_video_tasks[task_id].copy()

        campus_name = task_info["campus_name"]
        image_urls = task_info["image_urls"]
        params = task_info["params"]


        _update_task_status(task_id, AsyncVideoTaskStatus.PROCESSING)
        update_task_status(f"video_{campus_name}", 1, 0, None)

        logger.info(f"[{thread_name}] 开始为园区 '{campus_name}' 使用 {len(image_urls)} 张指定图片生成视频")


        result = generate_video_with_assigned_images(
            campus_name=campus_name,
            image_urls=image_urls,
            fps=params["fps"],
            duration_per_image=params["duration_per_image"],
            transition_type=params["transition_type"],
            transition_duration=params["transition_duration"],
            thread_id=thread_name
        )


        if result.get("success"):
            _update_task_status(task_id, AsyncVideoTaskStatus.COMPLETED, result)
            update_task_status(f"video_{campus_name}", 2, 100, None)
            logger.info(f"[{thread_name}] 园区 '{campus_name}' 指定图片视频生成成功")


        else:
            error_msg = result.get("message", "指定图片视频生成失败")
            _update_task_status(task_id, AsyncVideoTaskStatus.FAILED, result, error_msg)
            update_task_status(f"video_{campus_name}", 3, 0, error_msg)
            logger.error(f"[{thread_name}] 园区 '{campus_name}' 指定图片视频生成失败: {error_msg}")

    except Exception as e:
        error_msg = f"处理指定图片视频生成任务时发生异常: {str(e)}"
        logger.error(f"[{thread_name}] {error_msg}")
        logger.error(f"[{thread_name}] 详细错误: {traceback.format_exc()}")


        _update_task_status(task_id, AsyncVideoTaskStatus.FAILED, None, error_msg)


        try:
            with task_lock:
                if task_id in async_video_tasks:
                    campus_name = async_video_tasks[task_id]["campus_name"]
                    update_task_status(f"video_{campus_name}", 3, 0, error_msg)
        except:
            pass

def _update_task_status(task_id: str, status: str, result: Optional[Dict] = None, error_message: Optional[str] = None):
    with task_lock:
        if task_id in async_video_tasks:
            async_video_tasks[task_id]["status"] = status
            async_video_tasks[task_id]["updated_at"] = datetime.now()
            if result is not None:
                async_video_tasks[task_id]["result"] = result
            if error_message is not None:
                async_video_tasks[task_id]["error_message"] = error_message

def get_task_status(task_id: str) -> Optional[Dict]:
    with task_lock:
        if task_id in async_video_tasks:
            return async_video_tasks[task_id].copy()
        return None



async def async_wait_for_task_completion(task_id: str, timeout_seconds: int = 300, poll_interval: int = 5) -> Optional[Dict]:
    start_time = time.time()

    logger.info(f"开始异步等待视频生成任务完成: {task_id} (超时: {timeout_seconds}秒)")

    while time.time() - start_time < timeout_seconds:
        task_status = get_task_status(task_id)

        if not task_status:
            logger.error(f"任务 {task_id} 不存在")
            return None

        status = task_status["status"]
        campus_name = task_status["campus_name"]

        if status == AsyncVideoTaskStatus.COMPLETED:
            logger.info(f"视频生成任务完成: {task_id} (园区: {campus_name})")
            return task_status["result"]
        elif status == AsyncVideoTaskStatus.FAILED:
            error_msg = task_status.get("error_message", "未知错误")
            logger.error(f"视频生成任务失败: {task_id} (园区: {campus_name}) - {error_msg}")
            return None

        # 任务仍在进行中，异步等待一段时间后再检查
        elapsed = int(time.time() - start_time)
        logger.info(f"视频生成任务进行中: {task_id} (园区: {campus_name}) - 已等待 {elapsed} 秒")
        await asyncio.sleep(poll_interval)  # 使用异步sleep，不阻塞主线程

    # 超时
    logger.warning(f"异步等待视频生成任务超时: {task_id} (超时时间: {timeout_seconds}秒)")
    return None

def is_campus_video_generating(campus_name: str) -> bool:
    with task_lock:
        for task_info in async_video_tasks.values():
            if (task_info["campus_name"] == campus_name and 
                task_info["status"] in [AsyncVideoTaskStatus.PENDING, AsyncVideoTaskStatus.PROCESSING]):
                return True
        return False

def cleanup_completed_tasks(max_age_hours: int = 24):
    current_time = datetime.now()
    expired_task_ids = []
    
    with task_lock:
        for task_id, task_info in async_video_tasks.items():

            age_hours = (current_time - task_info["updated_at"]).total_seconds() / 3600
            
            # 清理超过指定时间的已完成或失败任务
            if (age_hours > max_age_hours and 
                task_info["status"] in [AsyncVideoTaskStatus.COMPLETED, AsyncVideoTaskStatus.FAILED]):
                expired_task_ids.append(task_id)
        
        # 删除过期任务
        for task_id in expired_task_ids:
            del async_video_tasks[task_id]
    
    if expired_task_ids:
        logger.info(f"已清理 {len(expired_task_ids)} 个过期的视频生成任务")

def get_all_tasks_status() -> Dict:
    with task_lock:
        total_tasks = len(async_video_tasks)
        pending_tasks = sum(1 for task in async_video_tasks.values() 
                          if task["status"] == AsyncVideoTaskStatus.PENDING)
        processing_tasks = sum(1 for task in async_video_tasks.values() 
                             if task["status"] == AsyncVideoTaskStatus.PROCESSING)
        completed_tasks = sum(1 for task in async_video_tasks.values() 
                            if task["status"] == AsyncVideoTaskStatus.COMPLETED)
        failed_tasks = sum(1 for task in async_video_tasks.values() 
                         if task["status"] == AsyncVideoTaskStatus.FAILED)
        
        return {
            "total": total_tasks,
            "pending": pending_tasks,
            "processing": processing_tasks,
            "completed": completed_tasks,
            "failed": failed_tasks,
            "tasks": list(async_video_tasks.values())
        }

# 定期清理任务的后台线程
def _start_cleanup_thread():
    def cleanup_loop():
        while True:
            try:
                time.sleep(3600)  # 每小时清理一次
                cleanup_completed_tasks()
            except Exception as e:
                logger.error(f"清理任务时发生异常: {str(e)}")
    
    cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True, name="VideoTaskCleanup")
    cleanup_thread.start()
    logger.info("视频任务清理线程已启动")

# 启动清理线程
_start_cleanup_thread()
