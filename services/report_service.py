#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
报告服务模块
提供生成PDF报告的功能
"""

import logging
import os
import json
import tempfile
from datetime import datetime
from pathlib import Path
from io import BytesIO

# 导入reportlab相关库
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm, mm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, PageBreak
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

from services.base_service import BaseService
from services.listing_analysis_service import ListingAnalysisService

class ReportService(BaseService):
    """报告服务类，提供生成PDF报告的功能"""

    def __init__(self):
        """初始化报告服务"""
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.listing_service = ListingAnalysisService()

        # 注册中文字体
        self._register_fonts()

        # 创建样式
        self.styles = getSampleStyleSheet()
        self._create_custom_styles()

    def _register_fonts(self):
        """注册中文字体"""
        try:
            # 使用默认字体，不自动创建fonts目录
            self.logger.info("使用默认字体，不自动创建fonts目录")
            
            # 检查是否存在字体目录和字体文件（如果用户手动创建了）
            fonts_dir = Path(__file__).parent.parent / "fonts"
            if fonts_dir.exists():
                # 注册思源黑体
                font_path = fonts_dir / "SourceHanSansCN-Regular.ttf"
                if font_path.exists():
                    pdfmetrics.registerFont(TTFont('SourceHanSans', str(font_path)))
                    self.logger.info("已注册思源黑体字体")
                
                # 注册思源黑体粗体
                bold_font_path = fonts_dir / "SourceHanSansCN-Bold.ttf"
                if bold_font_path.exists():
                    pdfmetrics.registerFont(TTFont('SourceHanSans-Bold', str(bold_font_path)))
                    self.logger.info("已注册思源黑体粗体字体")

        except Exception as e:
            self.logger.error(f"注册字体失败: {str(e)}")

    def _create_custom_styles(self):
        """创建自定义样式"""
        # 修改已有的标题样式
        self.styles['Title'].fontName = 'SourceHanSans-Bold' if 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
        self.styles['Title'].fontSize = 18
        self.styles['Title'].leading = 22
        self.styles['Title'].alignment = 1  # 居中
        self.styles['Title'].spaceAfter = 10

        # 创建自定义样式
        # 副标题样式
        self.styles.add(ParagraphStyle(
            name='ReportSubtitle',
            fontName='SourceHanSans-Bold' if 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold',
            fontSize=14,
            leading=18,
            alignment=0,  # 左对齐
            spaceAfter=6
        ))

        # 正文样式 - 使用已有的BodyText样式
        self.styles['BodyText'].fontName = 'SourceHanSans' if 'SourceHanSans' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'
        self.styles['BodyText'].fontSize = 10
        self.styles['BodyText'].leading = 14
        self.styles['BodyText'].alignment = 0  # 左对齐

        # 表格标题样式
        self.styles.add(ParagraphStyle(
            name='ReportTableHeader',
            fontName='SourceHanSans-Bold' if 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold',
            fontSize=10,
            leading=14,
            alignment=1  # 居中
        ))

        # 表格内容样式
        self.styles.add(ParagraphStyle(
            name='ReportTableCell',
            fontName='SourceHanSans' if 'SourceHanSans' in pdfmetrics.getRegisteredFontNames() else 'Helvetica',
            fontSize=9,
            leading=12,
            alignment=0  # 左对齐
        ))

    def generate_competitor_analysis_report(self, report_data):
        """
        生成竞争对手分析报告

        参数:
        - report_data: 报告数据，包含标题、日期和分析数据

        返回:
        - bytes: PDF文件的二进制数据
        """
        try:
            # 创建一个BytesIO对象作为PDF文件的容器
            buffer = BytesIO()

            # 注册默认的Helvetica字体别名，以支持中文
            # 如果没有中文字体，我们将使用基本的ASCII字符

            # 创建PDF文档
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )

            # 创建内容列表
            content = []

            # 添加标题 - 始终使用英文标题避免编码问题
            title = 'Competitor Analysis Report'
            content.append(Paragraph(title, self.styles['Title']))
            content.append(Spacer(1, 10))

            # 添加生成日期
            date_str = report_data.get('date', datetime.now().strftime('%Y-%m-%d'))
            content.append(Paragraph(f"Generation Date: {date_str}", self.styles['BodyText']))
            content.append(Spacer(1, 20))

            # 获取分析数据
            analysis_data = report_data.get('data', {})

            # 添加概览部分
            self._add_overview_section(content, analysis_data.get('overview', {}))

            # 添加竞争对手分析部分
            self._add_competitors_section(content, analysis_data.get('competitors', []))

            # 添加区域分析部分
            self._add_districts_section(content, analysis_data.get('districts', []))

            # 添加内容质量分析部分
            self._add_content_quality_section(content, analysis_data.get('contentQuality', {}))

            # 构建PDF文档
            doc.build(content)

            # 获取PDF数据
            pdf_data = buffer.getvalue()
            buffer.close()

            return pdf_data

        except Exception as e:
            self.logger.error(f"生成竞争对手分析报告失败: {str(e)}")
            raise

    def _add_overview_section(self, content, overview_data):
        """添加概览部分"""
        # 检查是否有中文字体
        has_chinese_font = 'SourceHanSans' in pdfmetrics.getRegisteredFontNames() or 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames()

        # 根据是否有中文字体选择标题
        section_title = "1. 市场概览" if has_chinese_font else "1. Market Overview"
        content.append(Paragraph(section_title, self.styles['ReportSubtitle']))
        content.append(Spacer(1, 10))

        # 创建概览表格数据
        if has_chinese_font:
            data = [
                ["指标", "数值"],
                ["总帖子数", str(overview_data.get('totalPosts', 0))],
                ["竞争对手数量", str(overview_data.get('totalCompetitors', 0))],
                ["新增帖子数", str(overview_data.get('newPosts', 0))],
                ["我方帖子占比", f"{overview_data.get('ourPostsPercentage', 0)}%"],
                ["单账号平均帖子数", str(overview_data.get('avgPostsPerAccount', 0))],
                ["竞争对手平均帖子数", str(overview_data.get('competitorAvgPostsPerAccount', 0))]
            ]
        else:
            data = [
                ["Metric", "Value"],
                ["Total Posts", str(overview_data.get('totalPosts', 0))],
                ["Total Competitors", str(overview_data.get('totalCompetitors', 0))],
                ["New Posts", str(overview_data.get('newPosts', 0))],
                ["Our Posts Percentage", f"{overview_data.get('ourPostsPercentage', 0)}%"],
                ["Avg Posts Per Account", str(overview_data.get('avgPostsPerAccount', 0))],
                ["Competitor Avg Posts", str(overview_data.get('competitorAvgPostsPerAccount', 0))]
            ]

        # 创建表格
        table = Table(data, colWidths=[200, 200])

        # 设置表格样式
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (1, 0), colors.black),
            ('ALIGN', (0, 0), (1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (1, 0), 'SourceHanSans-Bold' if 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (1, 0), 12),
            ('BACKGROUND', (0, 1), (1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (1, 1), (1, -1), 'CENTER')
        ]))

        content.append(table)
        content.append(Spacer(1, 20))

        # 添加最后更新时间
        last_update_time = overview_data.get('lastUpdateTime')
        if last_update_time:
            # 检查是否有中文字体
            has_chinese_font = 'SourceHanSans' in pdfmetrics.getRegisteredFontNames() or 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames()
            update_text = f"数据最后更新时间: {last_update_time}" if has_chinese_font else f"Last Data Update: {last_update_time}"
            content.append(Paragraph(update_text, self.styles['BodyText']))
            content.append(Spacer(1, 20))

    def _add_competitors_section(self, content, competitors_data):
        """添加竞争对手分析部分"""
        # 检查是否有中文字体
        has_chinese_font = 'SourceHanSans' in pdfmetrics.getRegisteredFontNames() or 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames()

        # 根据是否有中文字体选择标题
        section_title = "2. 竞争对手分析" if has_chinese_font else "2. Competitor Analysis"
        content.append(Paragraph(section_title, self.styles['ReportSubtitle']))
        content.append(Spacer(1, 10))

        if not competitors_data:
            no_data_text = "暂无竞争对手数据" if has_chinese_font else "No competitor data available"
            content.append(Paragraph(no_data_text, self.styles['BodyText']))
            content.append(Spacer(1, 20))
            return

        # 创建竞争对手表格数据
        if has_chinese_font:
            table_data = [["排名", "竞争对手", "帖子数量", "市场份额", "周增长率", "最近更新"]]
        else:
            table_data = [["Rank", "Competitor", "Posts", "Market Share", "Weekly Growth", "Last Update"]]

        # 最多显示前10个竞争对手
        for i, competitor in enumerate(competitors_data[:10]):
            table_data.append([
                str(i + 1),
                competitor.get('name', '未知'),
                str(competitor.get('postCount', 0)),
                f"{competitor.get('marketShare', 0)}%",
                f"{competitor.get('weeklyGrowth', 0)}%",
                competitor.get('lastUpdate', '未知')
            ])

        # 创建表格
        table = Table(table_data, colWidths=[30, 120, 70, 70, 70, 80])

        # 设置表格样式
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'SourceHanSans-Bold' if 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (0, 1), (0, -1), 'CENTER'),
            ('ALIGN', (2, 1), (4, -1), 'CENTER')
        ]))

        content.append(table)
        content.append(Spacer(1, 20))

    def _add_districts_section(self, content, districts_data):
        """添加区域分析部分"""
        # 检查是否有中文字体
        has_chinese_font = 'SourceHanSans' in pdfmetrics.getRegisteredFontNames() or 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames()

        # 根据是否有中文字体选择标题
        section_title = "3. 区域分布分析" if has_chinese_font else "3. District Distribution Analysis"
        content.append(Paragraph(section_title, self.styles['ReportSubtitle']))
        content.append(Spacer(1, 10))

        if not districts_data:
            no_data_text = "暂无区域分布数据" if has_chinese_font else "No district distribution data available"
            content.append(Paragraph(no_data_text, self.styles['BodyText']))
            content.append(Spacer(1, 20))
            return

        # 创建区域表格数据
        if has_chinese_font:
            table_data = [["区域", "帖子数量", "占比", "我方帖子数", "我方占比"]]
        else:
            table_data = [["District", "Posts", "Percentage", "Our Posts", "Our Percentage"]]

        # 最多显示前10个区域
        for i, district in enumerate(districts_data[:10]):
            table_data.append([
                district.get('name', '未知'),
                str(district.get('postCount', 0)),
                f"{district.get('percentage', 0)}%",
                str(district.get('ourPosts', 0)),
                f"{district.get('ourPercentage', 0)}%"
            ])

        # 创建表格
        table = Table(table_data, colWidths=[100, 80, 80, 80, 80])

        # 设置表格样式
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'SourceHanSans-Bold' if 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (1, 1), (-1, -1), 'CENTER')
        ]))

        content.append(table)
        content.append(Spacer(1, 20))

    def _add_content_quality_section(self, content, quality_data):
        """添加内容质量分析部分"""
        # 检查是否有中文字体
        has_chinese_font = 'SourceHanSans' in pdfmetrics.getRegisteredFontNames() or 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames()

        # 根据是否有中文字体选择标题
        section_title = "4. 内容质量分析" if has_chinese_font else "4. Content Quality Analysis"
        content.append(Paragraph(section_title, self.styles['ReportSubtitle']))
        content.append(Spacer(1, 10))

        if not quality_data:
            no_data_text = "暂无内容质量数据" if has_chinese_font else "No content quality data available"
            content.append(Paragraph(no_data_text, self.styles['BodyText']))
            content.append(Spacer(1, 20))
            return

        our_data = quality_data.get('our', {})
        competitor_data = quality_data.get('competitor', {})

        # 检查是否有中文字体
        has_chinese_font = 'SourceHanSans' in pdfmetrics.getRegisteredFontNames() or 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames()

        # 创建内容质量表格数据
        if has_chinese_font:
            table_data = [
                ["指标", "我方", "竞争对手"],
                ["平均受欢迎度", f"{our_data.get('avgPopularity', 0)}", f"{competitor_data.get('avgPopularity', 0)}"],
                ["平均图片数", f"{our_data.get('avgImages', 0)}", f"{competitor_data.get('avgImages', 0)}"],
                ["更新频率(天/次)", f"{our_data.get('updateFrequency', 0)}", f"{competitor_data.get('updateFrequency', 0)}"],
                ["平均视频数", f"{our_data.get('avgVideos', 0)}", f"{competitor_data.get('avgVideos', 0)}"]
            ]
        else:
            table_data = [
                ["Metric", "Our", "Competitors"],
                ["Avg Popularity", f"{our_data.get('avgPopularity', 0)}", f"{competitor_data.get('avgPopularity', 0)}"],
                ["Avg Images", f"{our_data.get('avgImages', 0)}", f"{competitor_data.get('avgImages', 0)}"],
                ["Update Frequency(days)", f"{our_data.get('updateFrequency', 0)}", f"{competitor_data.get('updateFrequency', 0)}"],
                ["Avg Videos", f"{our_data.get('avgVideos', 0)}", f"{competitor_data.get('avgVideos', 0)}"]
            ]

        # 创建表格
        table = Table(table_data, colWidths=[150, 100, 100])

        # 设置表格样式
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'SourceHanSans-Bold' if 'SourceHanSans-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (1, 1), (-1, -1), 'CENTER')
        ]))

        content.append(table)
        content.append(Spacer(1, 20))
