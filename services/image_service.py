#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图片处理服务
提供图片参数调整功能，如亮度、对比度、锐度等
"""

import os
import logging
import tempfile
from typing import Dict, Tuple, Union, Optional, BinaryIO
from pathlib import Path
from PIL import Image, ImageEnhance, ImageFilter

from services.base_service import BaseService
from services.oss_service import OssService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# 默认参数配置
# 在这里配置默认参数，这样只需要直接调用即可
DEFAULT_PARAMS = {
    # 基本图像调整参数
    'brightness': 1.2,  # 亮度调整因子 (0.0-2.0, 1.0为原始亮度)
    'contrast': 1.3,    # 对比度调整因子 (0.0-2.0, 1.0为原始对比度)
    'sharpness': 1.5,   # 锐度调整因子 (0.0-2.0, 1.0为原始锐度)
    'saturation': 1.1,  # 饱和度调整因子 (0.0-2.0, 1.0为原始饱和度)
    'color': None,      # 色彩平衡调整因子 (0.0-2.0, 1.0为原始色彩)

    # 自动增强参数
    'auto_enhance': False,  # 是否自动增强图片
    'equalize': False,      # 是否直方图均衡化处理

    # 模糊和滤镜参数
    'gamma': None,  # 伽马校正值 (0.1-10.0, 1.0为原始值)

    # 尺寸调整参数
    'crop': None,           # 自定义裁剪区域，格式为[left, top, right, bottom]
    'crop_mode': 'center',  # 裁剪模式: center(居中裁剪), golden(黄金比例), custom(自定义)
    'crop_ratio': 1.5,      # 裁剪比例，宽高比，默认为1.5(宽度是高度的1.5倍)
    'resize_after_crop': False,  # 裁剪后是否调整回原始尺寸

    # 其他参数
    'quality': 95,  # 保存质量 (1-100, 只对JPEG格式有效)
    'extensions': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],  # 图片扩展名列表

    # 预设参数
    'preset': 'standard',  # 预设参数：standard(标准), vivid(鲜明), clear(清晰), soft(柔和)
}

class ImageService(BaseService):
    """图片处理服务类，提供图片参数调整功能"""

    def __init__(self):
        """初始化图片处理服务"""
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.oss_service = OssService()

    def adjust_image(self,
                    image_path: str,
                    brightness: float = None,
                    contrast: float = None,
                    sharpness: float = None,
                    saturation: float = None,
                    color: float = None,
                    auto_enhance: bool = None,
                    equalize: bool = None,
                    gamma: float = None,
                    crop: list = None,
                    crop_mode: str = None,
                    crop_ratio: float = None,
                    resize_after_crop: bool = None,
                    quality: int = None,
                    preset: str = None,
                    storage_type: str = "house",
                    storage_subtype: str = "processed") -> Tuple[bool, Union[Dict, str]]:
        """
        调整图片参数

        Args:
            image_path: 图片路径（本地路径或URL）

            # 基本图像调整
            brightness: 亮度调整因子 (0.0-2.0, 1.0为原始亮度)
            contrast: 对比度调整因子 (0.0-2.0, 1.0为原始对比度)
            sharpness: 锐度调整因子 (0.0-2.0, 1.0为原始锐度)
            saturation: 饱和度调整因子 (0.0-2.0, 1.0为原始饱和度)
            color: 色彩平衡调整因子 (0.0-2.0, 1.0为原始色彩)

            # 自动增强
            auto_enhance: 是否自动增强图片
            equalize: 是否直方图均衡化处理

            # 模糊和滤镜
            gamma: 伽马校正值 (0.1-10.0, 1.0为原始值)

            # 尺寸调整
            crop: 自定义裁剪区域，格式为[left, top, right, bottom]
            crop_mode: 裁剪模式，可选值为'center'(居中裁剪), 'golden'(黄金比例), 'custom'(自定义)
            crop_ratio: 裁剪比例，宽高比，默认为1.5(宽度是高度的1.5倍)
            resize_after_crop: 裁剪后是否调整回原始尺寸

            # 其他参数
            quality: 保存质量 (1-100, 只对JPEG格式有效)
            preset: 预设参数，可选值为'standard'(标准), 'vivid'(鲜明), 'clear'(清晰), 'soft'(柔和)
            storage_type: 存储类型
            storage_subtype: 存储子类型

        Returns:
            处理结果和信息
        """
        temp_file = None
        try:
            # 判断是否为URL
            is_url = image_path.startswith(('http://', 'https://'))

            if is_url:
                # 从URL下载图片
                import requests
                response = requests.get(image_path, stream=True)
                if response.status_code != 200:
                    return False, {"error": f"下载图片失败，状态码: {response.status_code}"}

                # 创建临时文件
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
                temp_file.write(response.content)
                temp_file.close()
                image_path = temp_file.name

            # 打开图片
            img = Image.open(image_path)

            # 处理预设参数
            if preset is None:
                preset = DEFAULT_PARAMS['preset']

            # 根据预设参数调整值
            if preset == 'vivid':
                # 鲜明模式 - 增强饱和度和对比度
                preset_brightness = 1.1
                preset_contrast = 1.3
                preset_sharpness = 1.4
                preset_saturation = 1.3
            elif preset == 'clear':
                # 清晰模式 - 增强锐度和对比度
                preset_brightness = 1.05
                preset_contrast = 1.2
                preset_sharpness = 1.7
                preset_saturation = 1.0
            elif preset == 'soft':
                # 柔和模式 - 降低锐度和对比度
                preset_brightness = 1.1
                preset_contrast = 0.95
                preset_sharpness = 0.8
                preset_saturation = 1.05
            else:  # standard
                # 标准模式 - 使用默认值
                preset_brightness = DEFAULT_PARAMS['brightness']
                preset_contrast = DEFAULT_PARAMS['contrast']
                preset_sharpness = DEFAULT_PARAMS['sharpness']
                preset_saturation = DEFAULT_PARAMS['saturation']

            # 使用默认参数填充空值
            if brightness is None:
                brightness = preset_brightness
            if contrast is None:
                contrast = preset_contrast
            if sharpness is None:
                sharpness = preset_sharpness
            if saturation is None:
                saturation = preset_saturation
            if auto_enhance is None:
                auto_enhance = DEFAULT_PARAMS['auto_enhance']
            if equalize is None:
                equalize = DEFAULT_PARAMS['equalize']
            if gamma is None:
                gamma = DEFAULT_PARAMS['gamma']
            if crop is None:
                crop = DEFAULT_PARAMS['crop']
            if crop_mode is None:
                crop_mode = DEFAULT_PARAMS['crop_mode']
            if crop_ratio is None:
                crop_ratio = DEFAULT_PARAMS['crop_ratio']
            if resize_after_crop is None:
                resize_after_crop = DEFAULT_PARAMS['resize_after_crop']
            if quality is None:
                quality = DEFAULT_PARAMS['quality']

            # 裁剪图片
            original_size = img.size  # 保存原始尺寸
            crop_applied = False  # 标记是否应用了裁剪

            # 自定义裁剪
            if crop is not None:
                self.logger.info(f"执行自定义裁剪: {crop}")
                try:
                    # 确保裁剪区域是有效的
                    left, top, right, bottom = crop
                    width, height = img.size

                    # 验证裁剪区域
                    if left < 0 or top < 0 or right > width or bottom > height or left >= right or top >= bottom:
                        self.logger.warning(f"裁剪区域无效: {crop}, 图片尺寸: {img.size}")
                    else:
                        # 执行裁剪
                        img = img.crop((left, top, right, bottom))
                        crop_applied = True
                        self.logger.info(f"裁剪成功，新尺寸: {img.size}")
                except Exception as e:
                    self.logger.error(f"裁剪失败: {str(e)}")
            # 自动裁剪
            elif crop_mode in ['center', 'golden']:
                width, height = img.size

                if crop_mode == 'center':
                    # 居中裁剪
                    if width > height * crop_ratio:  # 宽图，裁剪宽度
                        new_width = int(height * crop_ratio)
                        left = (width - new_width) // 2
                        right = left + new_width
                        img = img.crop((left, 0, right, height))
                        crop_applied = True
                    elif height > width / crop_ratio:  # 长图，裁剪高度
                        new_height = int(width / crop_ratio)
                        top = (height - new_height) // 2
                        bottom = top + new_height
                        img = img.crop((0, top, width, bottom))
                        crop_applied = True
                elif crop_mode == 'golden':
                    # 黄金比例裁剪 (1:1.618)
                    golden_ratio = 1.618

                    if width / height > golden_ratio:  # 宽图，裁剪宽度
                        new_width = int(height * golden_ratio)
                        left = (width - new_width) // 2
                        right = left + new_width
                        img = img.crop((left, 0, right, height))
                        crop_applied = True
                    elif height / width > golden_ratio:  # 长图，裁剪高度
                        new_height = int(width * golden_ratio)
                        top = (height - new_height) // 2
                        bottom = top + new_height
                        img = img.crop((0, top, width, bottom))
                        crop_applied = True

            # 如果应用了裁剪并需要调整回原始尺寸
            if crop_applied and resize_after_crop:
                img = img.resize(original_size, Image.LANCZOS)

            # 如果选择了自动增强
            if auto_enhance:
                from PIL import ImageOps
                img = ImageOps.autocontrast(img)

                # 适度锐化
                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(1.3)
            else:
                # 应用各种调整
                enhancer = ImageEnhance.Brightness(img)
                img = enhancer.enhance(brightness)

                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(contrast)

                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(sharpness)

                enhancer = ImageEnhance.Color(img)
                img = enhancer.enhance(saturation)

                if color is not None:
                    enhancer = ImageEnhance.Color(img)
                    img = enhancer.enhance(color)

            # 直方图均衡化
            if equalize:
                from PIL import ImageOps
                if img.mode == 'RGBA':
                    r, g, b, a = img.split()
                    r, g, b = map(ImageOps.equalize, [r, g, b])
                    img = Image.merge('RGBA', (r, g, b, a))
                else:
                    img = ImageOps.equalize(img)

            # 伽马校正
            if gamma is not None:
                if img.mode == 'RGBA':
                    r, g, b, a = img.split()
                    r = r.point(lambda i: ((i / 255) ** (1/gamma)) * 255)
                    g = g.point(lambda i: ((i / 255) ** (1/gamma)) * 255)
                    b = b.point(lambda i: ((i / 255) ** (1/gamma)) * 255)
                    img = Image.merge('RGBA', (r, g, b, a))
                else:
                    img = img.point(lambda i: ((i / 255) ** (1/gamma)) * 255)

            # 保存处理后的图片到临时文件
            output_temp = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
            output_temp.close()
            img.save(output_temp.name, quality=quality)

            # 上传到OSS
            success, result = self.oss_service.upload_file(
                output_temp.name,
                'image',
                storage_type,
                storage_subtype
            )

            # 清理临时文件
            os.unlink(output_temp.name)
            if temp_file and os.path.exists(temp_file.name):
                os.unlink(temp_file.name)

            return success, result

        except Exception as e:
            self.logger.error(f"图片处理失败: {str(e)}")
            # 清理临时文件
            if temp_file and os.path.exists(temp_file.name):
                os.unlink(temp_file.name)
            return False, {"error": str(e)}

    def adjust_image_from_binary(self,
                               image_data: bytes,
                               brightness: float = None,
                               contrast: float = None,
                               sharpness: float = None,
                               saturation: float = None,
                               color: float = None,
                               auto_enhance: bool = None,
                               equalize: bool = None,
                               gamma: float = None,
                               crop: list = None,
                               crop_mode: str = None,
                               crop_ratio: float = None,
                               resize_after_crop: bool = None,
                               quality: int = None,
                               preset: str = None,
                               filename: str = "image.jpg",
                               storage_type: str = "house",
                               storage_subtype: str = "processed") -> Tuple[bool, Union[Dict, str]]:
        """
        从二进制数据调整图片参数

        Args:
            image_data: 图片二进制数据

            # 基本图像调整
            brightness: 亮度调整因子 (0.0-2.0, 1.0为原始亮度)
            contrast: 对比度调整因子 (0.0-2.0, 1.0为原始对比度)
            sharpness: 锐度调整因子 (0.0-2.0, 1.0为原始锐度)
            saturation: 饱和度调整因子 (0.0-2.0, 1.0为原始饱和度)
            color: 色彩平衡调整因子 (0.0-2.0, 1.0为原始色彩)

            # 自动增强
            auto_enhance: 是否自动增强图片
            equalize: 是否直方图均衡化处理

            # 模糊和滤镜
            gamma: 伽马校正值 (0.1-10.0, 1.0为原始值)

            # 尺寸调整
            crop: 自定义裁剪区域，格式为[left, top, right, bottom]
            crop_mode: 裁剪模式，可选值为'center'(居中裁剪), 'golden'(黄金比例), 'custom'(自定义)
            crop_ratio: 裁剪比例，宽高比，默认为1.5(宽度是高度的1.5倍)
            resize_after_crop: 裁剪后是否调整回原始尺寸

            # 其他参数
            quality: 保存质量 (1-100, 只对JPEG格式有效)
            preset: 预设参数，可选值为'standard'(标准), 'vivid'(鲜明), 'clear'(清晰), 'soft'(柔和)
            filename: 文件名
            storage_type: 存储类型
            storage_subtype: 存储子类型

        Returns:
            处理结果和信息
        """
        temp_file = None
        try:
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
            temp_file.write(image_data)
            temp_file.close()

            # 调用已有方法处理图片
            return self.adjust_image(
                temp_file.name,
                brightness,
                contrast,
                sharpness,
                saturation,
                color,
                auto_enhance,
                equalize,
                gamma,
                crop,
                crop_mode,
                crop_ratio,
                resize_after_crop,
                quality,
                preset,
                storage_type,
                storage_subtype
            )

        except Exception as e:
            self.logger.error(f"图片处理失败: {str(e)}")
            # 清理临时文件
            if temp_file and os.path.exists(temp_file.name):
                os.unlink(temp_file.name)
            return False, {"error": str(e)}

    def apply_auto_enhance(self,
                         image_path: str,
                         crop: list = None,
                         crop_mode: str = None,
                         crop_ratio: float = None,
                         resize_after_crop: bool = None,
                         quality: int = None,
                         storage_type: str = "house",
                         storage_subtype: str = "enhanced") -> Tuple[bool, Union[Dict, str]]:
        """
        自动增强图片质量

        Args:
            image_path: 图片路径（本地路径或URL）

            # 尺寸调整
            crop: 自定义裁剪区域，格式为[left, top, right, bottom]
            crop_mode: 裁剪模式，可选值为'center'(居中裁剪), 'golden'(黄金比例), 'custom'(自定义)
            crop_ratio: 裁剪比例，宽高比，默认为1.5(宽度是高度的1.5倍)
            resize_after_crop: 裁剪后是否调整回原始尺寸

            # 其他参数
            quality: 保存质量 (1-100, 只对JPEG格式有效)
            storage_type: 存储类型
            storage_subtype: 存储子类型

        Returns:
            处理结果和信息
        """
        temp_file = None
        try:
            # 判断是否为URL
            is_url = image_path.startswith(('http://', 'https://'))

            if is_url:
                # 从URL下载图片
                import requests
                response = requests.get(image_path, stream=True)
                if response.status_code != 200:
                    return False, {"error": f"下载图片失败，状态码: {response.status_code}"}

                # 创建临时文件
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
                temp_file.write(response.content)
                temp_file.close()
                image_path = temp_file.name

            # 打开图片
            img = Image.open(image_path)

            # 使用默认参数填充空值
            if crop is None:
                crop = DEFAULT_PARAMS['crop']
            if crop_mode is None:
                crop_mode = DEFAULT_PARAMS['crop_mode']
            if crop_ratio is None:
                crop_ratio = DEFAULT_PARAMS['crop_ratio']
            if resize_after_crop is None:
                resize_after_crop = DEFAULT_PARAMS['resize_after_crop']
            if quality is None:
                quality = DEFAULT_PARAMS['quality']

            # 裁剪图片
            original_size = img.size  # 保存原始尺寸
            crop_applied = False  # 标记是否应用了裁剪

            # 自定义裁剪
            if crop is not None:
                img = img.crop(tuple(crop))
                crop_applied = True
            # 自动裁剪
            elif crop_mode in ['center', 'golden']:
                width, height = img.size

                if crop_mode == 'center':
                    # 居中裁剪
                    if width > height * crop_ratio:  # 宽图，裁剪宽度
                        new_width = int(height * crop_ratio)
                        left = (width - new_width) // 2
                        right = left + new_width
                        img = img.crop((left, 0, right, height))
                        crop_applied = True
                    elif height > width / crop_ratio:  # 长图，裁剪高度
                        new_height = int(width / crop_ratio)
                        top = (height - new_height) // 2
                        bottom = top + new_height
                        img = img.crop((0, top, width, bottom))
                        crop_applied = True
                elif crop_mode == 'golden':
                    # 黄金比例裁剪 (1:1.618)
                    golden_ratio = 1.618

                    if width / height > golden_ratio:  # 宽图，裁剪宽度
                        new_width = int(height * golden_ratio)
                        left = (width - new_width) // 2
                        right = left + new_width
                        img = img.crop((left, 0, right, height))
                        crop_applied = True
                    elif height / width > golden_ratio:  # 长图，裁剪高度
                        new_height = int(width * golden_ratio)
                        top = (height - new_height) // 2
                        bottom = top + new_height
                        img = img.crop((0, top, width, bottom))
                        crop_applied = True

            # 如果应用了裁剪并需要调整回原始尺寸
            if crop_applied and resize_after_crop:
                img = img.resize(original_size, Image.LANCZOS)

            # 自动增强
            from PIL import ImageOps
            img = ImageOps.autocontrast(img)

            # 适度锐化
            enhancer = ImageEnhance.Sharpness(img)
            img = enhancer.enhance(1.3)

            # 保存处理后的图片到临时文件
            output_temp = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
            output_temp.close()
            img.save(output_temp.name, quality=quality)

            # 上传到OSS
            success, result = self.oss_service.upload_file(
                output_temp.name,
                'image',
                storage_type,
                storage_subtype
            )

            # 清理临时文件
            os.unlink(output_temp.name)
            if temp_file and os.path.exists(temp_file.name):
                os.unlink(temp_file.name)

            return success, result

        except Exception as e:
            self.logger.error(f"图片自动增强失败: {str(e)}")
            # 清理临时文件
            if temp_file and os.path.exists(temp_file.name):
                os.unlink(temp_file.name)
            return False, {"error": str(e)}
