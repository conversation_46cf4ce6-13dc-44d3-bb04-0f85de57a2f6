#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优推科技房源推送服务
封装房源推送和检查相关功能
"""

import logging
import time
import json
import base64
import os
from datetime import datetime
from pathlib import Path
from api.client import YoutuiApiClient
from config.settings import DEFAULT_CITY, CITY_DOMAINS
from utils.xml_utils import dict_to_xml, xml_to_dict
from services.base_service import BaseService
from database.db_connection import get_db_connection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 网站ID对应的名称
WEBSITE_NAMES = {
    '111': '58免费三网合一版本',
    '119': '搜房帮',
    '220': '百度乐居',
    '236': '搜狐焦点新端口',
    '238': '酷房网',
    '382': '网易房产',
    '402': '安居客三网合一版本',
    '403': '58付费三网合一版本',
    '404': '赶集付费三网合一版本',
    '414': '网易租房',
    '422': '今日头条房产',
    '514': '优房网付费版本',
    '521': '安居客VIP端口',
}


class PushService(BaseService):
    """优推科技房源推送服务类"""
    
    def __init__(self, city=DEFAULT_CITY, default_user_id=None, default_user_key=None):
        """
        初始化推送服务
        
        Args:
            city: 城市代码，如'bj'(北京)、'cc'(长春)等
            default_user_id: 默认用户ID，如果为None则使用城市默认账号
            default_user_key: 默认用户密钥，如果为None则使用城市默认账号
        """
        # 调用父类初始化方法
        super().__init__()
        
        self.city = city
        
        # 验证城市是否在支持列表中
        if city not in self.city_config.get("domains", {}):
            logger.warning(f"城市代码 '{city}' 未在配置中找到，使用默认城市 '{DEFAULT_CITY}'")
            self.city = DEFAULT_CITY
        
        # 获取城市账号
        city_account = self.get_city_account(self.city)
        
        # 设置默认用户ID和密钥
        self.default_user_id = default_user_id if default_user_id else city_account.get("user_id")
        self.default_user_key = default_user_key if default_user_key else city_account.get("user_key")
        
        logger.info(f"初始化推送服务实例 - 城市: {self.city}, 默认用户ID: {self.default_user_id}")
        
        # 初始化API客户端
        self.client = YoutuiApiClient(environment='production', city=self.city)
    
    def parse_push_info_result(self, result):
        """
        解析推送/外网下架详情结果
        
        Args:
            result: API返回的原始结果
            
        Returns:
            格式化后的结果
        """
        # 检查状态
        status = result.get('status')
        if status == '1':
            status_text = '成功'
        elif status == '2':
            status_text = '失败'
        else:
            status_text = '异常'
        
        # 获取网站名称
        web_id = result.get('webid', '未知')
        web_name = WEBSITE_NAMES.get(web_id, f'未知网站({web_id})')
        
        # 处理推送/下架动作
        act_name = result.get('actname', '')
        if act_name.lower() == 'push':
            action_text = '推送'
        elif act_name.lower() == 'delete':
            action_text = '外网下架房源'
        else:
            action_text = act_name
        
        formatted_result = {
            'process_id': result.get('processid', ''),
            'status': status_text,
            'raw_status': result.get('status', ''),
            'web_id': web_id,
            'web_name': web_name,
            'username': result.get('username', ''),
            'act_id': result.get('actid', ''),
            'action': action_text,
            'house_id': result.get('houseid', ''),
            'erp_id': result.get('id', ''),
            'push_url': result.get('pushurl', ''),
            'push_id': result.get('pushid', ''),
            'push_msg': result.get('pushmsg', ''),
            'message': result.get('msg', ''),
            'renzheng': result.get('renzheng', '')
        }
        
        return formatted_result
    
    def get_push_info(self, process_id, user_id=None, user_key=None):
        """
        获取房源推送/外网下架详情
        
        Args:
            process_id: 推送房源进程ID或外网下架房源进程ID（可能是base64编码的JSON字符串）
            user_id: 用户标识ID
            user_key: 优推科技用户登录密码
            
        Returns:
            推送/外网下架详情，字典格式
        """
        logger.info(f"获取房源推送详情 - 原始进程ID: {process_id}")
        
        try:
            # 处理process_id，如果是base64编码的JSON字符串，则解码并获取真实的进程ID
            actual_process_id = process_id
            try:
                # 尝试base64解码
                decoded_bytes = base64.b64decode(process_id)
                decoded_str = decoded_bytes.decode('utf-8')
                # 尝试解析JSON
                process_info = json.loads(decoded_str)
                if isinstance(process_info, dict):
                    # 获取字典中的第一个值作为真实的进程ID
                    actual_process_id = next(iter(process_info.values()))
                    logger.info(f"解析base64编码的进程ID成功，真实进程ID: {actual_process_id}")
            except Exception as e:
                # 如果解码失败，说明process_id可能已经是真实的进程ID
                logger.info(f"进程ID不是base64编码的JSON格式，使用原始值: {e}")
            
            # 创建新的客户端实例确保城市参数正确传递
            client = YoutuiApiClient(environment='production', city=self.city)
            
            # 构建请求数据
            data = {
                'platformID': client.platform_id,
                'companyID': client.company_id,
                'userID': user_id if user_id else self.default_user_id,
                'userkey': user_key if user_key else self.default_user_key,
                'processID': actual_process_id,
                'dbug': client.debug
            }
            
            # 转换为XML
            xml_data = dict_to_xml(data)
            
            # 发送请求到新的接口地址
            endpoint = '?api/house/pushinfo.html'
            response_xml = client.http_client.post(endpoint, xml_data)
            
            # 解析响应
            if response_xml:
                result = xml_to_dict(response_xml)
                if result:
                    # 检查是否因为违禁词导致失败
                    push_msg = result.get('pushMsg', '') or result.get('pushmsg', '')
                    msg = result.get('msg', '')
                    
                    logger.info(f"推送响应 - pushMsg: {push_msg}")
                    logger.info(f"推送响应 - msg: {msg}")
                    
                    # 检查pushMsg和msg中是否包含违禁词相关提示
                    if ('违法违规信息' in push_msg.lower() or '违禁词' in msg.lower() or 
                        '敏感词' in msg.lower() or '违反《广告法》' in push_msg):
                        # 从pushMsg或msg中提取违禁词
                        import re
                        # 提取各种格式的违禁词
                        patterns = [
                            r'【([^】]+)】',  # 匹配【xxx】格式
                            r'违禁词[:|：]\s*([^,，。\s]+)',  # 匹配 "违禁词: xxx" 格式
                            r'敏感词[:|：]\s*([^,，。\s]+)',  # 匹配 "敏感词: xxx" 格式
                            r'包含(违禁词|敏感词)\s*[:|：]?\s*([^,，。\s]+)',  # 匹配 "包含违禁词xxx" 格式
                            r':\[([^\]]+)\]',  # 匹配 ":[xxx]" 格式
                            r'：\[([^\]]+)\]'   # 匹配 "：[xxx]" 格式(中文冒号)
                        ]
                        
                        sensitive_words = []
                        # 首先检查pushMsg
                        for pattern in patterns:
                            matches = re.finditer(pattern, push_msg)
                            for match in matches:
                                # 根据正则表达式的分组获取违禁词
                                word = match.group(1)
                                if word and len(word) < 20:  # 避免匹配过长的内容
                                    # 处理可能包含多个违禁词的情况（用逗号分隔）
                                    sub_words = [w.strip("'\"[] ") for w in word.split(',')]
                                    sensitive_words.extend(w for w in sub_words if w)
                        
                        # 如果在pushMsg中没找到,再检查msg
                        if not sensitive_words:
                            for pattern in patterns:
                                matches = re.finditer(pattern, msg)
                                for match in matches:
                                    word = match.group(1)
                                    if word and len(word) < 20:
                                        # 处理可能包含多个违禁词的情况（用逗号分隔）
                                        sub_words = [w.strip("'\"[] ") for w in word.split(',')]
                                        sensitive_words.extend(w for w in sub_words if w)
                        
                        logger.info(f"检测到的违禁词: {sensitive_words}")
                        
                        if sensitive_words:
                            # 读取现有的违禁词
                            # 使用相对路径 - 先获取当前文件所在目录(services)，再上一级到项目根目录，然后定位到ai_generate_content
                            project_root = Path(__file__).parent.parent
                            sensitive_words_file = project_root / 'ai_generate_content' / 'sensitive_words.txt'
                            
                            # 确保目录存在
                            if not sensitive_words_file.parent.exists():
                                logger.info(f"创建目录: {sensitive_words_file.parent}")
                                sensitive_words_file.parent.mkdir(parents=True, exist_ok=True)
                                
                            logger.info(f"违禁词文件路径: {sensitive_words_file.absolute()}")
                            
                            existing_content = ''
                            # 初始化existing_words变量，确保在所有执行路径中都有定义
                            existing_words = set()
                            
                            if sensitive_words_file.exists():
                                logger.info("违禁词文件已存在,读取现有内容")
                                with open(sensitive_words_file, 'r', encoding='utf-8') as f:
                                    # 读取所有行并合并成一行
                                    lines = f.readlines()
                                    existing_content = ''.join(line.strip() for line in lines)
                                
                                # 解析现有的违禁词
                                if existing_content:
                                    # 分割并清理现有的违禁词
                                    words = re.findall(r"'([^']+)'", existing_content)
                                    existing_words = set(words)
                                logger.info(f"现有违禁词: {existing_words}")
                            
                            # 添加新的违禁词(避免重复)
                            new_words = set(sensitive_words) - existing_words
                            if new_words:
                                logger.info(f"发现新的违禁词: {new_words}")
                                try:
                                    # 重写整个文件内容
                                    with open(sensitive_words_file, 'w', encoding='utf-8') as f:
                                        if existing_content:
                                            f.write(existing_content)
                                            if not existing_content.endswith(','):
                                                f.write(',')
                                        f.write("'" + "','".join(new_words) + "'")
                                    logger.info(f"成功添加新的违禁词: {', '.join(new_words)}")
                                except Exception as e:
                                    logger.error(f"写入违禁词文件失败: {str(e)}")
                            else:
                                logger.info("没有新的违禁词需要添加")
                    
                    # 不管状态如何，只要有结果就解析
                    if isinstance(result, dict):
                        # 单个房源结果
                        process_result = self.parse_push_info_result(result)
                        logger.info(f"获取房源推送/外网下架详情成功: {actual_process_id}")
                        if result.get('status') == '2':
                            logger.warning(f"推送状态为失败: {result.get('msg', '未知错误')}")
                        return process_result
                    elif isinstance(result, list):
                        # 多个房源结果
                        process_results = []
                        for item in result:
                            process_results.append(self.parse_push_info_result(item))
                        logger.info(f"获取房源推送/外网下架详情成功: {actual_process_id}")
                        return process_results
                    else:
                        logger.error(f"获取推送信息失败: {result.get('msg', '未知错误')}")
                        return None
                else:
                    logger.error("无法解析响应")
                    return None
            else:
                logger.error("请求失败，未收到响应")
                return None
        
        except Exception as e:
            logger.error(f"获取房源推送/外网下架详情失败: {str(e)}")
            return None
    
    def get_push_records(self, erp_house_id=None, tt_id=None, website_id=None, limit=50, offset=0):
        """
        从数据库获取推送记录
        
        Args:
            erp_house_id: 可选，按ERP房源ID筛选
            tt_id: 可选，按优推科技房源ID筛选
            website_id: 可选，按网站ID筛选
            limit: 返回记录数量限制，默认50条
            offset: 记录偏移量，默认0
            
        Returns:
            推送记录列表
        """
        try:
            # 构建基础SQL查询
            sql = """
                SELECT 
                    ph.*,
                    pfd.community
                FROM push_history ph
                LEFT JOIN parsed_factory_data pfd ON ph.erp_house_id = pfd.erp_id
                WHERE ph.city = %s
            """
            params = [self.city]
            
            # 添加筛选条件
            if erp_house_id:
                sql += " AND ph.erp_house_id = %s"
                params.append(erp_house_id)
            
            if tt_id:
                sql += " AND ph.tt_id = %s"
                params.append(tt_id)
                
            if website_id:
                sql += " AND ph.website_id = %s"
                params.append(website_id)
            
            # 添加排序和分页
            sql += " ORDER BY ph.push_time DESC LIMIT %s OFFSET %s"
            params.extend([limit, offset])
            
            logger.info(f"执行SQL查询: {sql}")
            logger.info(f"查询参数: {params}")
            
            # 执行查询
            conn = get_db_connection()
            try:
                with conn.cursor() as cursor:
                    # 获取总记录数
                    count_sql = """
                        SELECT COUNT(*) as total 
                        FROM push_history ph
                        LEFT JOIN parsed_factory_data pfd ON ph.erp_house_id = pfd.erp_id
                        WHERE ph.city = %s
                    """
                    count_params = [self.city]
                    
                    if erp_house_id:
                        count_sql += " AND ph.erp_house_id = %s"
                        count_params.append(erp_house_id)
                    
                    if tt_id:
                        count_sql += " AND ph.tt_id = %s"
                        count_params.append(tt_id)
                        
                    if website_id:
                        count_sql += " AND ph.website_id = %s"
                        count_params.append(website_id)
                    
                    cursor.execute(count_sql, count_params)
                    total_result = cursor.fetchone()
                    total = total_result['total'] if isinstance(total_result, dict) else total_result[0]
                    
                    logger.info(f"总记录数: {total}")
                    
                    # 获取记录列表
                    cursor.execute(sql, params)
                    records = cursor.fetchall()
                    
                    logger.info(f"获取到记录数: {len(records)}")
                    
                    # 格式化记录
                    formatted_records = []
                    for record in records:
                        # 由于使用DictCursor，record已经是字典格式
                        formatted_record = {
                            "timestamp": record['push_time'].strftime("%Y-%m-%d %H:%M:%S") if record['push_time'] else None,
                            "erp_house_id": record['erp_house_id'],
                            "tt_id": record['tt_id'],
                            "website_id": record['website_id'],
                            "website_name": record['website_name'],
                            "city": record['city'],
                            "process_id": record['process_id'],
                            "success": True,  # 由于历史原因，保持这个字段为True
                            "push_info": None,  # 这个字段在新版本中不再使用
                            "community": record['community']  # 添加园区名称字段
                        }
                        formatted_records.append(formatted_record)
                    
                    return {
                        "total": total,
                        "records": formatted_records
                    }
            finally:
                conn.close()
            
        except Exception as e:
            logger.error(f"获取推送记录失败: {str(e)}")
            return {
                "total": 0,
                "records": []
            } 
        
    async def async_get_website_accounts(self, user_id=None, user_key=None):
        """
        异步获取指定用户可用的网站账号信息
        
        Args:
            user_id: 用户标识ID，如果为None则使用默认用户ID
            user_key: 优推科技用户登录密码，如果为None则使用默认用户密钥
            
        Returns:
            list: 网站账号信息列表，每个元素包含网站ID、账号信息和推广类型等
        """
        logger.info(f"异步获取用户(ID: {user_id if user_id else self.default_user_id})的网站账号信息")
        
        # 使用适当的用户ID和密钥
        actual_user_id = user_id if user_id else self.default_user_id
        actual_user_key = user_key if user_key else self.default_user_key
        
        try:
            # 异步调用API获取网站账号信息
            # 为了避免导入循环依赖，我们在这里创建异步客户端
            from api.client import AsyncYoutuiApiClient
            async_client = AsyncYoutuiApiClient(environment='production', city=self.city)
            
            result = await async_client.async_get_website_accounts(actual_user_id, actual_user_key)
            
            if result and result.get('status') == '1' and result.get('content'):
                try:
                    # 解码content字段内容
                    content = result.get('content', '')
                    decoded_content = base64.b64decode(content).decode('utf-8')
                    
                    # 解析JSON并返回原始数据
                    website_accounts = json.loads(decoded_content)
                    logger.info(f"成功异步获取网站账号信息，共{len(website_accounts)}个网站")
                    return website_accounts
                except Exception as e:
                    logger.error(f"解析异步获取的网站账号信息失败: {str(e)}")
            else:
                error_msg = result.get('msg') if result else "未知错误"
                logger.error(f"异步获取网站账号信息失败: {error_msg}")
            
            return None
        except Exception as e:
            logger.error(f"异步获取网站账号信息过程中发生错误: {str(e)}")
            return None

    async def async_get_website_data(self):
        """
        异步获取当前城市支持的网站数据
        
        Returns:
            list: 网站数据列表，每个元素包含网站ID、账号信息和推广类型等
        """
        city = self.city
        user_id = self.default_user_id
        
        # 尝试从API异步获取实际数据
        try:
            # 调用优推API异步获取网站列表
            api_data = await self.async_fetch_website_data_from_api(user_id)
            if api_data and len(api_data) > 0:
                logger.info(f"成功从API异步获取城市 '{city}' 的网站列表，共{len(api_data)}个网站")
                return api_data
        except Exception as e:
            logger.error(f"从API异步获取网站列表失败: {str(e)}")
            return []
            
    async def async_fetch_website_data_from_api(self, user_id):
        """
        异步从优推API获取网站数据
        
        Args:
            user_id: 用户ID
            
        Returns:
            list: 网站数据列表
        """
        try:
            # 获取用户密钥
            user_key = self.default_user_key
            
            # 异步调用API获取网站账号信息
            # 为了避免导入循环依赖，我们在这里创建异步客户端
            from api.client import AsyncYoutuiApiClient
            async_client = AsyncYoutuiApiClient(environment='production', city=self.city)
            
            result = await async_client.async_get_website_accounts(user_id, user_key)
            
            if result and result.get('status') == '1' and result.get('content'):
                # 解析API返回的数据
                logger.info("成功异步调用优推API获取网站数据")
                
                # 解析数据
                import base64
                import json
                
                try:
                    content = result.get('content', '')
                    decoded_content = base64.b64decode(content).decode('utf-8')
                    website_data = json.loads(decoded_content)
                    
                    # 转换为我们期望的格式
                    if isinstance(website_data, list):
                        return website_data
                    else:
                        logger.error(f"API返回的数据格式不是预期的列表: {type(website_data)}")
                except Exception as e:
                    logger.error(f"解析API返回的数据失败: {str(e)}")
            else:
                logger.error(f"API返回错误: {result.get('msg') if result else '未获取到响应'}")
        except Exception as e:
            logger.error(f"异步调用API获取网站数据失败: {str(e)}")
        
        return [] 