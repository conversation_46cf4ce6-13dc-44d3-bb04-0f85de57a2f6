#!/usr/bin/env python3
# -*- coding: utf-8 -*-



import json
import logging
from pathlib import Path
from typing import Dict



from services.oss_service import OssService
from database.db_connection import get_db_connection


logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

oss_service = OssService()
cities_json_path = Path(__file__).parent.parent / "config" / "cities.json"


class UploadService:

    @staticmethod
    def load_city_config():
        try:
            if not cities_json_path.exists():
                return {"domains": {}, "names": {}, "accounts": {}}

            with open(cities_json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载城市配置失败: {str(e)}")
            return {"domains": {}, "names": {}, "accounts": {}}

    @staticmethod
    async def get_campus_gallery(community: str = None, page: int = 1, page_size: int = 10) -> Dict:
        conn = None
        cursor = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            if community is None:
                count_query = """
                SELECT COUNT(*) as total_count
                FROM park_gallery_unified
                WHERE park_name IS NOT NULL AND park_name != ''
                AND images_json IS NOT NULL AND images_json != ''
                """

                cursor.execute(count_query)
                total_result = cursor.fetchone()
                total_count = total_result["total_count"] if total_result else 0

                offset = (page - 1) * page_size
                total_pages = (total_count + page_size - 1) // page_size
                query = """
                SELECT
                    park_name,
                    images_json,
                    created_at
                FROM park_gallery_unified
                WHERE park_name IS NOT NULL AND park_name != ''
                AND images_json IS NOT NULL AND images_json != ''
                ORDER BY park_name
                LIMIT %s OFFSET %s
                """

                cursor.execute(query, (page_size, offset))
                results = cursor.fetchall()

                communities = []
                for row in results:
                    community_name = row["park_name"]
                    images_json = row["images_json"]

                    import json
                    try:
                        images_data = json.loads(images_json) if images_json else {"images": []}
                        image_count = len(images_data.get("images", []))
                    except:
                        image_count = 0

                    community_info = {
                        "name": community_name,
                        "image_count": image_count
                    }
                    communities.append(community_info)

                # 构建分页信息
                pagination = {
                    "current_page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_previous": page > 1,
                    "has_next": page < total_pages
                }

                return {
                    "success": True,
                    "message": f"成功获取第{page}页园区信息，共{total_count}个园区",
                    "data": {
                        "communities": communities
                    },
                    "pagination": pagination
                }

            # 提供了园区名称，返回该园区的详细信息
            else:
                # 查询指定园区的所有图片数据
                query = """
                SELECT park_name, images_json, created_at
                FROM park_gallery_unified
                WHERE park_name = %s
                """

                cursor.execute(query, (community,))
                result = cursor.fetchone()

                if not result:
                    return {
                        "success": False,
                        "message": f"未找到园区'{community}'的数据",
                        "data": None
                    }

                images_json = result["images_json"]

                import json
                try:
                    images_data = json.loads(images_json) if images_json else {"images": []}
                    raw_images = images_data.get("images", [])
                except:
                    raw_images = []

                images = []
                for i, img in enumerate(raw_images):
                    images.append({
                        "id": f"unified_{i+1}",
                        "image_url": img.get("url", ""),
                        "created_at": result.get("created_at"),
                        "type": "园区图片",
                        "field_name": img.get("original_field", ""),
                        "source_record_id": img.get("source_record_id")
                    })

                image_count = len(images)

                response_data = {
                    "community": community,
                    "images": images,
                    "total_image_count": image_count
                }

                return {
                    "success": True,
                    "message": f"成功获取园区'{community}'的图片信息",
                    "data": response_data
                }

        except Exception as e:
            logger.error(f"获取园区图库信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "message": f"获取园区图库信息失败: {str(e)}",
                "data": None
            }
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()


    @staticmethod
    async def delete_campus_image(community: str, image_url: str, image_type: str) -> Dict:
        conn = None
        cursor = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            query = """
            SELECT id, images_json
            FROM park_gallery_unified
            WHERE park_name = %s
            """

            cursor.execute(query, (community,))
            result = cursor.fetchone()

            if not result:
                return {
                    "success": False,
                    "message": f"未找到园区'{community}'的数据",
                    "data": None
                }

            images_json = result["images_json"]
            record_id = result["id"]

            import json
            try:
                images_data = json.loads(images_json) if images_json else {"images": []}
                images_list = images_data.get("images", [])
            except:
                return {
                    "success": False,
                    "message": "图片数据格式错误",
                    "data": None
                }

            match_index = -1
            for i, img in enumerate(images_list):
                if img.get("url") == image_url:
                    match_index = i
                    break

            if match_index == -1:
                return {
                    "success": False,
                    "message": "要删除的图片URL不存在",
                    "data": {
                        "target_url": image_url,
                        "total_images": len(images_list)
                    }
                }

            images_list.pop(match_index)

            images_data["images"] = images_list
            new_images_json = json.dumps(images_data, ensure_ascii=False)

            update_query = """
            UPDATE park_gallery_unified
            SET images_json = %s
            WHERE id = %s
            """

            cursor.execute(update_query, (new_images_json, record_id))
            conn.commit()

            return {
                "success": True,
                "message": "图片删除成功",
                "data": {
                    "deleted_url": image_url,
                    "remaining_count": len(images_list)
                }
            }

        except Exception as e:
            if conn:
                conn.rollback()
            return {
                "success": False,
                "message": f"删除图片失败: {str(e)}",
                "data": None
            }
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

