#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
竞帖分析服务
提供竞帖数据分析和查询功能
"""

import logging
from services.base_service import BaseService
from database.db_connection import db_cursor
from datetime import datetime

class ListingAnalysisService(BaseService):
    """竞帖分析服务类，提供竞帖数据的分析和查询功能"""
    
    def __init__(self):
        """初始化竞帖分析服务"""
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get_all_cities(self):
        """
        获取所有已有数据的城市列表
        
        返回:
        - list: 城市列表，包含城市名称
        """
        try:
            with db_cursor() as cursor:
                query = """
                SELECT DISTINCT city 
                FROM property_listing 
                WHERE city IS NOT NULL AND city != '' 
                ORDER BY city
                """
                cursor.execute(query)
                results = cursor.fetchall()
                return [item['city'] for item in results]
        except Exception as e:
            self.logger.error(f"获取城市列表失败: {str(e)}")
            return []
    
    def get_districts_by_city(self, city):
        """
        获取指定城市的所有区列表
        
        参数:
        - city: 城市名称
        
        返回:
        - list: 区列表，包含区名称
        """
        try:
            with db_cursor() as cursor:
                query = """
                SELECT DISTINCT qu 
                FROM property_listing 
                WHERE city = %s AND qu IS NOT NULL AND qu != '' 
                ORDER BY qu
                """
                cursor.execute(query, (city,))
                results = cursor.fetchall()
                return [item['qu'] for item in results]
        except Exception as e:
            self.logger.error(f"获取城市{city}的区列表失败: {str(e)}")
            return []
    
    def get_listing_stats_by_area(self, city=None, district=None, listing_type=None, year=None, month=None, start_date=None, end_date=None):
        """
        获取按区域分组的竞帖统计数据

        参数:
        - city: 城市名称，可选
        - district: 区名称，可选
        - listing_type: 租售类型，可选（"出租"或"出售"）
        - year: 年份，可选
        - month: 月份，可选
        - start_date: 开始日期，格式为YYYY-MM-DD，可选
        - end_date: 结束日期，格式为YYYY-MM-DD，可选

        返回:
        - dict: 统计数据
        """
        try:
            # 处理租售类型转换
            if listing_type is not None:
                # 数字到文本的映射
                if listing_type == 0 or listing_type == '0':
                    listing_type = "出租"
                elif listing_type == 1 or listing_type == '1':
                    listing_type = "出售"

            with db_cursor() as cursor:
                # 构建查询条件
                conditions = []
                params = []

                if city:
                    conditions.append("city = %s")
                    params.append(city)

                if district:
                    conditions.append("qu = %s")
                    params.append(district)

                if listing_type is not None:
                    conditions.append("type = %s")
                    params.append(listing_type)

                # 日期范围条件
                if start_date and end_date:
                    # 处理日期格式，将MM-DD格式转为完整日期
                    date_condition = "CONCAT(YEAR(CURRENT_DATE()), '-', publish_time) BETWEEN %s AND %s"
                    conditions.append(date_condition)
                    params.append(start_date)
                    params.append(end_date)

                # 构建WHERE子句，确保语法正确
                base_conditions = ["title IS NOT NULL", "title != ''"]
                all_conditions = conditions + base_conditions
                where_clause = "WHERE " + " AND ".join(all_conditions)

                # 优化SQL：将多个查询合并为一个高效查询，减少数据库访问次数
                # 使用条件聚合和INSTR函数替代多个LIKE查询，提升全国查询性能
                if not conditions:  # 全国查询优化
                    unified_query = """
                    SELECT
                        COUNT(DISTINCT title) as total_posts,
                        COUNT(DISTINCT CASE
                            WHEN agent_company IS NOT NULL
                            AND agent_company != ''
                            AND INSTR(agent_company, '联东') > 0
                            THEN title
                        END) as our_posts_total,
                        COUNT(DISTINCT CASE
                            WHEN agent_company IS NOT NULL
                            AND agent_company != ''
                            AND INSTR(agent_company, '联东') = 0
                            THEN title
                        END) as competitor_posts_total,
                        COUNT(DISTINCT CASE
                            WHEN agent_company IS NOT NULL
                            AND agent_company != ''
                            AND INSTR(agent_company, '联东') = 0
                            THEN agent_company
                        END) as competitors_count
                    FROM property_listing
                    WHERE title IS NOT NULL AND title != ''
                    """
                    cursor.execute(unified_query)
                    unified_result = cursor.fetchone()

                    total_posts = unified_result['total_posts'] if unified_result else 0
                    our_posts_total = unified_result['our_posts_total'] if unified_result else 0
                    competitor_posts_total = unified_result['competitor_posts_total'] if unified_result else 0
                    competitors_count = unified_result['competitors_count'] if unified_result else 0

                else:  # 有筛选条件的查询 - 保持原有逻辑但优化LIKE为INSTR
                    # 构建基础WHERE条件
                    base_conditions = ["title IS NOT NULL", "title != ''"]
                    all_conditions = conditions + base_conditions
                    where_clause = "WHERE " + " AND ".join(all_conditions)

                    unified_query = f"""
                    SELECT
                        COUNT(DISTINCT title) as total_posts,
                        COUNT(DISTINCT CASE
                            WHEN agent_company IS NOT NULL
                            AND agent_company != ''
                            AND INSTR(agent_company, '联东') > 0
                            THEN title
                        END) as our_posts_total,
                        COUNT(DISTINCT CASE
                            WHEN agent_company IS NOT NULL
                            AND agent_company != ''
                            AND INSTR(agent_company, '联东') = 0
                            THEN title
                        END) as competitor_posts_total,
                        COUNT(DISTINCT CASE
                            WHEN agent_company IS NOT NULL
                            AND agent_company != ''
                            AND INSTR(agent_company, '联东') = 0
                            THEN agent_company
                        END) as competitors_count
                    FROM property_listing
                    {where_clause}
                    """
                    cursor.execute(unified_query, params)
                    unified_result = cursor.fetchone()

                    total_posts = unified_result['total_posts'] if unified_result else 0
                    our_posts_total = unified_result['our_posts_total'] if unified_result else 0
                    competitor_posts_total = unified_result['competitor_posts_total'] if unified_result else 0
                    competitors_count = unified_result['competitors_count'] if unified_result else 0

                # 创建新的月份筛选条件
                time_conditions = []
                time_params = []

                if year:
                    # 年份条件，使用CONCAT拼接年份和publish_time
                    time_conditions.append("YEAR(CONCAT(%s, '-', publish_time)) = %s")
                    time_params.append(year)
                    time_params.append(year)

                if month:
                    # 月份条件，从publish_time中提取月份部分（格式为MM-DD）
                    time_conditions.append("SUBSTRING(publish_time, 1, 2) = %s")
                    # 确保月份是两位数格式
                    month_str = str(month).zfill(2)
                    time_params.append(month_str)

                # 如果没有指定年月，则默认获取本月新增帖子数
                if not time_conditions:
                    current_year = str(datetime.now().year)
                    current_month = str(datetime.now().month).zfill(2)
                    time_conditions.append("SUBSTRING(publish_time, 1, 2) = %s")
                    time_params.append(current_month)

                # 获取本月新增帖子数（按标题去重）
                new_posts_conditions = conditions.copy() + time_conditions
                new_posts_params = params.copy() + time_params

                current_month_query = f"""
                SELECT COUNT(*) as count
                FROM (
                    SELECT DISTINCT title
                    FROM property_listing
                    WHERE {' AND '.join(new_posts_conditions)}
                    AND title IS NOT NULL AND title != ''
                ) as unique_titles
                """
                cursor.execute(current_month_query, new_posts_params)
                current_month_result = cursor.fetchone()
                new_posts = current_month_result['count'] if current_month_result else 0

                # 计算我方单账号平均帖子数（按agent_name字段统计，按标题去重）- 优化LIKE为INSTR
                our_accounts_conditions = ["agent_company IS NOT NULL", "agent_company != ''", "INSTR(agent_company, '联东') > 0", "agent_name IS NOT NULL", "agent_name != ''", "title IS NOT NULL", "title != ''"] + conditions
                our_accounts_where = "WHERE " + " AND ".join(our_accounts_conditions)

                our_accounts_query = f"""
                SELECT agent_name, COUNT(DISTINCT title) as post_count
                FROM property_listing
                {our_accounts_where}
                GROUP BY agent_name
                """
                cursor.execute(our_accounts_query, params)
                our_accounts_results = cursor.fetchall()

                our_accounts_count = len(our_accounts_results)
                avg_posts_per_account = 0
                if our_accounts_count > 0:
                    total_posts_by_accounts = sum(item['post_count'] for item in our_accounts_results)
                    avg_posts_per_account = round(total_posts_by_accounts / our_accounts_count)

                # 计算我方帖子占比（百分比）
                our_posts_percentage = 0
                competitor_posts_percentage = 0
                if total_posts > 0:
                    our_posts_percentage = round((our_posts_total / total_posts) * 100)
                    competitor_posts_percentage = round((competitor_posts_total / total_posts) * 100)
                
                return {
                    "total": total_posts,
                    "our_posts_total": our_posts_total,
                    "our_posts_percentage": our_posts_percentage,
                    "competitor_posts_total": competitor_posts_total,
                    "competitor_posts_percentage": competitor_posts_percentage,
                    "competitors_count": competitors_count,
                    "new_posts": new_posts,
                    "our_accounts_count": our_accounts_count,
                    "avg_posts_per_account": avg_posts_per_account
                }
                
        except Exception as e:
            self.logger.error(f"获取竞帖统计数据失败: {str(e)}")
            return {
                "total": 0,
                "our_posts_total": 0,
                "our_posts_percentage": 0,
                "competitor_posts_total": 0,
                "competitor_posts_percentage": 0,
                "competitors_count": 0,
                "new_posts": 0,
                "our_accounts_count": 0,
                "avg_posts_per_account": 0
            }
            
    def get_basic_statistics(self, city=None, district=None, listing_type=None, year=None, month=None, start_date=None, end_date=None):
        """
        获取基础统计数据，包括总竞争帖子数、竞争对手数、我方帖子总数和我方帖子占比
        
        参数:
        - city: 城市名称，可选
        - district: 区名称，可选
        - listing_type: 租售类型，可选（"出租"或"出售"）
        - year: 年份，可选
        - month: 月份，可选
        - start_date: 开始日期，格式为YYYY-MM-DD，可选
        - end_date: 结束日期，格式为YYYY-MM-DD，可选
        
        返回:
        - dict: 基础统计数据
        """
        # 处理租售类型转换
        if listing_type is not None:
            # 数字到文本的映射
            if listing_type == 0 or listing_type == '0':
                listing_type = "出租"
            elif listing_type == 1 or listing_type == '1':
                listing_type = "出售"
                
        # 如果同时提供了日期范围和年月，优先使用日期范围
        if start_date and end_date:
            stats = self.get_listing_stats_by_area(city, district, listing_type, None, None, start_date, end_date)
        else:
            stats = self.get_listing_stats_by_area(city, district, listing_type, year, month)
        
        # 计算竞争对手单账号平均帖子数
        competitor_avg_posts = 0
        if stats["competitors_count"] > 0:
            competitor_avg_posts = round(stats["competitor_posts_total"] / stats["competitors_count"])
            
        return {
            "totalPosts": stats["total"],
            "totalCompetitors": stats["competitors_count"],
            "newPosts": stats["our_posts_total"],  # 我方帖子总数
            "ourPostsPercentage": stats["our_posts_percentage"],
            "competitorPostsTotal": stats["competitor_posts_total"],  # 竞争对手帖子总数
            "avgPostsPerAccount": stats["avg_posts_per_account"],  # 我方单账号平均帖子数
            "ourAccountsCount": stats["our_accounts_count"],  # 我方账号数量
            "competitorAvgPostsPerAccount": competitor_avg_posts  # 竞争对手单账号平均帖子数
        }
    
    def get_top_companies(self, city=None, district=None, limit=5, listing_type=None, start_date=None, end_date=None):
        """
        获取帖子数量最多的前N个公司
        
        参数:
        - city: 城市名称，可选
        - district: 区名称，可选
        - limit: 返回记录数量，默认5条
        - listing_type: 租售类型，可选（"出租"或"出售"）
        - start_date: 开始日期，格式为YYYY-MM-DD，可选
        - end_date: 结束日期，格式为YYYY-MM-DD，可选
        
        返回:
        - list: 公司列表，包含公司名称、帖子数量和最近发帖时间
        """
        try:
            # 处理租售类型转换
            if listing_type is not None:
                # 数字到文本的映射
                if listing_type == 0 or listing_type == '0':
                    listing_type = "出租"
                elif listing_type == 1 or listing_type == '1':
                    listing_type = "出售"
                    
            with db_cursor() as cursor:
                # 构建查询条件
                conditions = []
                params = []
                
                if city:
                    conditions.append("city = %s")
                    params.append(city)
                
                if district:
                    conditions.append("qu = %s")
                    params.append(district)
                
                if listing_type is not None:
                    conditions.append("type = %s")
                    params.append(listing_type)
                
                # 日期范围条件
                if start_date and end_date:
                    # 处理日期格式，将MM-DD格式转为完整日期
                    date_condition = "CONCAT(YEAR(CURRENT_DATE()), '-', publish_time) BETWEEN %s AND %s"
                    conditions.append(date_condition)
                    params.append(start_date)
                    params.append(end_date)
                
                # 基础WHERE条件：公司名不为空
                where_clause = "WHERE agent_company IS NOT NULL AND agent_company != ''"
                if conditions:
                    where_clause += " AND " + " AND ".join(conditions)
                
                # 查询帖子数量最多的公司（按标题去重）- 优化子查询为直接聚合
                query = f"""
                SELECT
                    agent_company,
                    COUNT(DISTINCT title) as post_count,
                    MAX(publish_time) as last_post_time
                FROM property_listing
                {where_clause}
                AND title IS NOT NULL AND title != ''
                GROUP BY agent_company
                ORDER BY post_count DESC
                LIMIT %s
                """
                
                params.append(limit)
                cursor.execute(query, params)
                results = cursor.fetchall()
                
                # 格式化结果
                companies = []
                current_year = str(datetime.now().year)
                for item in results:
                    # 直接使用原始发布时间，不再尝试格式化
                    last_post_time = item['last_post_time']
                    # 如果有发布时间，拼接当前年份
                    formatted_time = f"{current_year}-{last_post_time}" if last_post_time else None
                    
                    companies.append({
                        "name": item['agent_company'],
                        "postCount": item['post_count'],
                        "lastPostTime": formatted_time
                    })
                
                return companies
                
        except Exception as e:
            self.logger.error(f"获取热门公司失败: {str(e)}")
            return []
            
    def get_time_series_data(self, city=None, district=None, days=None, listing_type=None, start_date=None, end_date=None):
        """
        获取一段时间内的每日发帖数量统计，按公司分组
        
        参数:
        - city: 城市名称，可选
        - district: 区名称，可选
        - days: 天数，兼容旧版本，已弃用
        - listing_type: 租售类型，可选（"出租"或"出售"）
        - start_date: 开始日期，格式为YYYY-MM-DD，可选
        - end_date: 结束日期，格式为YYYY-MM-DD，可选
        
        返回:
        - dict: 包含时间序列数据和公司列表
        """
        try:
            # 处理租售类型转换
            if listing_type is not None:
                # 数字到文本的映射
                if listing_type == 0 or listing_type == '0':
                    listing_type = "出租"
                elif listing_type == 1 or listing_type == '1':
                    listing_type = "出售"
                    
            with db_cursor() as cursor:
                # 构建查询条件
                conditions = []
                params = []
                
                if city:
                    conditions.append("city = %s")
                    params.append(city)
                
                if district:
                    conditions.append("qu = %s")
                    params.append(district)
                
                if listing_type is not None:
                    conditions.append("type = %s")
                    params.append(listing_type)
                
                # 日期范围条件
                if start_date and end_date:
                    # 解析开始和结束日期
                    try:
                        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
                        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
                        
                        # 提取月日部分进行比较
                        start_month_day = start_date_obj.strftime('%m-%d')
                        end_month_day = end_date_obj.strftime('%m-%d')
                        
                        date_condition = "publish_time BETWEEN %s AND %s"
                        conditions.append(date_condition)
                        params.append(start_month_day)
                        params.append(end_month_day)
                    except ValueError:
                        self.logger.error(f"日期格式错误: start_date={start_date}, end_date={end_date}")
                        # 默认查询最近30天
                        date_condition = "CONCAT(YEAR(CURRENT_DATE()), '-', publish_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)"
                        conditions.append(date_condition)
                elif days:
                    # 兼容旧版本
                    date_condition = "CONCAT(YEAR(CURRENT_DATE()), '-', publish_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL %s DAY)"
                    conditions.append(date_condition)
                    params.append(days)
                else:
                    # 默认查询最近30天
                    date_condition = "CONCAT(YEAR(CURRENT_DATE()), '-', publish_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)"
                    conditions.append(date_condition)
                
                where_clause = "WHERE " + " AND ".join(conditions)
                
                # 获取热门公司列表（前3家竞争对手 + 我方，按标题去重）- 优化子查询
                top_companies_query = f"""
                SELECT
                    agent_company,
                    COUNT(DISTINCT title) as post_count
                FROM property_listing
                {where_clause}
                AND title IS NOT NULL AND title != ''
                GROUP BY agent_company
                ORDER BY post_count DESC
                LIMIT 10
                """
                
                cursor.execute(top_companies_query, params)
                top_companies_results = cursor.fetchall()
                
                # 分离我方公司和竞争对手
                our_companies = []
                competitor_companies = []

                for company in top_companies_results:
                    company_name = company['agent_company']
                    if company_name:
                        if '联东' in company_name:
                            our_companies.append(company_name)
                        else:
                            competitor_companies.append(company_name)
                
                # 只取前3名竞争对手
                competitor_companies = competitor_companies[:3]
                
                # 构建日期序列（根据筛选条件，按标题去重）- 优化子查询
                date_series_query = f"""
                SELECT
                    CONCAT(YEAR(CURRENT_DATE()), '-', publish_time) as full_date,
                    COUNT(DISTINCT title) as total_count
                FROM property_listing
                {where_clause}
                AND title IS NOT NULL AND title != ''
                GROUP BY publish_time
                ORDER BY full_date
                """
                
                cursor.execute(date_series_query, params)
                date_results = cursor.fetchall()
                
                # 构建日期映射
                date_map = {}
                for date_row in date_results:
                    # 使用完整日期作为键
                    full_date = date_row['full_date']
                    date_map[full_date] = {
                        'date': full_date,
                        'total': date_row['total_count'],
                        '我方': 0,
                    }
                    
                    # 为每个竞争对手添加字段
                    for comp_name in competitor_companies:
                        date_map[full_date][comp_name] = 0
                    
                    # 添加其他字段
                    date_map[full_date]['其他'] = 0
                
                # 如果没有数据，返回空结果结构
                if not date_map:
                    # 过滤掉 competitor_companies 中的空值
                    filtered_companies = ['我方'] + [c for c in competitor_companies if c] + ['其他']
                    return {
                        'series': [],
                        'companies': filtered_companies
                    }
                
                # 获取我方每日发帖数
                our_companies_params = params + our_companies
                our_placeholders = ', '.join(['%s'] * len(our_companies))

                our_daily_query = f"""
                SELECT
                    CONCAT(YEAR(CURRENT_DATE()), '-', publish_time) as full_date,
                    COUNT(DISTINCT title) as post_count
                FROM property_listing
                {where_clause} AND agent_company IN ({our_placeholders})
                AND title IS NOT NULL AND title != ''
                GROUP BY publish_time
                ORDER BY full_date
                """

                if our_companies:
                    cursor.execute(our_daily_query, our_companies_params)
                    our_results = cursor.fetchall()

                    for row in our_results:
                        full_date = row['full_date']
                        if full_date in date_map:
                            date_map[full_date]['我方'] = row['post_count']

                # 获取每个竞争对手的每日发帖数
                for comp_name in competitor_companies:
                    if not comp_name:  # 跳过空公司名
                        continue

                    comp_params = params + [comp_name]

                    comp_daily_query = f"""
                    SELECT
                        CONCAT(YEAR(CURRENT_DATE()), '-', publish_time) as full_date,
                        COUNT(DISTINCT title) as post_count
                    FROM property_listing
                    {where_clause} AND agent_company = %s
                    AND title IS NOT NULL AND title != ''
                    GROUP BY publish_time
                    ORDER BY full_date
                    """

                    cursor.execute(comp_daily_query, comp_params)
                    comp_results = cursor.fetchall()

                    for row in comp_results:
                        full_date = row['full_date']
                        if full_date in date_map:
                            date_map[full_date][comp_name] = row['post_count']

                # 获取其他公司的每日发帖数
                excluded_companies = our_companies + [c for c in competitor_companies if c]  # 过滤掉空公司名
                excluded_placeholders = ', '.join(['%s'] * len(excluded_companies)) if excluded_companies else "''"
                excluded_params = params + excluded_companies

                others_daily_query = f"""
                SELECT
                    CONCAT(YEAR(CURRENT_DATE()), '-', publish_time) as full_date,
                    COUNT(DISTINCT title) as post_count
                FROM property_listing
                {where_clause} AND (agent_company NOT IN ({excluded_placeholders}) OR agent_company IS NULL OR agent_company = '')
                AND title IS NOT NULL AND title != ''
                GROUP BY publish_time
                ORDER BY full_date
                """ if excluded_companies else f"""
                SELECT
                    CONCAT(YEAR(CURRENT_DATE()), '-', publish_time) as full_date,
                    COUNT(DISTINCT title) as post_count
                FROM property_listing
                {where_clause} AND (agent_company IS NULL OR agent_company = '')
                AND title IS NOT NULL AND title != ''
                GROUP BY publish_time
                ORDER BY full_date
                """

                cursor.execute(others_daily_query, excluded_params if excluded_companies else params)
                others_results = cursor.fetchall()

                for row in others_results:
                    full_date = row['full_date']
                    if full_date in date_map:
                        date_map[full_date]['其他'] = row['post_count']
                
                # 转换为列表并按日期排序
                time_series = list(date_map.values())
                time_series.sort(key=lambda x: x['date'])
                
                # 过滤掉空公司名
                filtered_companies = ['我方'] + [c for c in competitor_companies if c] + ['其他']
                
                # 添加公司信息
                result = {
                    'series': time_series,
                    'companies': filtered_companies
                }
                
                return result
                
        except Exception as e:
            self.logger.error(f"获取时间序列数据失败: {str(e)}")
            # 出错时也返回一致的数据结构
            return {
                'series': [],
                'companies': ['我方', '其他']
            } 