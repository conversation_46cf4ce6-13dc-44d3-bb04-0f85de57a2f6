#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""工厂厂房Excel上传与解析服务"""

import json
import logging
import time
import os
import uuid
import tempfile
import random
import string
from typing import Dict, Optional, List
from pathlib import Path
from fastapi.concurrency import run_in_threadpool
import pandas as pd
import numpy as np
from fastapi import UploadFile
from datetime import datetime
import asyncio

from database.db_connection import get_db_connection
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ai_generate_content.property_description_generation import generate_all_property_descriptions
from ai_generate_content.get_community_data import get_community_data
from services.youtui_service import YoutuiService
from services.base_service import BaseService
from services.task_status_manager import update_task_status, get_task_status, get_all_tasks_status
from services.gallery_image_service import gallery_image_service

logger = logging.getLogger(__name__)

parsed_data_store = {}

# 兼容性函数，用于支持现有代码
def get_tasks_status():
    """获取所有任务状态的兼容性函数"""
    return get_all_tasks_status()

BASE62 = string.digits + string.ascii_letters

def get_youtui_service(city: str, city_name: Optional[str] = None):
    base_service = BaseService()
    city_config = base_service.city_config
    actual_city = city

    if not actual_city and city_name:
        name_to_code = {}
        for code, name in city_config.get("names", {}).items():
            name_to_code[name] = code

        city_code = name_to_code.get(city_name)
        if city_code:
            actual_city = city_code
        else:
            raise ValueError(
                f"未找到城市 '{city_name}' 的信息，支持的城市有: {', '.join(city_config.get('names', {}).values())}"
            )

    if not actual_city:
        raise ValueError("必须提供city或city_name参数之一")

    if actual_city not in city_config.get("domains", {}):
        raise ValueError(
            f"无效的城市代码 '{actual_city}'，支持的城市代码有: {', '.join(city_config.get('domains', {}).keys())}"
        )

    return YoutuiService(city=actual_city)

try:
    with open(os.path.join(Path(__file__).parent.parent, 'config', 'cities.json'), 'r', encoding='utf-8') as f:
        cities_config = json.load(f)
        CITY_CODE_MAP = cities_config.get('names', {})

        CITY_NAME_TO_CODE = {}
        for code, name in CITY_CODE_MAP.items():
            CITY_NAME_TO_CODE[name] = code
            if not name.endswith('市'):
                CITY_NAME_TO_CODE[f"{name}市"] = code

        logger.info(f"已加载 {len(CITY_CODE_MAP)} 个城市代码")
except Exception as e:
    logger.error(f"加载城市代码配置失败: {str(e)}")

EXCEL_TO_DB_MAPPING = {
    '标题': 'topic',
    '城市': 'city',
    '区域': 'zone',
    '街道': 'street',
    '租售类型': 'type',
    '园区名称': 'community',
    '描述': 'content',
    '租金': 'total',
    '租金单位': 'rentunitdaily',
    '面积（平方米）': 'square',
    '地址': 'address',
    '建筑类型': 'type4property',
    '封面图': 'thumb',
    '户型图': 'floorplans',
    '内部图': 'photointerior',
    '外部图': 'photooutdoor',
    '配套': 'infrastructure',
    '起租期': 'slease',
    '支付方式': 'towards',
    '楼层': 'floor',
    '首层层高': 'Storey',
    '价格': 'total',
    '首付百分比': None,
    '厂房结构': 'Plantstructure',
    '厂房类型': 'Typewarehouse',
    '厂房标签': None,
    '厂房新旧': 'Plantnew',
    '可办环评': 'Planteia',
    '性质': 'isNewHouse',
    '中介费': 'AgentFree',
    '产权': None,
    '建筑年代': 'years',
    '产权年限': 'Type4Years',
    '土地性质': 'Landnature',
    '供电容量': 'Powersupply',
    '楼板承重': 'Loadbearing'
}

def base62_encode(num):
    if num == 0:
        return BASE62[0]

    arr = []
    base = len(BASE62)

    while num:
        num, rem = divmod(num, base)
        arr.append(BASE62[rem])

    arr.reverse()
    return ''.join(arr)

def convert_to_native_types(obj):
    if isinstance(obj, dict):
        return {key: convert_to_native_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_native_types(item) for item in obj]
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64, np.float32)):
        return float(obj)
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, np.ndarray):
        return convert_to_native_types(obj.tolist())
    elif pd.isna(obj):
        return None
    else:
        return obj

class FactoryUploadService:
    @staticmethod
    async def parse_excel_file(file: UploadFile, save_to_db: bool = False):
        if not file.filename.endswith(('.xlsx', '.xls')):
            return {
                "success": False,
                "message": "请上传.xlsx或.xls格式的Excel文件"
            }

        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp:
                # 保存上传的文件
                temp.write(await file.read())
                temp_path = temp.name

            # 读取Excel文件
            try:
                # 读取Excel文件，使用第一行作为列名
                df = pd.read_excel(temp_path, header=0)

                # 检查是否为空
                if df.empty:
                    logger.error("上传的Excel文件是空的")
                    return {
                        "success": False,
                        "message": "上传的Excel文件没有数据"
                    }

                num_columns = len(df.columns)
                logger.info(f"Excel文件有 {num_columns} 列")

                num_rows = len(df)
                logger.info(f"Excel文件有 {num_rows} 行数据")

                try:
                    logger.info(f"列名: {', '.join(str(col) for col in df.columns)}")

                    all_records = []
                    db_records = []
                    city_list = set()
                    city_count = {}

                    # 处理所有行数据
                    for idx, row in df.iterrows():
                        row_data = {}

                        for column in df.columns:
                            value = row[column]

                            if pd.isna(column) or column == '':
                                continue

                            field_name = str(column).strip()

                            if not pd.isna(value) and value != '':
                                api_value = convert_to_native_types(value)
                                if isinstance(api_value, (int, float)):
                                    row_data[field_name] = str(api_value)
                                else:
                                    row_data[field_name] = api_value

                        if 'id' not in row_data:
                            row_data['id'] = f'F{int(time.time())}-{idx}'
                        if 'type4property' not in row_data:
                            row_data['type4property'] = '13'

                        city = row_data.get('城市', 'unknown')
                        if city:
                            city_list.add(city)
                            city_count[city] = city_count.get(city, 0) + 1

                        all_records.append(row_data)

                        if save_to_db:
                            db_record = FactoryUploadService._convert_to_db_format(row_data, row.to_dict())
                            db_records.append(db_record)

                    parse_id = str(uuid.uuid4())

                    parsed_data_store[parse_id] = {
                        'all_records': all_records,
                        'timestamp': time.time()
                    }

                    def clear_expired_data():
                        current_time = time.time()
                        expired_keys = [k for k, v in parsed_data_store.items()
                                      if current_time - v.get('timestamp', 0) > 600]
                        for key in expired_keys:
                            parsed_data_store.pop(key, None)

                    clear_expired_data()

                    db_result = None
                    if save_to_db and db_records:
                        db_result = FactoryUploadService._save_to_database(db_records)

                    result = {
                        "success": True,
                        "message": f"Excel文件解析成功，共有{num_rows}行数据",
                        "parse_id": parse_id,
                        "all_records": all_records,
                        "city_list": list(city_list),
                        "city_count": city_count,
                        "row_count": int(num_rows)
                    }

                    if save_to_db:
                        result["db_result"] = db_result

                    return result
                except Exception as e:
                    logger.error(f"解析Excel数据异常: {str(e)}")
                    return {
                        "success": False,
                        "message": f"解析Excel数据异常: {str(e)}"
                    }
            except ImportError as e:
                logger.error(f"处理Excel文件失败，缺少必要的库: {str(e)}")
                return {
                    "success": False,
                    "message": f"服务器缺少处理Excel文件所需的库: {str(e)}"
                }
            except Exception as e:
                logger.error(f"处理Excel文件失败: {str(e)}")
                return {
                    "success": False,
                    "message": f"处理Excel文件失败: {str(e)}"
                }
        except Exception as e:
            logger.error(f"保存上传文件失败: {str(e)}")
            return {
                "success": False,
                "message": f"文件上传失败: {str(e)}"
            }
        finally:
            # 删除临时文件
            if 'temp_path' in locals() and os.path.exists(temp_path):
                os.unlink(temp_path)

    @staticmethod
    async def parse_and_save_excel_file(file: UploadFile):
        return await FactoryUploadService.parse_excel_file(file, save_to_db=True)

    @staticmethod
    def check_park_data_availability(community_name: str):
        """
        检查园区的知识库和周边配套数据可用性
        返回: (has_knowledge_base, has_poi_data, warning_type, warning_message)
        """
        try:
            connection = get_db_connection()
            cursor = connection.cursor()

            # 检查知识库数据
            knowledge_base_sql = "SELECT COUNT(*) as count FROM park_info WHERE park_name = %s"
            cursor.execute(knowledge_base_sql, (community_name,))
            knowledge_result = cursor.fetchone()
            has_knowledge_base = knowledge_result.get('count', 0) > 0

            # 检查周边配套数据
            poi_sql = "SELECT COUNT(*) as count FROM search_poi_nearby WHERE center_id LIKE %s"
            cursor.execute(poi_sql, (f"{community_name}_%",))
            poi_result = cursor.fetchone()
            has_poi_data = poi_result.get('count', 0) > 0

            # 根据检查结果确定警告类型和消息
            if not has_knowledge_base and not has_poi_data:
                return has_knowledge_base, has_poi_data, "no_data", "该园区没有知识库和周边配套数据，为了保证生成的内容贴合园区，请联系平台管理员获取对应数据后再生成内容"
            elif not has_knowledge_base:
                return has_knowledge_base, has_poi_data, "partial_data", "该园区没有知识库数据，生成出的内容可能和园区存在偏差"
            elif not has_poi_data:
                return has_knowledge_base, has_poi_data, "partial_data", "该园区没有周边配套数据，生成出的内容可能和园区存在偏差"
            else:
                return has_knowledge_base, has_poi_data, "complete_data", ""

        except Exception as e:
            logger.error(f"检查园区 '{community_name}' 数据可用性失败: {str(e)}")
            return False, False, "error", f"检查数据可用性时发生错误: {str(e)}"
        finally:
            if 'cursor' in locals() and cursor:
                cursor.close()
            if 'connection' in locals() and connection:
                connection.close()

    @staticmethod
    async def generate_community_content(community_name: str):
        try:
            logger.info(f"开始为园区 '{community_name}' 生成标题和描述...")

            # 检查园区数据可用性
            has_knowledge_base, has_poi_data, warning_type, warning_message = FactoryUploadService.check_park_data_availability(community_name)

            # 如果没有任何数据，阻止生成
            if warning_type == "no_data":
                logger.warning(f"园区 '{community_name}' 缺少必要数据: {warning_message}")
                return {
                    "success": False,
                    "message": warning_message,
                    "community_name": community_name,
                    "warning_type": warning_type,
                    "data": {
                        "has_knowledge_base": has_knowledge_base,
                        "has_poi_data": has_poi_data
                    }
                }

            # 如果只有部分数据，返回警告但允许继续
            if warning_type == "partial_data":
                logger.warning(f"园区 '{community_name}' 数据不完整: {warning_message}")
                return {
                    "success": True,
                    "message": "数据检查完成，发现警告",
                    "community_name": community_name,
                    "warning_type": warning_type,
                    "warning_message": warning_message,
                    "data": {
                        "has_knowledge_base": has_knowledge_base,
                        "has_poi_data": has_poi_data,
                        "requires_confirmation": True
                    }
                }

            # 检查是否已有正在进行的任务
            existing_task = get_task_status(community_name)
            if existing_task and existing_task.get('status') in [0, 1]:
                logger.info(f"园区 '{community_name}' 已有内容生成任务正在进行中")
                return {
                    "success": False,
                    "message": f"园区 '{community_name}' 已有内容生成任务正在进行中",
                    "community_name": community_name,
                    "data": None
                }

            # 更新任务状态为进行中
            update_task_status(community_name, 1, 0)

            community_data = get_community_data(community_name)
            if not community_data:
                logger.error(f"找不到园区 '{community_name}' 的数据，无法生成内容")
                update_task_status(community_name, 3, 0, f"找不到园区 '{community_name}' 的数据")
                return {
                    "success": False,
                    "message": f"找不到园区 '{community_name}' 的数据，无法生成内容",
                    "community_name": community_name,
                    "data": None
                }

            async def run_generation():
                try:
                    logger.info(f"为园区 '{community_name}' 生成房源详细描述...")
                    property_descriptions = await run_in_threadpool(
                        generate_all_property_descriptions,
                        community_name,
                        save_to_db=True
                    )

                    logger.info(f"房源描述生成结果长度: {len(property_descriptions) if property_descriptions else 0}")

                    if not property_descriptions or "生成房源详细描述时发生错误" in property_descriptions:
                        error_msg = f"为园区 '{community_name}' 生成详细描述失败: {property_descriptions}"
                        logger.error(error_msg)
                        update_task_status(community_name, 3, 0, error_msg)
                        return

                    logger.info(f"园区 '{community_name}' 内容已成功生成并保存到数据库")

                    update_task_status(community_name, 2)
                    logger.info(f"园区 '{community_name}' 内容生成任务完成")

                except Exception as e:
                    error_msg = f"为园区 '{community_name}' 生成标题和描述时发生错误: {str(e)}"
                    logger.error(error_msg)
                    update_task_status(community_name, 3, 0, str(e))

            asyncio.create_task(run_generation())

            return {
                "success": True,
                "message": f"园区 '{community_name}' 内容生成任务已启动",
                "community_name": community_name,
                "data": {
                    "task_status": "started"
                }
            }

        except Exception as e:
            error_msg = f"为园区 '{community_name}' 启动生成任务失败: {str(e)}"
            logger.error(error_msg)
            update_task_status(community_name, 3, 0, str(e))
            return {
                "success": False,
                "message": error_msg,
                "community_name": community_name,
                "data": None
            }

    @staticmethod
    async def generate_community_content_force(community_name: str):
        """
        强制生成园区内容，跳过数据检查
        """
        try:
            logger.info(f"强制为园区 '{community_name}' 生成标题和描述...")

            # 检查是否已有正在进行的任务
            existing_task = get_task_status(community_name)
            if existing_task and existing_task.get('status') in [0, 1]:
                logger.info(f"园区 '{community_name}' 已有内容生成任务正在进行中")
                return {
                    "success": False,
                    "message": f"园区 '{community_name}' 已有内容生成任务正在进行中",
                    "community_name": community_name,
                    "data": None
                }

            # 更新任务状态为进行中
            update_task_status(community_name, 1, 0)

            community_data = get_community_data(community_name)
            if not community_data:
                logger.error(f"找不到园区 '{community_name}' 的数据，无法生成内容")
                update_task_status(community_name, 3, 0, f"找不到园区 '{community_name}' 的数据")
                return {
                    "success": False,
                    "message": f"找不到园区 '{community_name}' 的数据，无法生成内容",
                    "community_name": community_name,
                    "data": None
                }

            async def run_generation():
                try:
                    logger.info(f"为园区 '{community_name}' 生成房源详细描述...")
                    property_descriptions = await run_in_threadpool(
                        generate_all_property_descriptions,
                        community_name,
                        save_to_db=True
                    )

                    logger.info(f"房源描述生成结果长度: {len(property_descriptions) if property_descriptions else 0}")

                    if not property_descriptions or "生成房源详细描述时发生错误" in property_descriptions:
                        error_msg = f"为园区 '{community_name}' 生成详细描述失败: {property_descriptions}"
                        logger.error(error_msg)
                        update_task_status(community_name, 3, 0, error_msg)
                        return

                    logger.info(f"园区 '{community_name}' 内容已成功生成并保存到数据库")

                    update_task_status(community_name, 2)
                    logger.info(f"园区 '{community_name}' 内容生成任务完成")

                except Exception as e:
                    error_msg = f"为园区 '{community_name}' 生成标题和描述时发生错误: {str(e)}"
                    logger.error(error_msg)
                    update_task_status(community_name, 3, 0, str(e))

            asyncio.create_task(run_generation())

            return {
                "success": True,
                "message": f"园区 '{community_name}' 内容生成任务已启动",
                "community_name": community_name,
                "data": {
                    "task_status": "started"
                }
            }

        except Exception as e:
            error_msg = f"为园区 '{community_name}' 启动生成任务失败: {str(e)}"
            logger.error(error_msg)
            update_task_status(community_name, 3, 0, str(e))
            return {
                "success": False,
                "message": error_msg,
                "community_name": community_name,
                "data": None
            }

    @staticmethod
    def _convert_to_db_format(row_data: Dict, raw_data: Dict) -> Dict:
        timestamp = int(time.time())
        random_value = random.randint(0, 999999)

        ts_encoded = base62_encode(timestamp)
        rand_encoded = base62_encode(random_value)

        erp_id = f"E{ts_encoded}{rand_encoded}"

        record = {
            'erp_id': erp_id,
            'youtui_house_id': None,
            'city_name': None,
            'is_published': 0,
            'create_time': datetime.now(),
            'update_time': datetime.now(),
            'is_deleted': 0,
            'ts_status': 0
        }

        for excel_field, value in row_data.items():
            db_field = EXCEL_TO_DB_MAPPING.get(excel_field)
            if db_field:
                if db_field == 'city':
                    city_name = str(value)
                    record['city_name'] = city_name
                    record[db_field] = CITY_NAME_TO_CODE.get(city_name, city_name)
                else:
                    record[db_field] = value

        floor = 0
        if 'floor' in record:
            try:
                floor = int(record['floor'])
            except (ValueError, TypeError):
                if '楼层' in row_data:
                    try:
                        floor = int(row_data['楼层'])
                    except (ValueError, TypeError):
                        floor = 0

        record['floortype'] = '3' if floor > 1 else '1'
        record['Type4Years'] = '2073'
        record['towards'] = '东南朝向'
        record['isNewHouse'] = '1'

        return record

    @staticmethod
    def _save_to_database(records: List[Dict]) -> Dict:
        if not records:
            return {
                "success": False,
                "message": "没有数据需要保存",
                "rows_saved": 0
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            success_count = 0
            failed_records = []

            for record in records:
                try:
                    columns = ', '.join(record.keys())
                    placeholders = ', '.join(['%s'] * len(record))

                    sql = f"INSERT INTO parsed_factory_data ({columns}) VALUES ({placeholders})"
                    cursor.execute(sql, list(record.values()))
                    success_count += 1
                except Exception as e:
                    logger.error(f"插入记录失败: {str(e)}")
                    error_info = {
                        "error": str(e),
                        "record_keys": list(record.keys())
                    }
                    failed_records.append(error_info)

            conn.commit()
            logger.info(f"成功导入 {success_count}/{len(records)} 条记录到数据库")

            return {
                "success": True,
                "message": f"成功导入 {success_count}/{len(records)} 条记录到数据库",
                "rows_saved": success_count,
                "rows_failed": len(records) - success_count,
                "failed_records": failed_records if len(failed_records) > 0 else None
            }

        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库导入失败: {str(e)}")
            return {
                "success": False,
                "message": f"数据库导入失败: {str(e)}",
                "rows_saved": 0
            }

        finally:
            if conn:
                conn.close()

    @staticmethod
    async def get_imported_factory_data(page=1, page_size=10, is_published=None, has_content=None, has_gallery=None, has_knowledge_base=None, has_poi_data=None, city=None):
        try:
            connection = get_db_connection()
            cursor = connection.cursor()

            where_conditions = []
            params = []

            if is_published is not None:
                where_conditions.append("is_published = %s")
                params.append(is_published)

            if has_content is not None:
                if has_content == 1:
                    where_conditions.append("(topic IS NOT NULL AND content IS NOT NULL AND topic != '' AND content != '')")
                else:
                    where_conditions.append("(topic IS NULL OR content IS NULL OR topic = '' OR content = '')")

            # 添加城市筛选条件
            if city is not None and city.strip():
                where_conditions.append("city = %s")
                params.append(city.strip())

            gallery_sql = "SELECT DISTINCT park_name FROM park_gallery_unified WHERE park_name IS NOT NULL AND park_name != ''"
            cursor.execute(gallery_sql)
            gallery_data = cursor.fetchall()
            gallery_communities = [item['park_name'] for item in gallery_data]

            knowledge_base_sql = "SELECT DISTINCT park_name FROM park_info WHERE park_name IS NOT NULL AND park_name != ''"
            cursor.execute(knowledge_base_sql)
            knowledge_base_data = cursor.fetchall()
            knowledge_base_communities = [item['park_name'] for item in knowledge_base_data]

            poi_sql = "SELECT DISTINCT center_id FROM search_poi_nearby WHERE center_id IS NOT NULL AND center_id != ''"
            cursor.execute(poi_sql)
            poi_data = cursor.fetchall()
            poi_communities = []
            for item in poi_data:
                center_id = item['center_id']
                if '_' in center_id:
                    park_name = center_id.split('_')[0]
                    if park_name and park_name not in poi_communities:
                        poi_communities.append(park_name)

            check_communities_sql = "SELECT DISTINCT community FROM parsed_factory_data WHERE community IS NOT NULL AND community != ''"
            cursor.execute(check_communities_sql)
            all_communities_data = cursor.fetchall()
            all_communities = [item['community'] for item in all_communities_data]

            matching_communities = [community for community in all_communities if community in gallery_communities]
            knowledge_base_matching_communities = [community for community in all_communities if community in knowledge_base_communities]
            poi_matching_communities = [community for community in all_communities if community in poi_communities]

            if has_gallery is not None:
                if gallery_communities:
                    if has_gallery == 1:
                        placeholders = ', '.join(['%s'] * len(gallery_communities))
                        where_conditions.append(f"community IN ({placeholders})")
                        params.extend(gallery_communities)
                    else:
                        placeholders = ', '.join(['%s'] * len(gallery_communities))
                        where_conditions.append(f"(community IS NULL OR community NOT IN ({placeholders}))")
                        params.extend(gallery_communities)
                elif has_gallery == 1:
                    where_conditions.append("1=0")

            if has_knowledge_base is not None:
                if knowledge_base_communities:
                    if has_knowledge_base == 1:
                        placeholders = ', '.join(['%s'] * len(knowledge_base_communities))
                        where_conditions.append(f"community IN ({placeholders})")
                        params.extend(knowledge_base_communities)
                    else:
                        placeholders = ', '.join(['%s'] * len(knowledge_base_communities))
                        where_conditions.append(f"(community IS NULL OR community NOT IN ({placeholders}))")
                        params.extend(knowledge_base_communities)
                elif has_knowledge_base == 1:
                    where_conditions.append("1=0")

            if has_poi_data is not None:
                if poi_communities:
                    if has_poi_data == 1:
                        placeholders = ', '.join(['%s'] * len(poi_communities))
                        where_conditions.append(f"community IN ({placeholders})")
                        params.extend(poi_communities)
                    else:
                        placeholders = ', '.join(['%s'] * len(poi_communities))
                        where_conditions.append(f"(community IS NULL OR community NOT IN ({placeholders}))")
                        params.extend(poi_communities)
                elif has_poi_data == 1:
                    where_conditions.append("1=0")

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            count_sql = f"SELECT COUNT(*) as total FROM parsed_factory_data {where_clause}"
            cursor.execute(count_sql, params)
            total_result = cursor.fetchone()
            total_records = total_result.get('total', 0)

            total_pages = (total_records + page_size - 1) // page_size

            if page < 1:
                page = 1
            if page > total_pages and total_pages > 0:
                page = total_pages

            offset = (page - 1) * page_size

            sql = f"""
            SELECT
                id, erp_id, community, city, zone,
                CASE WHEN topic IS NOT NULL AND content IS NOT NULL AND topic != '' AND content != ''
                    THEN 1 ELSE 0 END as has_marketing_content,
                DATE_FORMAT(create_time, '%%Y-%%m-%%d %%H:%%i:%%s') as created_at,
                is_published, youtui_house_id
            FROM
                parsed_factory_data
            {where_clause}
            ORDER BY
                create_time DESC
            LIMIT %s OFFSET %s
            """

            query_params = params.copy()
            query_params.extend([page_size, offset])

            cursor.execute(sql, tuple(query_params))
            factory_data = cursor.fetchall()

            community_names = [item['community'] for item in factory_data if item.get('community')]

            if has_content is not None:
                sql_stats = f"""
                SELECT COUNT(*) as total FROM parsed_factory_data {where_clause}
                """
                cursor.execute(sql_stats, params)
                stats_data = cursor.fetchone()
                total_records = stats_data.get('total', 0)

                if has_content == 1:
                    total_with_content = total_records
                    total_without_content = 0
                else:
                    total_with_content = 0
                    total_without_content = total_records
            else:
                sql_stats = f"""
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN topic IS NOT NULL AND content IS NOT NULL AND topic != '' AND content != ''
                        THEN 1 ELSE 0 END) as with_content
                FROM
                    parsed_factory_data
                {where_clause}
                """
                cursor.execute(sql_stats, params)
                stats_data = cursor.fetchone()

                total_records = stats_data.get('total', 0)
                total_with_content = stats_data.get('with_content', 0)
                total_without_content = total_records - total_with_content

            if has_gallery is not None:
                if has_gallery == 1:
                    total_with_gallery = len(matching_communities)
                    without_gallery = 0
                    with_gallery_percent = 100.0 if total_records > 0 else 0
                else:
                    total_with_gallery = 0
                    without_gallery = total_records - len(matching_communities)
                    with_gallery_percent = 0.0
            else:
                for item in factory_data:
                    community_name = item.get('community')
                    item['has_gallery'] = community_name and community_name in matching_communities
                    item['has_knowledge_base'] = community_name and community_name in knowledge_base_matching_communities
                    item['has_poi_data'] = community_name and community_name in poi_matching_communities

            community_where_conditions = where_conditions.copy()
            community_where_conditions.append("community IS NOT NULL AND community != ''")
            community_where_clause = "WHERE " + " AND ".join(community_where_conditions) if community_where_conditions else ""

            filtered_communities_sql = f"""
            SELECT DISTINCT community FROM parsed_factory_data
            {community_where_clause}
            """
            cursor.execute(filtered_communities_sql, params)
            filtered_communities_data = cursor.fetchall()
            filtered_communities = [item['community'] for item in filtered_communities_data]

            filtered_matching_communities = [community for community in filtered_communities if community in gallery_communities]
            filtered_knowledge_base_communities = [community for community in filtered_communities if community in knowledge_base_communities]
            filtered_poi_communities = [community for community in filtered_communities if community in poi_communities]

            total_with_gallery = len(filtered_matching_communities)
            without_gallery = total_records - total_with_gallery
            with_gallery_percent = round(total_with_gallery / total_records * 100, 1) if total_records > 0 else 0

            total_with_knowledge_base = len(filtered_knowledge_base_communities)
            without_knowledge_base = total_records - total_with_knowledge_base
            with_knowledge_base_percent = round(total_with_knowledge_base / total_records * 100, 1) if total_records > 0 else 0

            total_with_poi_data = len(filtered_poi_communities)
            without_poi_data = total_records - total_with_poi_data
            with_poi_data_percent = round(total_with_poi_data / total_records * 100, 1) if total_records > 0 else 0

            final_statistics = {
                "total": total_records,
                "with_content": total_with_content,
                "without_content": total_without_content,
                "with_content_percent": round(total_with_content / total_records * 100, 1) if total_records > 0 else 0,
                "with_gallery": total_with_gallery,
                "without_gallery": without_gallery,
                "with_gallery_percent": with_gallery_percent,
                "with_knowledge_base": total_with_knowledge_base,
                "without_knowledge_base": without_knowledge_base,
                "with_knowledge_base_percent": with_knowledge_base_percent,
                "with_poi_data": total_with_poi_data,
                "without_poi_data": without_poi_data,
                "with_poi_data_percent": with_poi_data_percent
            }

            return {
                "success": True,
                "message": "获取房源数据成功",
                "data": factory_data,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total_records,
                    "total_pages": total_pages
                },
                "statistics": final_statistics
            }

        except Exception as e:
            logger.error(f"获取房源数据失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取房源数据失败: {str(e)}"
            }
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'connection' in locals():
                connection.close()

    @staticmethod
    async def get_factory_detail(community_name):
        try:
            if not community_name:
                return {
                    "success": False,
                    "message": "请提供园区名称参数",
                    "data": None
                }

            connection = get_db_connection()
            cursor = connection.cursor()

            # 根据园区名称查询单条记录
            sql = """
            SELECT
                city, city_name,
                topic, zone, street, type, community, content,
                total, square, address, floor, rentunitdaily
            FROM parsed_factory_data
            WHERE community = %s AND is_deleted = 0
            LIMIT 1
            """
            cursor.execute(sql, (community_name,))
            factory_data = cursor.fetchone()

            if not factory_data:
                return {
                    "success": False,
                    "message": f"未找到园区名称为 {community_name} 的厂房数据",
                    "data": None
                }

            return {
                "success": True,
                "message": "获取园区厂房数据成功",
                "data": factory_data
            }

        except Exception as e:
            logger.error(f"获取厂房详细数据失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取厂房详细数据失败: {str(e)}",
                "data": None
            }
        finally:
            if 'cursor' in locals() and cursor:
                cursor.close()
            if 'connection' in locals() and connection:
                connection.close()

    @staticmethod
    async def get_available_cities():
        """
        获取数据库中所有可用的城市列表
        """
        try:
            connection = get_db_connection()
            cursor = connection.cursor()

            # 查询所有不为空的城市，按城市名称排序
            sql = """
            SELECT DISTINCT city
            FROM parsed_factory_data
            WHERE city IS NOT NULL AND city != '' AND is_deleted = 0
            ORDER BY city
            """
            cursor.execute(sql)
            cities_data = cursor.fetchall()

            # 提取城市名称列表
            cities = [item['city'] for item in cities_data]

            return {
                "success": True,
                "message": f"获取城市列表成功，共 {len(cities)} 个城市",
                "data": cities
            }

        except Exception as e:
            logger.error(f"获取城市列表失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取城市列表失败: {str(e)}",
                "data": []
            }
        finally:
            if 'cursor' in locals() and cursor:
                cursor.close()
            if 'connection' in locals() and connection:
                connection.close()

    @staticmethod
    async def publish_unpublished_data(erp_ids=None, limit=10):
        try:
            connection = get_db_connection()
            cursor = connection.cursor()

            if erp_ids:
                placeholders = ', '.join(['%s'] * len(erp_ids))
                sql = f"""
                SELECT * FROM parsed_factory_data
                WHERE erp_id IN ({placeholders}) AND is_deleted = 0
                """
                cursor.execute(sql, erp_ids)
            else:
                sql = """
                SELECT * FROM parsed_factory_data
                WHERE is_published = 0 AND is_deleted = 0
                LIMIT %s
                """
                cursor.execute(sql, (limit,))

            unpublished_records = cursor.fetchall()

            if not unpublished_records:
                return {
                    "success": True,
                    "message": "没有未发布的数据",
                    "total": 0,
                    "success_count": 0,
                    "failed_count": 0,
                    "success_ids": [],
                    "failed_items": []
                }

            success_count = 0
            failed_count = 0
            success_ids = []
            failed_items = []

            for record in unpublished_records:
                try:
                    city = record.get('city')
                    city_name = record.get('city_name')
                    erp_id = record.get('erp_id')

                    factory_data = {}

                    fields_to_copy = [
                        'topic', 'zone', 'street', 'type', 'content',
                        'total', 'square', 'type4property', 'floor',
                        'floortype', 'Storey', 'rentunitdaily', 'slease', 'Planteia',
                        'Plantstructure', 'Typewarehouse', 'Plantnew', 'isNewHouse',
                        'AgentFree', 'years', 'Type4Years', 'Landnature', 'Powersupply',
                        'Loadbearing', 'video_url'
                    ]

                    for field in fields_to_copy:
                        if field in record and record[field] is not None:
                            factory_data[field] = record[field]

                    # 检查园区是否已完成视频生成
                    community_name = record.get('community')
                    video_url = record.get('video_url')
                    if not video_url or video_url.strip() == '':
                        # 如果没有视频URL，跳过该记录
                        failed_count += 1
                        failed_items.append({"erp_id": erp_id, "reason": "请先完成视频生成再进行发布"})
                        logger.warning(f"园区 '{community_name}' 没有视频URL，跳过发布")
                        continue

                    # 检查园区是否已完成内容生成
                    topic = record.get('topic')
                    content = record.get('content')
                    if not topic or topic.strip() == '' or not content or content.strip() == '':
                        # 如果没有完整的标题和内容，跳过该记录
                        failed_count += 1
                        failed_items.append({"erp_id": erp_id, "reason": "请先完成内容生成再进行发布"})
                        logger.warning(f"园区 '{community_name}' 没有完整的标题和内容，跳过发布")
                        continue

                    # 字段名称转换：将video_url转换为videoUrl（camelCase格式）以匹配优推API要求
                    if 'video_url' in factory_data and factory_data['video_url']:
                        factory_data['videoUrl'] = factory_data['video_url']
                        del factory_data['video_url']  # 删除原始字段，避免重复

                    factory_data['community'] = record['address']
                    factory_data['address'] = record['address']

                    # 使用新的混合图片获取服务，随机从两个表中选择图片
                    if community_name:
                        # 使用新的图片服务获取并分配图片
                        # 策略: 'random_table' - 随机选择一个表
                        image_fields, strategy_desc = gallery_image_service.get_assigned_gallery_images(
                            park_name=community_name,
                            strategy='random_table'
                        )

                        # 检查是否获取到图片
                        has_images = any(image_fields.get(field) for field in ['thumb', 'photooutdoor', 'photointerior', 'floorplans'])

                        if has_images:
                            # 将图片分配给工厂数据
                            factory_data['thumb'] = image_fields.get('thumb', '')
                            factory_data['photooutdoor'] = image_fields.get('photooutdoor', '')
                            factory_data['photointerior'] = image_fields.get('photointerior', '')
                            factory_data['floorplans'] = image_fields.get('floorplans', '')

                            # 确保每个字段至少有一张图片（如果有thumb的话）
                            if factory_data['thumb']:
                                if not factory_data.get('photooutdoor'):
                                    factory_data['photooutdoor'] = factory_data['thumb']
                                if not factory_data.get('photointerior'):
                                    factory_data['photointerior'] = factory_data['thumb']
                                if not factory_data.get('floorplans'):
                                    factory_data['floorplans'] = factory_data['thumb']
                        else:
                            # 如果没有获取到任何图片，跳过该记录
                            failed_count += 1
                            failed_items.append({"erp_id": erp_id, "reason": "该园区还未录入图库，请联系销售"})
                            logger.warning(f"园区 '{community_name}' 没有任何图片，跳过发布")
                            continue

                    # 将朝向写死为"东南朝向"
                    factory_data['towards'] = "东南朝向"

                    factory_data['id'] = erp_id

                    # 获取优推服务实例
                    service = get_youtui_service(city=city, city_name=city_name)
                    user_id = service.get_user_id(service.city)
                    userkey = service.get_user_key(service.city)

                    # 调用异步优推接口发布
                    publish_result = await service.async_publish_factory(
                        user_id=user_id, userkey=userkey, factory_data=factory_data
                    )

                    if publish_result:
                        # 发布成功，更新数据库记录
                        youtui_house_id = publish_result
                        update_sql = """
                        UPDATE parsed_factory_data
                        SET is_published = 1,
                            youtui_house_id = %s,
                            publish_time = NOW(),
                            update_time = NOW()
                        WHERE erp_id = %s
                        """
                        cursor.execute(
                            update_sql,
                            (youtui_house_id, erp_id)
                        )
                        connection.commit()

                        # 记录成功
                        success_count += 1
                        success_ids.append(youtui_house_id)
                        logger.info(f"成功发布厂房数据，ERP ID: {erp_id}, 优推房源ID: {youtui_house_id}")
                    else:
                        # 发布失败，只记录失败信息，不更新数据库
                        failed_count += 1
                        failed_items.append({"erp_id": erp_id, "reason": "发布失败"})
                        logger.error(f"发布厂房数据失败，ERP ID: {erp_id}")
                except Exception as e:
                    # 处理单条记录发布异常，只记录失败信息，不更新数据库
                    logger.error(f"处理记录时发生异常，ERP ID: {record.get('erp_id', 'unknown')}, 错误: {str(e)}")
                    failed_count += 1
                    failed_items.append({"erp_id": record.get('erp_id', 'unknown'), "reason": str(e)})

            # 构建返回消息
            message = f"处理完成，成功: {success_count}, 失败: {failed_count}"

            # 如果有失败项目，添加相应的提示
            if failed_count > 0:
                if any("该园区还未录入图库，请联系销售" in item.get("reason", "") for item in failed_items):
                    message += "\n\u6ce8意：部分园区还未录入图库，请联系销售进行图库录入"
                if any("请先完成视频生成再进行发布" in item.get("reason", "") for item in failed_items):
                    message += "\n\u6ce8意：部分园区还未生成视频，请先完成视频生成再进行发布"
                if any("请先完成内容生成再进行发布" in item.get("reason", "") for item in failed_items):
                    message += "\n\u6ce8意：部分园区还未生成内容，请先完成内容生成再进行发布"

            # 返回总体结果
            return {
                "success": True,
                "message": message,
                "total": len(unpublished_records),
                "success_count": success_count,
                "failed_count": failed_count,
                "success_ids": success_ids,
                "failed_items": failed_items
            }

        except Exception as e:
            logger.error(f"发布未发布数据时发生异常: {str(e)}")
            return {
                "success": False,
                "message": f"发布数据异常: {str(e)}",
                "total": 0,
                "success_count": 0,
                "failed_count": 0,
                "success_ids": [],
                "failed_items": []
            }
        finally:
            if 'cursor' in locals() and cursor:
                cursor.close()
            if 'connection' in locals() and connection:
                connection.close()