#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
远程视频生成服务
调用远程API生成视频，替代本地MoviePy实现
"""

import requests
import time
import logging
from typing import Dict, List, Optional
import traceback

from config.video_config import REMOTE_VIDEO_SERVICE
from database.db_connection import get_db_connection

# 配置日志
logger = logging.getLogger(__name__)

class RemoteVideoService:
    """远程视频生成服务客户端"""
    
    def __init__(self):
        self.base_url = REMOTE_VIDEO_SERVICE["base_url"]
        self.timeout = REMOTE_VIDEO_SERVICE["timeout"]
        self.retry_config = REMOTE_VIDEO_SERVICE["retry"]
        self.polling_config = REMOTE_VIDEO_SERVICE["polling"]
        self.defaults = REMOTE_VIDEO_SERVICE["defaults"]
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """发送HTTP请求，包含重试机制"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        for attempt in range(self.retry_config["max_attempts"]):
            try:
                response = requests.request(
                    method=method,
                    url=url,
                    timeout=(self.timeout["connect"], self.timeout["read"]),
                    **kwargs
                )
                response.raise_for_status()
                return response
                
            except requests.exceptions.RequestException as e:
                if attempt == self.retry_config["max_attempts"] - 1:
                    raise
                
                wait_time = self.retry_config["delay"] * (self.retry_config["backoff"] ** attempt)
                logger.warning(f"请求失败，{wait_time}秒后重试 (尝试 {attempt + 1}/{self.retry_config['max_attempts']}): {str(e)}")
                time.sleep(wait_time)
    
    def create_task(self, name: str) -> str:
        """创建远程视频生成任务"""
        response = self._make_request(
            "POST", 
            "/tasks/create",
            json={"mode": "image", "name": name}
        )
        return response.json()["data"]["task_id"]
    
    def upload_images_and_generate(self, task_id: str, image_urls: List[str], config: Dict) -> bool:
        """使用图片URL启动视频生成"""
        # 合并默认配置和用户配置
        generation_config = {**self.defaults, **config}
        
        response = self._make_request(
            "POST",
            "/image-mode/generate",
            json={
                "task_id": task_id,
                "image_files": image_urls,  # 直接使用图片URL
                "config": generation_config
            }
        )
        return response.status_code == 200
    
    def get_task_status(self, task_id: str) -> Dict:
        """获取任务状态"""
        response = self._make_request("GET", f"/tasks/{task_id}/status")
        return response.json()
    
    def get_task_result(self, task_id: str) -> Dict:
        """获取任务结果"""
        response = self._make_request("GET", f"/tasks/{task_id}/result")
        return response.json()
    
    def wait_for_completion(self, task_id: str, thread_prefix: str = "") -> Dict:
        """等待任务完成并返回结果"""
        start_time = time.time()
        max_wait = self.polling_config["max_wait"]
        interval = self.polling_config["interval"]
        
        while time.time() - start_time < max_wait:
            try:
                status_response = self.get_task_status(task_id)
                status = status_response.get("status")
                progress = status_response.get("progress", 0)
                
                logger.info(f"{thread_prefix}远程任务状态: {status}, 进度: {progress}%")
                
                if status == "completed":
                    return self.get_task_result(task_id)
                elif status == "failed":
                    return {"success": False, "message": "远程任务执行失败"}
                
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"{thread_prefix}查询远程任务状态失败: {str(e)}")
                time.sleep(interval)
        
        return {"success": False, "message": f"任务超时，等待时间超过{max_wait}秒"}

def generate_campus_video_remote(
    campus_name: str,
    fps: int = 30,
    duration_per_image: float = 3.0,
    transition_type: str = "push_right",
    transition_duration: float = 1.0,
    thread_id: Optional[str] = None
) -> dict:
    """
    使用远程服务生成园区视频
    
    参数:
        campus_name: 园区名称
        fps: 每秒帧数
        duration_per_image: 每张图片显示的时间(秒)
        transition_type: 转场效果类型
        transition_duration: 转场效果持续时间(秒)
        thread_id: 线程ID，用于日志标识(可选)
    
    返回:
        任务结果
    """
    thread_prefix = f"[{thread_id}] " if thread_id else ""
    conn = None
    cursor = None
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询园区的图片 - 从park_gallery_unified表获取图片
        query = """
        SELECT park_name, images_json
        FROM park_gallery_unified
        WHERE park_name = %s
        """
        
        cursor.execute(query, (campus_name,))
        result = cursor.fetchone()

        if not result or not result.get('images_json'):
            return {
                "success": False,
                "message": f"未找到园区 '{campus_name}' 的图片数据",
                "data": None
            }



        # 解析JSON数据获取图片URL
        import json
        try:
            images_data = json.loads(result['images_json'])
            raw_images = images_data.get("images", [])
        except Exception as e:
            return {
                "success": False,
                "message": f"图片数据格式错误: {str(e)}",
                "data": None
            }

        # 收集所有有效的图片URL
        all_image_urls = []
        
        for img in raw_images:
            image_url = img.get("url", "").strip()
            if image_url:
                # 验证URL是否可访问
                try:
                    response = requests.head(image_url, timeout=10)
                    if response.status_code == 200:
                        all_image_urls.append(image_url)
                except Exception as e:
                    pass
        
        # 检查是否有足够的有效图片
        min_required_images = 2
        if len(all_image_urls) < min_required_images:
            return {
                "success": False,
                "message": f"园区 '{campus_name}' 有效图片不足，需要至少{min_required_images}张，实际获得{len(all_image_urls)}张",
                "data": None
            }
        

        
        # 创建远程视频服务实例
        video_service = RemoteVideoService()
        
        # 创建远程任务
        task_name = f"园区视频_{campus_name}_{int(time.time())}"
        remote_task_id = video_service.create_task(task_name)

        
        # 准备视频生成配置
        video_config = {
            "fps": fps,
            "image_duration": duration_per_image,
            "transition_type": transition_type,
            "voice_id": "male-qn-qingse",
            "emotion": "happy"
        }
        
        # 启动远程视频生成
        success = video_service.upload_images_and_generate(remote_task_id, all_image_urls, video_config)
        if not success:
            return {
                "success": False,
                "message": "启动远程视频生成失败",
                "data": None
            }
        
        logger.info(f"{thread_prefix}远程视频生成任务已启动，等待完成...")
        
        # 等待任务完成
        result = video_service.wait_for_completion(remote_task_id, thread_prefix)
        
        if not result.get("success"):
            return {
                "success": False,
                "message": f"远程视频生成失败: {result.get('message', '未知错误')}",
                "data": None
            }
        
        # 获取生成的视频URL（远程服务已上传到对象存储）
        video_url = result.get("data", {}).get("download_url")
        if not video_url:
            return {
                "success": False,
                "message": "远程服务未返回视频URL",
                "data": None
            }

        logger.info(f"{thread_prefix}远程视频生成成功，视频URL: {video_url}")

        # 直接更新数据库，不需要重新下载和上传
        try:
            update_query = """
            UPDATE parsed_factory_data
            SET video_url = %s
            WHERE community = %s
            """
            cursor.execute(update_query, (video_url, campus_name))
            conn.commit()

            logger.info(f"{thread_prefix}视频URL {video_url} 已成功保存到数据库")

            return {
                "success": True,
                "message": f"园区视频生成成功，并已更新到数据库",
                "data": {
                    "video_url": video_url
                }
            }

        except Exception as e:
            logger.error(f"{thread_prefix}更新数据库失败: {str(e)}")
            return {
                "success": False,
                "message": f"视频生成成功，但数据库更新失败: {str(e)}",
                "data": {
                    "video_url": video_url
                }
            }
    
    except Exception as e:
        logger.error(f"{thread_prefix}为园区生成视频失败: {str(e)}")
        logger.error(f"{thread_prefix}详细错误: {traceback.format_exc()}")
        if conn:
            conn.rollback()
        return {
            "success": False,
            "message": f"为园区生成视频失败: {str(e)}",
            "data": None
        }
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


def generate_video_with_assigned_images(
    campus_name: str,
    image_urls: List[str],
    fps: int = 30,
    duration_per_image: float = 3.0,
    transition_type: str = "push_right",
    transition_duration: float = 1.0,
    thread_id: Optional[str] = None
) -> dict:
    """
    使用指定的图片URL列表生成园区视频

    参数:
        campus_name: 园区名称
        image_urls: 图片URL列表
        fps: 每秒帧数
        duration_per_image: 每张图片显示的时间(秒)
        transition_type: 转场效果类型
        transition_duration: 转场效果持续时间(秒)
        thread_id: 线程ID，用于日志标识(可选)

    返回:
        任务结果
    """
    thread_prefix = f"[{thread_id}] " if thread_id else ""
    conn = None
    cursor = None

    try:
        # 过滤和验证图片URL
        valid_image_urls = []
        logger.info(f"{thread_prefix}开始验证 {len(image_urls)} 张分配的图片URL")

        for image_url in image_urls:
            if image_url and image_url.strip():
                # 验证URL是否可访问
                try:
                    response = requests.head(image_url, timeout=10)
                    if response.status_code == 200:
                        valid_image_urls.append(image_url)
                        logger.info(f"{thread_prefix}添加有效图片URL: {image_url}")
                    else:
                        logger.warning(f"{thread_prefix}图片URL不可访问: {image_url} (状态码: {response.status_code})")
                except Exception as e:
                    logger.warning(f"{thread_prefix}验证图片URL失败: {image_url} - {str(e)}")

        # 检查是否有足够的有效图片
        min_required_images = 2
        if len(valid_image_urls) < min_required_images:
            return {
                "success": False,
                "message": f"园区 '{campus_name}' 有效图片不足，需要至少{min_required_images}张，实际获得{len(valid_image_urls)}张",
                "data": None
            }

        logger.info(f"{thread_prefix}准备使用 {len(valid_image_urls)} 张有效图片生成视频")

        # 创建远程视频服务实例
        video_service = RemoteVideoService()

        # 创建远程任务
        task_name = f"推送视频_{campus_name}_{int(time.time())}"
        remote_task_id = video_service.create_task(task_name)
        logger.info(f"{thread_prefix}创建远程任务成功: {remote_task_id}")

        # 准备视频生成配置
        video_config = {
            "fps": fps,
            "image_duration": duration_per_image,
            "transition_type": transition_type,
            "voice_id": "male-qn-qingse",
            "emotion": "happy"
        }

        # 启动远程视频生成
        success = video_service.upload_images_and_generate(remote_task_id, valid_image_urls, video_config)
        if not success:
            return {
                "success": False,
                "message": "启动远程视频生成失败",
                "data": None
            }

        logger.info(f"{thread_prefix}远程视频生成任务已启动，等待完成...")

        # 等待任务完成
        result = video_service.wait_for_completion(remote_task_id, thread_prefix)

        if not result.get("success"):
            return {
                "success": False,
                "message": f"远程视频生成失败: {result.get('message', '未知错误')}",
                "data": None
            }

        # 获取生成的视频URL（远程服务已上传到对象存储）
        video_url = result.get("data", {}).get("download_url")
        if not video_url:
            return {
                "success": False,
                "message": "远程服务未返回视频URL",
                "data": None
            }

        logger.info(f"{thread_prefix}远程视频生成成功，视频URL: {video_url}")

        # 更新数据库中的video_url
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            update_query = """
            UPDATE parsed_factory_data
            SET video_url = %s
            WHERE community = %s
            """
            cursor.execute(update_query, (video_url, campus_name))
            conn.commit()

            logger.info(f"{thread_prefix}视频URL {video_url} 已成功保存到数据库")

            return {
                "success": True,
                "message": f"园区视频生成成功，并已更新到数据库",
                "data": {
                    "video_url": video_url
                }
            }

        except Exception as e:
            logger.error(f"{thread_prefix}更新数据库失败: {str(e)}")
            return {
                "success": False,
                "message": f"视频生成成功，但数据库更新失败: {str(e)}",
                "data": {
                    "video_url": video_url
                }
            }

    except Exception as e:
        logger.error(f"{thread_prefix}使用分配图片生成视频时发生异常: {str(e)}")
        logger.error(f"{thread_prefix}详细错误: {traceback.format_exc()}")
        return {
            "success": False,
            "message": f"使用分配图片生成视频时发生异常: {str(e)}",
            "data": None
        }

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
