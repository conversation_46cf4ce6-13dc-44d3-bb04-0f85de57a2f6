#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
房本认证二维码对象存储服务
专门用于处理房本认证二维码的上传和URL生成
"""

import os
import logging
import base64
import requests
from typing import Dict, Tuple, Union, Optional
import mimetypes
import hashlib
import time

from utils.oss_utils import OssClient
from config.oss_settings import (
    ACCESS_KEY_ID, 
    ACCESS_KEY_SECRET, 
    ENDPOINT, 
    BUCKET_NAME,
    ALLOWED_FILE_TYPES,
    FILE_SIZE_LIMITS
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class QrcodeOssService:
    """房本认证二维码对象存储服务类"""
    
    def __init__(self):
        """初始化OSS服务"""
        # 创建自定义的OssClient实例
        self.client = self._create_custom_client()
        logger.info("初始化房本认证二维码OSS服务")
    
    def _create_custom_client(self):
        """创建自定义的OssClient实例，重写URL生成方法"""
        client = OssClient(
            access_key_id=ACCESS_KEY_ID,
            access_key_secret=ACCESS_KEY_SECRET,
            endpoint=ENDPOINT,
            bucket_name=BUCKET_NAME
        )
        
        # 保存原始的_get_file_url方法
        original_get_file_url = client._get_file_url
        
        # 定义新的URL生成方法，确保不包含临时目录
        def custom_get_file_url(oss_file_path):
            # 对于房本认证二维码（q开头加七位哈希值，不带扩展名），直接使用简短URL
            if oss_file_path.startswith('q') and len(oss_file_path) == 8:
                return f"https://ld-erp.oss-cn-beijing.aliyuncs.com/{oss_file_path}"
            # 对于其他文件，使用原始方法
            return original_get_file_url(oss_file_path)
        
        # 替换方法
        client._get_file_url = custom_get_file_url
        
        return client
    
    def upload_qrcode_base64(self, base64_data: str, username: str, process_id: str) -> Tuple[bool, Union[str, Dict]]:
        """
        上传Base64编码的二维码图片
        
        Args:
            base64_data: Base64编码的图片数据
            username: 用户名
            process_id: 进程ID
            
        Returns:
            上传状态和结果信息
        """
        try:
            # 提取Base64数据
            if "," in base64_data:
                header, encoded = base64_data.split(",", 1)
                image_data = base64.b64decode(encoded)
            else:
                image_data = base64.b64decode(base64_data)
            
            # 生成简短文件名，不带扩展名
            filename = self._generate_short_filename(username, process_id)
            
            # 检查数据大小
            data_size = len(image_data)
            size_limit = FILE_SIZE_LIMITS.get('image', 0)
            
            if size_limit > 0 and data_size > size_limit:
                return False, {
                    "error": f"数据过大: {data_size} 字节，限制: {size_limit} 字节"
                }
            
            # 上传二进制数据，即使没有扩展名，也强制指定Content-Type
            content_type = "image/png"  # 强制使用PNG格式的Content-Type
            headers = {'Content-Type': content_type}
            success, result = self.client.upload_binary(image_data, filename, content_type)
            
            if success:
                # 返回成功结果
                return True, {
                    "url": result,
                    "path": filename,
                    "size": data_size,
                    "type": content_type,
                    "name": filename
                }
            else:
                return False, {"error": result}
        
        except Exception as e:
            logger.error(f"房本认证二维码上传服务发生异常: {str(e)}")
            return False, {"error": str(e)}
    
    def upload_qrcode_url(self, image_url: str, username: str, process_id: str) -> Tuple[bool, Union[str, Dict]]:
        """
        从URL下载并上传二维码图片
        
        Args:
            image_url: 图片URL
            username: 用户名
            process_id: 进程ID
            
        Returns:
            上传状态和结果信息
        """
        try:
            # 下载图片
            response = requests.get(image_url, timeout=10)
            if response.status_code != 200:
                return False, {"error": f"下载图片失败，状态码: {response.status_code}"}
            
            image_data = response.content
            
            # 生成简短文件名，不带扩展名
            filename = self._generate_short_filename(username, process_id)
            
            # 检查数据大小
            data_size = len(image_data)
            size_limit = FILE_SIZE_LIMITS.get('image', 0)
            
            if size_limit > 0 and data_size > size_limit:
                return False, {
                    "error": f"数据过大: {data_size} 字节，限制: {size_limit} 字节"
                }
            
            # 上传二进制数据，即使没有扩展名，也强制指定Content-Type
            content_type = "image/png"  # 强制使用PNG格式的Content-Type
            headers = {'Content-Type': content_type}
            success, result = self.client.upload_binary(image_data, filename, content_type)
            
            if success:
                # 返回成功结果
                return True, {
                    "url": result,
                    "path": filename,
                    "size": data_size,
                    "type": content_type,
                    "name": filename
                }
            else:
                return False, {"error": result}
        
        except Exception as e:
            logger.error(f"房本认证二维码上传服务发生异常: {str(e)}")
            return False, {"error": str(e)}
    
    def _generate_short_filename(self, username: str, process_id: str) -> str:
        """
        生成极短的文件名，不带扩展名，总长度不超过8个字符，并尽量保持唯一性
        
        Args:
            username: 用户名
            process_id: 进程ID
            
        Returns:
            短文件名，总长度不超过8个字符，不带扩展名
        """
        # 结合用户名、进程ID和当前时间戳生成唯一字符串
        unique_str = f"{username}_{process_id}_{int(time.time())}"
        
        # 对唯一字符串进行MD5哈希
        hash_obj = hashlib.md5(unique_str.encode())
        hash_hex = hash_obj.hexdigest()
        
        # 使用7位哈希值（有16^7 = 268,435,456种可能），更大的唯一性空间
        # 格式为"q"前缀加7位哈希值，总共8个字符，不带扩展名
        unique_part = hash_hex[:7]
        
        # 生成最终文件名，格式为"q+七位哈希值"，总共8个字符，不带扩展名
        return f"q{unique_part}" 