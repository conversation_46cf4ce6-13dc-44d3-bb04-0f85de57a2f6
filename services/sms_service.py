#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阿里云短信服务实现
提供发送通知短信的功能
"""

import json
import time
import logging
from datetime import datetime
import threading
from typing import Dict, List, Optional, Union

from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest
from aliyunsdkcore.acs_exception.exceptions import ClientException, ServerException

from services.base_service import BaseService
from config.sms_settings import (
    ALIYUN_ACCESS_KEY_ID,
    ALIYUN_ACCESS_KEY_SECRET,
    SMS_SIGN_NAME,
    SMS_REGION_ID,
    SMS_TEMPLATE_CODE,
    SMS_SEND_CONFIG,
    SMS_RATE_LIMIT
)

class SmsService(BaseService):
    """阿里云短信服务类，提供发送通知短信的功能"""
    
    def __init__(self):
        """初始化短信服务类"""
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
        self._init_acs_client()
        
        # 用于存储短信发送记录的内存缓存
        self._sms_records = {}
        self._lock = threading.Lock()
        
    def _init_acs_client(self):
        """初始化阿里云ACS客户端"""
        try:
            self.client = AcsClient(
                ALIYUN_ACCESS_KEY_ID, 
                ALIYUN_ACCESS_KEY_SECRET, 
                SMS_REGION_ID
            )
            self.logger.info("阿里云SMS客户端初始化成功")
        except Exception as e:
            self.logger.error(f"阿里云SMS客户端初始化失败: {str(e)}")
            self.client = None
    
    def _check_rate_limit(self, phone_number: str) -> bool:
        """
        检查短信发送频率限制
        
        Args:
            phone_number: 手机号码
            
        Returns:
            bool: 是否允许发送
        """
        with self._lock:
            current_time = int(time.time())
            today = datetime.now().strftime('%Y-%m-%d')
            current_hour = datetime.now().strftime('%Y-%m-%d %H')
            
            # 初始化记录
            if phone_number not in self._sms_records:
                self._sms_records[phone_number] = {
                    'today': today,
                    'daily_count': 0,
                    'hourly': {current_hour: 0},
                    'last_send_time': 0
                }
            
            records = self._sms_records[phone_number]
            
            # 检查日期是否变更，重置计数
            if records['today'] != today:
                records['today'] = today
                records['daily_count'] = 0
                records['hourly'] = {current_hour: 0}
            
            # 检查小时是否存在，不存在则初始化
            if current_hour not in records['hourly']:
                records['hourly'][current_hour] = 0
            
            # 检查发送间隔
            if current_time - records['last_send_time'] < SMS_RATE_LIMIT['interval']:
                self.logger.warning(f"发送过于频繁: {phone_number}, 需要等待 {SMS_RATE_LIMIT['interval'] - (current_time - records['last_send_time'])} 秒")
                return False
            
            # 检查每日限制
            if records['daily_count'] >= SMS_RATE_LIMIT['per_day']:
                self.logger.warning(f"超出每日发送限制: {phone_number}")
                return False
            
            # 检查每小时限制
            if records['hourly'][current_hour] >= SMS_RATE_LIMIT['per_hour']:
                self.logger.warning(f"超出每小时发送限制: {phone_number}")
                return False
            
            return True
    
    def _update_rate_limit(self, phone_number: str) -> None:
        """
        更新短信发送记录
        
        Args:
            phone_number: 手机号码
        """
        with self._lock:
            current_time = int(time.time())
            today = datetime.now().strftime('%Y-%m-%d')
            current_hour = datetime.now().strftime('%Y-%m-%d %H')
            
            # 确保记录存在
            if phone_number not in self._sms_records:
                self._sms_records[phone_number] = {
                    'today': today,
                    'daily_count': 0,
                    'hourly': {current_hour: 0},
                    'last_send_time': 0
                }
            
            records = self._sms_records[phone_number]
            
            # 更新记录
            records['daily_count'] += 1
            if current_hour not in records['hourly']:
                records['hourly'][current_hour] = 0
            records['hourly'][current_hour] += 1
            records['last_send_time'] = current_time
    
    def send_notification(self, 
                         phone_numbers: Union[str, List[str]], 
                         template_param: Dict,
                         out_id: str = '') -> Dict:
        """
        发送通知短信
        
        Args:
            phone_numbers: 手机号码，可以是单个号码或号码列表
            template_param: 短信模板参数，根据模板参数格式设置
            out_id: 外部流水扩展字段
            
        Returns:
            Dict: 发送结果
        """
        # 检查客户端是否初始化成功
        if not self.client:
            error_msg = "阿里云SMS客户端未初始化"
            self.logger.error(error_msg)
            return {'success': False, 'message': error_msg, 'code': 'CLIENT_NOT_INITIALIZED'}
        
        # 处理号码格式
        if isinstance(phone_numbers, list):
            phone_numbers_str = ','.join(phone_numbers)
        else:
            phone_numbers_str = phone_numbers
            phone_numbers = [phone_numbers]
        
        # 频率限制检查
        for phone in phone_numbers:
            if not self._check_rate_limit(phone):
                return {
                    'success': False,
                    'message': f"短信发送频率超出限制: {phone}",
                    'code': 'RATE_LIMIT_EXCEEDED'
                }
        
        # 构建请求
        request = CommonRequest()
        request.set_accept_format('json')
        request.set_domain('dysmsapi.aliyuncs.com')
        request.set_method('POST')
        request.set_protocol_type('https')
        request.set_version('2017-05-25')
        request.set_action_name('SendSms')
        
        # 设置请求参数
        request.add_query_param('PhoneNumbers', phone_numbers_str)
        request.add_query_param('SignName', SMS_SIGN_NAME)
        request.add_query_param('TemplateCode', SMS_TEMPLATE_CODE)
        request.add_query_param('TemplateParam', json.dumps(template_param))
        
        if out_id:
            request.add_query_param('OutId', out_id)
        
        # 发送请求
        try:
            self.logger.info(f"发送通知短信到: {phone_numbers_str}")
            response = self.client.do_action_with_exception(request)
            response_dict = json.loads(response.decode('utf-8'))
            
            # 检查发送结果
            if response_dict.get('Code') == 'OK':
                self.logger.info(f"通知短信发送成功: {phone_numbers_str}")
                
                # 更新频率限制记录
                for phone in phone_numbers:
                    self._update_rate_limit(phone)
                
                return {
                    'success': True,
                    'message': '通知短信发送成功',
                    'code': 'OK',
                    'request_id': response_dict.get('RequestId', ''),
                    'biz_id': response_dict.get('BizId', '')
                }
            else:
                error_msg = f"通知短信发送失败: {response_dict.get('Code')}, {response_dict.get('Message')}"
                self.logger.error(error_msg)
                return {
                    'success': False,
                    'message': error_msg,
                    'code': response_dict.get('Code', 'UNKNOWN_ERROR'),
                    'request_id': response_dict.get('RequestId', '')
                }
                
        except ClientException as e:
            error_msg = f"客户端异常: {str(e)}"
            self.logger.error(error_msg)
            return {'success': False, 'message': error_msg, 'code': 'CLIENT_EXCEPTION'}
            
        except ServerException as e:
            error_msg = f"服务端异常: {str(e)}"
            self.logger.error(error_msg)
            return {'success': False, 'message': error_msg, 'code': 'SERVER_EXCEPTION'}
            
        except Exception as e:
            error_msg = f"发送短信时发生异常: {str(e)}"
            self.logger.error(error_msg)
            return {'success': False, 'message': error_msg, 'code': 'UNKNOWN_EXCEPTION'}
    
    def batch_send_notification(self, phone_dict: Dict[str, Dict]) -> Dict[str, Dict]:
        """
        批量发送通知短信，每个号码可以有不同的内容
        
        Args:
            phone_dict: {手机号: 内容参数字典} 的字典
            
        Returns:
            Dict[str, Dict]: 每个手机号的发送结果
        """
        results = {}
        for phone, content in phone_dict.items():
            results[phone] = self.send_notification(phone, content)
        return results 