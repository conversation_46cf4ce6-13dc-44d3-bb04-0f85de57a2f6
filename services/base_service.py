#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基础服务类，提供共享的服务功能
"""

import os
import json
import logging
from pathlib import Path

class BaseService:
    """服务基类，提供共享功能"""
    
    def __init__(self):
        """初始化基础服务类"""
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 获取cities.json的绝对路径
        self.cities_json_path = Path(__file__).parent.parent / "config" / "cities.json"
        
        # 加载城市配置
        self.city_config = self._load_city_config()
    
    def _load_city_config(self):
        """从JSON文件加载城市配置"""
        try:
            if not self.cities_json_path.exists():
                self.logger.warning(f"城市配置文件不存在: {self.cities_json_path}")
                return {"domains": {}, "names": {}, "accounts": {}}
            
            with open(self.cities_json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"加载城市配置失败: {str(e)}")
            return {"domains": {}, "names": {}, "accounts": {}}
    
    def get_city_domain(self, city_code):
        """获取城市的域名"""
        return self.city_config.get("domains", {}).get(city_code)
    
    def get_city_name(self, city_code):
        """获取城市的名称"""
        return self.city_config.get("names", {}).get(city_code, city_code)
    
    def get_city_account(self, city_code):
        """获取城市的账号信息"""
        return self.city_config.get("accounts", {}).get(city_code, {})
    
    def get_user_id(self, city_code, user_id=None):
        """获取用户ID，如果提供了user_id则使用，否则使用城市默认账号"""
        if user_id:
            return user_id
        return self.get_city_account(city_code).get("user_id")
    
    def get_user_key(self, city_code, user_key=None):
        """获取用户密钥，如果提供了user_key则使用，否则使用城市默认账号"""
        if user_key:
            return user_key
        return self.get_city_account(city_code).get("user_key") 