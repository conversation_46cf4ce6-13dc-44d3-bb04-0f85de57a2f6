#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优推科技服务层
封装优推科技API的业务逻辑
"""

import logging
import json
from config.settings import DEFAULT_CITY
from services.base_service import BaseService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class YoutuiService(BaseService):
    """优推科技服务类"""

    def __init__(self, city=DEFAULT_CITY):
        """
        初始化服务

        Args:
            city: 城市代码，如'bj'(北京)、'cc'(长春)等
        """
        # 调用父类初始化方法
        super().__init__()

        # 延迟导入以避免循环导入
        from api.client import YoutuiApiClient, AsyncYoutuiApiClient

        self.client = YoutuiApiClient(environment='production', city=city)
        self.async_client = AsyncYoutuiApiClient(environment='production', city=city)
        self.city = city

        # 初始化Redis连接
        try:
            import redis
            self.redis_conn = redis.StrictRedis(
                host="**************",
                port=6379,
                password="Meiyoumima333.",
                decode_responses=True
            )
            # 测试Redis连接
            self.redis_conn.ping()
            logger.info("Redis连接初始化成功")
        except Exception as e:
            logger.warning(f"Redis连接初始化失败: {str(e)}，将在缓存操作时降级到直接API调用")
            self.redis_conn = None

        # 添加日志记录
        logger.info(f"初始化YoutuiService，使用城市: {city}")

        # 验证城市是否在支持列表中
        if city not in self.city_config.get("domains", {}):
            logger.warning(f"城市代码 '{city}' 不在支持列表中，支持的城市有: {', '.join(self.city_config.get('domains', {}).keys())}")
            logger.warning(f"将使用默认城市: {DEFAULT_CITY}")
            self.city = DEFAULT_CITY
            # 重新初始化客户端
            self.client = YoutuiApiClient(environment='production', city=DEFAULT_CITY)
            self.async_client = AsyncYoutuiApiClient(environment='production', city=DEFAULT_CITY)

    async def async_publish_factory(self, user_id=None, userkey=None, city=None, factory_data=None):
        """
        异步发布厂房服务

        Args:
            user_id: 用户ID，如果为None则使用城市配置中的默认用户ID
            userkey: 用户密钥，如果为None则使用城市配置中的默认用户密钥
            city: 城市代码，如'bj'(北京)、'cc'(长春)等，如果为None则使用实例中存储的城市
            factory_data: 厂房数据，如果为None则使用默认数据

        Returns:
            str: 房源ID或None（如果发布失败）
        """
        # 如果未提供城市，使用实例中存储的城市
        actual_city = city if city is not None else self.city

        # 使用BaseService的方法获取用户ID和密钥
        actual_user_id = self.get_user_id(actual_city, user_id)
        actual_userkey = self.get_user_key(actual_city, userkey)

        logger.info(f"异步发布厂房，城市: {actual_city}")

        # 每次请求时创建新的异步客户端，使用传入的城市参数
        from api.client import AsyncYoutuiApiClient
        async_client = AsyncYoutuiApiClient(environment='production', city=actual_city)

        # 确保用户传入的数据不被覆盖
        factory_data_copy = factory_data.copy()

        # 确保图片字段存在，即使为空
        image_fields = ['thumb', 'floorplans', 'photointerior', 'photooutdoor']
        for field in image_fields:
            if field not in factory_data_copy:
                factory_data_copy[field] = ''

        # 异步发布厂房
        result = await async_client.async_sync_house(actual_user_id, factory_data_copy, action='INSERT')

        if result:
            logger.info(f"异步厂房发布结果: {result}")

            # 检查发布是否成功
            if result.get('status') == '1':
                # 使用小写的houseid字段名
                house_id = result.get('houseid') or result.get('houseId') or result.get('HOUSEID')
                logger.info(f"异步厂房发布成功，优推科技房源ID: {house_id}")
                return house_id
            else:
                logger.error(f"异步厂房发布失败: {result.get('msg')}")
                logger.error(f"状态码: {result.get('status')}")
                logger.error(f"详细信息: {result}")
        else:
            logger.error("异步请求失败")

        return None


    def _process_posts_data(self, data):
        """
        处理帖子数据：清空Retime字段并添加update_status字段

        Args:
            data: 原始帖子数据（优推API返回的完整响应）

        Returns:
            处理后的数据
        """
        if not data:
            return data

        # 深拷贝数据以避免修改原始数据
        import copy
        processed_data = copy.deepcopy(data)

        # 检查是否为status="0"的响应（表示无帖子或无套餐）
        if isinstance(processed_data, dict) and processed_data.get('status') == '0':
            # 当status为"0"时，通常表示账号没有帖子或没开通套餐
            # 保存原始msg信息用于日志记录
            original_msg = processed_data.get('msg', '')
            # 将msg设置为空列表，以便上层逻辑正确处理
            processed_data['msg'] = json.dumps([], ensure_ascii=False)
            logger.info(f"检测到status='0'响应，原始msg: {original_msg}，已设置为空帖子列表")
            return processed_data

        # 优推返回的数据结构：{"status": "1", "taocan": "1-19", "msg": "[{...}]"}
        if isinstance(processed_data, dict) and 'msg' in processed_data:
            try:
                # msg字段包含JSON字符串格式的帖子列表
                msg_content = processed_data['msg']
                if isinstance(msg_content, str):
                    # 解析JSON字符串
                    posts_list = json.loads(msg_content)
                    if isinstance(posts_list, list):
                        # 处理每个帖子
                        for post in posts_list:
                            if isinstance(post, dict):
                                # 清空Retime字段
                                if 'Retime' in post:
                                    post['Retime'] = ''
                                # 添加update_status字段
                                post['update_status'] = '未更新'

                        # 将处理后的帖子列表重新转换为JSON字符串
                        processed_data['msg'] = json.dumps(posts_list, ensure_ascii=False)
                        logger.info(f"已处理 {len(posts_list)} 条帖子数据：清空Retime字段并添加update_status字段")
                elif isinstance(msg_content, list):
                    # 如果msg直接是列表
                    for post in msg_content:
                        if isinstance(post, dict):
                            # 清空Retime字段
                            if 'Retime' in post:
                                post['Retime'] = ''
                            # 添加update_status字段
                            post['update_status'] = '未更新'
                    logger.info(f"已处理 {len(msg_content)} 条帖子数据：清空Retime字段并添加update_status字段")
            except Exception as e:
                logger.warning(f"处理帖子数据时发生错误: {str(e)}，返回原始数据")

        return processed_data

    def _update_taocan_after_offline(self, taocan):
        """
        下架帖子后更新taocan字段：已用数量减1，剩余数量加1

        Args:
            taocan: 原始taocan字符串，格式如 "20-0", "18-2"

        Returns:
            更新后的taocan字符串
        """
        try:
            if not taocan or '-' not in taocan:
                logger.warning(f"taocan格式不正确: {taocan}")
                return taocan

            parts = taocan.split('-')
            if len(parts) != 2:
                logger.warning(f"taocan格式不正确: {taocan}")
                return taocan

            used = int(parts[0])
            remaining = int(parts[1])

            # 下架一个帖子：已用数量减1，剩余数量加1
            new_used = max(0, used - 1)  # 确保不会小于0
            new_remaining = remaining + 1

            new_taocan = f"{new_used}-{new_remaining}"
            logger.info(f"taocan更新: {taocan} → {new_taocan}")
            return new_taocan

        except (ValueError, IndexError) as e:
            logger.error(f"更新taocan时发生错误: {str(e)}，保持原值: {taocan}")
            return taocan

    def _extract_account_id_from_webcontent(self, webcontent: str):
        """
        从webcontent中提取账号ID

        Args:
            webcontent: Base64编码的JSON字符串

        Returns:
            str: 账号ID，如果解析失败返回None
        """
        try:
            if not webcontent:
                return None

            import base64
            # 解码base64
            decoded_bytes = base64.b64decode(webcontent)
            decoded_str = decoded_bytes.decode('utf-8')

            # 解析JSON
            webcontent_data = json.loads(decoded_str)

            # 提取账号ID
            # 格式: {"webarr":[{"webID":"402","actArr":{"1186881":"1"}}]}
            if isinstance(webcontent_data, dict) and 'webarr' in webcontent_data:
                webarr = webcontent_data['webarr']
                if isinstance(webarr, list) and len(webarr) > 0:
                    first_web = webarr[0]
                    if isinstance(first_web, dict) and 'actArr' in first_web:
                        act_arr = first_web['actArr']
                        if isinstance(act_arr, dict):
                            # 获取第一个账号ID
                            account_ids = list(act_arr.keys())
                            if account_ids:
                                return account_ids[0]

            return None

        except Exception as e:
            logger.warning(f"从webcontent提取账号ID失败: {str(e)}")
            return None

    def _remove_post_from_cache(self, webcontent, remote_id):
        """
        从Redis缓存中删除指定的帖子

        Args:
            webcontent: Base64编码的JSON，用于构建缓存键
            remote_id: 要删除的帖子的远程ID（RID字段）
        """
        if not self.redis_conn:
            logger.warning("Redis连接不可用，无法删除缓存中的帖子")
            return

        # 从webcontent中提取账号ID
        account_id = self._extract_account_id_from_webcontent(webcontent)
        if not account_id:
            logger.warning("无法从webcontent提取账号ID，跳过缓存删除操作")
            return

        cache_key = f"agent_posts:{account_id}"

        try:
            # 获取缓存数据
            cached_data = self.redis_conn.get(cache_key)
            if not cached_data:
                logger.info(f"缓存中未找到数据，键: {cache_key}")
                return

            # 解析缓存数据
            cached_result = json.loads(cached_data)

            # 检查数据结构并删除对应的帖子
            if isinstance(cached_result, dict) and 'msg' in cached_result:
                try:
                    msg_content = cached_result['msg']
                    if isinstance(msg_content, str):
                        # 解析JSON字符串
                        posts_list = json.loads(msg_content)
                        if isinstance(posts_list, list):
                            # 查找并删除指定的帖子
                            original_count = len(posts_list)
                            posts_list = [post for post in posts_list if post.get('RID') != remote_id]

                            if len(posts_list) < original_count:
                                # 将更新后的帖子列表重新转换为JSON字符串
                                cached_result['msg'] = json.dumps(posts_list, ensure_ascii=False)

                                # 更新taocan字段：已用数量减1，剩余数量加1
                                if 'taocan' in cached_result:
                                    cached_result['taocan'] = self._update_taocan_after_offline(cached_result['taocan'])

                                # 更新缓存
                                updated_cache_data = json.dumps(cached_result, ensure_ascii=False)
                                self.redis_conn.setex(cache_key, 86400, updated_cache_data)  # 保持1天过期时间

                                logger.info(f"已从缓存中删除帖子 RID: {remote_id}，剩余帖子数: {len(posts_list)}，更新后的taocan: {cached_result.get('taocan')}")
                            else:
                                logger.info(f"缓存中未找到 RID 为 {remote_id} 的帖子")
                    elif isinstance(msg_content, list):
                        # 如果msg直接是列表
                        original_count = len(msg_content)
                        cached_result['msg'] = [post for post in msg_content if post.get('RID') != remote_id]

                        if len(cached_result['msg']) < original_count:
                            # 更新taocan字段：已用数量减1，剩余数量加1
                            if 'taocan' in cached_result:
                                cached_result['taocan'] = self._update_taocan_after_offline(cached_result['taocan'])

                            # 更新缓存
                            updated_cache_data = json.dumps(cached_result, ensure_ascii=False)
                            self.redis_conn.setex(cache_key, 86400, updated_cache_data)

                            logger.info(f"已从缓存中删除帖子 RID: {remote_id}，剩余帖子数: {len(cached_result['msg'])}，更新后的taocan: {cached_result.get('taocan')}")
                        else:
                            logger.info(f"缓存中未找到 RID 为 {remote_id} 的帖子")

                except Exception as e:
                    logger.error(f"处理缓存数据时发生错误: {str(e)}")
            else:
                logger.warning("缓存数据格式不符合预期，无法删除帖子")

        except Exception as e:
            logger.error(f"从缓存中删除帖子时发生错误: {str(e)}")

    def _update_post_in_cache(self, webcontent, remote_id):
        """
        在Redis缓存中更新指定帖子的激活状态

        Args:
            webcontent: Base64编码的JSON，用于构建缓存键
            remote_id: 要更新的帖子的远程ID（RID字段）
        """
        if not self.redis_conn:
            logger.warning("Redis连接不可用，无法更新缓存中的帖子")
            return

        # 从webcontent中提取账号ID
        account_id = self._extract_account_id_from_webcontent(webcontent)
        if not account_id:
            logger.warning("无法从webcontent提取账号ID，跳过缓存更新操作")
            return

        cache_key = f"agent_posts:{account_id}"

        try:
            # 获取缓存数据
            cached_data = self.redis_conn.get(cache_key)
            if not cached_data:
                logger.info(f"缓存中未找到数据，键: {cache_key}")
                return

            # 解析缓存数据
            cached_result = json.loads(cached_data)

            # 检查数据结构并更新对应的帖子
            if isinstance(cached_result, dict) and 'msg' in cached_result:
                try:
                    msg_content = cached_result['msg']
                    updated = False

                    if isinstance(msg_content, str):
                        # 解析JSON字符串
                        posts_list = json.loads(msg_content)
                        if isinstance(posts_list, list):
                            # 查找并更新指定的帖子
                            for post in posts_list:
                                if post.get('RID') == remote_id:
                                    # 更新Retime为当前时间
                                    from datetime import datetime
                                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    post['Retime'] = current_time
                                    # 更新状态为已更新
                                    post['update_status'] = '已更新'
                                    updated = True
                                    logger.info(f"已更新帖子 RID: {remote_id}，Retime: {current_time}，状态: 已更新")
                                    break

                            if updated:
                                # 将更新后的帖子列表重新转换为JSON字符串
                                cached_result['msg'] = json.dumps(posts_list, ensure_ascii=False)

                                # 更新缓存
                                updated_cache_data = json.dumps(cached_result, ensure_ascii=False)
                                self.redis_conn.setex(cache_key, 86400, updated_cache_data)  # 保持1天过期时间

                                logger.info(f"已在缓存中更新帖子 RID: {remote_id} 的激活状态")
                            else:
                                logger.info(f"缓存中未找到 RID 为 {remote_id} 的帖子")

                    elif isinstance(msg_content, list):
                        # 如果msg直接是列表
                        for post in msg_content:
                            if post.get('RID') == remote_id:
                                # 更新Retime为当前时间
                                from datetime import datetime
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                post['Retime'] = current_time
                                # 更新状态为已更新
                                post['update_status'] = '已更新'
                                updated = True
                                logger.info(f"已更新帖子 RID: {remote_id}，Retime: {current_time}，状态: 已更新")
                                break

                        if updated:
                            # 更新缓存
                            updated_cache_data = json.dumps(cached_result, ensure_ascii=False)
                            self.redis_conn.setex(cache_key, 86400, updated_cache_data)

                            logger.info(f"已在缓存中更新帖子 RID: {remote_id} 的激活状态")
                        else:
                            logger.info(f"缓存中未找到 RID 为 {remote_id} 的帖子")

                except Exception as e:
                    logger.error(f"处理缓存数据时发生错误: {str(e)}")
            else:
                logger.warning("缓存数据格式不符合预期，无法更新帖子")

        except Exception as e:
            logger.error(f"在缓存中更新帖子时发生错误: {str(e)}")

    def _update_post_in_cache_by_account_id(self, account_id: str, remote_id: str):
        """
        根据账号ID在Redis缓存中更新指定帖子的激活状态

        Args:
            account_id: 账号ID
            remote_id: 要更新的帖子的远程ID（RID字段）
        """
        if not self.redis_conn:
            logger.warning("Redis连接不可用，无法更新缓存中的帖子")
            return

        cache_key = f"agent_posts:{account_id}"

        try:
            # 获取缓存数据
            cached_data = self.redis_conn.get(cache_key)
            if not cached_data:
                logger.info(f"缓存中未找到数据，键: {cache_key}")
                return

            # 解析缓存数据
            cached_result = json.loads(cached_data)

            # 检查数据结构并更新对应的帖子
            if isinstance(cached_result, dict) and 'msg' in cached_result:
                try:
                    msg_content = cached_result['msg']
                    updated = False

                    if isinstance(msg_content, str):
                        # 解析JSON字符串
                        posts_list = json.loads(msg_content)
                        if isinstance(posts_list, list):
                            # 查找并更新指定的帖子
                            for post in posts_list:
                                if post.get('RID') == remote_id:
                                    # 更新Retime为当前时间
                                    from datetime import datetime
                                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    post['Retime'] = current_time
                                    # 更新状态为已更新
                                    post['update_status'] = '已更新'
                                    updated = True
                                    logger.info(f"已更新帖子 RID: {remote_id}，Retime: {current_time}，状态: 已更新")
                                    break

                            if updated:
                                # 将更新后的帖子列表重新转换为JSON字符串
                                cached_result['msg'] = json.dumps(posts_list, ensure_ascii=False)

                                # 更新缓存
                                updated_cache_data = json.dumps(cached_result, ensure_ascii=False)
                                self.redis_conn.setex(cache_key, 86400, updated_cache_data)  # 保持1天过期时间

                                logger.info(f"已在缓存中更新帖子 RID: {remote_id} 的激活状态")
                            else:
                                logger.info(f"缓存中未找到 RID 为 {remote_id} 的帖子")

                    elif isinstance(msg_content, list):
                        # 如果msg直接是列表
                        for post in msg_content:
                            if post.get('RID') == remote_id:
                                # 更新Retime为当前时间
                                from datetime import datetime
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                post['Retime'] = current_time
                                # 更新状态为已更新
                                post['update_status'] = '已更新'
                                updated = True
                                logger.info(f"已更新帖子 RID: {remote_id}，Retime: {current_time}，状态: 已更新")
                                break

                        if updated:
                            # 更新缓存
                            updated_cache_data = json.dumps(cached_result, ensure_ascii=False)
                            self.redis_conn.setex(cache_key, 86400, updated_cache_data)

                            logger.info(f"已在缓存中更新帖子 RID: {remote_id} 的激活状态")
                        else:
                            logger.info(f"缓存中未找到 RID 为 {remote_id} 的帖子")

                except Exception as e:
                    logger.error(f"处理缓存数据时发生错误: {str(e)}")
            else:
                logger.warning("缓存数据格式不符合预期，无法更新帖子")

        except Exception as e:
            logger.error(f"在缓存中更新帖子时发生错误: {str(e)}")

    def _update_taocan_after_push(self, taocan):
        """
        推送帖子后更新taocan字段：已用数量加1，剩余数量减1

        Args:
            taocan: 原始taocan字符串，格式如 "20-0", "18-2"

        Returns:
            更新后的taocan字符串
        """
        try:
            if not taocan or '-' not in taocan:
                logger.warning(f"taocan格式不正确: {taocan}")
                return taocan

            parts = taocan.split('-')
            if len(parts) != 2:
                logger.warning(f"taocan格式不正确: {taocan}")
                return taocan

            used = int(parts[0])
            remaining = int(parts[1])

            # 推送一个帖子：已用数量加1，剩余数量减1
            new_used = used + 1
            new_remaining = max(0, remaining - 1)  # 确保不会小于0

            new_taocan = f"{new_used}-{new_remaining}"
            logger.info(f"推送后taocan更新: {taocan} → {new_taocan}")
            return new_taocan

        except (ValueError, IndexError) as e:
            logger.error(f"更新taocan时发生错误: {str(e)}，保持原值: {taocan}")
            return taocan

    def _add_pushed_post_to_cache(self, account_id: str, house_id: str, title: str, website_id: str, process_id: str, city: str, house_type: str):
        """
        将推送成功的房源信息添加到Redis缓存中

        Args:
            account_id: 账号ID
            house_id: 推送成功后返回的房源ID
            title: 房源标题
            website_id: 网站ID
            process_id: 推送进程ID
            city: 城市代码
            house_type: 房源类型（租售类型）
        """
        if not self.redis_conn:
            logger.warning("Redis连接不可用，无法添加推送房源到缓存")
            return

        cache_key = f"agent_posts:{account_id}"

        try:
            # 获取缓存数据
            cached_data = self.redis_conn.get(cache_key)
            if not cached_data:
                logger.info(f"缓存中未找到数据，键: {cache_key}，无法添加推送房源")
                return

            # 解析缓存数据
            cached_result = json.loads(cached_data)

            # 检查数据结构
            if isinstance(cached_result, dict) and 'msg' in cached_result:
                try:
                    # 构建新的帖子数据
                    from datetime import datetime
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    new_post = {
                        "RID": process_id,
                        "SID": website_id,
                        "Title": title,
                        "Tags": "待校验",
                        "Com": "待校验",
                        "House_URL": "待校验",
                        "clickT": "待校验",
                        "clickM": "待校验",
                        "thumbnail": "待校验",
                        "Type": self._convert_type_to_code(house_type),
                        "Retime": "",
                        "Potime": current_time,
                        "update_status": "待校验",
                        "city": city
                    }

                    msg_content = cached_result['msg']

                    if isinstance(msg_content, str):
                        # 解析JSON字符串
                        posts_list = json.loads(msg_content)
                        if isinstance(posts_list, list):
                            posts_list.insert(0, new_post)
                            cached_result['msg'] = json.dumps(posts_list, ensure_ascii=False)
                    elif isinstance(msg_content, list):
                        msg_content.insert(0, new_post)

                    if 'taocan' in cached_result:
                        cached_result['taocan'] = self._update_taocan_after_push(cached_result['taocan'])

                    updated_cache_data = json.dumps(cached_result, ensure_ascii=False)
                    self.redis_conn.setex(cache_key, 86400, updated_cache_data)

                except Exception as e:
                    logger.error(f"处理缓存数据时发生错误: {str(e)}")
            else:
                logger.warning("缓存数据格式不符合预期，无法添加推送房源")

        except Exception as e:
            logger.error(f"添加推送房源到缓存时发生错误: {str(e)}")

    def _convert_type_to_code(self, house_type):
        """
        将房源类型转换为数字代码

        Args:
            house_type: 房源类型（可能是文字描述或数字代码）

        Returns:
            str: 数字代码，"6"表示出售，"3"表示出租
        """
        if not house_type:
            return "6"  # 默认为出售

        house_type_str = str(house_type).lower()

        if "租" in house_type_str or "rent" in house_type_str:
            return "3"
        elif "售" in house_type_str or "sale" in house_type_str:
            return "6"
        elif house_type_str in ["3", "6"]:
            return house_type_str
        else:
            return "6"

    async def _check_and_update_pending_posts(self, cached_result, cache_key):
        """
        检查并更新缓存中"待校验"状态的帖子

        Args:
            cached_result: 缓存中的数据
            cache_key: Redis缓存键

        Returns:
            更新后的缓存数据
        """
        try:
            # 检查数据结构
            if isinstance(cached_result, dict) and 'msg' in cached_result:
                msg_content = cached_result['msg']

                # 解析msg内容
                if isinstance(msg_content, str):
                    try:
                        posts_list = json.loads(msg_content)
                    except:
                        logger.warning("无法解析msg字段的JSON内容")
                        return cached_result
                elif isinstance(msg_content, list):
                    posts_list = msg_content
                else:
                    logger.warning("msg字段格式不符合预期")
                    return cached_result

                # 检查是否有"待校验"状态的帖子
                pending_posts = []
                for i, post in enumerate(posts_list):
                    if isinstance(post, dict) and post.get('update_status') == '待校验':
                        pending_posts.append((i, post))

                if not pending_posts:
                    return cached_result

                # 检查每个待校验帖子的推送状态
                updated = False
                failed_posts_count = 0  # 记录失败帖子数量
                posts_to_remove = []  # 收集需要移除的帖子索引，避免索引变化问题

                for i, post in pending_posts:
                    rid = post.get('RID')
                    city = post.get('city', self.city)

                    if rid and city:
                        try:
                            # 调用推送状态检查服务
                            from services.push_service import PushService
                            push_service = PushService(city=city)
                            push_info = push_service.get_push_info(process_id=rid)

                            if push_info:
                                raw_status = push_info.get('raw_status', '')

                                if raw_status == '1':
                                    # 推送成功，更新帖子状态
                                    posts_list[i] = self._update_pending_post_status(post, push_info)
                                    updated = True
                                    logger.info(f"帖子 RID:{rid} 推送校验成功")
                                elif raw_status == '2':
                                    # 推送失败，标记为需要移除
                                    posts_to_remove.append(i)
                                    failed_posts_count += 1
                                    updated = True
                                    logger.info(f"帖子 RID:{rid} 推送校验失败，将从缓存中移除")
                        except Exception as e:
                            logger.error(f"检查帖子 RID:{rid} 推送状态时发生错误: {str(e)}")

                # 从后往前移除失败的帖子，避免索引变化问题
                for i in sorted(posts_to_remove, reverse=True):
                    posts_list.pop(i)

                # 如果有失败的帖子被移除，需要更新taocan字段
                if failed_posts_count > 0 and 'taocan' in cached_result:
                    original_taocan = cached_result['taocan']
                    # 对每个失败的帖子调用taocan更新逻辑
                    for _ in range(failed_posts_count):
                        cached_result['taocan'] = self._update_taocan_after_offline(cached_result['taocan'])

                    logger.info(f"移除 {failed_posts_count} 个校验失败的帖子，taocan更新: {original_taocan} → {cached_result['taocan']}")

                if updated:
                    # 更新msg字段
                    if isinstance(msg_content, str):
                        cached_result['msg'] = json.dumps(posts_list, ensure_ascii=False)
                    else:
                        cached_result['msg'] = posts_list

                    # 记录更新后的状态
                    remaining_posts = len(posts_list)
                    logger.info(f"待校验帖子检查完成 - 剩余帖子数: {remaining_posts}, 当前taocan: {cached_result.get('taocan', 'N/A')}")

                    # 更新Redis缓存
                    try:
                        updated_cache_data = json.dumps(cached_result, ensure_ascii=False)
                        self.redis_conn.setex(cache_key, 86400, updated_cache_data)
                        logger.info(f"Redis缓存更新成功 - 缓存键: {cache_key}")
                    except Exception as e:
                        logger.error(f"更新Redis缓存失败: {str(e)}")

            return cached_result

        except Exception as e:
            logger.error(f"检查和更新待校验帖子时发生错误: {str(e)}")
            return cached_result

    def _update_pending_post_status(self, post, push_info):
        """
        将待校验帖子的状态更新为校验成功

        Args:
            post: 帖子数据字典
            push_info: 推送信息

        Returns:
            更新后的帖子数据
        """
        updated_post = post.copy()

        for key, value in updated_post.items():
            if value == "待校验":
                updated_post[key] = "校验成功"

        if push_info:
            if push_info.get('push_url'):
                updated_post['House_URL'] = push_info['push_url']
            if push_info.get('push_id'):
                updated_post['RID'] = push_info['push_id']
        return updated_post

    async def async_get_agent_post_stats(self, user_id, webcontent=None, timeout=30):
        """
        异步获取经纪人名下所有帖子的点击量

        Args:
            user_id: 用户标识ID，即经纪人ID
            webcontent: 网站与账号信息的Base64编码JSON (可选)
            timeout: 超时时间（秒），设置为None表示永不超时

        Returns:
            帖子点击量信息，字典或列表格式
        """
        logger.info(f"异步获取经纪人(ID: {user_id})名下所有帖子点击量")
        logger.info(f"服务层超时设置: {'永不超时' if timeout is None else f'{timeout}秒'}")

        # 构建缓存键 - 使用账号ID而不是完整的webcontent
        cache_key = None
        account_id = None
        if webcontent:
            account_id = self._extract_account_id_from_webcontent(webcontent)
            if account_id:
                cache_key = f"agent_posts:{account_id}"

        try:
            # 只有提供了webcontent且能提取到账号ID才进行缓存操作
            if cache_key and self.redis_conn:
                logger.info(f"使用账号ID构建缓存键: {cache_key}")

                try:
                    cached_data = self.redis_conn.get(cache_key)
                    if cached_data:
                        logger.info(f"从Redis缓存中获取到经纪人的帖子数据")
                        result = json.loads(cached_data)

                        # 检查并更新"待校验"状态的帖子
                        updated_result = await self._check_and_update_pending_posts(result, cache_key)

                        if isinstance(updated_result, list):
                            logger.info(f"缓存命中，返回 {len(updated_result)} 条帖子信息")
                        else:
                            logger.info(f"缓存命中，返回帖子信息")
                        return updated_result
                    else:
                        logger.info(f"Redis缓存中未找到帖子数据，将调用API获取")
                except Exception as redis_error:
                    logger.warning(f"Redis缓存读取失败: {str(redis_error)}，将直接调用API")
            else:
                if not webcontent:
                    logger.info("未提供webcontent，跳过缓存检查，直接调用API")
                elif not account_id:
                    logger.info("无法从webcontent提取账号ID，跳过缓存检查，直接调用API")
                elif not self.redis_conn:
                    logger.info("Redis连接不可用，直接调用API获取数据")

            # 缓存未命中或Redis连接失败，调用异步API客户端方法
            if webcontent:
                logger.info("使用自定义webcontent参数")
                result = await self.async_client.async_get_agent_post_stats(user_id, webcontent, timeout=timeout)
            else:
                logger.info("使用默认参数")
                result = await self.async_client.async_get_agent_post_stats(user_id, timeout=timeout)

            # 记录响应
            if result:
                if isinstance(result, list):
                    logger.info(f"成功获取到 {len(result)} 条帖子信息")
                else:
                    logger.info(f"成功获取到帖子信息")

                # 处理数据：清空Retime并添加update_status字段
                processed_result = self._process_posts_data(result)

                # 只有提供了webcontent才写入缓存
                if cache_key and self.redis_conn:
                    try:
                        cache_data = json.dumps(processed_result, ensure_ascii=False)
                        self.redis_conn.setex(cache_key, 86400, cache_data)  # 86400秒 = 1天
                        logger.info(f"已将经纪人的帖子数据存入Redis缓存，过期时间：1天")
                    except Exception as redis_error:
                        logger.warning(f"Redis缓存写入失败: {str(redis_error)}，但API调用成功")
                else:
                    if not webcontent:
                        logger.info("未提供webcontent，跳过缓存写入")
                    elif not account_id:
                        logger.info("无法从webcontent提取账号ID，跳过缓存写入")
                    elif not self.redis_conn:
                        logger.info("Redis连接不可用，跳过缓存写入")

                # 返回处理后的数据
                return processed_result
            else:
                logger.warning(f"未获取到帖子信息或发生错误")

            return result
        except Exception as e:
            logger.error(f"异步获取经纪人帖子点击量时发生错误: {str(e)}")
            return None


    async def async_activate_house(self, user_id=None, userkey=None, city=None, remote_id=None, webcontent=None, house_type=None):
        """
        异步房源激活服务

        Args:
            user_id: 用户ID，如果为None则使用城市配置中的默认用户ID
            userkey: 用户密钥，如果为None则使用城市配置中的默认用户密钥
            city: 城市代码，如'bj'(北京)、'cc'(长春)等，如果为None则使用实例中存储的城市
            remote_id: 要激活的房源远程ID
            webcontent: 网站与账号信息的Base64编码JSON (可选)
            house_type: 房源类型，'6'表示出售，'3'表示出租 (可选，默认为'6')

        Returns:
            dict: 激活结果，包含状态和消息
        """
        # 如果未提供城市，使用实例中存储的城市
        actual_city = city if city is not None else self.city

        # 使用BaseService的方法获取用户ID和密钥
        actual_user_id = self.get_user_id(actual_city, user_id)
        actual_userkey = self.get_user_key(actual_city, userkey)

        logger.info(f"异步激活房源，城市: {actual_city}，远程ID: {remote_id}")

        # 获取房源类型代码（'6'表示出售，'3'表示出租），如果未提供则默认为'6'
        actual_house_type = house_type if house_type is not None else '6'

        if not remote_id:
            logger.error("未提供要激活的房源远程ID")
            return {"success": False, "message": "未提供要激活的房源远程ID"}

        try:
            # 调用API客户端的房源激活接口
            endpoint = '?api/house/edithouse.html'

            # 构建XML数据
            xml_data = {
                'platformID': self.async_client.platform_id,
                'companyID': self.async_client.company_id,
                'userID': actual_user_id,
                'RemoteID': remote_id,
                'type': actual_house_type,
                'type4property': '13',  # 根据实际情况可能需要动态设置
                'dbug': self.async_client.debug
            }

            # 添加webcontent参数（如果提供）
            if webcontent:
                xml_data['webcontent'] = webcontent

            # 转换为XML并发送请求
            from utils.xml_utils import dict_to_xml
            xml_payload = dict_to_xml(xml_data)
            response_xml = await self.async_client.http_client.post(endpoint, xml_payload)

            # 解析响应
            if response_xml:
                from utils.xml_utils import xml_to_dict
                result = xml_to_dict(response_xml)
                logger.info(f"异步房源激活结果: {result}")

                # 检查激活是否成功
                if result and (result.get('status') == '1' or result.get('status') == '9' or result.get('houseid')):
                    logger.info(f"异步房源激活成功，远程ID: {remote_id}")

                    # 激活成功后，更新Redis缓存中对应帖子的状态
                    if webcontent and self.redis_conn:
                        try:
                            self._update_post_in_cache(webcontent, remote_id)
                        except Exception as cache_error:
                            logger.warning(f"更新缓存中的帖子状态失败: {str(cache_error)}，但激活操作成功")

                    return {"success": True, "message": "房源激活成功", "data": result}
                else:
                    error_msg = result.get('msg') if result else "未知错误"
                    logger.error(f"异步房源激活失败: {error_msg}")
                    return {"success": False, "message": f"房源激活失败: {error_msg}", "data": result}
            else:
                logger.error("异步房源激活请求失败")
                return {"success": False, "message": "房源激活请求失败"}
        except Exception as e:
            logger.error(f"异步房源激活过程中发生错误: {str(e)}")
            return {"success": False, "message": f"房源激活失败: {str(e)}", "data": None}

    async def async_offline_house(self, user_id=None, userkey=None, city=None, remote_id=None, webcontent=None, house_type=None):
        """
        异步房源下架服务

        Args:
            user_id: 用户ID，如果为None则使用城市配置中的默认用户ID
            userkey: 用户密钥，如果为None则使用城市配置中的默认用户密钥
            city: 城市代码，如'bj'(北京)、'cc'(长春)等，如果为None则使用实例中存储的城市
            remote_id: 要下架的房源远程ID
            webcontent: 网站与账号信息的Base64编码JSON (可选)
            house_type: 房源类型，'6'表示出售，'3'表示出租 (可选，默认为'6')

        Returns:
            dict: 下架结果，包含状态和消息
        """
        # 如果未提供城市，使用实例中存储的城市
        actual_city = city if city is not None else self.city

        # 使用BaseService的方法获取用户ID和密钥
        actual_user_id = self.get_user_id(actual_city, user_id)
        actual_userkey = self.get_user_key(actual_city, userkey)

        logger.info(f"异步下架房源，城市: {actual_city}，远程ID: {remote_id}")

        # 获取房源类型代码（'6'表示出售，'3'表示出租），如果未提供则默认为'6'
        actual_house_type = house_type if house_type is not None else '6'

        if not remote_id:
            logger.error("未提供要下架的房源远程ID")
            return {"success": False, "message": "未提供要下架的房源远程ID"}

        try:

            # 调用API客户端的房源下架接口
            endpoint = '?api/house/offline.html'
            xml_data = {
                'platformID': self.async_client.platform_id,
                'companyID': self.async_client.company_id,
                'userID': actual_user_id,
                'RemoteID': remote_id,
                'type': actual_house_type,  # 使用数字代码
                'type4property': '13',
                'dbug': self.async_client.debug
            }

            # 添加webcontent参数（如果提供）
            if webcontent:
                xml_data['webcontent'] = webcontent

            # 转换为XML并发送请求
            from utils.xml_utils import dict_to_xml
            xml_payload = dict_to_xml(xml_data)
            response_xml = await self.async_client.http_client.post(endpoint, xml_payload)

            # 解析响应
            if response_xml:
                from utils.xml_utils import xml_to_dict
                result = xml_to_dict(response_xml)
                logger.info(f"异步房源下架结果: {result}")

                # 检查下架是否成功
                if result and (result.get('status') == '1' or result.get('status') == '9' or result.get('houseid')):
                    logger.info(f"异步房源下架成功，远程ID: {remote_id}")

                    # 下架成功后，从Redis缓存中删除对应的帖子数据
                    if webcontent and self.redis_conn:
                        try:
                            self._remove_post_from_cache(webcontent, remote_id)
                        except Exception as cache_error:
                            logger.warning(f"从缓存中删除帖子失败: {str(cache_error)}，但下架操作成功")

                    return {"success": True, "message": "房源下架成功", "data": result}
                else:
                    error_msg = result.get('msg') if result else "未知错误"
                    logger.error(f"异步房源下架失败: {error_msg}")
                    return {"success": False, "message": f"房源下架失败: {error_msg}", "data": result}
            else:
                logger.error("异步房源下架请求失败")
                return {"success": False, "message": "房源下架请求失败"}
        except Exception as e:
            logger.error(f"异步房源下架过程中发生错误: {str(e)}")
            return {"success": False, "message": f"房源下架失败: {str(e)}", "data": None}