#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
钉钉消息服务
提供发送钉钉工作通知消息的功能
"""

import json
import time
import logging
import os
from typing import Dict, List, Union, Optional, Any
from pathlib import Path
import requests

from services.base_service import BaseService
from config.settings import DEFAULT_CITY

class DingTalkService(BaseService):
    """钉钉消息服务类，提供发送钉钉工作通知消息的功能"""
    
    def __init__(self, city=DEFAULT_CITY):
        """
        初始化钉钉消息服务类
        
        Args:
            city: 城市代码，默认使用配置中的DEFAULT_CITY
        """
        # 调用父类初始化方法
        super().__init__()
        
        self.logger = logging.getLogger(self.__class__.__name__)
        self.city = city
        
        # 钉钉开放平台相关配置
        self.config_dir = Path(__file__).parent.parent / "config"
        self.dingtalk_config_path = self.config_dir / "dingtalk_config.json"
        
        # 加载钉钉配置
        self.dingtalk_config = self._load_dingtalk_config()
        
        # 初始化access_token缓存
        self.access_token_cache = {}

    def _load_dingtalk_config(self) -> Dict:
        """
        从JSON文件加载钉钉配置
        
        Returns:
            Dict: 钉钉配置
        """
        try:
            # 如果配置文件不存在，创建默认配置
            if not self.dingtalk_config_path.exists():
                default_config = {
                    "app_key": "",
                    "app_secret": "",
                    "agent_id": "",
                    "token_expires_in": 7200,
                    "api_host": "https://oapi.dingtalk.com"
                }
                
                # 确保配置目录存在
                os.makedirs(self.config_dir, exist_ok=True)
                
                # 写入默认配置
                with open(self.dingtalk_config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, ensure_ascii=False, indent=2)
                
                self.logger.warning(f"钉钉配置文件不存在，已创建默认配置: {self.dingtalk_config_path}")
                return default_config
            
            # 加载配置文件
            with open(self.dingtalk_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.logger.info("钉钉配置加载成功")
                return config
        except Exception as e:
            self.logger.error(f"加载钉钉配置失败: {str(e)}")
            # 返回默认配置
            return {
                "app_key": "",
                "app_secret": "",
                "agent_id": "",
                "token_expires_in": 7200,
                "api_host": "https://oapi.dingtalk.com"
            }
    
    def _get_access_token(self) -> Optional[str]:
        """
        获取钉钉access_token
        
        Returns:
            str: 钉钉access_token
        """
        # 检查缓存的access_token是否有效
        current_time = time.time()
        if (self.city in self.access_token_cache and 
            self.access_token_cache[self.city]["expires_time"] > current_time):
            return self.access_token_cache[self.city]["access_token"]
        
        try:
            app_key = self.dingtalk_config.get("app_key")
            app_secret = self.dingtalk_config.get("app_secret")
            
            if not app_key or not app_secret:
                self.logger.error("钉钉配置中缺少app_key或app_secret")
                return None
            
            # 请求钉钉获取access_token接口
            url = f"{self.dingtalk_config.get('api_host')}/gettoken"
            params = {
                "appkey": app_key,
                "appsecret": app_secret
            }
            
            self.logger.info(f"请求钉钉获取access_token: {url}")
            response = requests.get(url, params=params)
            result = response.json()
            
            if result.get("errcode") == 0:
                access_token = result.get("access_token")
                expires_in = result.get("expires_in", self.dingtalk_config.get("token_expires_in", 7200))
                
                # 缓存access_token，有效期提前5分钟过期
                self.access_token_cache[self.city] = {
                    "access_token": access_token,
                    "expires_time": current_time + expires_in - 300
                }
                
                self.logger.info(f"获取钉钉access_token成功，有效期: {expires_in}秒")
                return access_token
            else:
                self.logger.error(f"获取钉钉access_token失败: {result}")
                return None
        except Exception as e:
            self.logger.error(f"获取钉钉access_token异常: {str(e)}")
            return None
    
    def send_work_notification(self, 
                             message: str, 
                             user_ids: Optional[List[str]] = None,
                             dept_ids: Optional[List[str]] = None,
                             to_all_user: bool = False,
                             msg_type: str = "text",
                             title: Optional[str] = None,
                             message_url: Optional[str] = None,
                             pic_url: Optional[str] = None) -> Dict:
        """
        发送钉钉工作通知消息
        
        Args:
            message: 消息内容
            user_ids: 接收者的用户ID列表，最大列表长度100
            dept_ids: 接收者的部门ID列表，最大列表长度20
            to_all_user: 是否发送给企业全部用户
            msg_type: 消息类型，支持text、link、markdown等
            title: 消息标题，适用于link、markdown等类型
            message_url: 点击消息后跳转的URL，适用于link类型
            pic_url: 图片URL，适用于link类型
            
        Returns:
            Dict: 发送结果
        """
        if not user_ids and not dept_ids and not to_all_user:
            error_msg = "必须指定接收者(user_ids或dept_ids)，或设置to_all_user为True"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "RECIPIENT_REQUIRED"}
        
        access_token = self._get_access_token()
        if not access_token:
            error_msg = "获取钉钉access_token失败"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "ACCESS_TOKEN_FAILED"}
        
        agent_id = self.dingtalk_config.get("agent_id")
        if not agent_id:
            error_msg = "钉钉配置中缺少agent_id"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "AGENT_ID_MISSING"}
        
        # 构建消息内容
        msg = self._build_message(msg_type, message, title, message_url, pic_url)
        
        # 构建请求数据
        url = f"{self.dingtalk_config.get('api_host')}/topapi/message/corpconversation/asyncsend_v2"
        params = {
            "access_token": access_token
        }
        data = {
            "agent_id": agent_id,
            "to_all_user": to_all_user,
            "msg": msg
        }
        
        # 添加接收者
        if user_ids:
            data["userid_list"] = ','.join(user_ids)
        if dept_ids:
            data["dept_id_list"] = ','.join(dept_ids)
        
        try:
            self.logger.info(f"发送钉钉工作通知: {url}")
            self.logger.info(f"请求参数: {data}")
            
            response = requests.post(url, params=params, json=data)
            result = response.json()
            
            self.logger.info(f"钉钉工作通知发送结果: {result}")
            
            if result.get("errcode") == 0:
                task_id = result.get("task_id")
                return {
                    "success": True, 
                    "message": "发送成功", 
                    "task_id": task_id,
                    "request_id": result.get("request_id")
                }
            else:
                error_msg = f"发送钉钉工作通知失败: {result.get('errmsg')}"
                self.logger.error(error_msg)
                return {
                    "success": False, 
                    "message": error_msg, 
                    "code": result.get("errcode"),
                    "request_id": result.get("request_id")
                }
        except Exception as e:
            error_msg = f"发送钉钉工作通知异常: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "EXCEPTION"}
    
    def _build_message(self, 
                      msg_type: str, 
                      message: str, 
                      title: Optional[str] = None, 
                      message_url: Optional[str] = None, 
                      pic_url: Optional[str] = None) -> Dict:
        """
        构建消息内容
        
        Args:
            msg_type: 消息类型
            message: 消息内容
            title: 消息标题
            message_url: 消息链接
            pic_url: 图片链接
            
        Returns:
            Dict: 消息内容
        """
        msg = {"msgtype": msg_type}
        
        if msg_type == "text":
            msg["text"] = {"content": message}
        elif msg_type == "link":
            msg["link"] = {
                "title": title or "通知消息",
                "text": message,
                "messageUrl": message_url or "",
                "picUrl": pic_url or ""
            }
        elif msg_type == "markdown":
            msg["markdown"] = {
                "title": title or "通知消息",
                "text": message
            }
        elif msg_type == "action_card":
            # 整体跳转的卡片消息
            msg["action_card"] = {
                "title": title or "通知消息",
                "markdown": message,
                "single_title": "查看详情",
                "single_url": message_url or ""
            }
        
        return msg
    
    def get_send_progress(self, task_id: str) -> Dict:
        """
        获取工作通知消息的发送进度
        
        Args:
            task_id: 发送消息时钉钉返回的任务ID
            
        Returns:
            Dict: 发送进度
        """
        access_token = self._get_access_token()
        if not access_token:
            error_msg = "获取钉钉access_token失败"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "ACCESS_TOKEN_FAILED"}
        
        agent_id = self.dingtalk_config.get("agent_id")
        if not agent_id:
            error_msg = "钉钉配置中缺少agent_id"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "AGENT_ID_MISSING"}
        
        url = f"{self.dingtalk_config.get('api_host')}/topapi/message/corpconversation/getsendprogress"
        params = {
            "access_token": access_token
        }
        data = {
            "agent_id": agent_id,
            "task_id": task_id
        }
        
        try:
            self.logger.info(f"获取钉钉工作通知发送进度: {url}")
            response = requests.post(url, params=params, json=data)
            result = response.json()
            
            self.logger.info(f"获取钉钉工作通知发送进度结果: {result}")
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "message": "获取发送进度成功",
                    "progress": result.get("progress"),
                    "status": result.get("status")
                }
            else:
                error_msg = f"获取钉钉工作通知发送进度失败: {result.get('errmsg')}"
                self.logger.error(error_msg)
                return {"success": False, "message": error_msg, "code": result.get("errcode")}
        except Exception as e:
            error_msg = f"获取钉钉工作通知发送进度异常: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "EXCEPTION"}
    
    def get_send_result(self, task_id: str) -> Dict:
        """
        获取工作通知消息的发送结果
        
        Args:
            task_id: 发送消息时钉钉返回的任务ID
            
        Returns:
            Dict: 发送结果
        """
        access_token = self._get_access_token()
        if not access_token:
            error_msg = "获取钉钉access_token失败"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "ACCESS_TOKEN_FAILED"}
        
        agent_id = self.dingtalk_config.get("agent_id")
        if not agent_id:
            error_msg = "钉钉配置中缺少agent_id"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "AGENT_ID_MISSING"}
        
        url = f"{self.dingtalk_config.get('api_host')}/topapi/message/corpconversation/getsendresult"
        params = {
            "access_token": access_token
        }
        data = {
            "agent_id": agent_id,
            "task_id": task_id
        }
        
        try:
            self.logger.info(f"获取钉钉工作通知发送结果: {url}")
            response = requests.post(url, params=params, json=data)
            result = response.json()
            
            self.logger.info(f"获取钉钉工作通知发送结果: {result}")
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "message": "获取发送结果成功",
                    "send_result": result.get("send_result"),
                    "invalid_user_id_list": result.get("invalid_user_id_list", []),
                    "invalid_dept_id_list": result.get("invalid_dept_id_list", []),
                    "read_user_id_list": result.get("read_user_id_list", []),
                    "unread_user_id_list": result.get("unread_user_id_list", [])
                }
            else:
                error_msg = f"获取钉钉工作通知发送结果失败: {result.get('errmsg')}"
                self.logger.error(error_msg)
                return {"success": False, "message": error_msg, "code": result.get("errcode")}
        except Exception as e:
            error_msg = f"获取钉钉工作通知发送结果异常: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "EXCEPTION"}
    
    def recall_work_notification(self, task_id: str) -> Dict:
        """
        撤回工作通知消息
        
        Args:
            task_id: 发送消息时钉钉返回的任务ID
            
        Returns:
            Dict: 撤回结果
        """
        access_token = self._get_access_token()
        if not access_token:
            error_msg = "获取钉钉access_token失败"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "ACCESS_TOKEN_FAILED"}
        
        agent_id = self.dingtalk_config.get("agent_id")
        if not agent_id:
            error_msg = "钉钉配置中缺少agent_id"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "AGENT_ID_MISSING"}
        
        url = f"{self.dingtalk_config.get('api_host')}/topapi/message/corpconversation/recall"
        params = {
            "access_token": access_token
        }
        data = {
            "agent_id": agent_id,
            "msg_task_id": task_id
        }
        
        try:
            self.logger.info(f"撤回钉钉工作通知: {url}")
            response = requests.post(url, params=params, json=data)
            result = response.json()
            
            self.logger.info(f"撤回钉钉工作通知结果: {result}")
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "message": "撤回工作通知成功",
                    "request_id": result.get("request_id")
                }
            else:
                error_msg = f"撤回钉钉工作通知失败: {result.get('errmsg')}"
                self.logger.error(error_msg)
                return {
                    "success": False, 
                    "message": error_msg, 
                    "code": result.get("errcode"),
                    "request_id": result.get("request_id")
                }
        except Exception as e:
            error_msg = f"撤回钉钉工作通知异常: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "EXCEPTION"}
    
    def get_department_list(self, parent_id: str = "1") -> Dict:
        """
        获取部门列表
        
        Args:
            parent_id: 父部门ID，默认为1（根部门）
            
        Returns:
            Dict: 部门列表
        """
        access_token = self._get_access_token()
        if not access_token:
            error_msg = "获取钉钉access_token失败"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "ACCESS_TOKEN_FAILED"}
        
        url = f"{self.dingtalk_config.get('api_host')}/topapi/v2/department/listsub"
        params = {
            "access_token": access_token
        }
        data = {
            "dept_id": parent_id
        }
        
        try:
            self.logger.info(f"获取钉钉部门列表: {url}")
            response = requests.post(url, params=params, json=data)
            result = response.json()
            
            self.logger.info(f"获取钉钉部门列表结果: {result}")
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "message": "获取部门列表成功",
                    "department_list": result.get("result", [])
                }
            else:
                error_msg = f"获取钉钉部门列表失败: {result.get('errmsg')}"
                self.logger.error(error_msg)
                return {"success": False, "message": error_msg, "code": result.get("errcode")}
        except Exception as e:
            error_msg = f"获取钉钉部门列表异常: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "EXCEPTION"}
    
    def get_user_list(self, department_id: str = "1", size: int = 100, cursor: int = 0) -> Dict:
        """
        获取部门用户列表
        
        Args:
            department_id: 部门ID，默认为1（根部门）
            size: 分页大小，最大100
            cursor: 分页游标
            
        Returns:
            Dict: 用户列表
        """
        access_token = self._get_access_token()
        if not access_token:
            error_msg = "获取钉钉access_token失败"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "ACCESS_TOKEN_FAILED"}
        
        url = f"{self.dingtalk_config.get('api_host')}/topapi/v2/user/list"
        params = {
            "access_token": access_token
        }
        data = {
            "dept_id": department_id,
            "cursor": cursor,
            "size": size
        }
        
        try:
            self.logger.info(f"获取钉钉部门用户列表: {url}")
            response = requests.post(url, params=params, json=data)
            result = response.json()
            
            self.logger.info(f"获取钉钉部门用户列表结果: {result}")
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "message": "获取部门用户列表成功",
                    "user_list": result.get("result", {}).get("list", []),
                    "has_more": result.get("result", {}).get("has_more", False),
                    "next_cursor": result.get("result", {}).get("next_cursor", 0)
                }
            else:
                error_msg = f"获取钉钉部门用户列表失败: {result.get('errmsg')}"
                self.logger.error(error_msg)
                return {"success": False, "message": error_msg, "code": result.get("errcode")}
        except Exception as e:
            error_msg = f"获取钉钉部门用户列表异常: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg, "code": "EXCEPTION"}
    
    def get_all_users(self) -> Dict:
        """
        获取企业所有用户（递归获取所有部门的用户）并与ERP系统账号绑定
        
        此方法会执行以下操作：
        1. 获取所有钉钉用户
        2. 通过手机号匹配ERP系统中的account_info表记录
        3. 如果匹配成功，则将钉钉用户ID(userid)更新到account_info表的dingtalk_userid字段
        
        Returns:
            Dict: 所有用户列表及绑定结果
        """
        # 先获取所有部门
        all_departments = []
        departments_to_process = ["1"]  # 从根部门开始
        processed_departments = set()
        
        while departments_to_process:
            dept_id = departments_to_process.pop(0)
            if dept_id in processed_departments:
                continue
                
            processed_departments.add(dept_id)
            dept_result = self.get_department_list(dept_id)
            
            if not dept_result["success"]:
                return dept_result
                
            departments = dept_result.get("department_list", [])
            all_departments.extend(departments)
            
            # 添加子部门到处理队列
            for dept in departments:
                departments_to_process.append(str(dept["dept_id"]))
        
        # 获取所有部门的用户
        all_users = []
        all_dept_ids = list(processed_departments) + [str(dept["dept_id"]) for dept in all_departments]
        
        for dept_id in set(all_dept_ids):
            cursor = 0
            has_more = True
            
            while has_more:
                user_result = self.get_user_list(dept_id, cursor=cursor)
                
                if not user_result["success"]:
                    self.logger.warning(f"获取部门 {dept_id} 的用户列表失败: {user_result.get('message')}")
                    break
                    
                users = user_result.get("user_list", [])
                all_users.extend(users)
                
                has_more = user_result.get("has_more", False)
                cursor = user_result.get("next_cursor", 0)
        
        # 去重（同一用户可能出现在多个部门）
        unique_users = []
        user_ids = set()
        
        for user in all_users:
            if user["userid"] not in user_ids:
                user_ids.add(user["userid"])
                unique_users.append(user)
        
        # 与ERP系统账号绑定
        bind_count = 0
        unbind_count = 0
        
        try:
            from database.db_connection import get_db_connection
            
            # 获取数据库连接
            conn = get_db_connection()
            cursor = conn.cursor()
            
            for user in unique_users:
                # 检查用户是否有手机号
                if not user.get("mobile"):
                    self.logger.warning(f"用户 {user.get('name')}({user.get('userid')}) 没有手机号，无法绑定")
                    unbind_count += 1
                    continue
                    
                # 通过手机号查询account_info表
                query = "SELECT id FROM account_info WHERE account = %s"
                cursor.execute(query, (user.get("mobile"),))
                result = cursor.fetchone()
                
                if result:
                    # 更新account_info表中的dingtalk_userid字段
                    update_query = "UPDATE account_info SET dingtalk_userid = %s WHERE id = %s"
                    cursor.execute(update_query, (user.get("userid"), result["id"]))
                    conn.commit()
                    bind_count += 1
                else:
                    self.logger.warning(f"未找到与手机号 {user.get('mobile')} 匹配的ERP账号")
                    unbind_count += 1
                
            cursor.close()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"与ERP系统账号绑定失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取用户成功，但绑定ERP账号失败: {str(e)}",
                "total_count": len(unique_users)
            }
        
        return {
            "success": True,
            "message": "获取企业所有用户并绑定ERP账号成功",
            "total_count": len(unique_users),
            "bind_count": bind_count,
            "unbind_count": unbind_count
        } 