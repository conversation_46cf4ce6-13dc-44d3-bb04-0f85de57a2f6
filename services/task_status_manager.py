#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""基于文件系统的任务状态管理器"""

import json
import time
import threading
import logging
from typing import Dict, Any, Optional, Callable, List
from pathlib import Path
import fcntl

logger = logging.getLogger(__name__)

def normalize_task_key(task_key: str) -> str:
    """
    标准化任务键，将全角括号转换为半角括号以保持前后端一致性

    Args:
        task_key: 原始任务键

    Returns:
        str: 标准化后的任务键
    """
    return task_key.replace('（', '(').replace('）', ')')

class TaskStatusManager:
    """基于文件系统的任务状态管理器，支持多进程环境"""

    def __init__(self, status_dir: str = "data/task_status", cleanup_interval: int = 600):
        """
        初始化任务状态管理器

        Args:
            status_dir: 状态文件存储目录
            cleanup_interval: 自动清理过期任务的间隔时间（秒）
        """
        self.status_dir = Path(status_dir)
        self.cleanup_interval = cleanup_interval
        self.status_dir.mkdir(parents=True, exist_ok=True)

        # 状态变更回调函数列表
        self._status_change_callbacks: List[Callable[[str, Dict[str, Any]], None]] = []

        # 启动清理线程
        self._cleanup_thread = threading.Thread(target=self._cleanup_expired_tasks, daemon=True)
        self._cleanup_thread.start()

        logger.info(f"任务状态管理器已初始化，状态目录: {self.status_dir}")

    def add_status_change_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """添加状态变更回调函数"""
        self._status_change_callbacks.append(callback)
        logger.info(f"已添加状态变更回调函数: {callback.__name__}")

    def _get_task_file_path(self, task_key: str) -> Path:
        """获取任务状态文件路径"""
        # 使用安全的文件名
        safe_filename = "".join(c for c in task_key if c.isalnum() or c in ('-', '_')).rstrip()
        return self.status_dir / f"{safe_filename}.json"

    def _read_task_status(self, task_key: str) -> Optional[Dict[str, Any]]:
        """读取任务状态（带文件锁）"""
        file_path = self._get_task_file_path(task_key)

        if not file_path.exists():
            return None

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # 获取共享锁进行读取
                fcntl.flock(f.fileno(), fcntl.LOCK_SH)
                try:
                    data = json.load(f)
                    return data
                finally:
                    fcntl.flock(f.fileno(), fcntl.LOCK_UN)
        except (json.JSONDecodeError, FileNotFoundError) as e:
            logger.warning(f"读取任务状态文件失败: {task_key}, 错误: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"读取任务状态时发生异常: {task_key}, 错误: {str(e)}")
            return None

    def _write_task_status(self, task_key: str, status_data: Dict[str, Any]) -> bool:
        """写入任务状态（带文件锁）"""
        file_path = self._get_task_file_path(task_key)

        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)

            with open(file_path, 'w', encoding='utf-8') as f:
                # 获取排他锁进行写入
                fcntl.flock(f.fileno(), fcntl.LOCK_EX)
                try:
                    json.dump(status_data, f, ensure_ascii=False, indent=2)
                    return True
                finally:
                    fcntl.flock(f.fileno(), fcntl.LOCK_UN)
        except Exception as e:
            logger.error(f"写入任务状态文件失败: {task_key}, 错误: {str(e)}")
            return False

    def update_task_status(self, task_key: str, status: int, progress: int = 0,
                          error_message: Optional[str] = None, **kwargs) -> bool:
        """
        更新任务状态

        Args:
            task_key: 任务唯一标识
            status: 任务状态 (0=等待中, 1=进行中, 2=已完成, 3=失败)
            progress: 进度百分比 (0-100)
            error_message: 错误信息（仅在失败时）
            **kwargs: 其他自定义字段

        Returns:
            bool: 更新是否成功
        """
        # 标准化任务键
        normalized_task_key = normalize_task_key(task_key)
        current_time = time.time()

        # 读取现有状态
        existing_status = self._read_task_status(normalized_task_key) or {}

        # 构建新状态数据
        status_data = {
            'task_key': normalized_task_key,
            'status': status,
            'progress': progress,
            'updated_at': current_time,
            'start_time': existing_status.get('start_time', current_time),
        }

        # 添加状态文本
        status_texts = {0: '等待中', 1: '进行中', 2: '已完成', 3: '失败'}
        status_data['status_text'] = status_texts.get(status, '未知')

        # 处理完成或失败状态
        if status in [2, 3]:
            status_data['completed_at'] = current_time
            if status == 2:
                status_data['progress'] = 100

        # 添加错误信息
        if error_message:
            status_data['error_message'] = error_message

        # 添加自定义字段
        status_data.update(kwargs)

        # 写入文件
        success = self._write_task_status(normalized_task_key, status_data)

        if success:
            logger.info(f"任务状态已更新: {normalized_task_key} -> {status_data['status_text']}")

            # 触发状态变更回调
            for callback in self._status_change_callbacks:
                try:
                    callback(normalized_task_key, status_data.copy())
                except Exception as e:
                    logger.error(f"执行状态变更回调失败: {callback.__name__}, 错误: {str(e)}")

        return success

    def get_task_status(self, task_key: str) -> Optional[Dict[str, Any]]:
        """获取单个任务状态"""
        normalized_task_key = normalize_task_key(task_key)
        return self._read_task_status(normalized_task_key)

    def get_all_tasks_status(self, task_prefix: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        获取所有任务状态

        Args:
            task_prefix: 任务前缀过滤器（可选）

        Returns:
            Dict[task_key, status_data]: 所有任务状态
        """
        all_tasks = {}

        try:
            for file_path in self.status_dir.glob("*.json"):
                task_key = file_path.stem

                # 应用前缀过滤器
                if task_prefix and not task_key.startswith(task_prefix):
                    continue

                status_data = self._read_task_status(task_key)
                if status_data:
                    all_tasks[task_key] = status_data
        except Exception as e:
            logger.error(f"获取所有任务状态时发生异常: {str(e)}")

        return all_tasks

    def remove_task_status(self, task_key: str) -> bool:
        """删除任务状态"""
        normalized_task_key = normalize_task_key(task_key)
        file_path = self._get_task_file_path(normalized_task_key)

        try:
            if file_path.exists():
                file_path.unlink()
                logger.info(f"已删除任务状态: {normalized_task_key}")
                return True
            return False
        except Exception as e:
            logger.error(f"删除任务状态失败: {normalized_task_key}, 错误: {str(e)}")
            return False

    def _cleanup_expired_tasks(self):
        """清理过期任务的后台线程"""
        while True:
            try:
                current_time = time.time()
                expired_tasks = []

                for file_path in self.status_dir.glob("*.json"):
                    task_key = file_path.stem
                    status_data = self._read_task_status(task_key)

                    if status_data:
                        # 检查是否过期（已完成或失败的任务超过清理间隔时间）
                        completed_at = status_data.get('completed_at')
                        if completed_at and (current_time - completed_at) > self.cleanup_interval:
                            expired_tasks.append(task_key)

                # 删除过期任务
                for task_key in expired_tasks:
                    self.remove_task_status(task_key)

                if expired_tasks:
                    logger.info(f"已清理 {len(expired_tasks)} 个过期任务")

            except Exception as e:
                logger.error(f"清理过期任务时发生异常: {str(e)}")

            # 等待下一次清理
            time.sleep(self.cleanup_interval)

# 全局任务状态管理器实例
task_status_manager = TaskStatusManager()

# 添加SSE广播回调
def _sse_broadcast_callback(task_key: str, status_data: Dict[str, Any]):
    """SSE广播回调函数"""
    try:
        # 导入SSE服务并触发广播
        from services.sse_service import broadcast_task_status_update
        import asyncio

        # 在事件循环中执行异步广播
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，创建任务
                asyncio.create_task(broadcast_task_status_update(task_key, status_data))
            else:
                # 如果事件循环未运行，直接运行
                loop.run_until_complete(broadcast_task_status_update(task_key, status_data))
        except RuntimeError:
            # 如果没有事件循环，创建新的事件循环
            asyncio.run(broadcast_task_status_update(task_key, status_data))
    except Exception as e:
        logger.error(f"SSE广播回调失败: {str(e)}")

# 注册SSE广播回调
task_status_manager.add_status_change_callback(_sse_broadcast_callback)

# 便捷函数
def update_task_status(task_key: str, status: int, progress: int = 0,
                      error_message: Optional[str] = None, **kwargs) -> bool:
    """更新任务状态的便捷函数"""
    return task_status_manager.update_task_status(task_key, status, progress, error_message, **kwargs)

def get_task_status(task_key: str) -> Optional[Dict[str, Any]]:
    """获取任务状态的便捷函数"""
    return task_status_manager.get_task_status(task_key)

def get_all_tasks_status(task_prefix: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
    """获取所有任务状态的便捷函数"""
    return task_status_manager.get_all_tasks_status(task_prefix)

def remove_task_status(task_key: str) -> bool:
    """删除任务状态的便捷函数"""
    return task_status_manager.remove_task_status(task_key)
