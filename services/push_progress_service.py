#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
import logging
from typing import Dict, Any, Optional

import redis

from services.sse_service import sse_service

logger = logging.getLogger(__name__)


class PushProgressService:
    def __init__(self):
        try:
            self.redis_conn = redis.StrictRedis(
                host="**************",
                port=6379,
                password="Meiyoumima333.",
                decode_responses=True
            )
            self.redis_conn.ping()
        except Exception as e:
            logger.error(f"推送进度服务Redis连接失败: {str(e)}")
            self.redis_conn = None
    
    def _get_progress_key(self, factory_id: str) -> str:
        return f"push_progress:{factory_id}"

    def _get_all_progress_pattern(self) -> str:
        return "push_progress:*"
    
    async def start_push_task(self, factory_id: str, task_id: str,
                             initial_step: int = 1, initial_message: str = "开始推送") -> bool:
        if not self.redis_conn:
            return False
        
        try:
            progress_data = {
                "factory_id": factory_id,
                "task_id": task_id,
                "step": initial_step,
                "status": "processing",
                "message": initial_message,
                "start_time": time.time(),
                "updated_at": time.time(),
                "is_background": False
            }
            
            key = self._get_progress_key(factory_id)
            self.redis_conn.setex(key, 3600, json.dumps(progress_data))

            await sse_service.broadcast_event("push_status", {
                "task_id": task_id,
                "factory_id": factory_id,
                "step": initial_step,
                "status": "processing",
                "message": initial_message
            })

            return True
            
        except Exception as e:
            logger.error(f"创建推送进度失败: {str(e)}")
            return False
    
    async def update_push_progress(self, factory_id: str, step: int,
                                  status: str, message: str) -> bool:
        if not self.redis_conn:
            return False
        
        try:
            key = self._get_progress_key(factory_id)
            existing_data = self.redis_conn.get(key)

            if not existing_data:
                return False
            
            progress_data = json.loads(existing_data)
            progress_data.update({
                "step": step,
                "status": status,
                "message": message,
                "updated_at": time.time()
            })

            if status in ["completed", "error"]:
                progress_data["end_time"] = time.time()

            self.redis_conn.setex(key, 3600, json.dumps(progress_data))

            await sse_service.broadcast_event("push_status", {
                "task_id": progress_data["task_id"],
                "factory_id": factory_id,
                "step": step,
                "status": status,
                "message": message
            })

            return True
            
        except Exception as e:
            logger.error(f"更新推送进度失败: {str(e)}")
            return False
    
    async def set_background_push(self, factory_id: str) -> bool:
        if not self.redis_conn:
            return False
        try:
            key = self._get_progress_key(factory_id)
            existing_data = self.redis_conn.get(key)
            if not existing_data:
                return False

            progress_data = json.loads(existing_data)
            current_status = progress_data.get("status", "")

            if current_status in ["completed", "error"]:
                self.redis_conn.delete(key)
                await sse_service.broadcast_event("push_completed", {
                    "factory_id": factory_id,
                    "task_id": progress_data["task_id"],
                    "success": current_status == "completed",
                    "message": "任务已完成",
                    "is_background": True
                })
                return True

            progress_data["is_background"] = True
            progress_data["background_time"] = time.time()
            self.redis_conn.setex(key, 3600, json.dumps(progress_data))

            await sse_service.broadcast_event("push_background", {
                "factory_id": factory_id,
                "task_id": progress_data["task_id"]
            })
            return True
        except Exception as e:
            logger.error(f"设置后台推送失败: {str(e)}")
            return False
    
    async def complete_push_task(self, factory_id: str, success: bool = True,
                                message: str = None, is_sensitive_word_error: bool = False) -> bool:
        if not self.redis_conn:
            return False

        try:
            key = self._get_progress_key(factory_id)
            existing_data = self.redis_conn.get(key)

            if not existing_data:
                return False

            progress_data = json.loads(existing_data)
            is_background = progress_data.get("is_background", False)

            if is_sensitive_word_error:
                progress_data.update({
                    "step": 2,
                    "status": "sensitive_word_error",
                    "message": message or "内容包含敏感词，无法推送",
                    "updated_at": time.time(),
                    "end_time": time.time(),
                    "can_retry": True
                })

                self.redis_conn.setex(key, 3600, json.dumps(progress_data))

                await sse_service.broadcast_event("push_sensitive_word_error", {
                    "factory_id": factory_id,
                    "task_id": progress_data["task_id"],
                    "message": message or "内容包含敏感词，无法推送"
                })
            else:
                final_status = "completed" if success else "error"
                final_message = message or ("推送完成" if success else "推送失败")

                await self.update_push_progress(factory_id, 5, final_status, final_message)

                if is_background:
                    self.redis_conn.delete(key)
                else:
                    self.redis_conn.expire(key, 30)

                await sse_service.broadcast_event("push_completed", {
                    "factory_id": factory_id,
                    "task_id": progress_data["task_id"],
                    "success": success,
                    "message": final_message,
                    "is_background": is_background
                })

            return True

        except Exception as e:
            logger.error(f"完成推送任务失败: {str(e)}")
            return False
    
    def get_push_progress(self, factory_id: str) -> Optional[Dict[str, Any]]:
        if not self.redis_conn:
            return None
        
        try:
            key = self._get_progress_key(factory_id)
            data = self.redis_conn.get(key)
            
            if data:
                return json.loads(data)
            return None
            
        except Exception as e:
            logger.error(f"获取推送进度失败: {str(e)}")
            return None
    
    def get_all_active_pushes(self) -> Dict[str, Dict[str, Any]]:
        if not self.redis_conn:
            return {}
        
        try:
            pattern = self._get_all_progress_pattern()
            keys = self.redis_conn.keys(pattern)
            
            active_pushes = {}
            for key in keys:
                factory_id = key.split(":")[-1]
                data = self.redis_conn.get(key)
                if data:
                    active_pushes[factory_id] = json.loads(data)
            
            return active_pushes
            
        except Exception as e:
            logger.error(f"获取所有活跃推送失败: {str(e)}")
            return {}
    
    def cleanup_expired_pushes(self) -> int:
        if not self.redis_conn:
            return 0

        try:
            pattern = self._get_all_progress_pattern()
            keys = self.redis_conn.keys(pattern)

            cleaned_count = 0
            current_time = time.time()

            for key in keys:
                data = self.redis_conn.get(key)
                if data:
                    progress_data = json.loads(data)
                    # 清理超过2小时的记录
                    if current_time - progress_data.get("start_time", 0) > 7200:
                        self.redis_conn.delete(key)
                        cleaned_count += 1
            return cleaned_count

        except Exception as e:
            logger.error(f"清理过期推送记录失败: {str(e)}")
            return 0

    def clear_push_progress(self, factory_id: str) -> bool:
        if not self.redis_conn:
            return False
        try:
            key = self._get_progress_key(factory_id)
            exists = self.redis_conn.exists(key)
            if exists:
                self.redis_conn.delete(key)
            return True
        except Exception as e:
            logger.error(f"清理推送进度失败: {str(e)}")
            return False


push_progress_service = PushProgressService()
