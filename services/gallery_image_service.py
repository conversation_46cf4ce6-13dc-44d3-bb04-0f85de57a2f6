import logging
import random
from typing import Dict, List, Tuple
from database.db_connection import get_db_connection

logger = logging.getLogger(__name__)




class GalleryImageService:






    def get_mixed_gallery_images(self, park_name: str, strategy: str = None, exclude_fields: List[str] = None) -> Tuple[List[str], str]:
        """从park_gallery_unified表中随机选择最多9张图片"""
        if not park_name:
            return [], "园区名称为空"

        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # 从park_gallery_unified表查询园区图片
            query = """
            SELECT images_json
            FROM park_gallery_unified
            WHERE park_name = %s
            """

            cursor.execute(query, (park_name,))
            result = cursor.fetchone()

            if not result or not result.get('images_json'):
                return [], "未找到图片"

            import json
            try:
                images_data = json.loads(result['images_json'])
                raw_images = images_data.get("images", [])
            except Exception as e:
                return [], "图片数据格式错误"

            if not raw_images:
                return [], "没有可用图片"

            # 提取所有图片URL
            all_image_urls = []
            for img in raw_images:
                url = img.get("url", "").strip()
                if url:
                    all_image_urls.append(url)

            if not all_image_urls:
                return [], "没有有效图片URL"

            selected_count = min(9, len(all_image_urls))
            selected_images = random.sample(all_image_urls, selected_count)

            return selected_images, f"统一图库随机选择({len(selected_images)}张)"

        except Exception as e:
            return [], f"获取图片失败: {str(e)}"
        finally:
            if 'cursor' in locals() and cursor:
                cursor.close()
            if 'conn' in locals() and conn:
                conn.close()

    def assign_images_to_fields(self, image_urls: List[str]) -> Dict[str, str]:
        result = {'thumb': '', 'photooutdoor': '', 'photointerior': '', 'floorplans': ''}
        if not image_urls:
            return result

        shuffled_images = image_urls.copy()
        random.shuffle(shuffled_images)

        thumb_image = ''
        if len(shuffled_images) > 0:
            thumb_image = shuffled_images[0]
            result['thumb'] = thumb_image

        remaining_images = shuffled_images[1:] if len(shuffled_images) > 1 else []

        if remaining_images:
            third = max(1, len(remaining_images) // 3)
            outdoor_images = remaining_images[:third]
            if thumb_image:
                outdoor_images = [thumb_image] + outdoor_images

            result['photooutdoor'] = ','.join(outdoor_images)
            result['photointerior'] = ','.join(remaining_images[third:2*third])
            result['floorplans'] = ','.join(remaining_images[2*third:])
        else:
            if thumb_image:
                result['photooutdoor'] = thumb_image

        return result

    def get_assigned_gallery_images(self, park_name: str, strategy: str = None, exclude_fields: List[str] = None) -> Tuple[Dict[str, str], str]:
        image_urls, strategy_desc = self.get_mixed_gallery_images(park_name, strategy, exclude_fields)
        assigned_images = self.assign_images_to_fields(image_urls)
        return assigned_images, strategy_desc


gallery_image_service = GalleryImageService()
