#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阿里云对象存储服务
提供文件上传、下载和管理的业务逻辑
"""

import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union, BinaryIO
import mimetypes

from services.base_service import BaseService
from utils.oss_utils import OssClient
from config.oss_settings import (
    ACCESS_KEY_ID, 
    ACCESS_KEY_SECRET, 
    ENDPOINT, 
    BUCKET_NAME,
    OSS_DOMAIN,
    STORAGE_PATHS,
    URL_EXPIRES,
    ALLOWED_FILE_TYPES,
    FILE_SIZE_LIMITS
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OssService(BaseService):
    """阿里云对象存储服务类"""
    
    def __init__(self):
        """初始化OSS服务"""
        super().__init__()
        self.client = OssClient(
            access_key_id=ACCESS_KEY_ID,
            access_key_secret=ACCESS_KEY_SECRET,
            endpoint=ENDPOINT,
            bucket_name=BUCKET_NAME,
            oss_domain=OSS_DOMAIN
        )
        logger.info("初始化OSS服务")
    
    def upload_file(
        self, 
        file_path: str, 
        file_type: str, 
        storage_type: str, 
        storage_subtype: str = None
    ) -> Tuple[bool, Union[str, Dict]]:
        """
        上传文件到OSS
        
        Args:
            file_path: 本地文件路径
            file_type: 文件类型，如 'image', 'document', 'video'
            storage_type: 存储类型，如 'house', 'user', 'contract'
            storage_subtype: 存储子类型，如 'thumbnail', 'avatar'
            
        Returns:
            上传状态和结果信息
        """
        try:
            # 验证文件是否存在
            if not os.path.exists(file_path):
                return False, {"error": f"文件不存在: {file_path}"}
            
            # 获取文件扩展名并检查是否允许
            _, ext = os.path.splitext(file_path)
            ext = ext[1:].lower()  # 去掉前导点并转为小写
            
            if ext not in ALLOWED_FILE_TYPES.get(file_type, []):
                return False, {"error": f"不支持的文件类型: {ext}，允许的类型: {ALLOWED_FILE_TYPES.get(file_type, [])}"}
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            size_limit = FILE_SIZE_LIMITS.get(file_type, 0)
            
            if size_limit > 0 and file_size > size_limit:
                return False, {
                    "error": f"文件过大: {file_size} 字节，限制: {size_limit} 字节"
                }
            
            # 生成OSS存储路径
            oss_path = self._generate_oss_path(file_path, storage_type, storage_subtype)
            
            # 获取文件内容类型
            content_type = mimetypes.guess_type(file_path)[0]
            headers = {}
            if content_type:
                headers['Content-Type'] = content_type
            
            # 上传文件
            success, result = self.client.upload_file(file_path, oss_path, headers)
            
            if success:
                # 返回成功结果
                return True, {
                    "url": result,
                    "path": oss_path,
                    "size": file_size,
                    "type": content_type,
                    "name": os.path.basename(file_path)
                }
            else:
                return False, {"error": result}
        
        except Exception as e:
            logger.error(f"文件上传服务发生异常: {str(e)}")
            return False, {"error": str(e)}
    
    def upload_binary(
        self, 
        data: bytes, 
        filename: str, 
        file_type: str, 
        storage_type: str, 
        storage_subtype: str = None
    ) -> Tuple[bool, Union[str, Dict]]:
        """
        上传二进制数据到OSS
        
        Args:
            data: 二进制数据
            filename: 文件名，用于确定文件类型和扩展名
            file_type: 文件类型，如 'image', 'document', 'video'
            storage_type: 存储类型，如 'house', 'user', 'contract'
            storage_subtype: 存储子类型，如 'thumbnail', 'avatar'
            
        Returns:
            上传状态和结果信息
        """
        try:
            # 获取文件扩展名并检查是否允许
            _, ext = os.path.splitext(filename)
            ext = ext[1:].lower()  # 去掉前导点并转为小写
            
            if ext not in ALLOWED_FILE_TYPES.get(file_type, []):
                return False, {"error": f"不支持的文件类型: {ext}，允许的类型: {ALLOWED_FILE_TYPES.get(file_type, [])}"}
            
            # 检查数据大小
            data_size = len(data)
            size_limit = FILE_SIZE_LIMITS.get(file_type, 0)
            
            if size_limit > 0 and data_size > size_limit:
                return False, {
                    "error": f"数据过大: {data_size} 字节，限制: {size_limit} 字节"
                }
            
            # 生成OSS存储路径
            oss_path = self._generate_oss_path(filename, storage_type, storage_subtype)
            
            # 获取内容类型
            content_type = mimetypes.guess_type(filename)[0]
            
            # 上传二进制数据
            success, result = self.client.upload_binary(data, oss_path, content_type)
            
            if success:
                # 返回成功结果
                return True, {
                    "url": result,
                    "path": oss_path,
                    "size": data_size,
                    "type": content_type,
                    "name": os.path.basename(filename)
                }
            else:
                return False, {"error": result}
        
        except Exception as e:
            logger.error(f"二进制数据上传服务发生异常: {str(e)}")
            return False, {"error": str(e)}
    
    def delete_file(self, oss_path: str) -> Tuple[bool, Union[str, Dict]]:
        """
        删除OSS中的文件
        
        Args:
            oss_path: OSS中的文件路径
            
        Returns:
            删除状态和结果信息
        """
        try:
            # 检查文件是否存在
            if not self.client.check_file_exists(oss_path):
                return False, {"error": f"文件不存在: {oss_path}"}
            
            # 执行删除操作
            if self.client.delete_file(oss_path):
                return True, {"message": f"文件已删除: {oss_path}"}
            else:
                return False, {"error": f"文件删除失败: {oss_path}"}
        
        except Exception as e:
            logger.error(f"文件删除服务发生异常: {str(e)}")
            return False, {"error": str(e)}
    
    def get_file_url(self, oss_path: str, expires: int = None) -> Tuple[bool, Union[str, Dict]]:
        """
        获取文件的临时访问URL
        
        Args:
            oss_path: OSS中的文件路径
            expires: URL有效期，单位为秒，默认使用配置值
            
        Returns:
            获取状态和URL信息
        """
        try:
            # 检查文件是否存在
            if not self.client.check_file_exists(oss_path):
                return False, {"error": f"文件不存在: {oss_path}"}
            
            # 获取临时URL
            if expires is None:
                expires = URL_EXPIRES
                
            url = self.client.get_file_url(oss_path, expires)
            
            if url:
                return True, {
                    "url": url,
                    "path": oss_path,
                    "expires": expires,
                    "expires_at": datetime.now().timestamp() + expires
                }
            else:
                return False, {"error": f"获取临时URL失败: {oss_path}"}
        
        except Exception as e:
            logger.error(f"获取文件URL服务发生异常: {str(e)}")
            return False, {"error": str(e)}
    
    def list_files(
        self, 
        storage_type: str = None, 
        storage_subtype: str = None, 
        max_files: int = 100
    ) -> Tuple[bool, Union[List, Dict]]:
        """
        列出文件
        
        Args:
            storage_type: 存储类型，如 'house', 'user', 'contract'
            storage_subtype: 存储子类型，如 'thumbnail', 'avatar'
            max_files: 最大返回条目数
            
        Returns:
            列表获取状态和文件列表
        """
        try:
            # 构建前缀路径
            prefix = ""
            if storage_type:
                if storage_subtype and storage_type in STORAGE_PATHS and storage_subtype in STORAGE_PATHS[storage_type]:
                    prefix = f"{STORAGE_PATHS[storage_type][storage_subtype]}/"
                elif storage_type in STORAGE_PATHS:
                    # 如果只有存储类型，尝试使用该类型的根路径
                    if isinstance(STORAGE_PATHS[storage_type], str):
                        prefix = f"{STORAGE_PATHS[storage_type]}/"
            
            # 获取文件列表
            files = self.client.list_files(prefix=prefix, max_keys=max_files)
            
            return True, files
        
        except Exception as e:
            logger.error(f"列出文件服务发生异常: {str(e)}")
            return False, {"error": str(e)}
    
    def _generate_oss_path(self, filename: str, storage_type: str, storage_subtype: str = None) -> str:
        """
        生成OSS存储路径
        
        Args:
            filename: 文件名
            storage_type: 存储类型，如 'house', 'user', 'contract'
            storage_subtype: 存储子类型，如 'thumbnail', 'avatar'
            
        Returns:
            OSS存储路径
        """
        # 获取文件扩展名
        _, ext = os.path.splitext(filename)
        
        # 生成基于时间和文件名的唯一标识
        basename = os.path.basename(filename)
        name_without_ext = os.path.splitext(basename)[0]
        name_part = ''.join(e for e in name_without_ext if e.isalnum())[:20]  # 取文件名的前20个字母数字字符
        
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        
        # 构建存储路径
        storage_path = "temp"  # 默认为临时目录
        
        if storage_type in STORAGE_PATHS:
            if storage_subtype and storage_subtype in STORAGE_PATHS[storage_type]:
                storage_path = STORAGE_PATHS[storage_type][storage_subtype]
            elif isinstance(STORAGE_PATHS[storage_type], str):
                storage_path = STORAGE_PATHS[storage_type]
        
        # 拼接最终的OSS路径
        return f"{storage_path}/{timestamp}_{name_part}{ext}" 