#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""独立的SSE（Server-Sent Events）服务模块"""

import json
import time
import asyncio
import logging
from typing import Dict, Any, Set, Optional, List
from fastapi import Request
from sse_starlette.sse import EventSourceResponse

logger = logging.getLogger(__name__)

class SSEService:
    """SSE服务管理器，负责管理客户端连接和事件广播"""
    
    def __init__(self):
        """初始化SSE服务"""
        # 客户端连接集合
        self.event_clients: Set[asyncio.Queue] = set()
        # 异步锁，保护客户端集合
        self.event_lock = asyncio.Lock()
        # 待广播的消息队列
        self.pending_broadcasts: List[Dict[str, Any]] = []
        # 广播队列锁
        self.broadcast_lock = asyncio.Lock()
        
        logger.info("SSE服务已初始化")
    
    async def add_client(self, client_queue: asyncio.Queue):
        """添加客户端连接"""
        async with self.event_lock:
            self.event_clients.add(client_queue)
            logger.info(f"新客户端已连接，当前连接数: {len(self.event_clients)}")
    
    async def remove_client(self, client_queue: asyncio.Queue):
        """移除客户端连接"""
        async with self.event_lock:
            self.event_clients.discard(client_queue)
            logger.info(f"客户端已断开连接，当前连接数: {len(self.event_clients)}")
    
    async def broadcast_event(self, event_type: str, data: Dict[str, Any], 
                             event_id: Optional[str] = None):
        """
        广播事件到所有连接的客户端
        
        Args:
            event_type: 事件类型
            data: 事件数据
            event_id: 事件ID（可选）
        """
        if not self.event_clients:
            logger.debug("没有连接的客户端，跳过广播")
            return
        
        # 构建事件数据
        event_data = {
            "type": event_type,
            "data": data,
            "timestamp": time.time()
        }
        
        payload = json.dumps(event_data, ensure_ascii=False)
        
        async with self.event_lock:
            disconnected_clients = set()
            
            # 向所有客户端发送事件
            for client_queue in self.event_clients:
                try:
                    await client_queue.put(payload)
                except Exception as e:
                    logger.error(f"向客户端发送事件失败: {str(e)}")
                    disconnected_clients.add(client_queue)
            
            # 移除断开连接的客户端
            for client_queue in disconnected_clients:
                self.event_clients.discard(client_queue)
        
        logger.info(f"事件已广播给 {len(self.event_clients)} 个客户端: {event_type}")
    
    async def queue_broadcast(self, event_type: str, data: Dict[str, Any]):
        """将广播事件加入队列，由后台任务处理"""
        async with self.broadcast_lock:
            self.pending_broadcasts.append({
                "event_type": event_type,
                "data": data,
                "timestamp": time.time()
            })
            logger.debug(f"事件已加入广播队列: {event_type}, 队列长度: {len(self.pending_broadcasts)}")
    
    async def process_broadcast_queue(self):
        """处理广播队列中的事件"""
        async with self.broadcast_lock:
            broadcasts_to_process = self.pending_broadcasts.copy()
            self.pending_broadcasts.clear()
        
        for broadcast in broadcasts_to_process:
            try:
                await self.broadcast_event(
                    broadcast["event_type"],
                    broadcast["data"]
                )
            except Exception as e:
                logger.error(f"处理广播事件失败: {str(e)}")
    
    async def create_event_stream(self, request: Request, 
                                 initial_data: Optional[Dict[str, Any]] = None) -> EventSourceResponse:
        """
        创建SSE事件流
        
        Args:
            request: FastAPI请求对象
            initial_data: 初始数据（可选）
        
        Returns:
            EventSourceResponse: SSE响应对象
        """
        # 创建客户端队列
        client_queue = asyncio.Queue()
        
        # 注册客户端
        await self.add_client(client_queue)
        
        async def event_generator():
            try:
                # 发送连接成功事件
                initial_event_data = json.dumps({
                    "type": "connected",
                    "message": "SSE连接已建立",
                    "timestamp": time.time()
                })
                yield {
                    "event": "connected",
                    "id": str(id(client_queue)),
                    "retry": 5000,  # 重连间隔（毫秒）
                    "data": initial_event_data
                }
                
                # 发送初始数据（如果提供）
                if initial_data:
                    initial_payload = json.dumps({
                        "type": "initial_data",
                        "data": initial_data,
                        "timestamp": time.time()
                    })
                    yield {
                        "event": "message",
                        "id": str(id(client_queue)),
                        "data": initial_payload
                    }
                
                # 持续监听队列
                while True:
                    # 检查客户端是否已断开连接
                    if await request.is_disconnected():
                        logger.info("客户端已断开连接")
                        break
                    
                    # 等待队列中的新消息，设置超时以便定期检查连接状态
                    try:
                        data = await asyncio.wait_for(client_queue.get(), timeout=30.0)
                        yield {
                            "event": "message",
                            "id": str(id(client_queue)),
                            "data": data
                        }
                    except asyncio.TimeoutError:
                        # 定期发送ping保持连接
                        ping_data = json.dumps({
                            "type": "ping",
                            "timestamp": time.time()
                        })
                        yield {
                            "event": "ping",
                            "id": str(id(client_queue)),
                            "data": ping_data
                        }
            
            except Exception as e:
                logger.error(f"SSE事件流出错: {str(e)}")
            finally:
                # 客户端断开连接时，从集合中移除
                await self.remove_client(client_queue)
        
        return EventSourceResponse(event_generator())
    
    async def broadcast_task_status_update(self, task_key: str, status_data: Dict[str, Any]):
        """广播任务状态更新事件"""
        await self.queue_broadcast("task_update", {task_key: status_data})

    async def broadcast_push_status(self, task_id: str, factory_id: str, step: int, status: str, message: str):
        """广播推送状态更新事件"""
        await self.queue_broadcast("push_status", {
            "task_id": task_id,
            "factory_id": factory_id,
            "step": step,
            "status": status,
            "message": message,
            "timestamp": time.time()
        })
    
    async def start_background_processor(self):
        """启动后台广播处理器"""
        async def processor():
            while True:
                try:
                    await self.process_broadcast_queue()
                except Exception as e:
                    logger.error(f"处理广播队列时出错: {str(e)}")
                
                await asyncio.sleep(1)  # 每秒处理一次
        
        # 创建后台任务
        task = asyncio.create_task(processor())
        logger.info("SSE后台广播处理器已启动")
        return task

# 全局SSE服务实例
sse_service = SSEService()

# 便捷函数
async def broadcast_event(event_type: str, data: Dict[str, Any], event_id: Optional[str] = None):
    """广播事件的便捷函数"""
    await sse_service.broadcast_event(event_type, data, event_id)

async def queue_broadcast(event_type: str, data: Dict[str, Any]):
    """队列广播的便捷函数"""
    await sse_service.queue_broadcast(event_type, data)

async def create_event_stream(request: Request, initial_data: Optional[Dict[str, Any]] = None) -> EventSourceResponse:
    """创建事件流的便捷函数"""
    return await sse_service.create_event_stream(request, initial_data)

async def broadcast_task_status_update(task_key: str, status_data: Dict[str, Any]):
    """广播任务状态更新的便捷函数"""
    await sse_service.broadcast_task_status_update(task_key, status_data)

async def start_sse_background_processor():
    """启动SSE后台处理器的便捷函数"""
    return await sse_service.start_background_processor()
